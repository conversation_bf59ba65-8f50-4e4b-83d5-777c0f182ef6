import numpy as np
import pandas as pd
import random
from sklearn.preprocessing import MinMaxScaler
import tensorflow as tf
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import LSTM, Dense

# 读取Excel文件中的观测点数据
def read_observation_data(file_path):
    df = pd.read_excel(file_path)
    return df

# 读取观测点1和观测点2数据
observation1_data = read_observation_data('G:/数据汇总.xlsx',sheet_name="观测点1")
observation2_data = read_observation_data('G:/数据汇总.xlsx',sheet_name="观测点2")

# 4. 拥堵预测：使用LSTM机器学习模型
# 从观测点1和观测点2的历史数据中提取车流量、车流密度、平均速度等特征
vehicle_count_data_1 = observation1_data['Vehicle Count'].values
average_speed_data_1 = observation1_data['Average Speed'].values
traffic_density_data_1 = observation1_data['Traffic Density'].values

vehicle_count_data_2 = observation2_data['Vehicle Count'].values
average_speed_data_2 = observation2_data['Average Speed'].values
traffic_density_data_2 = observation2_data['Traffic Density'].values

# 将观测点1和观测点2的数据进行合并
vehicle_count_data = np.concatenate((vehicle_count_data_1, vehicle_count_data_2), axis=0)
average_speed_data = np.concatenate((average_speed_data_1, average_speed_data_2), axis=0)
traffic_density_data = np.concatenate((traffic_density_data_1, traffic_density_data_2), axis=0)

# 合并数据为特征矩阵
data = np.column_stack((vehicle_count_data, average_speed_data, traffic_density_data))

# 标准化数据
scaler = MinMaxScaler()
data_scaled = scaler.fit_transform(data)

# 创建LSTM训练数据集
def create_dataset(dataset, look_back=1):
    X, Y = [], []
    for i in range(len(dataset)-look_back-1):
        X.append(dataset[i:(i+look_back), :])
        Y.append(dataset[i + look_back, :])
    return np.array(X), np.array(Y)

look_back = 3  # 使用3个时间步的窗口
X, Y = create_dataset(data_scaled, look_back)

# 5. 构建LSTM模型
model = Sequential()
model.add(LSTM(50, return_sequences=True, input_shape=(look_back, 3)))
model.add(LSTM(50))
model.add(Dense(3))
model.compile(optimizer='adam', loss='mse')

# 6. 训练LSTM模型
model.fit(X, Y, epochs=100, batch_size=1, verbose=2)

# 7. 预测未来的交通流量和拥堵情况
predicted = model.predict(X[-1].reshape(1, look_back, 3))
predicted = scaler.inverse_transform(predicted)

# 输出预测结果
print(f"未来车流量预测: {predicted[0][0]}")
print(f"未来平均速度预测: {predicted[0][1]}")
print(f"未来车流密度预测: {predicted[0][2]}")

# 8. 拥堵预警机制
def congestion_warning(predicted_density, threshold=35.0):
    if predicted_density > threshold:
        return "预警：预计将出现交通拥堵"
    else:
        return "交通流正常"

# 判断是否触发拥堵预警
congestion_status = congestion_warning(predicted[0][2])
print(congestion_status)

