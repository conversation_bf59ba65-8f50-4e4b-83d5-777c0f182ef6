import numpy as np
from sklearn.impute import KNNImputer

# 构建观测点数据矩阵，NaN表示缺失值
data_matrix = np.array([
    [31.62, 30.23, 25.42, np.nan],
    [31.28, 38.4, 28.89, np.nan],
    [34.27, 32.54, 35.64, 23.08],
    [29.97, 33.34, 35.98, 24.6],
    [19.43, 18.52, 7.38, 24.8],
    [24.17, 24.92, 8.38, 20.57],
    [26.74, 28.06, 9.38, np.nan],
    [28.42, np.nan, 10.39, np.nan]
])

# 使用KNN进行协同过滤的补全
imputer = KNNImputer(n_neighbors=2)
filled_data = imputer.fit_transform(data_matrix)

filled_data
