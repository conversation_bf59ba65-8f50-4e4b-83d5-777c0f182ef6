import math
import matplotlib.pyplot as plt
from matplotlib.ticker import MaxNLocator
from math import floor
import numpy as np
import xlrd
import pandas as pd
import pulp as pl
import pandas as pd
from math import cos, sin, acos, asin, pi, atan, fabs, ceil, floor
from collections import defaultdict
import random
import time
from gurobipy import *
import openpyxl
import sys
import os
import pulp as pl
import numpy as np
import time


# 问题建模emergency lane
class Assignment:
    @classmethod
    # maxDrone: the amount of the drones
    def EL(cls, Q, La, L):
        row = len(Q)
        col = len(Q[0])

        # build a optimal problem
        pro = pl.LpProblem('MIN(connection and coverage)', pl.LpMinimize)
        # build variables for the optimal problem
        lpvars = [[pl.LpVariable("x" + str(i) + "y" + str(j), lowBound=0, upBound=10000, cat='Integer') for j in range(col)]
                  for i in range(row)]

        # build optimal function
        all = pl.LpAffineExpression()
        for i in range(0, row):
            for j in range(0, col):
                all += Q[i][j] * lpvars[i][j]

        pro += all

        # build constraint
        ## 满足需要通过的车需求
        for j in range(0, col):
            tempSum = 0
            for i in range(0, row):
                tempSum += lpvars[i][j]
            pro += tempSum == L[j]

        ## 不超过每个时段允许通过的最大车流量
        for i in range(0,row):
            tempSum = 0
            for j in range(0, col):
                tempSum += lpvars[i][j]
            pro += tempSum <= La[i]

        #规则1 车流密度限制
        for i in range(0,row):
            lpvars[i] > k_c

        #规则1 车速度限制
        for i in range(0,row):
            v[i] > v_c

        status = pro.solve()
        print("Assignment Status: ", pl.LpStatus[status])
        print("Final Assignment Result", pl.value(pro.objective))

        # get the result of T matrix
        T = [[lpvars[i][j].varValue for j in range(col)] for i in range(row)]
        return [T, pl.value(pro.status), pl.value(pro.objective)]

# 归一化后的Q
def getNormalizedQ(Q):
    max = min = Q[0][0]
    row = len(Q)
    column = len(Q[0])
    for i in range(row):
        for j in range(column):
            if (Q[i, j] > max):
                max = Q[i, j]
            if (Q[i, j] < min):
                min = Q[i, j]
    for i in range(row):
        for j in range(column):
            Q[i, j] = round((Q[i, j] - min) / (max - min), 4)
    return Q


if __name__ == '__main__':
    timeperiod = 8
    point = 4
    L = [18772, 13188, 9239, 10216]
    La = [2000, 2000, 2000, 2000, 2000, 2000, 2000, 2000]

    Q_Matrix = [
        [31.62, 30.23, 25.42, 23.84],
        [31.28, 38.4, 28.89, 23.83],
        [34.27, 32.54, 35.64, 23.08],
        [29.97, 33.34, 35.98, 24.6],
        [19.43, 18.52, 7.38, 24.8],
        [24.17, 24.92, 8.38, 20.57],
        [26.74, 28.06, 9.38, 22.685],
        [28.42, 26.49, 10.39, 22.685],
    ]

    Q_Matrix = np.array(Q_Matrix)
    QMatrix = getNormalizedQ(Q_Matrix)

    start_1 = time.perf_counter()
    T1 = Assignment.EL(QMatrix, La, L)
    total_1 = time.perf_counter() - start_1

    TMAT = np.array(T1[0])

    print(TMAT)
