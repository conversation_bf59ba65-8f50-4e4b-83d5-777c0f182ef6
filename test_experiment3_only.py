#!/usr/bin/env python3
"""
专门测试实验三的脚本
只运行实验三，跳过图表生成，专注于路径提取和Excel输出
"""

import numpy as np
import pulp as pl
from collections import defaultdict
import time
import math
import random
import pandas as pd
from datetime import datetime
import os

def generate_simple_test_data():
    """生成简单的测试数据"""
    print("--- 生成简单测试数据 ---")
    
    num_drones = 2
    num_points = 4
    time_period = 8
    num_pesticides = 2
    
    params = {
        'num_drones': num_drones,
        'num_points': num_points, 
        'time_period': time_period,
        'num_pesticides': num_pesticides
    }
    
    # 简化的作物分配和坐标
    params['all_crop_assignments'] = {0: 0, 1: 0, 2: 1, 3: 1}
    params['points_coords'] = {0: (100, 100), 1: (200, 100), 2: (100, 200), 3: (200, 200)}
    params['base_station_coord'] = (0, 0)
    
    # 无人机参数
    params['La'] = np.array([20, 20])
    params['F_tasks'] = np.array([4, 4])
    params['F_time'] = np.array([30, 30])
    params['zeta_max_flow'] = 6.0
    params['alpha'] = 0.05
    
    # 农艺参数
    zeta_standard = np.zeros((num_points, num_pesticides))
    L = np.zeros((num_points, num_pesticides))
    Q_base = np.zeros((num_points, num_pesticides))
    
    # 前两个点需要农药0，后两个点需要农药1
    zeta_standard[0:2, 0] = 1.5; L[0:2, 0] = 1.5; Q_base[0:2, 0] = 0.8
    zeta_standard[2:4, 1] = 1.0; L[2:4, 1] = 1.0; Q_base[2:4, 1] = 1.0
    
    params['zeta_standard'] = zeta_standard
    params['L'] = L
    params['L_max'] = np.ceil(L * 1.3)
    params['Q_base'] = Q_base
    
    # 时间参数
    params['mu_vec'] = np.array([2, 6])
    params['delta_vec'] = np.array([2, 2])
    params['wind_speed'] = np.array([2.0] * time_period)
    
    # 冲突和距离参数
    params['C_conflict'] = [(0, 1)]
    params['Delta_t_conflict'] = 2
    
    # 距离矩阵
    dist_matrix = np.zeros((num_points, num_points))
    for i in range(num_points):
        for j in range(num_points):
            coord_i, coord_j = params['points_coords'][i], params['points_coords'][j]
            dist_matrix[i, j] = math.sqrt((coord_i[0] - coord_j[0])**2 + (coord_i[1] - coord_j[1])**2)
    params['dist_matrix'] = dist_matrix
    
    params['dist_base'] = {}
    for i in range(num_points):
        coord_i = params['points_coords'][i]
        base_coord = params['base_station_coord']
        params['dist_base'][i] = math.sqrt((coord_i[0] - base_coord[0])**2 + (coord_i[1] - base_coord[1])**2)
    
    # VRP参数
    params['V_drone'] = 20; params['tau_task'] = 2
    params['w1'] = 0.6; params['w2'] = 0.4
    params['yield_ref'] = 1.0; params['path_ref'] = 1000
    
    print("--- 简单测试数据生成完毕 ---")
    return params

def solve_simple_moo_vrp(params):
    """简化的多目标VRP求解"""
    print("开始求解简化多目标VRP...")
    
    # 生成角色
    roles = []
    Q_j, zeta_j, eta_j = {}, {}, {}
    eta_values = np.maximum(0.1, 1 - params['alpha'] * params['wind_speed'])
    
    for p in range(params['num_points']):
        for t in range(params['time_period']):
            for c in range(params['num_pesticides']):
                if params['L'][p, c] > 0:
                    zeta_dispense = params['zeta_standard'][p, c] / eta_values[t]
                    if zeta_dispense <= params['zeta_max_flow']:
                        roles.append((p, t, c))
                        j = len(roles) - 1
                        g_value = 1 / (np.sqrt(2 * np.pi) * params['delta_vec'][c]) * np.exp(
                            -((t - params['mu_vec'][c]) ** 2) / (2 * params['delta_vec'][c] ** 2))
                        Q_j[j] = params['Q_base'][p, c] * g_value
                        zeta_j[j] = zeta_dispense
                        eta_j[j] = eta_values[t]
    
    print(f"有效角色数量: {len(roles)}")
    
    # 创建优化模型
    prob = pl.LpProblem("Simple_MOO_VRP", pl.LpMaximize)
    solver = pl.getSolver('GUROBI_CMD', msg=False, timeLimit=60)
    
    T = pl.LpVariable.dicts("T", (range(params['num_drones']), range(len(roles))), 0, 1, pl.LpBinary)
    Y = pl.LpVariable.dicts("Y", (range(params['num_drones']), range(params['num_pesticides'])), 0, 1, pl.LpBinary)
    role_nodes = list(range(len(roles))) + ['N']
    X = pl.LpVariable.dicts("X", (range(params['num_drones']), role_nodes, role_nodes), 0, 1, pl.LpBinary)
    
    # 目标函数
    yield_obj = pl.lpSum(Q_j[j] * T[i][j] for i in range(params['num_drones']) for j in range(len(roles)))
    path_cost_obj = pl.lpSum(
        params['dist_base'][roles[k][0]] * X[i]['N'][k] for i in range(params['num_drones']) for k in range(len(roles))) + \
                    pl.lpSum(params['dist_matrix'][roles[j][0]][roles[k][0]] * X[i][j][k] 
                            for i in range(params['num_drones']) for j in range(len(roles)) for k in range(len(roles)) if j != k) + \
                    pl.lpSum(params['dist_base'][roles[j][0]] * X[i][j]['N'] 
                            for i in range(params['num_drones']) for j in range(len(roles)))
    
    prob += params['w1'] * (yield_obj / params['yield_ref']) - params['w2'] * (path_cost_obj / params['path_ref'])
    
    # 基本约束
    for p in range(params['num_points']):
        for c in range(params['num_pesticides']):
            if params['L'][p, c] > 0:
                relevant_roles = [j for j, role in enumerate(roles) if role[0] == p and role[2] == c]
                effective_spray = pl.lpSum(T[i][j] * (zeta_j[j] * eta_j[j]) 
                                         for i in range(params['num_drones']) for j in relevant_roles)
                prob += effective_spray >= params['L'][p, c]
                prob += effective_spray <= params['L_max'][p, c]
    
    # 其他约束
    for i in range(params['num_drones']):
        prob += pl.lpSum(Y[i][c] for c in range(params['num_pesticides'])) <= 1
        for j in range(len(roles)):
            c_j = roles[j][2]
            prob += T[i][j] <= Y[i][c_j]
        for t in range(params['time_period']):
            relevant_roles = [j for j, role in enumerate(roles) if role[1] == t]
            prob += pl.lpSum(T[i][j] for j in relevant_roles) <= 1
    
    # VRP约束
    for i in range(params['num_drones']):
        prob += pl.lpSum(X[i]['N'][k] for k in range(len(roles))) <= 1
        for j in range(len(roles)):
            prob += pl.lpSum(X[i][k][j] for k in role_nodes if k != j) == T[i][j]
            prob += pl.lpSum(X[i][j][k] for k in role_nodes if k != j) == T[i][j]
            prob += X[i][j][j] == 0
    
    # 求解
    prob.solve(solver)
    
    if pl.value(prob.objective) is not None:
        solution_t = {(i, j): T[i][j].varValue for i in range(params['num_drones']) for j in range(len(roles))}
        solution_x = {}
        for i in range(params['num_drones']):
            for j in role_nodes:
                for k in role_nodes:
                    if j != k:
                        try:
                            value = X[i][j][k].varValue
                            if value is not None and value > 0.5:
                                solution_x[(i, j, k)] = value
                        except:
                            pass
        return solution_t, prob.status, pl.value(prob.objective), roles, solution_x
    else:
        return None, prob.status, None, None, None

def extract_and_analyze_paths(solution_t, solution_x, roles, params):
    """提取和分析路径"""
    print("\n=== 路径提取和分析 ===")
    
    # 分析任务分配
    print("任务分配分析:")
    total_tasks = 0
    for i in range(params['num_drones']):
        drone_tasks = [j for j in range(len(roles)) if solution_t.get((i, j), 0) > 0.5]
        total_tasks += len(drone_tasks)
        
        if drone_tasks:
            print(f"  无人机 {i}: 分配了 {len(drone_tasks)} 个任务")
            for task_id in drone_tasks[:3]:
                p, t, c = roles[task_id]
                print(f"    任务 {task_id}: P{p}(T{t},C{c})")
        else:
            print(f"  无人机 {i}: 没有分配任务")
    
    print(f"总分配任务数: {total_tasks}")
    
    # 提取路径
    paths = {}
    if total_tasks > 0:
        for i in range(params['num_drones']):
            print(f"\n提取无人机 {i} 的路径:")
            
            # 收集路径段
            drone_edges = [(f, t) for (d, f, t), v in solution_x.items() if d == i and v > 0.5]
            print(f"  路径段数: {len(drone_edges)}")
            
            if drone_edges:
                # 构建图
                graph = {}
                for from_node, to_node in drone_edges:
                    if from_node not in graph:
                        graph[from_node] = []
                    graph[from_node].append(to_node)
                    print(f"    {from_node} -> {to_node}")
                
                # 查找路径
                if 'N' in graph:
                    path = ['N']
                    current = 'N'
                    visited = set(['N'])
                    
                    while current in graph and len(path) <= len(roles) + 2:
                        next_nodes = [n for n in graph[current] if n not in visited or n == 'N']
                        if next_nodes:
                            next_node = next_nodes[0]
                            path.append(next_node)
                            if next_node == 'N':
                                break
                            visited.add(next_node)
                            current = next_node
                        else:
                            break
                    
                    paths[i] = path
                    print(f"  完整路径: {path}")
                else:
                    # 启发式路径
                    assigned_tasks = [j for j in range(len(roles)) if solution_t.get((i, j), 0) > 0.5]
                    if assigned_tasks:
                        assigned_tasks.sort(key=lambda j: roles[j][1])
                        path = ['N'] + assigned_tasks + ['N']
                        paths[i] = path
                        print(f"  启发式路径: {path}")
                    else:
                        paths[i] = []
            else:
                paths[i] = []
    else:
        for i in range(params['num_drones']):
            paths[i] = []
    
    return paths

def save_simple_results(params, solution_t, solution_x, roles, paths, objective, status):
    """保存简化结果到Excel"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"Experiment3_Test_Results_{timestamp}.xlsx"
    
    print(f"\n保存结果到: {filename}")
    
    with pd.ExcelWriter(filename, engine='openpyxl') as writer:
        # 基本信息
        basic_info = {
            'Objective': objective,
            'Status': pl.LpStatus[status],
            'Num_Drones': params['num_drones'],
            'Num_Points': params['num_points'],
            'Num_Roles': len(roles)
        }
        pd.DataFrame.from_dict(basic_info, orient='index', columns=['Value']).to_excel(
            writer, sheet_name='Basic_Info')
        
        # 任务分配
        assignment_list = []
        for (i, j), val in solution_t.items():
            if val > 0.5:
                p, t, c = roles[j]
                assignment_list.append({'Drone': i, 'Role_ID': j, 'Point': p, 'Time': t, 'Pesticide': c})
        pd.DataFrame(assignment_list).to_excel(writer, sheet_name='Task_Assignment', index=False)
        
        # 路径详情
        path_details = []
        for i, path in paths.items():
            for step, node in enumerate(path):
                if node == 'N':
                    path_details.append({'Drone': i, 'Step': step, 'Node': node, 'Type': 'Base'})
                else:
                    p, t, c = roles[node]
                    path_details.append({'Drone': i, 'Step': step, 'Node': node, 'Type': 'Task', 
                                       'Point': p, 'Time': t, 'Pesticide': c})
        pd.DataFrame(path_details).to_excel(writer, sheet_name='Path_Details', index=False)
        
        # 路径变量
        path_vars = []
        for (i, f, t), val in solution_x.items():
            path_vars.append({'Drone': i, 'From': f, 'To': t, 'Value': val})
        pd.DataFrame(path_vars).to_excel(writer, sheet_name='Path_Variables', index=False)
    
    print("Excel文件保存成功!")

def main():
    """主测试函数"""
    print("=== 实验三专项测试开始 ===")
    
    # 生成测试数据
    params = generate_simple_test_data()
    
    # 求解
    solution_t, status, objective, roles, solution_x = solve_simple_moo_vrp(params)
    
    if objective is not None:
        print(f"\n求解成功!")
        print(f"状态: {pl.LpStatus[status]}")
        print(f"目标值: {objective}")
        print(f"角色数量: {len(roles)}")
        print(f"路径变量数量: {len(solution_x)}")
        
        # 提取路径
        paths = extract_and_analyze_paths(solution_t, solution_x, roles, params)
        
        # 保存结果
        save_simple_results(params, solution_t, solution_x, roles, paths, objective, status)
        
        print("\n=== 实验三测试成功完成 ===")
        print("✓ 求解成功")
        print("✓ 路径提取成功")
        print("✓ Excel保存成功")
        
        return True
    else:
        print(f"\n求解失败!")
        print(f"状态: {pl.LpStatus[status]}")
        return False

if __name__ == "__main__":
    main()
