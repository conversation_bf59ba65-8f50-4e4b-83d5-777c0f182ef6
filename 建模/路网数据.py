import numpy as np
import pandas as pd
import matplotlib.pyplot as plt

df1 = pd.read_excel('交通路口.xlsx')
df1 = np.array(df1)
df2 = pd.read_excel('400板块.xlsx')
df2 = np.array(df2)
df3 = pd.read_excel('路线.xlsx')
df3 = np.array(df3)

point_sum = len(df1)
block_sum = len(df2)
path = len(df3)

cont = [0 for i in range(point_sum)] #所属板块

for i in range(point_sum):
    for j in range(block_sum):
        if df1[i][1] > df2[j][1] and df1[i][1] < df2[j][2] and df1[i][2] > df2[j][3] and df1[i][2] < df2[j][4]:
            cont[i] = df2[j][0]

cont = np.array(cont)

dis = [0 for i in range(block_sum)]
temp = 0
#计算板块内距离
for i in range(point_sum):
    for j in range(block_sum):
        if cont[i] == j:
            for k in range(path):
                if i == df3[k][0]:
                    temp += df3[k][2]
                if i == df3[k][1]:
                    temp += df3[k][2]
        dis[j] += temp
        temp = 0

dis = np.array(dis)