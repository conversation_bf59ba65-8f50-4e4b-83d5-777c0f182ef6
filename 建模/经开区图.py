import math
import matplotlib.pyplot as plt
from matplotlib.ticker import MaxNLocator
from math import floor
import numpy as np
import xlrd
import pandas as pd
import pulp as pl
import pandas as pd
from math import cos, sin, acos, asin, pi, atan, fabs, ceil, floor
from collections import defaultdict
import random
import time

# 散点图
up_axis = [[77.149325, 39.156466667]]
mid_axis = [[67.058, 34.474], [67.058, 34.474], [68.33925, 31.20966667], [68.33925, 31.20966667],
            [68.33925, 31.20966667], [66.98975, 32.618], [68.45575, 30.01333333], [65.873, 33.61466667],
            [68.00125, 34.63666667], [65.79525, 31.757], [65.79525, 31.757], [65.98625, 34.74033333],
            [66.56625, 33.24933333], [66.81575, 31.60066667], [65.06775, 31.43433333], [65.095, 30.35333333],
            [65.81775, 30.251], [66.72475, 31.817],
            [74.52675, 32.81366667], [71.85425, 32.15866667], [71.85425, 32.15866667], [74.68875, 32.01433333],
            [74.68875, 32.01433333], [72.17725, 32.94766667], [74.44875, 33.206], [74.44875, 33.206],
            [74.44875, 33.206], [74.2425, 34.58366667], [71.48275, 32.95166667], [74.1505, 33.08766667],
            [70.7445, 32.17133333], [71.5015, 34.68566667], [74.0845, 33.626],
            [76.95175, 28.307], [76.95175, 28.307], [75.28, 28.28433333], [76.21825, 28.571], [76.95175, 28.307],
            [76.096, 26.749],
            [78.18575, 30.446], [78.18575, 30.446], [78.18575, 30.446], [78.18575, 30.446], [78.07125, 32.70133333],
            [77.98375, 31.51733333],
            [75.4705, 35.132], [76.6115, 38.22266667],
            [75.01, 44.28766667], [75.01, 44.28766667], [78.7295, 44.97], [77.0035, 42.14333333], [75.87675, 42.99],
            [76.58, 43.90633333], [76.65775, 41.20333333], [76.65775, 41.20333333], [75.86075, 41.115],
            [75.86075, 41.115], [79.829, 43.50833333],
            [79.5875, 46.18133333], [79.5875, 46.18133333], [78.4925, 45.11033333], [78.4925, 45.11033333],
            [78.4925, 45.11033333], [78.4925, 45.11033333],
            [81.1755, 41.35233333], [81.1755, 41.35233333], [81.1755, 41.35233333], [81.09575, 42.207],
            [81.09575, 42.207], [81.40475, 40.50133333], [81.40475, 40.50133333], [80.08125, 41.509],
            [82.76325, 41.12466667], [80.3915, 43.32866667], [83.05725, 43.45733333], [82.8455, 42.669],
            [82.8455, 42.669], [82.8455, 42.669], [82.12225, 41.132], [82.57525, 42.115], [3371, 84.50575, 42.37433333],
            [3371, 84.50575, 42.37433333], [82.09975, 43.47733333], [83.985, 41.53566667],
            [84.06125, 45.678], [84.06125, 45.678], [81.698, 47.22766667], [81.698, 47.22766667], [81.698, 47.22766667],
            [82.03425, 48.617], [82.03425, 48.617], [82.03425, 48.617], [82.03425, 48.617], [82.03425, 48.617],
            [82.03425, 48.617], [82.03425, 48.617], [82.03425, 48.617], [82.03425, 48.617], [82.6255, 46.20866667],
            [82.6255, 46.20866667], [82.6255, 46.20866667],
            [80.09525, 50.61066667], [80.09525, 50.61066667]]
temp = 0
x = []
y = []
x1 = []
y1 = []
for i in range(len(mid_axis)):
    temp = mid_axis[i][0]
    x.append(temp)
    temp = mid_axis[i][1]
    y.append(temp)
for i in range(len(up_axis)):
    temp = up_axis[i][0]
    x1.append(temp)
    temp = up_axis[i][1]
    y1.append(temp)
# x = [1.61, 1.24, 0.98, 4.66, 6.75, 10.5, 13.81, 17.33, 12.41, 17.09, 2.73, 9.39, 12.44, 17.57, 12.54, 17.22, 8.39, 6.35, 2.28, 2.78, 7.99, 15.08]
# y = [19.53, 14.76, 12.94, 15.11, 15.87, 16.48, 18.55, 17.36, 14.76, 13.79, 11.4, 8.55, 10.19, 8.39, 6.3, 3.41, 3.73, 1.06, 2.8, 6.61, 10.16, 8.88]
# colors = [1,2,1,3,3,1,2,2,2,2,1,2,1,2,2,3,2,2,2,3,1,3]
# sizes = [150,300,150,450,450,150,300,300,300,300,150,300,150,300,300,450,300,300,300,450,150,450]
cluster_list = [(1.61, 19.53), (1.24, 14.76), (0.98, 12.94), (4.66, 15.11), (6.75, 15.87), (10.5, 16.48),(13.81, 18.55),(17.33, 17.36),
                (12.41, 14.76), (17.09, 13.79), (2.73, 11.4), (9.39, 8.55), (12.44, 10.19),(17.57, 8.39),(12.54, 6.3), (17.22, 3.41),
                (8.39, 3.73), (6.35, 1.06), (2.28, 2.8), (2.78, 6.61), (7.99, 10.16), (15.48,8.88)]
plt.scatter(x, y, c=colors , s = sizes, alpha = 0.5, edgecolors = 'w', label = 'Crowded Areas',cmap='copper_r')
x1 = [18.95, 6.42, 11.52, 16.77]
y1 = [6.92, 9.6, 18.78, 15.33]
base_list = [(18.95, 6.92), (6.42, 9.6), (11.52, 18.78), (16.77, 15.33)]
plt.scatter(x1, y1, color = "darkred", s = 400, alpha = 0.7, marker = '*', label = 'Base Station')
plt.xlim(0, 20, 1)
plt.ylim(0, 20, 2)
plt.gca().xaxis.set_major_locator(MaxNLocator(integer=True))   #限制坐标轴刻度为整数
plt.gca().yaxis.set_major_locator(MaxNLocator(integer=True))
plt.legend(loc='lower right', fontsize=9)
plt.title("Coordinate diagram of position distribution",fontdict={'weight':'normal','size': 15})
# plt.show()