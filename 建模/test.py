# from pso import PSO
import math
import matplotlib.pyplot as plt
from matplotlib.ticker import MaxNLocator
import numpy as np
import xlrd
import pandas as pd
import pulp as pl
import pandas as pd
from math import cos, sin, acos, asin, pi, atan, fabs, ceil,  floor
from collections import defaultdict
import random
import time
from gurobipy import *

# test
def genUAVpos(base):
    temp = 0
    tempsum = []
    UAV_start = []
    for i in range(0,len(La)):
        temp = La[i]
        tempsum = np.tile(base_start[i], (La[i],1))
        UAV_start.append(tempsum)
    return UAV_start

# 随机生成La
def genLaMatrix(role_amount, agent_amount):
		La = []
		temp = agent_amount
		while(temp>=1):
				val = math.ceil(role_amount/agent_amount)
				val += random.randint(1, agent_amount)
				if val not in La:
						La.append(val)
						temp-=1
		return La

if __name__ == '__main__':
    #无人机相关参数设置
    base = 4
    cluster = 22
    combination = 16
    L = [1,2,1,3,3,1,2,2,2,2,1,2,1,2,2,3,2,2,2,3,1,3] #聚集点需求total：43
    L_sum = sum(L)
    La = genLaMatrix(L_sum, base)
    capacity = [i * 3 for i in La]
    ftcharge = 0.75 # 充电45分钟
    fv = 50  # 飞行速度50km/h
    Qmax = ftcharge*fv

    base_start = np.random.uniform(0,20,(base,2))

    UAV_startpos = genUAVpos(base)


    target_pos = np.random.uniform(0, 20, (cluster, 2))
    target_pos = np.array(target_pos, dtype=object)
