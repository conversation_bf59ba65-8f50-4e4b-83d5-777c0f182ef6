import math
import matplotlib.pyplot as plt
from matplotlib.ticker import MaxNLocator
from math import floor
import numpy as np
import xlrd
import pandas as pd
import pulp as pl
import pandas as pd
from math import cos, sin, acos, asin, pi, atan, fabs, ceil, floor
from collections import defaultdict
import random
import time
from gurobipy import *

class Assignment:
    @classmethod
    # maxDrone: the amount of the drones
    def KM(cls, Q, La, L):
        row = len(Q)
        col = len(Q[0])

        # build a optimal problem
        pro = pl.LpProblem('MIN(connection and coverage )', pl.LpMinimize)
        solver = pl.getSolver('GUROBI_CMD')

        # build variables for the optimal problem
        lpvars = [[[pl.LpVariable("x" + str(i) + "y" + str(j), lowBound=0, upBound=1, cat='Integer') for
                    j in range(col)] for i in range(row)]]


        # build optimal function
        all = pl.LpAffineExpression()
        for i in range(0,row):
            for j in range(0,col):
                all += Q[i][j] * lpvars[i][j]
        pro += all


        # build constraint
        ## 满足聚集点需求
        for j in range(0, col):
            tempSum = 0
            for i in range(0, row):
                tempSum += lpvars[i][j]
            pro += tempSum == L[j]

        ## when b==0, <la(i)
        for i in range(0, row):
            tempSum = 0
            for j in range(0, col):
                tempSum += lpvars[i][j]
            pro += tempSum <= La[i]


        # solve optimal problem
        status = pro.solve(solver)
        print("Assignment Status: ", pl.LpStatus[status])
        print("Final Assignment Result", pl.value(pro.objective))

        # get the result of T matrix
        T = [[[lpvars[b][i][j].varValue for j in range(col)] for i in range(row)]]

        # return T
        return [T, pl.value(pro.status), pl.value(pro.objective)]

# 计算两点之间距离公式
def calDistance(x1, x2, y1, y2):
    result = math.sqrt((x2-x1)**2+(y2-y1)**2)
    return result

# 上游与中游距离（21*4的矩阵）
def genDistance(x,y,x1,y1):
    distance_BC = []
    for j in range(len(up_axis)):
        tempMatrix = []
        for i in range(len(mid_axis)):
            tempMatrix.append(calDistance(x[i],x1[j],y[i],y1[j]))
        distance_BC.append(tempMatrix)
    return distance_BC

# 归一化后的Q
def getNormalizedQ(Q):
    max = min = Q[0][0]
    row = len(Q)
    column = len(Q[0])
    for i in range(row):
        for j in range(column):
            if (Q[i, j] > max):
                max = Q[i, j]
            if (Q[i, j] < min):
                min = Q[i, j]
    for i in range(row):
        for j in range(column):
            Q[i, j] = round((Q[i, j] - min) / (max - min), 4)
    return Q

# 随机生成La
def genLaMatrix(role_amount, agent_amount):
		La = []
		temp = agent_amount
		while(temp>=1):
				val = ceil(role_amount/agent_amount)
				val += random.randint(1, agent_amount)
				if val not in La:
						La.append(val)
						temp-=1
		return La

if __name__ == '__main__':
    up_axis = [[77.149325, 39.156466667]]
    mid_axis = [[67.058, 34.474], [67.058, 34.474], [68.33925, 31.20966667],[68.33925, 31.20966667], [68.33925, 31.20966667], [66.98975, 32.618], [68.45575, 30.01333333], [65.873, 33.61466667], [68.00125, 34.63666667], [65.79525, 31.757], [65.79525, 31.757], [65.98625, 34.74033333], [66.56625, 33.24933333], [66.81575, 31.60066667], [65.06775, 31.43433333], [ 65.095, 30.35333333], [65.81775, 30.251], [66.72475, 31.817],
    [74.52675, 32.81366667],  [71.85425, 32.15866667], [ 71.85425, 32.15866667], [74.68875, 32.01433333], [74.68875, 32.01433333], [72.17725, 32.94766667], [74.44875, 33.206], [74.44875, 33.206], [74.44875, 33.206], [74.2425, 34.58366667], [71.48275, 32.95166667], [74.1505, 33.08766667], [70.7445, 32.17133333], [71.5015, 34.68566667], [74.0845, 33.626],
    [76.95175, 28.307], [76.95175, 28.307], [75.28, 28.28433333], [76.21825, 28.571], [76.95175, 28.307], [76.096, 26.749],
    [78.18575, 30.446], [78.18575, 30.446], [78.18575, 30.446], [78.18575, 30.446],[78.07125, 32.70133333], [77.98375, 31.51733333],
    [75.4705, 35.132], [76.6115, 38.22266667],
    [75.01, 44.28766667], [75.01, 44.28766667], [78.7295, 44.97], [77.0035, 42.14333333], [75.87675, 42.99], [76.58, 43.90633333], [76.65775, 41.20333333], [76.65775, 41.20333333], [75.86075, 41.115], [75.86075, 41.115], [79.829, 43.50833333],
    [79.5875, 46.18133333], [79.5875, 46.18133333], [78.4925, 45.11033333], [78.4925, 45.11033333], [78.4925, 45.11033333], [78.4925, 45.11033333],
    [81.1755, 41.35233333],[81.1755, 41.35233333],[81.1755, 41.35233333],[81.09575, 42.207],[81.09575, 42.207], [81.40475, 40.50133333],[81.40475, 40.50133333], [80.08125, 41.509], [82.76325, 41.12466667], [80.3915, 43.32866667], [ 83.05725, 43.45733333], [82.8455, 42.669],[82.8455, 42.669],[82.8455, 42.669], [82.12225, 41.132], [82.57525, 42.115], [3371, 84.50575, 42.37433333],[3371, 84.50575, 42.37433333], [ 82.09975, 43.47733333],[ 83.985, 41.53566667],
    [84.06125, 45.678],[84.06125, 45.678],[81.698, 47.22766667],[81.698, 47.22766667],[81.698, 47.22766667],[82.03425, 48.617],[82.03425, 48.617],[82.03425, 48.617],[82.03425, 48.617],[82.03425, 48.617],[82.03425, 48.617],[82.03425, 48.617],[82.03425, 48.617],[82.03425, 48.617],[82.6255, 46.20866667],[82.6255, 46.20866667],[82.6255, 46.20866667],
    [80.09525, 50.61066667],[80.09525, 50.61066667]]

    temp = 0
    x = []
    y = []
    x1 = []
    y1 = []
    for i in range(len(mid_axis)):
        temp = mid_axis[i][0]
        x.append(temp)
        temp = mid_axis[i][1]
        y.append(temp)
    for i in range(len(up_axis)):
        temp = up_axis[i][0]
        x1.append(temp)
        temp = up_axis[i][1]
        y1.append(temp)

    distance_UM = genDistance(x, y, x1, y1)
    distance_UM_array = np.array(distance_UM)
    Q = distance_UM_array
    QMatrix = getNormalizedQ(Q)

    La = genLaMatrix(127,1)

    L_sum = 127 #需要总吨数
    L = [1,3,2,1,1,1,1,1,1,1,
         1,1,1,1,1,1,1,1,1,3,
         1,1,1,1,1,1,1,1,1,1,
         1,1,1,1,2,1,1,1,1,1,
         1,1,1,5,1,1,1,1,1,1,
         1,1,1,1,1,1,1,1,1,1,
         1,1,1,1,1,1,2,1,1,1,
         1,1,2,1,1,1,1,1,1,1,
         1,1,1,1,1,1,1,1,2,1,
         1,1,1,2,1,1,1,1,1,1,
         1,1,1,1,1,1,1,1,1,1,
         1,1,1,1,1,1,1,1,1,1,
         1,1,1,1,1,1,1]

    T1, status, p = Assignment.KM(QMatrix, La, L)

