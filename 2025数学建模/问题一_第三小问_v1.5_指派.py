import pandas as pd
import numpy as np
import re
import matplotlib.pyplot as plt
import networkx as nx

# 能跑 但是路线在外围

class TourFinalizer:
    def __init__(self, score_matrix_path, road_excel_path):
        self.road_excel_path = road_excel_path
        self.graph = nx.Graph()
        self._build_accurate_graph()
        self._integrate_scores(score_matrix_path)

    def _parse_road_coordinates(self, df):
        coord_series = df.iloc[:, 0].dropna()
        polylines = []
        current_polyline_points = []
        coord_pattern = re.compile(r'\{([\d.-]+),\s*([\d.-]+),\s*([\d.-]+)\}')

        for item in coord_series:
            item_str = str(item)
            if item_str.strip().startswith('{0;'):
                if len(current_polyline_points) > 1:
                    polylines.append(current_polyline_points)
                current_polyline_points = []

            match = coord_pattern.search(item_str)
            if match:
                x, y, z = map(float, match.groups())
                current_polyline_points.append((round(x, 2), round(y, 2)))

        if len(current_polyline_points) > 1:
            polylines.append(current_polyline_points)
        return polylines

    def _build_accurate_graph(self):
        print("1. 正在构建精确的园区路网图...")
        try:
            road_raw_df = pd.read_excel(self.road_excel_path, sheet_name="道路", header=0)
            polylines = self._parse_road_coordinates(road_raw_df)

            for polyline in polylines:
                for i in range(len(polyline) - 1):
                    p1, p2 = polyline[i], polyline[i + 1]
                    distance = np.linalg.norm(np.array(p1) - np.array(p2))
                    self.graph.add_edge(p1, p2, length=distance, score=0.0)

            print(
                f"   > 路网图构建完成，包含 {self.graph.number_of_nodes()} 个节点和 {self.graph.number_of_edges()} 条边。")
        except Exception as e:
            print(f"构建路网图时发生错误: {e}")

    def _integrate_scores(self, score_matrix_path):
        print("2. 正在将趣味性评分融入路网图...")
        try:
            df = pd.read_csv(score_matrix_path)
            df['Start_Point_Parsed'] = df['Start_Point'].apply(self._parse_coord_string)
            df['End_Point_Parsed'] = df['End_Point'].apply(self._parse_coord_string)

            for index, row in df.iterrows():
                start_node, end_node = row['Start_Point_Parsed'], row['End_Point_Parsed']
                score = row['Final_Score']

                if start_node and end_node and self.graph.has_node(start_node) and self.graph.has_node(end_node):
                    try:
                        path_nodes = nx.shortest_path(self.graph, source=start_node, target=end_node, weight='length')
                        for i in range(len(path_nodes) - 1):
                            edge_data = self.graph.edges[path_nodes[i], path_nodes[i + 1]]
                            edge_data['score'] = max(edge_data.get('score', 0.0), score)
                    except nx.NetworkXNoPath:
                        continue
            print("   > 评分融入完成。")
        except Exception as e:
            print(f"融入评分时发生错误: {e}")

    def _parse_coord_string(self, s):
        try:
            s_cleaned = str(s).replace('{', '').replace('}', '').replace('(', '').replace(')', '')
            parts = [float(p.strip()) for p in s_cleaned.split(',')]
            if len(parts) >= 2:
                return (round(parts[0], 2), round(parts[1], 2))
        except (ValueError, TypeError):
            return None
        return None

    def find_best_tour(self):
        print("3. 正在从所有候选起点进行全局路径探索...")

        candidate_starts = [node for node, degree in self.graph.degree() if degree == 1]
        if not candidate_starts:
            print("   > 未找到天然出入口(端点)，将使用所有交叉路口作为候选起点。")
            candidate_starts = [node for node, degree in self.graph.degree() if degree > 2]

        print(f"   > 找到 {len(candidate_starts)} 个候选起点。")

        best_path = []
        # ******** 此处为修正部分 ********
        best_eval_score = (-1, -1)  # 初始化为一个元组
        # *****************************

        for i, start_node in enumerate(candidate_starts):
            current_path = [start_node]
            visited_edges = set()
            current_node = start_node

            while True:
                candidate_edges = []
                for neighbor in self.graph.neighbors(current_node):
                    edge = tuple(sorted((current_node, neighbor)))
                    if edge not in visited_edges:
                        edge_data = self.graph.get_edge_data(current_node, neighbor)
                        candidate_edges.append((neighbor, edge_data.get('score', 0.0), edge))

                if not candidate_edges: break

                best_next_node, _, best_edge = max(candidate_edges, key=lambda item: item[1])

                current_path.append(best_next_node)
                visited_edges.add(best_edge)
                current_node = best_next_node

            path_score = sum(self.graph.edges[current_path[i], current_path[i + 1]].get('score', 0.0) for i in
                             range(len(current_path) - 1))

            # 使用元组 (路径长度, 路径总分) 作为评估标准
            eval_score = (len(current_path), path_score)

            if eval_score > best_eval_score:
                best_path = current_path
                best_eval_score = eval_score
                print(f"   > 尝试起点 {i + 1}: 发现更优路径！ 长度: {len(best_path)}, 总分: {path_score:.2f}")

        return best_path

    def visualize_tour(self, path):
        if not path:
            print("未能找到有效路径进行可视化。")
            return
        print("4. 正在生成最终的路径可视化图...")

        plt.rcParams['font.sans-serif'] = ['SimHei']
        plt.rcParams['axes.unicode_minus'] = False
        plt.figure(figsize=(16, 16))

        pos = {node: (node[0], node[1]) for node in self.graph.nodes()}
        nx.draw_networkx_edges(self.graph, pos, edge_color='lightgray', width=1.5)

        path_edges = list(zip(path, path[1:]))
        nx.draw_networkx_edges(self.graph, pos, edgelist=path_edges, edge_color='red', width=3.5, alpha=0.8,
                               label="最优路径")

        start_node, end_node = path[0], path[-1]
        nx.draw_networkx_nodes(self.graph, pos, nodelist=[start_node], node_color='green', node_size=200, label="起点")
        nx.draw_networkx_nodes(self.graph, pos, nodelist=[end_node], node_color='blue', node_shape='s', node_size=200,
                               label="终点")

        plt.title('拙政园最优游览路径（最终版）', fontsize=20)
        plt.legend(fontsize=14)
        plt.axis('off')

        output_image_path = r'H:\OneDrive\Desktop\optimal_tour_path_final.png'
        plt.savefig(output_image_path, dpi=300, bbox_inches='tight')
        print(f"   > 路径图已保存到: {output_image_path}")
        plt.show()


# --- 主程序入口 ---
if __name__ == "__main__":
    SCORE_MATRIX_PATH = r'H:\OneDrive\Desktop\final_scoring_matrix_v4_final.csv'
    ROAD_EXCEL_PATH = r'H:\OneDrive\Desktop\F题\赛题F江南古典园林美学特征建模附件资料\1. 拙政园\4-拙政园数据坐标.xlsx'

    optimizer = TourFinalizer(score_matrix_path=SCORE_MATRIX_PATH,
                              road_excel_path=ROAD_EXCEL_PATH)

    if optimizer.graph.number_of_edges() > 0:
        best_tour_path = optimizer.find_best_tour()

        if best_tour_path:
            path_score = sum(optimizer.graph.edges[best_tour_path[i], best_tour_path[i + 1]].get('score', 0.0) for i in
                             range(len(best_tour_path) - 1))
            path_length = sum(
                optimizer.graph.edges[best_tour_path[i], best_tour_path[i + 1]].get('length', 0.0) for i in
                range(len(best_tour_path) - 1))

            print("\n==============================================")
            print("            最终最佳游览路径方案")
            print("==============================================")
            print(f"最佳起点为: {best_tour_path[0]}")
            print(f"路径总步数: {len(best_tour_path) - 1} 段")
            print(f"估算总长度: 约 {path_length / 1000:.2f} 米")
            print(f"累积总趣味分: {path_score:.4f}")

            optimizer.visualize_tour(best_tour_path)