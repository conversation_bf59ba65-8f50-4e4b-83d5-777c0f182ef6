import pandas as pd
import re
import matplotlib.pyplot as plt
from scipy.spatial.distance import cdist
import heapq
import numpy as np
import pulp as pl
from collections import deque
import os
from datetime import datetime
import matplotlib.cm as cm
import matplotlib.colors as mcolors
import matplotlib.ticker as mticker
from matplotlib.patches import FancyArrowPatch

# --- 尝试导入可选的、用于绘图的库 ---
try:
    from shapely.geometry import Point, LineString
    import networkx as nx

    VISUALIZATION_LIB_AVAILABLE = True
    print("用于精细绘图的'shapely'和'networkx'库已找到。")
except ImportError:
    VISUALIZATION_LIB_AVAILABLE = False
    print("警告：未安装 'shapely' 或 'networkx' 库。背景路网将使用简化方法绘制。")
    print("建议运行: pip install shapely networkx")

# --- 配置中文字体 ---
try:
    plt.rcParams['font.sans-serif'] = ['SimHei']
    plt.rcParams['axes.unicode_minus'] = False
except Exception:
    print("警告：未找到'SimHei'字体，图例可能显示不正常。")


# =================================================================
#  2. 核心数据解析函数 (保持不变)
# =================================================================
def parse_road_data_for_routing(df):
    all_points = []
    for _, row in df.iterrows():
        coords_str = "".join(str(s) for s in row if pd.notna(s))
        points = re.findall(r'\{([-\d\.]+),\s*([-\d\.]+),\s*([-\d\.]+)\}', coords_str)
        for p in points:
            all_points.append((float(p[0]), float(p[1])))

    if not all_points: return pd.DataFrame(), pd.DataFrame()

    nodes_df = pd.DataFrame(all_points, columns=['x', 'y']).drop_duplicates().reset_index(drop=True)
    nodes_df['node_id'] = nodes_df.index
    coord_to_id = {(round(row.x, 4), round(row.y, 4)): row.node_id for _, row in nodes_df.iterrows()}

    edges = []
    for _, row in df.iterrows():
        coords_str = "".join(str(s) for s in row if pd.notna(s))
        points_in_row = re.findall(r'\{([-\d\.]+),\s*([-\d\.]+),\s*([-\d\.]+)\}', coords_str)
        current_nodes = []
        for p in points_in_row:
            coord = (round(float(p[0]), 4), round(float(p[1]), 4))
            node_id = coord_to_id.get(coord)
            if node_id is not None: current_nodes.append(node_id)
        for i in range(len(current_nodes) - 1):
            u, v = sorted((current_nodes[i], current_nodes[i + 1]))
            if u != v: edges.append({'source': u, 'target': v})

    edges_df = pd.DataFrame(edges).drop_duplicates().reset_index(drop=True)
    return nodes_df, edges_df


def parse_and_map_interest_scores(interest_df, nodes_df, edges_df):
    edges_df['interest_score'] = 0.0
    node_coords_map = {row.node_id: {'x': row.x, 'y': row.y} for _, row in nodes_df.iterrows()}

    # 使用 Start_Point, End_Point, Interest_Score 列
    for _, row in interest_df.iterrows():
        start_coords_list = re.findall(r'\(([-\d\.]+),\s*([-\d\.]+)\)', str(row['Start_Point']))
        end_coords_list = re.findall(r'\(([-\d\.]+),\s*([-\d\.]+)\)', str(row['End_Point']))

        if start_coords_list and end_coords_list:
            start_coord = (float(start_coords_list[0][0]), float(start_coords_list[0][1]))
            end_coord = (float(end_coords_list[0][0]), float(end_coords_list[0][1]))
            interest_mid_x, interest_mid_y = (start_coord[0] + end_coord[0]) / 2, (start_coord[1] + end_coord[1]) / 2
            min_dist, best_edge_idx = float('inf'), -1
            for idx, edge in edges_df.iterrows():
                p1, p2 = node_coords_map.get(edge['source']), node_coords_map.get(edge['target'])
                if p1 and p2:
                    edge_mid_x, edge_mid_y = (p1['x'] + p2['x']) / 2, (p1['y'] + p2['y']) / 2
                    dist = (edge_mid_x - interest_mid_x) ** 2 + (edge_mid_y - interest_mid_y) ** 2
                    if dist < min_dist: min_dist, best_edge_idx = dist, idx
            if best_edge_idx != -1:
                # 确保 'Interest_Score' 列存在
                if 'Interest_Score' in row:
                    edges_df.loc[best_edge_idx, 'interest_score'] += float(row['Interest_Score'])
    return edges_df


# =========================================================================
#  3. 背景图函数 (保持不变)
# =========================================================================
def parse_road_coordinates_for_background(df):
    coord_series = df.iloc[:, 0].dropna()
    segments = []
    current_segment_points = []
    coord_pattern = re.compile(r'\{([\d.-]+),\s*([\d.-]+),\s*([\d.-]+)\}')
    segment_separator_pattern = re.compile(r'\{0;\s*\d+\}')
    for item in coord_series:
        item_str = str(item)
        if segment_separator_pattern.search(item_str):
            if current_segment_points and len(current_segment_points) > 1:
                segments.append(LineString(current_segment_points))
            current_segment_points = []
        points_in_str = re.findall(coord_pattern, item_str)
        for p in points_in_str:
            current_segment_points.append((float(p[0]), float(p[1])))
    if current_segment_points and len(current_segment_points) > 1:
        segments.append(LineString(current_segment_points))
    return segments


def build_path_graph_for_background(lines):
    G = nx.Graph()
    if not lines: return G
    all_segments = []
    for line in lines:
        coords = list(line.coords)
        for i in range(len(coords) - 1):
            segment = LineString([coords[i], coords[i + 1]])
            all_segments.append(segment)
    node_coords_set = set()
    for seg in all_segments:
        node_coords_set.add(seg.coords[0])
        node_coords_set.add(seg.coords[1])
    for i in range(len(all_segments)):
        for j in range(i + 1, len(all_segments)):
            if all_segments[i].crosses(all_segments[j]):
                intersection_point = all_segments[i].intersection(all_segments[j])
                if isinstance(intersection_point, Point):
                    node_coords_set.add(intersection_point.coords[0])
    node_list = list(node_coords_set)
    for line in lines:
        nodes_on_line = []
        for node in node_list:
            if line.distance(Point(node)) < 1e-9:
                nodes_on_line.append((line.project(Point(node)), node))
        nodes_on_line.sort()
        for k in range(len(nodes_on_line) - 1):
            start_node, end_node = nodes_on_line[k][1], nodes_on_line[k + 1][1]
            if start_node != end_node:
                distance = Point(start_node).distance(Point(end_node))
                if distance > 1e-9:
                    G.add_edge(start_node, end_node, length=distance)
    return G


# =================================================================
#  4. 核心建模与路径规划函数 (保持不变)
# =================================================================
def dijkstra_for_q_matrix(graph, start_node):
    dists = {node: [float('inf'), 0] for node in graph}
    if start_node not in dists: return None
    dists[start_node] = [0, 0];
    pq = [(0, start_node)]
    while pq:
        dist, curr = heapq.heappop(pq)
        if dist > dists[curr][0]: continue
        for edge_info in graph.get(curr, []):
            neighbor, weight, interest = edge_info['target'], edge_info['dist'], edge_info['interest']
            if dists[curr][0] + weight < dists[neighbor][0]:
                dists[neighbor] = [dists[curr][0] + weight, dists[curr][1] + interest]
                heapq.heappush(pq, (dists[neighbor][0], neighbor))
    return dists


def solve_tsp_with_pulp(cost_matrix, start_idx, end_idx):
    num_nodes = len(cost_matrix);
    prob = pl.LpProblem("Garden_Tour_TSP", pl.LpMinimize)
    x = pl.LpVariable.dicts("x", ((i, j) for i in range(num_nodes) for j in range(num_nodes) if i != j), cat='Binary')
    prob += pl.lpSum(cost_matrix[i][j] * x.get((i, j), 0) for i in range(num_nodes) for j in range(num_nodes) if i != j)
    for i in range(num_nodes):
        if i != end_idx: prob += pl.lpSum(x.get((i, j), 0) for j in range(num_nodes) if i != j) == 1
        if i != start_idx: prob += pl.lpSum(x.get((j, i), 0) for j in range(num_nodes) if i != j) == 1
    u = pl.LpVariable.dicts('u', range(num_nodes), lowBound=1, upBound=num_nodes - 1, cat='Continuous')
    for i in range(num_nodes):
        for j in range(num_nodes):
            if i != j and i != start_idx and j != start_idx:
                prob += u[i] - u[j] + (num_nodes - 1) * x.get((i, j), 0) <= num_nodes - 2
    prob.solve(pl.PULP_CBC_CMD(msg=0))
    if prob.status == pl.LpStatusOptimal:
        path, curr = [start_idx], start_idx
        while curr != end_idx and len(path) <= num_nodes:
            found_next = False
            for j in range(num_nodes):
                if (curr, j) in x and x[(curr, j)].varValue > 0.9:
                    path.append(j);
                    curr = j;
                    found_next = True;
                    break
            if not found_next:
                break
        return path
    return None


def dijkstra_for_physical_path(graph, start_node, end_node):
    distances = {node: float('inf') for node in graph}
    if start_node not in distances: return []
    distances[start_node] = 0
    pq = [(0, start_node, [])]
    while pq:
        dist, curr, path = heapq.heappop(pq)
        if dist > distances[curr]: continue
        path = path + [curr]
        if curr == end_node: return path
        for edge_info in graph.get(curr, []):
            neighbor, weight = edge_info['target'], edge_info['dist']
            if distances[curr] + weight < distances[neighbor]:
                distances[neighbor] = distances[curr] + weight
                heapq.heappush(pq, (distances[neighbor], neighbor, path))
    return []


def dijkstra_with_penalty(graph, start_node, end_node, traversed_nodes_for_penalty, penalty_factor=50.0):
    distances = {node: float('inf') for node in graph}
    if start_node not in distances: return []
    distances[start_node] = 0
    pq = [(0, start_node, [])]
    while pq:
        cost, curr, path = heapq.heappop(pq)
        if cost > distances[curr]: continue
        path = path + [curr]
        if curr == end_node: return path
        for edge_info in graph.get(curr, []):
            neighbor, weight = edge_info['target'], edge_info['dist']
            penalized_weight = weight * penalty_factor if neighbor in traversed_nodes_for_penalty else weight
            new_cost = distances[curr] + penalized_weight
            if new_cost < distances.get(neighbor, float('inf')):
                distances[neighbor] = new_cost
                heapq.heappush(pq, (new_cost, neighbor, path))
    return []


# =================================================================
#  5. 主处理函数
# =================================================================
def process_garden(garden_name, garden_id, interest_path, road_path, output_dir):
    """
    对单个园林执行完整的路径规划和可视化流程。
    """
    print(f"\n\n{'=' * 60}\n>>> 开始处理园林: {garden_name} <<<\n{'=' * 60}")

    # --- 1. 数据加载 ---
    try:
        print(f"   - 正在从 '{interest_path}' 加载趣味性评分...")
        interest_scores_df_raw = pd.read_excel(interest_path, sheet_name='分段结果')
        print(f"   - 正在从 '{road_path}' 加载园林数据...")
        road_df = pd.read_excel(road_path, sheet_name='道路', header=None)
        print("   - 文件加载成功！")
    except FileNotFoundError as e:
        print(f"!!! 错误：找不到文件 {e.filename}。跳过处理园林: {garden_name}")
        return None
    except Exception as e:
        print(f"!!! 读取文件时发生未知错误: {e}。跳过处理园林: {garden_name}")
        return None

    # --- 2. 核心建模与路径规划 ---
    print("\n--- 开始执行E-CARGO建模 ---")
    print("1. 正在解析数据用于路径规划...")
    nodes_df, edges_df = parse_road_data_for_routing(road_df)

    # 确保 'Interest_Score' 列存在
    if 'Interest_Score' not in interest_scores_df_raw.columns:
        print(f"!!! 错误: 趣味性评分文件 {interest_path} 中缺少 'Interest_Score' 列。跳过园林 {garden_name}")
        return None

    edges_df = parse_and_map_interest_scores(interest_scores_df_raw, nodes_df, edges_df)

    if nodes_df.empty or edges_df.empty:
        print(f"错误：无法为 {garden_name} 构建有效的道路网络。")
        return None

    print(f"路网构建完成：包含 {len(nodes_df)} 个节点和 {len(edges_df)} 条边。")
    graph = {i: [] for i in nodes_df['node_id']}
    for _, edge in edges_df.iterrows():
        u, v = int(edge['source']), int(edge['target'])
        p1, p2 = nodes_df.loc[u], nodes_df.loc[v]
        dist = np.sqrt((p1['x'] - p2['x']) ** 2 + (p1['y'] - p2['y']) ** 2)
        interest = edge['interest_score']
        graph[u].append({'target': v, 'dist': dist, 'interest': interest})
        graph[v].append({'target': u, 'dist': dist, 'interest': interest})

    # ... 后续处理逻辑与原始代码相同 ...
    print("2. 正在分析路网连通性...")
    visited, components = set(), []
    for node in graph:
        if node not in visited:
            component, q = [], deque([node])
            visited.add(node)
            while q:
                curr = q.popleft();
                component.append(curr)
                for edge in graph.get(curr, []):
                    neighbor = edge['target']
                    if neighbor not in visited: visited.add(neighbor); q.append(neighbor)
            components.append(component)

    if not components: print(f"错误：{garden_name} 未能构建任何有效的路网图。"); return None
    largest_component = max(components, key=len)
    largest_component_set = set(largest_component)
    print(f"路网包含 {len(components)} 个独立区域。已锁定最大区域进行规划。")

    NUM_ROLES = 8
    MIN_ROLE_DISTANCE = 30000
    print(f"3. 阶段一：筛选 {NUM_ROLES} 个核心景点...")
    interest_edges = edges_df[edges_df['interest_score'] > 0].copy()
    interest_edges = interest_edges[
        interest_edges['source'].isin(largest_component_set) & interest_edges['target'].isin(largest_component_set)]
    if interest_edges.empty:
        print(f"警告：在 {garden_name} 的最大连通区域内未找到任何有趣味性评分的路径。无法规划。")
        return None

    interest_edges['mid_x'] = interest_edges.apply(
        lambda r: (nodes_df.loc[r['source']]['x'] + nodes_df.loc[r['target']]['x']) / 2, axis=1)
    interest_edges['mid_y'] = interest_edges.apply(
        lambda r: (nodes_df.loc[r['source']]['y'] + nodes_df.loc[r['target']]['y']) / 2, axis=1)
    interest_edges['role_node_id'] = interest_edges.apply(
        lambda r: r['source'] if np.random.rand() > 0.5 else r['target'], axis=1)
    sorted_roles = interest_edges.sort_values(by='interest_score', ascending=False)

    selected_roles = []
    for _, role in sorted_roles.iterrows():
        if len(selected_roles) >= NUM_ROLES: break
        is_far_enough = True
        current_pos = np.array([role['mid_x'], role['mid_y']])
        for selected_role in selected_roles:
            selected_pos = np.array([selected_role['mid_x'], selected_role['mid_y']])
            if np.linalg.norm(current_pos - selected_pos) < MIN_ROLE_DISTANCE: is_far_enough = False; break
        if is_far_enough: selected_roles.append(role)

    if not selected_roles: print(f"错误：{garden_name} 未能找到满足条件的核心景点。"); return None
    roles_df = pd.DataFrame(selected_roles)
    role_node_ids = [int(x) for x in roles_df['role_node_id']]
    role_node_ids_set = set(role_node_ids)
    print(f"已筛选出 {len(role_node_ids)} 个角色。")

    valid_nodes_df = nodes_df[nodes_df['node_id'].isin(largest_component_set)]
    min_x, max_x, min_y = valid_nodes_df['x'].min(), valid_nodes_df['x'].max(), valid_nodes_df['y'].min()
    entrance_idx = cdist([[max_x * 0.9 + min_x * 0.1, min_y]], valid_nodes_df[['x', 'y']]).argmin()
    entrance_node_id = int(valid_nodes_df.iloc[entrance_idx].name)
    exit_idx = cdist([[min_x * 0.9 + max_x * 0.1, min_y]], valid_nodes_df[['x', 'y']]).argmin()
    exit_node_id = int(valid_nodes_df.iloc[exit_idx].name)
    print(f"4. 已定义入口: {entrance_node_id}, 出口: {exit_node_id}。")

    print("5. 阶段二：最优路径指派...")
    critical_nodes = sorted(list(set([entrance_node_id] + role_node_ids + [exit_node_id])))
    node_map = {node_id: i for i, node_id in enumerate(critical_nodes)}
    n_crit = len(critical_nodes)
    dist_matrix = np.full((n_crit, n_crit), np.inf)
    interest_matrix = np.full((n_crit, n_crit), 0.0)

    for i, start_node in enumerate(critical_nodes):
        all_node_dists = dijkstra_for_q_matrix(graph, start_node)
        if all_node_dists is None: continue
        for j, end_node in enumerate(critical_nodes):
            if end_node in all_node_dists:
                dist_matrix[i, j], interest_matrix[i, j] = all_node_dists[end_node]

    if np.isinf(dist_matrix).all(): print("错误: 成本矩阵计算失败。"); return None
    max_dist = np.max(dist_matrix[np.isfinite(dist_matrix)]) if np.any(np.isfinite(dist_matrix)) else 1
    max_interest = np.max(interest_matrix) if np.max(interest_matrix) > 0 else 1
    norm_dist = dist_matrix / max_dist
    norm_interest = interest_matrix / max_interest
    q_matrix = 0.5 * norm_dist - 0.5 * norm_interest
    q_matrix[np.isinf(q_matrix)] = 1e6

    start_idx, end_idx = node_map[entrance_node_id], node_map.get(exit_node_id, -1)
    best_path_indices = solve_tsp_with_pulp(q_matrix, start_idx, end_idx)
    best_path_node_ids = [critical_nodes[i] for i in best_path_indices] if best_path_indices else []

    final_tour_nodes = []
    all_traversed_nodes_for_penalty = set()
    if best_path_node_ids:
        print("6. 正在构建最终物理路径...")
        for i in range(len(best_path_node_ids) - 1):
            start, end = best_path_node_ids[i], best_path_node_ids[i + 1]
            path_segment = dijkstra_with_penalty(graph, start, end, all_traversed_nodes_for_penalty)
            if not path_segment:
                path_segment = dijkstra_for_physical_path(graph, start, end)
                if not path_segment:
                    print(f"!!! 严重错误：无法连接从 {start} 到 {end}。");
                    final_tour_nodes = [];
                    break
            for node in path_segment[:-1]: all_traversed_nodes_for_penalty.add(node)
            final_tour_nodes.extend(path_segment if not final_tour_nodes else path_segment[1:])
    else:
        print(f"错误：{garden_name} 未能找到最优访问顺序。")
        return None

    # --- 3. 可视化与结果返回 ---
    if final_tour_nodes:
        total_length_mm = sum(
            np.sqrt((nodes_df.loc[final_tour_nodes[i]]['x'] - nodes_df.loc[final_tour_nodes[i + 1]]['x']) ** 2 +
                    (nodes_df.loc[final_tour_nodes[i]]['y'] - nodes_df.loc[final_tour_nodes[i + 1]]['y']) ** 2)
            for i in range(len(final_tour_nodes) - 1))
        total_length_meters = total_length_mm / 1000
        tour_time_minutes = (total_length_meters / 1.1) / 60

        print("\n7. 正在生成路线图...")
        fig, ax = plt.subplots(figsize=(22, 22))

        if VISUALIZATION_LIB_AVAILABLE:
            try:
                road_lines = parse_road_coordinates_for_background(road_df)
                background_graph = build_path_graph_for_background(road_lines)
                if background_graph.number_of_edges() > 0:
                    pos = {node: (node[0], node[1]) for node in background_graph.nodes()}
                    nx.draw_networkx_edges(background_graph, pos, ax=ax, alpha=0.5, width=1.2, edge_color='gray')
            except Exception as e:
                print(f"   - 绘制背景时发生错误: {e}")

        current_path_index = 0
        path_colors = plt.cm.viridis(np.linspace(0, 1, len(best_path_node_ids) - 1))
        for i in range(len(best_path_node_ids) - 1):
            start_node, end_node = best_path_node_ids[i], best_path_node_ids[i + 1]
            try:
                start_idx_in_final = final_tour_nodes.index(start_node, current_path_index)
                end_idx_in_final = final_tour_nodes.index(end_node, start_idx_in_final)
                segment_nodes = final_tour_nodes[start_idx_in_final: end_idx_in_final + 1]
                segment_coords = [(nodes_df.loc[nid]['x'], nodes_df.loc[nid]['y']) for nid in segment_nodes]
                ax.plot(*zip(*segment_coords), color=path_colors[i], linewidth=3.0, zorder=10, alpha=0.8)
                current_path_index = end_idx_in_final
            except ValueError:
                continue

        ax.plot(nodes_df.loc[entrance_node_id]['x'], nodes_df.loc[entrance_node_id]['y'], 'p', color='lime',
                markersize=22, label='入口', zorder=11, markeredgecolor='black', markeredgewidth=1.5)
        ax.plot(nodes_df.loc[exit_node_id]['x'], nodes_df.loc[exit_node_id]['y'], 'h', color='yellow', markersize=22,
                label='出口', zorder=11, markeredgecolor='black', markeredgewidth=1.5)

        scores = roles_df['interest_score'].astype(float)
        norm = mcolors.Normalize(vmin=scores.min(), vmax=scores.max())
        cmap = cm.Blues
        sm = plt.cm.ScalarMappable(cmap=cmap, norm=norm)

        role_order_map = {node_id: f"景点 {i + 1}" for i, node_id in
                          enumerate(nid for nid in best_path_node_ids if nid in role_node_ids_set)}

        for role_node_id, role_label in role_order_map.items():
            role_info = roles_df[roles_df['role_node_id'] == role_node_id].iloc[0]
            score = float(role_info['interest_score'])
            x, y = nodes_df.loc[role_node_id]['x'], nodes_df.loc[role_node_id]['y']
            color = cmap(norm(score))
            marker_size = 35 + norm(score) * 45
            ax.scatter(x, y, marker='*', c=[color], s=marker_size * 10, zorder=12, edgecolors='black', linewidth=1.2)
            ax.text(x, y - 2500, role_label, ha='center', va='top', fontsize=12, color='black', fontweight='bold',
                    bbox=dict(boxstyle='round,pad=0.3', fc='ivory', ec='none', alpha=0.85))

        cbar = plt.colorbar(sm, ax=ax, shrink=0.6, orientation='vertical')
        cbar.set_label('异景程度 (趣味性评分)', rotation=270, labelpad=25, fontsize=16)

        ax.set_aspect('equal', adjustable='box')
        ax.set_title(f"{garden_name}游览路线图", fontsize=24, pad=20)
        ax.set_xlabel("X 坐标 (mm)", fontsize=16)
        ax.set_ylabel("Y 坐标 (mm)", fontsize=16)
        ax.tick_params(axis='both', which='major', labelsize=12)
        ax.grid(False)

        from matplotlib.lines import Line2D
        legend_elements = [
            Line2D([0], [0], color='lime', marker='p', linestyle='None', markersize=12, label='入口',
                   markeredgecolor='k'),
            Line2D([0], [0], color='yellow', marker='h', linestyle='None', markersize=12, label='出口',
                   markeredgecolor='k'),
            Line2D([0], [0], marker='*', color='w', label='核心景点', markerfacecolor='cornflowerblue',
                   markeredgecolor='k', markersize=15),
            Line2D([0], [0], color='#37939A', lw=3, label='分段路线 (按彩虹色顺序)')
        ]
        ax.legend(handles=legend_elements, prop={'size': 15}, loc='best')

        output_filename = os.path.join(output_dir, f"{garden_id}_{garden_name}_tour_guide.png")
        plt.savefig(output_filename, dpi=300, bbox_inches='tight')
        print(f"\n--- {garden_name} 处理完成！已生成路线图 '{output_filename}' ---")
        plt.close(fig)  # 关闭图形，释放内存

        return {
            "园林名称": garden_name,
            "路径总长度(米)": round(total_length_meters, 2),
            "预估游览时间(分钟)": round(tour_time_minutes, 2)
        }
    else:
        print(f"\n由于未能为 {garden_name} 构建完整路径，程序已结束，未生成路线图。")
        return None


# =================================================================
#  6. 批量执行的主程序
# =================================================================
if __name__ == '__main__':
    # --- 配置区域 ---
    # 1. 定义十个园林及其对应的编号 (用于查找坐标文件)
    gardens_info = {
        "拙政园": 1, "留园": 2, "寄畅园": 3, "瞻园": 4,
        "豫园": 5, "秋霞圃": 6, "沈园": 7, "怡园": 8,
        "耦园": 9, "绮园": 10
    }

    # 2. 定义数据和输出的根目录
    DESKTOP_PATH = r'H:\OneDrive\Desktop'
    INTEREST_SCORE_BASE_PATH = os.path.join(DESKTOP_PATH, '十个园林趣味性评分')
    ROAD_DATA_BASE_PATH = os.path.join(DESKTOP_PATH, 'F题', '赛题F江南古典园林美学特征建模附件资料')

    # 3. 创建用于存放所有图片的文件夹
    IMAGE_OUTPUT_DIR = os.path.join(DESKTOP_PATH, '所有园林路线图')
    os.makedirs(IMAGE_OUTPUT_DIR, exist_ok=True)

    # --- 执行循环 ---
    all_results = []

    for garden_name, garden_id in gardens_info.items():
        # ##################################################################
        # ### --- 这是根据您的要求修改的唯一一行 --- ###
        # ##################################################################
        interest_path = os.path.join(INTEREST_SCORE_BASE_PATH, f'{garden_name}_path_segments_analysis.xlsx')

        road_path = os.path.join(ROAD_DATA_BASE_PATH, f'{garden_id}. {garden_name}', f'4-{garden_name}数据坐标.xlsx')

        # 调用主函数处理单个园林
        result = process_garden(
            garden_name=garden_name,
            garden_id=garden_id,
            interest_path=interest_path,
            road_path=road_path,
            output_dir=IMAGE_OUTPUT_DIR
        )

        # 如果处理成功，则收集结果
        if result:
            all_results.append(result)

    # --- 结果汇总 ---
    if all_results:
        print("\n\n==========================================================")
        print(">>> 所有园林处理完毕，正在生成汇总Excel文件... <<<")

        summary_df = pd.DataFrame(all_results)
        summary_excel_path = os.path.join(DESKTOP_PATH, '园林游览数据汇总.xlsx')

        try:
            summary_df.to_excel(summary_excel_path, index=False)
            print(f"--- 任务全部完成！已成功生成汇总文件: '{summary_excel_path}' ---")
        except Exception as e:
            print(f"!!! 错误：无法保存汇总Excel文件。原因: {e}")

    else:
        print("\n--- 所有园林均处理失败，未生成任何结果。---")