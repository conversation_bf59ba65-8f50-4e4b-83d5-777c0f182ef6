import pandas as pd
import re
import numpy as np
import matplotlib.pyplot as plt
import networkx as nx
from shapely.geometry import Point, LineString
import os
from scipy.spatial import KDTree

# 太密集

def parse_points_from_excel(file_path, sheet_name='道路'):
    """
    从Excel文件中解析出所有坐标点的列表。
    """
    try:
        df = pd.read_excel(file_path, sheet_name=sheet_name, header=0)
    except FileNotFoundError:
        print(f"错误：在指定路径找不到文件 {file_path}")
        return []
    except Exception as e:
        print(f"读取Excel文件时出错: {e}")
        return []

    if df.shape[1] < 2:
        print(f"错误：工作表 '{sheet_name}' 的列数少于两列。")
        return []

    coord_series = df.iloc[:, 1].dropna()
    points = []
    coord_pattern = re.compile(r'\{([\d.-]+),\s*([\d.-]+),[\d.-]+\}')

    for item in coord_series:
        match = coord_pattern.search(str(item))
        if match:
            x, y = map(float, match.groups())
            points.append((x, y))

    unique_points = sorted(list(set(points)))
    return unique_points


def build_graph_from_points(points, distance_threshold):
    """
    根据空间距离阈值从点集构建网络图。
    """
    if not points:
        return nx.Graph()

    tree = KDTree(points)
    graph = nx.Graph()
    pairs = tree.query_pairs(r=distance_threshold)

    for (i, j) in pairs:
        p1 = points[i]
        p2 = points[j]
        distance = np.linalg.norm(np.array(p1) - np.array(p2))
        graph.add_edge(p1, p2, weight=distance)

    return graph


def calculate_angle(p1, p2, p3):
    """计算由三个点p1-p2-p3形成的角度。"""
    v1 = np.array(p1) - np.array(p2)
    v2 = np.array(p3) - np.array(p2)
    norm_v1 = np.linalg.norm(v1)
    norm_v2 = np.linalg.norm(v2)
    if norm_v1 == 0 or norm_v2 == 0:
        return 180.0

    dot_product = np.dot(v1, v2)
    cos_angle = np.clip(dot_product / (norm_v1 * norm_v2), -1.0, 1.0)
    angle = np.degrees(np.arccos(cos_angle))
    return angle


def analyze_graph_features(graph):
    """
    分析图的特征，如交叉点和转折点。
    """
    features = {
        'total_length': graph.size(weight='weight'),
        'intersections': [],
        'terminals': [],
        'sharp_turns': []
    }

    node_degrees = dict(graph.degree())

    for node, degree in node_degrees.items():
        if degree == 1:
            features['terminals'].append(node)
        elif degree > 2:
            features['intersections'].append(node)
        elif degree == 2:
            neighbors = list(graph.neighbors(node))
            p1, p3 = neighbors[0], neighbors[1]
            p2 = node
            angle = calculate_angle(p1, p2, p3)
            if angle <= 120:
                features['sharp_turns'].append({'point': node, 'angle': angle})

    return features


def plot_garden_network(graph, features, output_filename):
    """
    可视化园林网络图。
    """
    plt.figure(figsize=(16, 12))

    try:
        plt.rcParams['font.sans-serif'] = ['SimHei']
        plt.rcParams['axes.unicode_minus'] = False
    except:
        print("警告：未找到SimHei字体，图表中的中文可能无法正确显示。")

    pos = {node: (node[0], node[1]) for node in graph.nodes()}

    nx.draw_networkx_edges(graph, pos, edge_color='gray', width=1.5, alpha=0.8)

    intersection_points = features['intersections']
    if intersection_points:
        inter_x, inter_y = zip(*intersection_points)
        plt.scatter(inter_x, inter_y, c='red', s=80, zorder=3, label=f'交叉点 ({len(intersection_points)})')

    sharp_turn_points = [turn['point'] for turn in features['sharp_turns']]
    if sharp_turn_points:
        turn_x, turn_y = zip(*sharp_turn_points)
        plt.scatter(turn_x, turn_y, c='blue', s=50, zorder=2, marker='^', label=f'转折点 ({len(sharp_turn_points)})')

    plt.title('何园道路网络分析图 (增强连接)', fontsize=20)
    plt.xlabel('X 坐标', fontsize=14)
    plt.ylabel('Y 坐标', fontsize=14)
    plt.legend(fontsize=12)
    plt.grid(True, linestyle='--', alpha=0.6)
    plt.gca().set_aspect('equal', adjustable='box')

    plt.savefig(output_filename, dpi=300, bbox_inches='tight')
    print(f"\n网络图已成功保存到: {output_filename}")


def main():
    input_file_path = r"H:\OneDrive\Desktop\何园-处理.xlsx"
    sheet_name = "道路"

    # --- 关键参数调整 ---
    # 将距离阈值从15增大到20，以连接更多稍远的节点，使网络更完整。
    # 如果您觉得连接得太过或仍然不够，可以继续微调这个数值。
    DISTANCE_THRESHOLD = 40.0

    output_directory = os.path.dirname(input_file_path)
    # 修改输出文件名以作区分
    output_image_path = os.path.join(output_directory, "he_garden_network_extended.png")

    print(f"开始处理Excel文件: '{input_file_path}' 的工作表: '{sheet_name}'")

    points = parse_points_from_excel(input_file_path, sheet_name)
    if not points:
        print("未能解析出任何坐标点，程序终止。")
        return
    print(f"成功解析出 {len(points)} 个唯一的坐标点。")

    print(f"使用 {DISTANCE_THRESHOLD} 作为距离阈值，开始从点集重构路径网络...")
    path_graph = build_graph_from_points(points, DISTANCE_THRESHOLD)
    print("路径图构建完成。")
    print(f"图中共有 {path_graph.number_of_nodes()} 个节点和 {path_graph.number_of_edges()} 条边。")

    graph_features = analyze_graph_features(path_graph)
    print("图特征分析完成。")

    print("\n--- 何园路径网络分析结果 ---")
    print(f"  - 总路径长度: {graph_features['total_length']:.2f}")
    print(f"  - 路径端点数量: {len(graph_features['terminals'])}")
    print(f"  - 交叉点 (degree > 2) 数量: {len(graph_features['intersections'])}")
    print(f"  - 转折点 (angle <= 120°) 数量: {len(graph_features['sharp_turns'])}")

    plot_garden_network(path_graph, graph_features, output_image_path)


if __name__ == '__main__':
    main()