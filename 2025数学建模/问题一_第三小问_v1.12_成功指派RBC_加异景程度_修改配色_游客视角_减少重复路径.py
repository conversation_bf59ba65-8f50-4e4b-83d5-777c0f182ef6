import pandas as pd
import re
import matplotlib.pyplot as plt
from scipy.spatial.distance import cdist
import heapq
import numpy as np
import pulp as pl
from collections import deque
import os
from datetime import datetime
import matplotlib.cm as cm
import matplotlib.colors as mcolors
import matplotlib.ticker as mticker
from matplotlib.patches import FancyArrowPatch

# --- 尝试导入可选的、用于绘图的库 ---
try:
    from shapely.geometry import Point, LineString
    import networkx as nx

    VISUALIZATION_LIB_AVAILABLE = True
    print("用于精细绘图的'shapely'和'networkx'库已找到。")
except ImportError:
    VISUALIZATION_LIB_AVAILABLE = False
    print("警告：未安装 'shapely' 或 'networkx' 库。背景路网将使用简化方法绘制。")
    print("建议运行: pip install shapely networkx")

# --- 配置中文字体 ---
try:
    plt.rcParams['font.sans-serif'] = ['SimHei']
    plt.rcParams['axes.unicode_minus'] = False
except Exception:
    print("警告：未找到'SimHei'字体，图例可能显示不正常。")

# =================================================================
#  1. 数据加载与路径设置 (保持不变)
# =================================================================
SCORE_MATRIX_PATH = r'H:\OneDrive\Desktop\final_scoring_matrix_v4_final.csv'
ROAD_EXCEL_PATH = r'H:\OneDrive\Desktop\F题\赛题F江南古典园林美学特征建模附件资料\1. 拙政园\4-拙政园数据坐标.xlsx'

try:
    print(f"正在从 '{SCORE_MATRIX_PATH}' 加载趣味性评分矩阵...")
    interest_scores_df_raw = pd.read_csv(SCORE_MATRIX_PATH)
    print(f"正在从 '{ROAD_EXCEL_PATH}' 加载园林数据...")
    road_df = pd.read_excel(ROAD_EXCEL_PATH, sheet_name='道路', header=None)
    print("所有必需文件成功加载！")
except FileNotFoundError as e:
    print(f"错误：无法找到文件 {e.filename}。请检查路径是否正确。")
    exit()


# =================================================================
#  2. 核心数据解析函数 (保持不变)
# =================================================================
def parse_road_data_for_routing(df):
    all_points = []
    for _, row in df.iterrows():
        coords_str = "".join(str(s) for s in row if pd.notna(s))
        points = re.findall(r'\{([-\d\.]+),\s*([-\d\.]+),\s*([-\d\.]+)\}', coords_str)
        for p in points:
            all_points.append((float(p[0]), float(p[1])))

    if not all_points: return pd.DataFrame(), pd.DataFrame()

    nodes_df = pd.DataFrame(all_points, columns=['x', 'y']).drop_duplicates().reset_index(drop=True)
    nodes_df['node_id'] = nodes_df.index
    coord_to_id = {(round(row.x, 4), round(row.y, 4)): row.node_id for _, row in nodes_df.iterrows()}

    edges = []
    for _, row in df.iterrows():
        coords_str = "".join(str(s) for s in row if pd.notna(s))
        points_in_row = re.findall(r'\{([-\d\.]+),\s*([-\d\.]+),\s*([-\d\.]+)\}', coords_str)
        current_nodes = []
        for p in points_in_row:
            coord = (round(float(p[0]), 4), round(float(p[1]), 4))
            node_id = coord_to_id.get(coord)
            if node_id is not None: current_nodes.append(node_id)
        for i in range(len(current_nodes) - 1):
            u, v = sorted((current_nodes[i], current_nodes[i + 1]))
            if u != v: edges.append({'source': u, 'target': v})

    edges_df = pd.DataFrame(edges).drop_duplicates().reset_index(drop=True)
    return nodes_df, edges_df


def parse_and_map_interest_scores(interest_df, nodes_df, edges_df):
    edges_df['interest_score'] = 0.0
    node_coords_map = {row.node_id: {'x': row.x, 'y': row.y} for _, row in nodes_df.iterrows()}

    for _, row in interest_df.iterrows():
        start_coords_list = re.findall(r'\(([-\d\.]+),\s*([-\d\.]+)\)', str(row['Start_Point']))
        end_coords_list = re.findall(r'\(([-\d\.]+),\s*([-\d\.]+)\)', str(row['End_Point']))

        if start_coords_list and end_coords_list:
            start_coord = (float(start_coords_list[0][0]), float(start_coords_list[0][1]))
            end_coord = (float(end_coords_list[0][0]), float(end_coords_list[0][1]))
            interest_mid_x, interest_mid_y = (start_coord[0] + end_coord[0]) / 2, (start_coord[1] + end_coord[1]) / 2
            min_dist, best_edge_idx = float('inf'), -1
            for idx, edge in edges_df.iterrows():
                p1, p2 = node_coords_map.get(edge['source']), node_coords_map.get(edge['target'])
                if p1 and p2:
                    edge_mid_x, edge_mid_y = (p1['x'] + p2['x']) / 2, (p1['y'] + p2['y']) / 2
                    dist = (edge_mid_x - interest_mid_x) ** 2 + (edge_mid_y - interest_mid_y) ** 2
                    if dist < min_dist: min_dist, best_edge_idx = dist, idx
            if best_edge_idx != -1:
                edges_df.loc[best_edge_idx, 'interest_score'] += float(row['Interest_Score'])
    return edges_df


# =========================================================================
#  3. 背景图函数 (保持不变)
# =========================================================================
def parse_road_coordinates_for_background(df):
    coord_series = df.iloc[:, 0].dropna()
    segments = []
    current_segment_points = []
    coord_pattern = re.compile(r'\{([\d.-]+),\s*([\d.-]+),\s*([\d.-]+)\}')
    segment_separator_pattern = re.compile(r'\{0;\s*\d+\}')
    for item in coord_series:
        item_str = str(item)
        if segment_separator_pattern.search(item_str):
            if current_segment_points and len(current_segment_points) > 1:
                segments.append(LineString(current_segment_points))
            current_segment_points = []
        points_in_str = re.findall(coord_pattern, item_str)
        for p in points_in_str:
            current_segment_points.append((float(p[0]), float(p[1])))
    if current_segment_points and len(current_segment_points) > 1:
        segments.append(LineString(current_segment_points))
    return segments


def build_path_graph_for_background(lines):
    G = nx.Graph()
    if not lines: return G
    all_segments = []
    for line in lines:
        coords = list(line.coords)
        for i in range(len(coords) - 1):
            segment = LineString([coords[i], coords[i + 1]])
            all_segments.append(segment)
    node_coords_set = set()
    for seg in all_segments:
        node_coords_set.add(seg.coords[0])
        node_coords_set.add(seg.coords[1])
    for i in range(len(all_segments)):
        for j in range(i + 1, len(all_segments)):
            if all_segments[i].crosses(all_segments[j]):
                intersection_point = all_segments[i].intersection(all_segments[j])
                if isinstance(intersection_point, Point):
                    node_coords_set.add(intersection_point.coords[0])
    node_list = list(node_coords_set)
    for line in lines:
        nodes_on_line = []
        for node in node_list:
            if line.distance(Point(node)) < 1e-9:
                nodes_on_line.append((line.project(Point(node)), node))
        nodes_on_line.sort()
        for k in range(len(nodes_on_line) - 1):
            start_node, end_node = nodes_on_line[k][1], nodes_on_line[k + 1][1]
            if start_node != end_node:
                distance = Point(start_node).distance(Point(end_node))
                if distance > 1e-9:
                    G.add_edge(start_node, end_node, length=distance)
    return G


# =================================================================
#  4. 核心建模与路径规划流程 (部分修改)
# =================================================================
print("\n--- 开始执行E-CARGO建模 ---")
print("1. 正在解析数据用于路径规划...")
nodes_df, edges_df = parse_road_data_for_routing(road_df)
edges_df = parse_and_map_interest_scores(interest_scores_df_raw, nodes_df, edges_df)

if nodes_df.empty or edges_df.empty:
    print("错误：无法从数据中构建有效的道路网络。程序终止。");
    exit()

print(f"路网构建完成：包含 {len(nodes_df)} 个节点和 {len(edges_df)} 条边。")
graph = {i: [] for i in nodes_df['node_id']}
for _, edge in edges_df.iterrows():
    u, v = int(edge['source']), int(edge['target'])
    p1, p2 = nodes_df.loc[u], nodes_df.loc[v]
    dist = np.sqrt((p1['x'] - p2['x']) ** 2 + (p1['y'] - p2['y']) ** 2)
    interest = edge['interest_score']
    graph[u].append({'target': v, 'dist': dist, 'interest': interest})
    graph[v].append({'target': u, 'dist': dist, 'interest': interest})

print("2. 正在分析路网连通性...")
# ... (此部分代码保持不变) ...
visited, components = set(), []
for node in graph:
    if node not in visited:
        component, q = [], deque([node])
        visited.add(node)
        while q:
            curr = q.popleft();
            component.append(curr)
            for edge in graph.get(curr, []):
                neighbor = edge['target']
                if neighbor not in visited: visited.add(neighbor); q.append(neighbor)
        components.append(component)
if not components:
    print("错误：未能从地图数据构建任何有效的路网图。");
    exit()
largest_component = max(components, key=len)
largest_component_set = set(largest_component)
print(f"路网包含 {len(components)} 个独立区域。已锁定最大区域（含 {len(largest_component)} 个节点）进行规划。")

NUM_ROLES = 8
MIN_ROLE_DISTANCE = 30000
print(f"3. 阶段一：在最大连通区域内筛选 {NUM_ROLES} 个核心景点作为'角色'...")
# ... (此部分代码保持不变) ...
interest_edges = edges_df[edges_df['interest_score'] > 0].copy()
interest_edges = interest_edges[
    interest_edges['source'].isin(largest_component_set) & interest_edges['target'].isin(largest_component_set)]
interest_edges['mid_x'] = interest_edges.apply(
    lambda r: (nodes_df.loc[r['source']]['x'] + nodes_df.loc[r['target']]['x']) / 2, axis=1)
interest_edges['mid_y'] = interest_edges.apply(
    lambda r: (nodes_df.loc[r['source']]['y'] + nodes_df.loc[r['target']]['y']) / 2, axis=1)
interest_edges['role_node_id'] = interest_edges.apply(lambda r: r['source'] if np.random.rand() > 0.5 else r['target'],
                                                      axis=1)
sorted_roles = interest_edges.sort_values(by='interest_score', ascending=False)
selected_roles = []
for _, role in sorted_roles.iterrows():
    if len(selected_roles) >= NUM_ROLES: break
    is_far_enough = True
    current_pos = np.array([role['mid_x'], role['mid_y']])
    for selected_role in selected_roles:
        selected_pos = np.array([selected_role['mid_x'], selected_role['mid_y']])
        if np.linalg.norm(current_pos - selected_pos) < MIN_ROLE_DISTANCE: is_far_enough = False; break
    if is_far_enough: selected_roles.append(role)
if not selected_roles:
    print("错误：未能找到满足条件的核心景点。请尝试减小 MIN_ROLE_DISTANCE 的值。");
    exit()
roles_df = pd.DataFrame(selected_roles)
role_node_ids = [int(x) for x in roles_df['role_node_id']]
role_node_ids_set = set(role_node_ids)
print(f"已筛选出 {len(role_node_ids)} 个符合要求的角色。")

valid_nodes_df = nodes_df[nodes_df['node_id'].isin(largest_component_set)]
min_x, max_x, min_y = valid_nodes_df['x'].min(), valid_nodes_df['x'].max(), valid_nodes_df['y'].min()
entrance_idx = cdist([[max_x * 0.9 + min_x * 0.1, min_y]], valid_nodes_df[['x', 'y']]).argmin()
entrance_node_id = int(valid_nodes_df.iloc[entrance_idx].name)
exit_idx = cdist([[min_x * 0.9 + max_x * 0.1, min_y]], valid_nodes_df[['x', 'y']]).argmin()
exit_node_id = int(valid_nodes_df.iloc[exit_idx].name)
print(f"4. 已定义入口: 节点 {entrance_node_id}, 出口: 节点 {exit_node_id}。")

print("5. 阶段二：基于综合成本Q矩阵进行最优路径指派...")


# ... (此部分代码保持不变) ...
def dijkstra_for_q_matrix(graph, start_node):
    dists = {node: [float('inf'), 0] for node in graph}
    if start_node not in dists: return None
    dists[start_node] = [0, 0];
    pq = [(0, start_node)]
    while pq:
        dist, curr = heapq.heappop(pq)
        if dist > dists[curr][0]: continue
        for edge_info in graph.get(curr, []):
            neighbor, weight, interest = edge_info['target'], edge_info['dist'], edge_info['interest']
            if dists[curr][0] + weight < dists[neighbor][0]:
                dists[neighbor] = [dists[curr][0] + weight, dists[curr][1] + interest]
                heapq.heappush(pq, (dists[neighbor][0], neighbor))
    return dists


critical_nodes = sorted(list(set([entrance_node_id] + role_node_ids + [exit_node_id])))
node_map = {node_id: i for i, node_id in enumerate(critical_nodes)}
n_crit = len(critical_nodes)
dist_matrix = np.full((n_crit, n_crit), np.inf)
interest_matrix = np.full((n_crit, n_crit), 0.0)
print("   - 正在计算关键节点间的距离与趣味性矩阵...")
for i, start_node in enumerate(critical_nodes):
    all_node_dists = dijkstra_for_q_matrix(graph, start_node)
    if all_node_dists is None: continue
    for j, end_node in enumerate(critical_nodes):
        if end_node in all_node_dists:
            dist_matrix[i, j], interest_matrix[i, j] = all_node_dists[end_node]
if np.isinf(dist_matrix).all():
    print("错误: 成本矩阵计算失败，所有关键节点之间似乎都不连通。");
    exit()
max_dist = np.max(dist_matrix[np.isfinite(dist_matrix)]) if np.any(np.isfinite(dist_matrix)) else 1
max_interest = np.max(interest_matrix) if np.max(interest_matrix) > 0 else 1
norm_dist = dist_matrix / max_dist
norm_interest = interest_matrix / max_interest
q_matrix = 0.5 * norm_dist - 0.5 * norm_interest
q_matrix[np.isinf(q_matrix)] = 1e6


def solve_tsp_with_pulp(cost_matrix, start_idx, end_idx):
    num_nodes = len(cost_matrix);
    prob = pl.LpProblem("Garden_Tour_TSP", pl.LpMinimize)
    x = pl.LpVariable.dicts("x", ((i, j) for i in range(num_nodes) for j in range(num_nodes) if i != j), cat='Binary')
    prob += pl.lpSum(cost_matrix[i][j] * x.get((i, j), 0) for i in range(num_nodes) for j in range(num_nodes) if i != j)
    for i in range(num_nodes):
        if i != end_idx: prob += pl.lpSum(x.get((i, j), 0) for j in range(num_nodes) if i != j) == 1
        if i != start_idx: prob += pl.lpSum(x.get((j, i), 0) for j in range(num_nodes) if i != j) == 1
    u = pl.LpVariable.dicts('u', range(num_nodes), lowBound=1, upBound=num_nodes - 1, cat='Continuous')
    for i in range(num_nodes):
        for j in range(num_nodes):
            if i != j and i != start_idx and j != start_idx:
                prob += u[i] - u[j] + (num_nodes - 1) * x.get((i, j), 0) <= num_nodes - 2
    prob.solve(pl.PULP_CBC_CMD(msg=0))
    if prob.status == pl.LpStatusOptimal:
        path, curr = [start_idx], start_idx
        while curr != end_idx and len(path) <= num_nodes:
            found_next = False
            for j in range(num_nodes):
                if (curr, j) in x and x[(curr, j)].varValue > 0.9:
                    path.append(j);
                    curr = j;
                    found_next = True;
                    break
            if not found_next:
                print(f"警告: 从节点 {curr} 出发未能找到下一跳, 路径构建中断。");
                break
        return path
    return None


# ===============================================================
# V V V V V V V V V V V  这里是核心修改 V V V V V V V V V V V V V
# ===============================================================

def dijkstra_for_physical_path(graph, start_node, end_node):
    """
    标准的Dijkstra算法，仅用于无法找到带惩罚路径时的备用方案。
    """
    distances = {node: float('inf') for node in graph}
    if start_node not in distances: return []
    distances[start_node] = 0
    pq = [(0, start_node, [])]
    while pq:
        dist, curr, path = heapq.heappop(pq)
        if dist > distances[curr]: continue
        path = path + [curr]
        if curr == end_node: return path
        for edge_info in graph.get(curr, []):
            neighbor, weight = edge_info['target'], edge_info['dist']
            if distances[curr] + weight < distances[neighbor]:
                distances[neighbor] = distances[curr] + weight
                heapq.heappush(pq, (distances[neighbor], neighbor, path))
    return []


def dijkstra_with_penalty(graph, start_node, end_node, traversed_nodes_for_penalty, penalty_factor=50.0):
    """
    改进的Dijkstra算法，对已走过的节点施加成本惩罚，以鼓励寻找新路。
    """
    distances = {node: float('inf') for node in graph}
    if start_node not in distances: return []
    distances[start_node] = 0
    pq = [(0, start_node, [])]  # (成本, 当前节点, 路径)

    while pq:
        cost, curr, path = heapq.heappop(pq)

        if cost > distances[curr]:
            continue

        path = path + [curr]

        if curr == end_node:
            return path

        for edge_info in graph.get(curr, []):
            neighbor, weight = edge_info['target'], edge_info['dist']

            # 如果邻近节点是已走过的节点，则增加其通行成本
            if neighbor in traversed_nodes_for_penalty:
                penalized_weight = weight * penalty_factor
            else:
                penalized_weight = weight

            new_cost = distances[curr] + penalized_weight
            if new_cost < distances.get(neighbor, float('inf')):
                distances[neighbor] = new_cost
                heapq.heappush(pq, (new_cost, neighbor, path))

    return []  # 如果找不到路径则返回空列表


start_idx, end_idx = node_map[entrance_node_id], node_map.get(exit_node_id, -1)
best_path_indices = solve_tsp_with_pulp(q_matrix, start_idx, end_idx)
best_path_node_ids = []
if best_path_indices:
    best_path_node_ids = [critical_nodes[i] for i in best_path_indices]

final_tour_nodes = []
all_traversed_nodes_for_penalty = set()  # 用于“记忆”所有走过的节点

if best_path_node_ids:
    print("6. 已找到最优顺序，正在构建尽量减少回头路的连贯物理路径...")
    for i in range(len(best_path_node_ids) - 1):
        start, end = best_path_node_ids[i], best_path_node_ids[i + 1]

        # 优先使用带惩罚的寻路算法
        path_segment = dijkstra_with_penalty(graph, start, end, all_traversed_nodes_for_penalty, penalty_factor=50.0)

        # 如果带惩罚的算法找不到路，则使用标准的最短路算法作为备用
        if not path_segment:
            print(f"   - 警告：从节点 {start} 到 {end} 无法找到无重复路径。正在尝试标准最短路径...")
            path_segment = dijkstra_for_physical_path(graph, start, end)
            if not path_segment:
                print(f"!!! 严重错误：备用路径查找失败！无法连接从 {start} 到 {end}。")
                final_tour_nodes = []
                break

        # 将新路段的节点（除了最后一个）加入“记忆”中，为下一段寻路做准备
        for node in path_segment[:-1]:
            all_traversed_nodes_for_penalty.add(node)

        # 将路段拼接成最终的总路线
        final_tour_nodes.extend(path_segment if not final_tour_nodes else path_segment[1:])
else:
    print("错误：未能使用PuLP求解器找到最优访问顺序。")

# ===============================================================
# ^ ^ ^ ^ ^ ^ ^ ^ ^ ^ ^  修改结束 ^ ^ ^ ^ ^ ^ ^ ^ ^ ^ ^ ^ ^
# ===============================================================


# =================================================================
#  5. 可视化最终结果 (保持不变)
# =================================================================
if final_tour_nodes:
    # --- 计算总长度和时间 ---
    total_length_mm = 0
    for i in range(len(final_tour_nodes) - 1):
        p1 = nodes_df.loc[final_tour_nodes[i]]
        p2 = nodes_df.loc[final_tour_nodes[i + 1]]
        total_length_mm += np.sqrt((p1['x'] - p2['x']) ** 2 + (p1['y'] - p2['y']) ** 2)

    total_length_meters = total_length_mm / 1000
    walking_speed_mps = 1.1
    tour_time_minutes = (total_length_meters / walking_speed_mps) / 60

    # --- 创建从节点ID到景点顺序的映射 ---
    role_order_map = {}
    role_counter = 1
    for node_id in best_path_node_ids:
        if node_id in role_node_ids_set:
            role_order_map[node_id] = f"景点 {role_counter}"
            role_counter += 1

    # --- 生成文本行程单 ---
    print("\n\n==========================================================")
    print("           *** 游客推荐行程单 ***")
    print("----------------------------------------------------------")

    step_counter = 1
    for node_id in best_path_node_ids:
        if node_id == entrance_node_id:
            step_desc = f"第 {step_counter} 步: 从 [入口] 出发"
        elif node_id == exit_node_id:
            step_desc = f"第 {step_counter} 步: 前往 [出口], 结束行程"
        else:
            step_desc = f"第 {step_counter} 步: 前往 [{role_order_map.get(node_id, '未知景点')}]"
        print(step_desc)
        step_counter += 1

    print("----------------------------------------------------------")
    print(f"-> 路线总长度: {total_length_meters:.2f} 米")
    print(f"-> 预估游览时间: {tour_time_minutes:.2f} 分钟 (按成人正常步行速度计算)")
    print("==========================================================\n")

    print("7. 正在生成游客友好型路线图...")
    fig, ax = plt.subplots(figsize=(22, 22))

    # --- 绘制背景路网 ---
    if VISUALIZATION_LIB_AVAILABLE:
        print("   - 正在绘制灰色背景路网...")
        try:
            road_lines = parse_road_coordinates_for_background(road_df)
            background_graph = build_path_graph_for_background(road_lines)
            if background_graph.number_of_edges() > 0:
                pos = {node: (node[0], node[1]) for node in background_graph.nodes()}
                nx.draw_networkx_edges(background_graph, pos, ax=ax, alpha=0.5, width=1.2, edge_color='gray')
        except Exception as e:
            print(f"   - 绘制精细背景时发生未知错误: {e}")

    # --- 绘制分段推荐路线 ---
    current_path_index = 0
    path_colors = plt.cm.viridis(np.linspace(0, 1, len(best_path_node_ids) - 1))

    for i in range(len(best_path_node_ids) - 1):
        start_node = best_path_node_ids[i]
        end_node = best_path_node_ids[i + 1]

        try:
            start_idx_in_final = final_tour_nodes.index(start_node, current_path_index)
            end_idx_in_final = final_tour_nodes.index(end_node, start_idx_in_final)
        except ValueError:
            print(f"警告: 无法在最终路径中定位从 {start_node} 到 {end_node} 的路径段。跳过此段绘制。")
            continue

        segment_nodes = final_tour_nodes[start_idx_in_final: end_idx_in_final + 1]
        segment_coords = [(nodes_df.loc[nid]['x'], nodes_df.loc[nid]['y']) for nid in segment_nodes]

        ax.plot(*zip(*segment_coords), color=path_colors[i], linewidth=3.0, linestyle='-', zorder=10, alpha=0.8)

        current_path_index = end_idx_in_final

    # --- 绘制出入口 ---
    ax.plot(nodes_df.loc[entrance_node_id]['x'], nodes_df.loc[entrance_node_id]['y'], 'p', color='lime', markersize=22,
            label='入口', zorder=11, markeredgecolor='black', markeredgewidth=1.5)
    ax.plot(nodes_df.loc[exit_node_id]['x'], nodes_df.loc[exit_node_id]['y'], 'h', color='yellow', markersize=22,
            label='出口', zorder=11, markeredgecolor='black', markeredgewidth=1.5)

    # --- 绘制带编号的核心景点 ---
    scores = roles_df['interest_score'].astype(float)
    norm = mcolors.Normalize(vmin=scores.min(), vmax=scores.max())
    cmap = cm.Blues
    sm = plt.cm.ScalarMappable(cmap=cmap, norm=norm)

    for role_node_id, role_label in role_order_map.items():
        role_info_rows = roles_df.loc[roles_df['role_node_id'] == role_node_id]
        if not role_info_rows.empty:
            role_info = role_info_rows.iloc[0]
            score = float(role_info['interest_score'])
            x, y = nodes_df.loc[role_node_id]['x'], nodes_df.loc[role_node_id]['y']
            color = cmap(norm(score))
            marker_size = 35 + norm(score) * 45

            ax.scatter(x, y, marker='*', c=[color], s=marker_size * 10,
                       zorder=12, edgecolors='black', linewidth=1.2)

            ax.text(x, y - 2500, role_label, ha='center', va='top', fontsize=12,
                    color='black', fontweight='bold',
                    bbox=dict(boxstyle='round,pad=0.3', fc='ivory', ec='none', alpha=0.85))

    # --- 添加颜色条 ---
    cbar = plt.colorbar(sm, ax=ax, shrink=0.6, orientation='vertical')
    cbar.set_label('异景程度 (趣味性评分)', rotation=270, labelpad=25, fontsize=16)

    # --- 设置图表样式 ---
    ax.set_aspect('equal', adjustable='box')
    ax.set_title(
        f"拙政园游览路线图",
        fontsize=24, pad=20)
    ax.set_xlabel("X 坐标 (mm)", fontsize=16)
    ax.set_ylabel("Y 坐标 (mm)", fontsize=16)
    ax.tick_params(axis='both', which='major', labelsize=12)
    ax.grid(False)

    # --- 创建自定义图例 ---
    from matplotlib.lines import Line2D

    legend_elements = [
        Line2D([0], [0], color='lime', marker='p', linestyle='None', markersize=12, label='入口', markeredgecolor='k'),
        Line2D([0], [0], color='yellow', marker='h', linestyle='None', markersize=12, label='出口',
               markeredgecolor='k'),
        Line2D([0], [0], marker='*', color='w', label='核心景点', markerfacecolor='cornflowerblue', markeredgecolor='k',
               markersize=15),
        Line2D([0], [0], color='#37939A', lw=3, label='分段路线 (按彩虹色顺序)')
    ]
    ax.legend(handles=legend_elements, prop={'size': 15}, loc='best')

    # --- 保存和显示 ---
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_filename = f"garden_tour_guide_optimized_{timestamp}.png"
    plt.savefig(output_filename, dpi=300, bbox_inches='tight')
    print(f"\n--- 任务完成！已生成优化后的路线图 '{output_filename}' ---")
    plt.show()

else:
    print("\n由于未能构建完整的路径，程序已结束，未生成路线图。")