import pandas as pd
import re
import numpy as np
import os
import heapq
import pulp as pl
from collections import deque
from scipy.spatial.distance import cdist

# 确保关键库已安装
try:
    from shapely.geometry import Point, LineString
    import networkx as nx
    import matplotlib.pyplot as plt
except ImportError:
    print("关键库未安装，请在您的环境中运行: pip install pandas numpy shapely networkx matplotlib pulp")
    exit()


# --- 1. 数据解析与图构建 (采用您的新方法) ---

def parse_road_coordinates(df):
    """
    解析从Excel读取的道路坐标DataFrame，提取所有LineString对象。
    单位从毫米(mm)转换为厘米(cm)。
    """
    coord_series = df.iloc[:, 0].dropna()
    segments = []
    current_segment_points = []
    coord_pattern = re.compile(r'\d+\.\s*\{([\d.-]+),\s*([\d.-]+),\s*([\d.-]+)\}')
    segment_separator_pattern = re.compile(r'\{0;\s*\d+\}')
    for item in coord_series:
        item_str = str(item)
        if segment_separator_pattern.match(item_str):
            if len(current_segment_points) > 1:
                segments.append(LineString(current_segment_points))
            current_segment_points = []
        else:
            match = coord_pattern.search(item_str)
            if match:
                x, y, z = map(float, match.groups())
                current_segment_points.append((x / 10, y / 10))  # mm to cm
    if len(current_segment_points) > 1:
        segments.append(LineString(current_segment_points))
    return segments


def build_path_graph(lines):
    """
    根据LineString列表构建一个NetworkX图，并返回节点位置字典。
    """
    G = nx.Graph()
    all_segments = []
    for line in lines:
        coords = list(line.coords)
        for i in range(len(coords) - 1):
            segment = LineString([coords[i], coords[i + 1]])
            all_segments.append(segment)

    node_coords_set = set()
    for seg in all_segments:
        node_coords_set.add(seg.coords[0])
        node_coords_set.add(seg.coords[1])

    for i in range(len(all_segments)):
        for j in range(i + 1, len(all_segments)):
            if all_segments[i].intersects(all_segments[j]):
                intersection = all_segments[i].intersection(all_segments[j])
                if isinstance(intersection, Point):
                    node_coords_set.add(intersection.coords[0])
                # 如果有多点相交，也可以加入
                elif intersection.geom_type.startswith('Multi') or intersection.geom_type == 'GeometryCollection':
                    for geom in intersection.geoms:
                        if isinstance(geom, Point):
                            node_coords_set.add(geom.coords[0])

    node_list = list(node_coords_set)
    pos_dict = {node: node for node in node_list}

    for line in lines:
        nodes_on_line = []
        for node in node_list:
            # 使用一个小的缓冲来判断点是否在线上
            if line.distance(Point(node)) < 1e-9:
                nodes_on_line.append((line.project(Point(node)), node))

        nodes_on_line.sort()

        for k in range(len(nodes_on_line) - 1):
            start_node, end_node = nodes_on_line[k][1], nodes_on_line[k + 1][1]
            if start_node != end_node:
                distance = Point(start_node).distance(Point(end_node))
                if not G.has_edge(start_node, end_node):
                    G.add_edge(start_node, end_node, length=distance, interest=0.0)

    return G, pos_dict


def map_interest_scores_to_graph(G, interest_df, pos_dict):
    """将趣味性评分映射到图的边上"""
    interest_pattern = re.compile(r'\(([-\d\.]+),\s*([-\d\.]+)\)')
    for _, row in interest_df.iterrows():
        start_match = interest_pattern.search(str(row['Start_Point']))
        end_match = interest_pattern.search(str(row['End_Point']))

        if start_match and end_match:
            # mm to cm
            start_coord = (float(start_match.group(1)) / 10, float(start_match.group(2)) / 10)
            end_coord = (float(end_match.group(1)) / 10, float(end_match.group(2)) / 10)
            interest_score = float(row['Interest_Score'])

            interest_midpoint = Point((start_coord[0] + end_coord[0]) / 2, (start_coord[1] + end_coord[1]) / 2)

            # 找到图中距离最近的边
            min_dist = float('inf')
            best_edge = None
            for u, v in G.edges():
                edge_midpoint = Point((u[0] + v[0]) / 2, (u[1] + v[1]) / 2)
                dist = interest_midpoint.distance(edge_midpoint)
                if dist < min_dist:
                    min_dist = dist
                    best_edge = (u, v)

            if best_edge:
                G.edges[best_edge]['interest'] = max(G.edges[best_edge]['interest'], interest_score)
    return G


# --- 2. E-CARGO 路径规划 ---

def select_roles(G, num_roles=8, min_dist=3000):  # min_dist in cm
    """在图中选择核心景点(角色)"""
    interest_edges = [(u, v, data) for u, v, data in G.edges(data=True) if data['interest'] > 0]
    interest_edges.sort(key=lambda x: x[2]['interest'], reverse=True)

    selected_roles = []
    for u, v, data in interest_edges:
        if len(selected_roles) >= num_roles:
            break

        # 使用边的中点作为景点的代表位置
        role_pos = Point((u[0] + v[0]) / 2, (u[1] + v[1]) / 2)
        is_far_enough = True
        for sel_role_pos, _ in selected_roles:
            if role_pos.distance(sel_role_pos) * 10 < min_dist:  # convert back to mm for comparison
                is_far_enough = False
                break

        if is_far_enough:
            # 角色节点选为边的端点之一
            selected_roles.append((role_pos, u))

    if not selected_roles:
        return []
    return [role_node for _, role_node in selected_roles]


def get_path_attributes(G, path):
    """计算一条路径的总长度和总趣味性"""
    total_length = 0
    total_interest = 0
    for i in range(len(path) - 1):
        u, v = path[i], path[i + 1]
        edge_data = G.get_edge_data(u, v)
        if edge_data:
            total_length += edge_data.get('length', 0)
            total_interest += edge_data.get('interest', 0)
    return total_length, total_interest


def solve_tsp_with_pulp(cost_matrix, start_idx, end_idx):
    # (此函数与之前相同，无需修改)
    num_nodes = len(cost_matrix)
    prob = pl.LpProblem("Garden_Tour_TSP", pl.LpMinimize)
    x = pl.LpVariable.dicts("x", ((i, j) for i in range(num_nodes) for j in range(num_nodes)), cat='Binary')
    prob += pl.lpSum(cost_matrix[i][j] * x[i, j] for i in range(num_nodes) for j in range(num_nodes))
    for i in range(num_nodes):
        if i != end_idx: prob += pl.lpSum(x[i, j] for j in range(num_nodes) if i != j) == 1
        if i != start_idx: prob += pl.lpSum(x[j, i] for j in range(num_nodes) if i != j) == 1
    u = pl.LpVariable.dicts('u', range(num_nodes), lowBound=1, upBound=num_nodes - 1, cat='Continuous')
    for i in range(num_nodes):
        for j in range(num_nodes):
            if i != j and (i != start_idx and j != start_idx):
                prob += u[i] - u[j] + (num_nodes - 1) * x[i, j] <= num_nodes - 2
    prob.solve(pl.PULP_CBC_CMD(msg=0))
    if prob.status == pl.LpStatusOptimal:
        path, curr = [start_idx], start_idx
        while len(path) < num_nodes:
            found_next = False
            for j in range(num_nodes):
                if j != curr and x[curr, j].varValue == 1:
                    path.append(j);
                    curr = j;
                    found_next = True;
                    break
            if not found_next: break  # Should be when curr == end_idx
        return path
    return None


# --- 主程序 ---
if __name__ == '__main__':
    # --- 文件路径定义 ---
    DATA_DIRECTORY = r'H:\OneDrive\Desktop\F题\赛题F江南古典园林美学特征建模附件资料\1. 拙政园'
    ROAD_FILE = '4-拙政园数据坐标.xlsx'
    INTEREST_FILE = r'H:\OneDrive\Desktop\final_scoring_matrix_v4_final.csv'

    try:
        # 1. 加载和解析数据，构建精确的图
        print("1. 正在加载和解析数据...")
        road_df = pd.read_excel(os.path.join(DATA_DIRECTORY, ROAD_FILE), sheet_name='道路')
        interest_df = pd.read_csv(INTEREST_FILE)

        road_lines = parse_road_coordinates(road_df)
        path_graph, pos_dict = build_path_graph(road_lines)
        path_graph = map_interest_scores_to_graph(path_graph, interest_df, pos_dict)
        print(f"图构建完成: {path_graph.number_of_nodes()} 个节点, {path_graph.number_of_edges()} 条边。")

        # 2. 选择角色(核心景点)
        print("\n2. 正在选择核心景点 (角色)...")
        role_nodes = select_roles(path_graph)
        if not role_nodes: raise ValueError("未能选择任何核心景点。")
        print(f"已选择 {len(role_nodes)} 个角色。")

        # 3. 定义出入口
        all_nodes = np.array(list(path_graph.nodes()))
        min_x, max_x = all_nodes[:, 0].min(), all_nodes[:, 0].max()
        min_y, max_y = all_nodes[:, 1].min(), all_nodes[:, 1].max()

        # 使用 scipy.spatial.distance.cdist 找到最近点
        entrance_target = np.array([[max_x * 0.9 + min_x * 0.1, min_y]])
        entrance_node_idx = cdist(entrance_target, all_nodes).argmin()
        entrance_node = tuple(all_nodes[entrance_node_idx])

        exit_target = np.array([[min_x * 0.9 + max_x * 0.1, min_y]])
        exit_node_idx = cdist(exit_target, all_nodes).argmin()
        exit_node = tuple(all_nodes[exit_node_idx])

        # 4. 计算综合成本矩阵 Q
        print("\n3. 正在计算综合成本矩阵 Q...")
        critical_nodes = sorted(list(set([entrance_node] + role_nodes + [exit_node])), key=lambda x: (x[0], x[1]))
        node_map = {node: i for i, node in enumerate(critical_nodes)}
        n_crit = len(critical_nodes)

        dist_matrix = np.full((n_crit, n_crit), 0.0)
        interest_matrix = np.full((n_crit, n_crit), 0.0)

        for i, u in enumerate(critical_nodes):
            for j, v in enumerate(critical_nodes):
                if i == j: continue
                try:
                    path = nx.dijkstra_path(path_graph, source=u, target=v, weight='length')
                    length, interest = get_path_attributes(path_graph, path)
                    dist_matrix[i, j] = length
                    interest_matrix[i, j] = interest
                except nx.NetworkXNoPath:
                    dist_matrix[i, j] = float('inf')

        if np.any(np.isinf(dist_matrix)):
            max_finite_dist = np.max(dist_matrix[np.isfinite(dist_matrix)])
            dist_matrix[np.isinf(dist_matrix)] = max_finite_dist * 10


        def normalize(matrix):
            min_val, max_val = matrix.min(), matrix.max()
            return (matrix - min_val) / (max_val - min_val) if max_val > min_val else matrix


        q_matrix = 0.5 * normalize(dist_matrix) - 0.5 * normalize(interest_matrix)

        # 5. 求解 TSP
        print("\n4. 正在求解最优访问顺序 (TSP)...")
        start_idx = node_map[entrance_node]
        end_idx = node_map[exit_node]
        best_path_indices = solve_tsp_with_pulp(q_matrix, start_idx, end_idx)

        # 6. 构建最终路径
        final_tour_nodes = []
        if best_path_indices:
            print("\n5. 正在构建最终连贯路径...")
            ordered_nodes = [critical_nodes[i] for i in best_path_indices]
            for i in range(len(ordered_nodes) - 1):
                u, v = ordered_nodes[i], ordered_nodes[i + 1]
                path_segment = nx.dijkstra_path(path_graph, source=u, target=v, weight='length')
                final_tour_nodes.extend(path_segment if i == 0 else path_segment[1:])
        else:
            raise ValueError("TSP求解失败，无法生成路径。")

        # 7. 可视化
        print("\n6. 正在生成最终路线图...")
        plt.rcParams['font.sans-serif'] = ['SimHei']
        plt.rcParams['axes.unicode_minus'] = False
        plt.figure(figsize=(16, 16))

        # 绘制简洁的灰色背景图
        nx.draw_networkx_edges(path_graph, pos_dict, alpha=0.5, width=1.5, edge_color='gray')

        # 绘制推荐路线
        path_x = [node[0] for node in final_tour_nodes]
        path_y = [node[1] for node in final_tour_nodes]
        plt.plot(path_x, path_y, color='red', linewidth=3.5, zorder=10, label='推荐路线')

        # 绘制标记点
        plt.scatter(*zip(*[entrance_node]), c='lime', s=400, marker='p', label='入口', zorder=11, edgecolors='black')
        plt.scatter(*zip(*[exit_node]), c='cyan', s=400, marker='h', label='出口', zorder=11, edgecolors='black')
        plt.scatter(*zip(*role_nodes), c='gold', s=500, marker='*', label='核心景点(角色)', zorder=12,
                    edgecolors='black')

        plt.title("拙政园 E-CARGO 游览路线", fontsize=30)
        plt.xlabel("X坐标 (厘米)", fontsize=28)
        plt.ylabel("Y坐标 (厘米)", fontsize=28)
        plt.legend(fontsize=20)
        plt.axis('equal')
        plt.grid(True)

        output_filename = 'zhuozhengyuan_tour_route_final.png'
        plt.savefig(output_filename, dpi=300, bbox_inches='tight')
        print(f"任务完成！图已保存至: {output_filename}")
        plt.show()

    except Exception as e:
        print(f"\n程序发生严重错误: {e}")