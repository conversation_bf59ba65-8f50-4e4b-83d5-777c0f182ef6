import pandas as pd
import re
import os
import random
import matplotlib.pyplot as plt
import networkx as nx
import numpy as np
from scipy.spatial import KDTree
from matplotlib.lines import Line2D
import matplotlib.cm as cm
import matplotlib.colors as mcolors
from itertools import combinations

# --- 1. 配置 ---
try:
    # 设置支持中文的字体
    plt.rcParams['font.sans-serif'] = ['SimHei']
    plt.rcParams['axes.unicode_minus'] = False
except Exception:
    print("警告：未找到'SimHei'字体，图中的中文可能无法正常显示。")

# --- 2. 文件路径设置 ---
# 使用您提供的绝对路径来读取Excel文件
EXCEL_FILE_PATH = r'H:\OneDrive\Desktop\个园-处理.xlsx'

# 输出图片将保存在与Excel文件相同的目录下
OUTPUT_DIRECTORY = os.path.dirname(EXCEL_FILE_PATH)
OUTPUT_IMAGE_PATH = os.path.join(OUTPUT_DIRECTORY, 'Ge_Garden_Random_Tour_Annotated.png')


# --- 3. 数据解析函数 ---
def parse_coordinates(coord_series):
    """解析坐标字符串。"""
    coords = []
    for s in coord_series.dropna():
        if isinstance(s, str):
            nums = re.findall(r"[-+]?\d*\.\d+|\d+", s)
            if len(nums) >= 2:
                coords.append((float(nums[0]), float(nums[1])))
    return coords


def load_data_from_excel(filepath, sheet_name):
    """从指定的Excel文件和工作表中加载数据。"""
    if not os.path.exists(filepath):
        print(f"严重错误：无法在指定路径找到Excel文件: {filepath}")
        return None
    try:
        df = pd.read_excel(filepath, sheet_name=sheet_name)

        coord_col_name = None
        potential_cols = [col for col in df.columns if 'coord' in str(col).lower()]
        if potential_cols:
            coord_col_name = potential_cols[0]
        elif len(df.columns) > 1 and 'Unnamed' in str(df.columns[0]):
            coord_col_name = df.columns[1]
        elif len(df.columns) > 0:
            coord_col_name = df.columns[0]
        else:
            return []

        print(f"成功读取工作表'{sheet_name}'...")
        coords = sorted(list(set(parse_coordinates(df[coord_col_name]))))
        return coords

    except Exception as e:
        print(f"读取工作表 '{sheet_name}' 时发生错误: {e}")
        return None


# --- 4. 图构建函数 ---
def build_graph_from_points(points, distance_threshold):
    G = nx.Graph()
    points_array = np.array(points)
    kdtree = KDTree(points_array)

    for i, point in enumerate(points):
        G.add_node(point, pos=point)

    for i, point in enumerate(points):
        indices = kdtree.query_ball_point(points_array[i], distance_threshold)
        for j in indices:
            if i < j:
                dist = np.linalg.norm(points_array[i] - points_array[j])
                G.add_edge(points[i], points[j], weight=dist)
    return G


def connect_disconnected_components(G):
    components = list(nx.connected_components(G))
    if len(components) <= 1:
        print("路网图已经完全连通，无需桥接。")
        return G

    print(f"路网包含 {len(components)} 个不连通的部分，正在尝试连接它们...")

    all_component_nodes = list(components)

    while len(all_component_nodes) > 1:
        min_dist = float('inf')
        bridge_nodes = None, None

        for comp1_nodes, comp2_nodes in combinations(all_component_nodes, 2):
            pos1 = np.array([G.nodes[n]['pos'] for n in comp1_nodes])
            pos2 = np.array([G.nodes[n]['pos'] for n in comp2_nodes])

            kdtree = KDTree(pos1)
            dist, idx = kdtree.query(pos2)

            if dist.min() < min_dist:
                min_dist = dist.min()
                node_from_comp2 = list(comp2_nodes)[np.argmin(dist)]
                node_from_comp1 = list(comp1_nodes)[idx[np.argmin(dist)]]
                bridge_nodes = (node_from_comp1, node_from_comp2)

        if bridge_nodes[0] is not None:
            node1, node2 = bridge_nodes
            G.add_edge(node1, node2, weight=min_dist)

            comp1_to_merge, comp2_to_merge = None, None

            for comp in all_component_nodes:
                if node1 in comp: comp1_to_merge = comp
                if node2 in comp: comp2_to_merge = comp

            if comp1_to_merge and comp2_to_merge and comp1_to_merge != comp2_to_merge:
                merged_component = comp1_to_merge.union(comp2_to_merge)
                all_component_nodes.remove(comp1_to_merge)
                all_component_nodes.remove(comp2_to_merge)
                all_component_nodes.append(merged_component)
        else:
            break

    print("所有不连通部分已连接。")
    return G


def find_nearest_node(graph, point):
    nodes = np.array(list(nx.get_node_attributes(graph, 'pos').values()))
    kdtree = KDTree(nodes)
    _, idx = kdtree.query(point)
    return tuple(nodes[idx])


# --- 5. 可视化函数 (反转路线颜色) ---
def plot_garden_path_zhuozheng_style(road_graph, full_path, attractions_with_levels, start_node, end_node, layers):
    fig, ax = plt.subplots(figsize=(20, 25))
    ax.set_facecolor('#f0f0f0')

    pos = nx.get_node_attributes(road_graph, 'pos')

    if 'buildings' in layers and layers['buildings']:
        ax.plot(*zip(*layers['buildings']), 's', color='#8B4513', markersize=8)
    if 'rockeries' in layers and layers['rockeries']:
        ax.plot(*zip(*layers['rockeries']), '^', color='#696969', markersize=10)

    nx.draw_networkx_edges(road_graph, pos, ax=ax, edge_color='gray', width=1.0, alpha=0.5)

    if full_path:
        path_edges = list(zip(full_path[:-1], full_path[1:]))
        # --- 关键修改：反转颜色列表，使起点为紫色 ---
        path_cmap = mcolors.LinearSegmentedColormap.from_list("path_grad", ["purple", "cyan"])
        edge_colors = np.linspace(0, 1, len(path_edges))
        nx.draw_networkx_edges(road_graph, pos, edgelist=path_edges, width=3.5,
                               edge_color=edge_colors, edge_cmap=path_cmap, ax=ax)

    if attractions_with_levels:
        max_level = max(attr['level'] for attr in attractions_with_levels)
        min_level = min(attr['level'] for attr in attractions_with_levels)
        norm = plt.Normalize(vmin=min_level, vmax=max_level + 1)
        cmap = plt.cm.get_cmap('Blues')

        for attr in attractions_with_levels:
            node, level, label = attr['node'], attr['level'], attr['label']
            node_pos = pos[node]
            color = cmap(norm(level) * 0.6 + 0.4)

            ax.plot(node_pos[0], node_pos[1], '*', color=color, markersize=20, markeredgecolor='black')

            ax.text(node_pos[0] + 1, node_pos[1] + 1, label, fontsize=12, color='black',
                    fontweight='bold', ha='left', va='bottom',
                    bbox=dict(boxstyle='round,pad=0.2', fc='white', alpha=0.7))

    if start_node in pos:
        ax.plot(pos[start_node][0], pos[start_node][1], 'p', color='lime',
                markersize=15, markeredgecolor='black')
    if end_node in pos:
        ax.plot(pos[end_node][0], pos[end_node][1], 'h', color='yellow',
                markersize=15, markeredgecolor='black')

    legend_elements = [
        Line2D([0], [0], marker='s', color='w', label='实体建筑', markerfacecolor='#8B4513', markersize=12),
        Line2D([0], [0], marker='^', color='w', label='假山', markerfacecolor='#696969', markersize=12),
        Line2D([0], [0], color='gray', linewidth=1, alpha=0.5, label='道路网络'),
        Line2D([0], [0], color=path_cmap(0.5), linewidth=3.5, label='推荐路线 '),
        Line2D([0], [0], marker='p', color='lime', markersize=15, markeredgecolor='black', linestyle='None',
               label='入口'),
        Line2D([0], [0], marker='h', color='yellow', markersize=15, markeredgecolor='black', linestyle='None',
               label='出口'),
        Line2D([0], [0], marker='*', color=plt.cm.get_cmap('Blues')(0.7), markersize=15, markeredgecolor='black',
               linestyle='None', label='核心景点')
    ]
    ax.legend(handles=legend_elements, loc='upper left', fontsize=14, framealpha=0.9, fancybox=True, shadow=True)

    ax.set_title("个园游览路线图", fontsize=35, pad=20)
    ax.set_xlabel("X 坐标", fontsize=25)
    ax.set_ylabel("Y 坐标", fontsize=25)
    ax.tick_params(axis='both', which='major', labelsize=15)
    ax.set_aspect('equal', adjustable='box')
    plt.grid(True, linestyle='--', alpha=0.6)

    plt.savefig(OUTPUT_IMAGE_PATH, dpi=300, bbox_inches='tight')
    print(f"游览路径图已成功保存至: {OUTPUT_IMAGE_PATH}")


# --- 6. 主程序执行 ---
if __name__ == '__main__':
    print("--- 开始生成个园随机游览路线 ---")

    road_points = load_data_from_excel(EXCEL_FILE_PATH, sheet_name='道路')
    if road_points is None:
        raise ValueError("道路数据加载失败，程序终止。")

    building_points = load_data_from_excel(EXCEL_FILE_PATH, sheet_name='实体建筑')
    rockery_points = load_data_from_excel(EXCEL_FILE_PATH, sheet_name='假山')

    print("正在创建和连接路网图...")
    road_graph = build_graph_from_points(road_points, distance_threshold=24.0)
    road_graph = connect_disconnected_components(road_graph)

    if road_graph.number_of_nodes() == 0:
        raise ValueError("路网图为空，程序终止。")

    road_nodes_arr = np.array(list(road_graph.nodes()))
    start_node = tuple(road_nodes_arr[np.argmin(road_nodes_arr[:, 1])])
    end_node = tuple(road_nodes_arr[np.argmax(road_nodes_arr[:, 1])])

    print("正在随机选择8个核心景点...")
    potential_attractions = (building_points or []) + (rockery_points or [])
    if len(potential_attractions) < 8:
        raise ValueError(f"可用景点总数({len(potential_attractions)})少于8个，无法完成随机选择。")

    random_attractions_points = random.sample(potential_attractions, 8)

    core_attraction_nodes = {find_nearest_node(road_graph, p) for p in random_attractions_points}
    if start_node in core_attraction_nodes: core_attraction_nodes.remove(start_node)
    if end_node in core_attraction_nodes: core_attraction_nodes.remove(end_node)

    attractions_with_levels = []
    for i, node in enumerate(core_attraction_nodes):
        attractions_with_levels.append({
            'node': node,
            'level': random.randint(1, 5),
            'label': f'景点{i + 1}'
        })

    print(f"已随机选择 {len(attractions_with_levels)} 个核心景点并分配等级。")

    print("正在计算游览路径...")
    final_ordered_nodes = [start_node]
    unvisited_attractions = {attr['node'] for attr in attractions_with_levels}
    current_node = start_node

    while unvisited_attractions:
        next_node = min(unvisited_attractions,
                        key=lambda p: nx.shortest_path_length(road_graph, source=current_node, target=p,
                                                              weight='weight'))
        final_ordered_nodes.append(next_node)
        unvisited_attractions.remove(next_node)
        current_node = next_node

    final_ordered_nodes.append(end_node)

    full_path = []
    for i in range(len(final_ordered_nodes) - 1):
        source, target = final_ordered_nodes[i], final_ordered_nodes[i + 1]
        try:
            path_segment = nx.shortest_path(road_graph, source=source, target=target, weight='weight')
            full_path.extend(path_segment if i == 0 else path_segment[1:])
        except nx.NetworkXNoPath:
            print(f"严重警告：在 {source} 和 {target} 之间依然未找到路径，请检查数据。")
            continue

    print("路径计算完成。")

    total_length_mm = 0
    for i in range(len(full_path) - 1):
        point1 = np.array(full_path[i])
        point2 = np.array(full_path[i + 1])
        total_length_mm += np.linalg.norm(point1 - point2)

    total_length_m = total_length_mm / 1000

    walking_speed_mps = 1.2
    total_time_seconds = total_length_m / walking_speed_mps
    total_time_minutes = total_time_seconds / 60

    print("\n--- 游览信息 ---")
    print(f"推荐游览路线总长度：{total_length_m:.2f} 米")
    print(f"推荐游览时间 (按1.2m/s速度)：{total_time_minutes:.2f} 分钟")
    print("------------------\n")

    print("正在生成图像...")
    plot_garden_path_zhuozheng_style(
        road_graph, full_path, attractions_with_levels, start_node, end_node,
        {'buildings': building_points, 'rockeries': rockery_points}
    )

    print("--- 所有任务已完成 ---")