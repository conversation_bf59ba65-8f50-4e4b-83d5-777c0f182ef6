import pandas as pd
import re
import matplotlib.pyplot as plt
import networkx as nx
from shapely.geometry import Point, LineString
import numpy as np
import heapq
import pulp as pl
from collections import deque
from datetime import datetime
import matplotlib.cm as cm
import matplotlib.colors as mcolors
from scipy.spatial.distance import cdist

# --- 配置 ---
# 确保已安装所需库: pip install pandas openpyxl matplotlib networkx shapely scipy pulp
try:
    plt.rcParams['font.sans-serif'] = ['SimHei']
    plt.rcParams['axes.unicode_minus'] = False
except Exception:
    print("警告：未找到'SimHei'字体，图例可能显示不正常。")

# =================================================================
#  1. 文件路径设置 (已根据您的反馈最终修正)
# =================================================================
# 趣味性评分文件
INTEREST_SCORE_EXCEL_PATH = r'H:\OneDrive\Desktop\十个园林趣味性评分\拙政园_path_segments_analysis.xlsx'

# 包含所有坐标图层的工作簿 (Excel文件)
# !!! 请务必将此路径修改为您存放该文件的真实路径 !!!
COORD_EXCEL_PATH = r'H:\OneDrive\Desktop\F题\赛题F江南古典园林美学特征建模附件资料\1. 拙政园\4-拙政园数据坐标.xlsx'


# =================================================================
#  2. 数据解析与绘图函数 (已修正)
# =================================================================
def parse_coords_from_worksheet(file_path, sheet_name):
    """从Excel文件的指定工作表中解析坐标数据"""
    try:
        df = pd.read_excel(file_path, sheet_name=sheet_name, header=0)  # 第一行是表头
    except Exception as e:
        print(f"错误: 无法从文件 '{file_path}' 的工作表 '{sheet_name}' 读取数据。错误信息: {e}")
        return []

    segments = []
    current_segment = []
    coord_pattern = re.compile(r'\{([-\d\.]+),\s*([-\d\.]+),\s*([-\d\.]+)\}')

    # 假设坐标数据在第一列
    if df.empty or len(df.columns) == 0:
        print(f"警告: 工作表 '{sheet_name}' 为空或没有列。")
        return []

    for item in df.iloc[:, 0].dropna():
        item_str = str(item)
        if re.match(r'\{0;\d+\}', item_str):
            if current_segment:
                segments.append(current_segment)
                current_segment = []
        else:
            points = coord_pattern.findall(item_str)
            for p in points:
                current_segment.append((float(p[0]), float(p[1])))

    if current_segment:
        segments.append(current_segment)

    return segments


def plot_layer(ax, segments, color, linewidth, linestyle, zorder, label):
    is_first = True
    for segment in segments:
        if len(segment) > 1:
            x, y = zip(*segment)
            if is_first:
                ax.plot(x, y, color=color, linewidth=linewidth, linestyle=linestyle, zorder=zorder, label=label)
                is_first = False
            else:
                ax.plot(x, y, color=color, linewidth=linewidth, linestyle=linestyle, zorder=zorder)


def build_graph_from_segments(road_segments):
    G = nx.Graph()
    for segment in road_segments:
        if len(segment) > 1:
            for i in range(len(segment) - 1):
                p1, p2 = segment[i], segment[i + 1]
                p1_node = (round(p1[0], 4), round(p1[1], 4))
                p2_node = (round(p2[0], 4), round(p2[1], 4))
                distance = Point(p1).distance(Point(p2))
                G.add_edge(p1_node, p2_node, weight=distance, interest=0.0)
    return G


def map_interest_to_graph(interest_df, G):
    node_list = list(G.nodes)
    if not node_list: return G

    for _, row in interest_df.iterrows():
        start_coords_list = re.findall(r'\(([-\d\.]+),\s*([-\d\.]+)\)', str(row['Start_Point']))
        end_coords_list = re.findall(r'\(([-\d\.]+),\s*([-\d\.]+)\)', str(row['End_Point']))

        if start_coords_list and end_coords_list:
            start_coord = (float(start_coords_list[0][0]), float(start_coords_list[0][1]))
            end_coord = (float(end_coords_list[0][0]), float(end_coords_list[0][1]))
            interest_mid_point = Point((start_coord[0] + end_coord[0]) / 2, (start_coord[1] + end_coord[1]) / 2)

            min_dist = float('inf')
            best_edge = None

            for u, v in G.edges():
                edge_line = LineString([u, v])
                dist = edge_line.centroid.distance(interest_mid_point)
                if dist < min_dist:
                    min_dist = dist
                    best_edge = (u, v)

            if best_edge:
                G.edges[best_edge]['interest'] += float(row['Interest_Score'])
    return G


def find_closest_node(graph, coord):
    nodes = np.array(list(graph.nodes))
    target_coord = np.array([coord])
    dist_sq = np.sum((nodes - target_coord) ** 2, axis=1)
    return tuple(nodes[np.argmin(dist_sq)])


def dijkstra_for_q_matrix(graph, start_node):
    dists = {node: [float('inf'), 0] for node in graph.nodes}
    if start_node not in graph: return None

    path_lengths = nx.shortest_path_length(graph, source=start_node, weight='weight')

    for end_node, length in path_lengths.items():
        path = nx.shortest_path(graph, source=start_node, target=end_node, weight='weight')
        interest = 0
        for i in range(len(path) - 1):
            interest += graph.edges[path[i], path[i + 1]]['interest']
        dists[end_node] = [length, interest]

    return dists


def solve_tsp_with_pulp(cost_matrix, start_idx, end_idx):
    num_nodes = len(cost_matrix)
    prob = pl.LpProblem("Garden_Tour_TSP", pl.LpMinimize)
    x = pl.LpVariable.dicts("x", ((i, j) for i in range(num_nodes) for j in range(num_nodes) if i != j), cat='Binary')
    prob += pl.lpSum(cost_matrix[i][j] * x.get((i, j), 0) for i in range(num_nodes) for j in range(num_nodes) if i != j)
    for i in range(num_nodes):
        if i != end_idx: prob += pl.lpSum(x.get((i, j), 0) for j in range(num_nodes) if i != j) == 1
        if i != start_idx: prob += pl.lpSum(x.get((j, i), 0) for j in range(num_nodes) if i != j) == 1
    u = pl.LpVariable.dicts('u', range(num_nodes), lowBound=1, upBound=num_nodes - 1, cat='Continuous')
    for i in range(num_nodes):
        for j in range(num_nodes):
            if i != j and i != start_idx and j != start_idx:
                prob += u[i] - u[j] + (num_nodes - 1) * x.get((i, j), 0) <= num_nodes - 2
    prob.solve(pl.PULP_CBC_CMD(msg=0))
    if prob.status == pl.LpStatusOptimal:
        path, curr = [start_idx], start_idx
        while curr != end_idx and len(path) <= num_nodes:
            found_next = False
            for j in range(num_nodes):
                if (curr, j) in x and x[(curr, j)].varValue > 0.9:
                    path.append(j);
                    curr = j;
                    found_next = True;
                    break
            if not found_next: break
        return path
    return None


def dijkstra_with_penalty_nx(graph, start_node, end_node, traversed_nodes, penalty_factor=50.0):
    def weight_func(u, v, d):
        cost = d['weight']
        if v in traversed_nodes: cost *= penalty_factor
        return cost

    try:
        return nx.shortest_path(graph, source=start_node, target=end_node, weight=weight_func)
    except nx.NetworkXNoPath:
        try:
            return nx.shortest_path(graph, source=start_node, target=end_node, weight='weight')
        except nx.NetworkXNoPath:
            return []


# =================================================================
#  4. 主执行流程 (已修正)
# =================================================================
print("--- 开始生成留园高精度游览路线 ---")

# --- 1. 加载所有数据 ---
print("1. 正在加载所有坐标图层和趣味性评分...")
try:
    interest_scores_df = pd.read_excel(INTEREST_SCORE_EXCEL_PATH, sheet_name='分段结果')

    # 修正: 从同一个Excel文件的不同工作表读取数据
    road_segments = parse_coords_from_worksheet(COORD_EXCEL_PATH, sheet_name='道路')
    water_segments = parse_coords_from_worksheet(COORD_EXCEL_PATH, sheet_name='水体')
    rockery_segments = parse_coords_from_worksheet(COORD_EXCEL_PATH, sheet_name='假山')
    building_solid_segments = parse_coords_from_worksheet(COORD_EXCEL_PATH, sheet_name='实体建筑')
    building_semi_segments = parse_coords_from_worksheet(COORD_EXCEL_PATH, sheet_name='半开放建筑')
    print("所有数据加载成功！")
except FileNotFoundError as e:
    print(f"错误：找不到文件 {e.filename}。请检查文件路径是否正确。")
    exit()
except Exception as e:
    print(f"加载数据时发生未知错误: {e}")
    exit()

# --- 2. 构建路网图并映射趣味性 ---
print("2. 正在根据道路数据构建精确路网图...")
G = build_graph_from_segments(road_segments)
if G.number_of_edges() == 0:
    print("错误：未能从道路数据构建有效的路网图。请检查'道路'工作表的内容。")
    exit()
print(f"路网构建完成，包含 {G.number_of_nodes()} 个节点和 {G.number_of_edges()} 条边。")
print("   - 正在将趣味性评分映射到路网...")
G = map_interest_to_graph(interest_scores_df, G)

# --- 3. 筛选核心景点 ---
print("3. 正在筛选核心景点...")
NUM_ROLES = 8
MIN_ROLE_DISTANCE = 30000
interest_edges = [(u, v, d) for u, v, d in G.edges(data=True) if d['interest'] > 0]
sorted_interest_edges = sorted(interest_edges, key=lambda x: x[2]['interest'], reverse=True)

selected_roles_nodes = []
for u, v, data in sorted_interest_edges:
    if len(selected_roles_nodes) >= NUM_ROLES: break
    mid_point = Point((u[0] + v[0]) / 2, (u[1] + v[1]) / 2)
    is_far_enough = all(mid_point.distance(Point(sr[1])) >= MIN_ROLE_DISTANCE for sr in selected_roles_nodes)
    if is_far_enough:
        role_node = u if np.random.rand() > 0.5 else v
        selected_roles_nodes.append((role_node, mid_point, data['interest']))

if not selected_roles_nodes:
    print("错误：未能筛选出任何核心景点，请检查趣味性评分数据或调整MIN_ROLE_DISTANCE。")
    exit()
role_node_ids = [r[0] for r in selected_roles_nodes]
print(f"已筛选出 {len(role_node_ids)} 个核心景点。")

# --- 4. 定义出入口 ---
print("4. 正在定义出入口...")
all_nodes = np.array(list(G.nodes))
min_x, max_x, min_y, max_y = all_nodes[:, 0].min(), all_nodes[:, 0].max(), all_nodes[:, 1].min(), all_nodes[:, 1].max()
entrance_coord = (max_x * 0.9 + min_x * 0.1, min_y)
exit_coord = (min_x * 0.9 + max_x * 0.1, min_y)
entrance_node = find_closest_node(G, entrance_coord)
exit_node = find_closest_node(G, exit_coord)
print(f"入口节点: {entrance_node}, 出口节点: {exit_node}")

# --- 5. 计算成本矩阵并求解TSP ---
print("5. 正在计算关键节点间的成本矩阵并求解最优顺序...")
critical_nodes = sorted(list(set([entrance_node] + role_node_ids + [exit_node])), key=str)
node_map = {node: i for i, node in enumerate(critical_nodes)}
n_crit = len(critical_nodes)
dist_matrix = np.full((n_crit, n_crit), np.inf)
interest_matrix = np.full((n_crit, n_crit), 0.0)

for i, start_node in enumerate(critical_nodes):
    all_node_dists = dijkstra_for_q_matrix(G, start_node)
    if all_node_dists:
        for j, end_node in enumerate(critical_nodes):
            if end_node in all_node_dists:
                dist_matrix[i, j], interest_matrix[i, j] = all_node_dists[end_node]

max_dist = np.max(dist_matrix[np.isfinite(dist_matrix)]) or 1
max_interest = np.max(interest_matrix) or 1
norm_dist = dist_matrix / max_dist
norm_interest = interest_matrix / max_interest
q_matrix = 0.5 * norm_dist - 0.5 * norm_interest
q_matrix[np.isinf(q_matrix)] = 1e6

start_idx, end_idx = node_map[entrance_node], node_map[exit_node]
best_path_indices = solve_tsp_with_pulp(q_matrix, start_idx, end_idx)

# --- 6. 构建最终物理路径 ---
final_tour_nodes = []
if best_path_indices:
    best_path_node_ids = [critical_nodes[i] for i in best_path_indices]
    print("6. 已找到最优访问顺序，正在构建连贯的物理路径...")
    all_traversed_nodes = set()
    for i in range(len(best_path_node_ids) - 1):
        start, end = best_path_node_ids[i], best_path_node_ids[i + 1]
        path_segment = dijkstra_with_penalty_nx(G, start, end, all_traversed_nodes)

        if not path_segment:
            print(f"!!! 严重错误：无法连接从 {start} 到 {end}。")
            final_tour_nodes = []
            break

        all_traversed_nodes.update(path_segment[:-1])
        final_tour_nodes.extend(path_segment if not final_tour_nodes else path_segment[1:])
else:
    print("错误：未能求解最优访问顺序。")

# --- 7. 可视化最终结果 ---
if final_tour_nodes:
    print("7. 正在生成高精度游览路线图...")
    fig, ax = plt.subplots(figsize=(22, 22))
    ax.set_aspect('equal', adjustable='box')
    ax.set_facecolor('whitesmoke')

    # --- 绘制所有背景图层 ---
    print("   - 正在绘制地图背景...")
    plot_layer(ax, water_segments, color='#a1d2e6', linewidth=1, linestyle='-', zorder=1, label='水体')
    plot_layer(ax, rockery_segments, color='#b0a497', linewidth=1, linestyle='-', zorder=2, label='假山')
    plot_layer(ax, building_solid_segments, color='#e3dcd5', linewidth=1.5, linestyle='-', zorder=3, label='实体建筑')
    plot_layer(ax, building_semi_segments, color='#f0ebe5', linewidth=1, linestyle='--', zorder=3, label='半开放建筑')

    # --- 绘制灰色路网作为参考 ---
    pos = {node: (node[0], node[1]) for node in G.nodes()}

    # 修正：移除zorder参数，使用matplotlib的zorder设置
    edges = nx.draw_networkx_edges(G, pos, ax=ax, alpha=0.3, width=1.0, edge_color='gray')
    # 通过设置线条的zorder属性来控制图层顺序
    if edges is not None:
        edges.set_zorder(4)

    # 7a. 计算路径总长度和预估游览时间
    print("7a. 正在计算路径总长度和预估游览时间...")
    total_length_mm = 0
    for i in range(len(final_tour_nodes) - 1):
        p1 = final_tour_nodes[i]
        p2 = final_tour_nodes[i + 1]
        segment_length = np.linalg.norm(np.array(p1) - np.array(p2))
        total_length_mm += segment_length

    total_length_meters = total_length_mm / 1000
    walking_speed_mps = 1.2
    tour_time_seconds = total_length_meters / walking_speed_mps
    tour_time_minutes = tour_time_seconds / 60

    print("\n" + "=" * 40)
    print("         游览路线分析结果")
    print("-" * 40)
    print(f"-> 路线总长度: {total_length_meters:.2f} 米")
    print(f"-> 预估游览时间: {tour_time_minutes:.2f} 分钟 (按{walking_speed_mps}m/s速度估算)")
    print("=" * 40 + "\n")

    # --- 绘制最终分段路线 ---
    print("   - 正在绘制推荐路线...")
    path_colors_norm = mcolors.Normalize(vmin=0, vmax=len(best_path_node_ids) - 1)
    path_cmap = plt.cm.viridis

    current_path_index = 0
    for i in range(len(best_path_node_ids) - 1):
        start_node = best_path_node_ids[i]
        end_node = best_path_node_ids[i + 1]
        try:
            start_idx_in_final = final_tour_nodes.index(start_node, current_path_index)
            end_idx_in_final = final_tour_nodes.index(end_node, start_idx_in_final)
            segment_nodes = final_tour_nodes[start_idx_in_final: end_idx_in_final + 1]
            x_coords = [n[0] for n in segment_nodes]
            y_coords = [n[1] for n in segment_nodes]
            ax.plot(x_coords, y_coords, color=path_cmap(path_colors_norm(i)), linewidth=3.5, zorder=10,
                    solid_capstyle='round', label='推荐路线' if i == 0 else "")
            current_path_index = end_idx_in_final
        except ValueError:
            continue

    # --- 绘制出入口和核心景点 ---
    ax.plot(entrance_node[0], entrance_node[1], 'p', color='lime', markersize=22, label='入口', zorder=11,
            markeredgecolor='black')
    ax.plot(exit_node[0], exit_node[1], 'h', color='yellow', markersize=22, label='出口', zorder=11,
            markeredgecolor='black')

    # 绘制核心景点
    scores = [role[2] for role in selected_roles_nodes]
    if scores:
        norm_interest = mcolors.Normalize(vmin=min(scores), vmax=max(scores))
    else:
        norm_interest = mcolors.Normalize(vmin=0, vmax=1)
    cmap_interest = cm.Blues

    role_order_map = {role[0]: f"景点 {i + 1}" for i, role in enumerate(selected_roles_nodes)}
    for role_node, _, interest_score in selected_roles_nodes:
        label = role_order_map.get(role_node, "景点")
        color = cmap_interest(norm_interest(interest_score))
        marker_size = 400 + norm_interest(interest_score) * 600
        ax.scatter(role_node[0], role_node[1], marker='*', c=[color], s=marker_size,
                   zorder=12, edgecolors='black', linewidth=1.2,
                   label='核心景点' if role_node == selected_roles_nodes[0][0] else "")
        ax.text(role_node[0], role_node[1] - 3000, label, ha='center', va='top', fontsize=14, fontweight='bold',
                bbox=dict(boxstyle='round,pad=0.3', fc='ivory', alpha=0.85))

    # # --- 添加颜色条 ---
    # # 异景程度颜色条
    # sm_interest = plt.cm.ScalarMappable(cmap=cmap_interest, norm=norm_interest)
    # sm_interest.set_array([])
    # cbar_interest = plt.colorbar(sm_interest, ax=ax, shrink=0.5, orientation='vertical', pad=0.01)
    # cbar_interest.set_label('异景程度 (趣味性评分)', rotation=270, labelpad=25, fontsize=16)
    #
    # # 游览顺序颜色条
    # flipped_path_cmap = path_cmap.reversed()
    # sm_path_order = plt.cm.ScalarMappable(cmap=flipped_path_cmap, norm=path_colors_norm)
    # sm_path_order.set_array([])
    # cbar_path_order = plt.colorbar(sm_path_order, ax=ax, shrink=0.5, orientation='vertical', pad=0.05)
    # cbar_path_order.set_label('游览顺序 (起点 -> 终点)', rotation=270, labelpad=25, fontsize=16)
    # cbar_path_order.set_ticks([0, len(best_path_node_ids) - 1])
    # cbar_path_order.set_ticklabels(['终点', '起点'])

    # --- 创建完整的图例 ---
    from matplotlib.lines import Line2D
    from matplotlib.patches import Patch

    # 创建自定义图例元素
    legend_elements = [
        # 图层元素
        Line2D([0], [0], color='#a1d2e6', linewidth=2, label='水体'),
        Line2D([0], [0], color='#b0a497', linewidth=2, label='假山'),
        Line2D([0], [0], color='#e3dcd5', linewidth=2, label='实体建筑'),
        Line2D([0], [0], color='#f0ebe5', linewidth=2, linestyle='--', label='半开放建筑'),
        Line2D([0], [0], color='gray', linewidth=1, alpha=0.5, label='道路网络'),

        # 路线和标记元素
        Line2D([0], [0], color=path_cmap(0.5), linewidth=3.5, label='推荐路线'),
        Line2D([0], [0], marker='p', color='lime', markersize=15, markeredgecolor='black',
               linestyle='None', label='入口'),
        Line2D([0], [0], marker='h', color='yellow', markersize=15, markeredgecolor='black',
               linestyle='None', label='出口'),
        Line2D([0], [0], marker='*', color='blue', markersize=15, markeredgecolor='black',
               linestyle='None', label='核心景点')
    ]

    # 添加图例
    ax.legend(handles=legend_elements, loc='upper left', bbox_to_anchor=(0, 1),
              fontsize=14, framealpha=0.9, fancybox=True, shadow=True)

    # --- 设置图表样式 ---
    ax.set_title(f"拙政园游览路线图",
                 fontsize=35, pad=20)
    ax.set_xlabel("X 坐标 (mm)", fontsize=25)
    ax.set_ylabel("Y 坐标 (mm)", fontsize=25)
    ax.tick_params(axis='both', which='major', labelsize=12)
    ax.grid(True, linestyle='--', alpha=0.6)

    # --- 保存和显示 ---
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_filename = f"LingeringGarden_Tour_HighFidelity_{timestamp}.png"
    plt.savefig(output_filename, dpi=300, bbox_inches='tight')
    print(f"\n--- 任务完成！已生成高精度路线图 '{output_filename}' ---")
    plt.show()

else:
    print("\n由于未能构建完整的路径，程序已结束，未生成路线图。")