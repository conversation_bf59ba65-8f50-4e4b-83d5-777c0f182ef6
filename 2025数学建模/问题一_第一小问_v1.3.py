import pandas as pd
import re
import numpy as np
import os

# 只显示转折点


try:
    from shapely.geometry import Point, LineString
    import networkx as nx
    import matplotlib.pyplot as plt
except ImportError:
    print("关键库未安装，请在您的环境中运行: pip install pandas numpy shapely networkx matplotlib")
    exit()


def parse_road_coordinates(df):
    """
    解析从CSV文件读取的道路坐标DataFrame，提取所有线段。
    文件中的坐标单位是毫米(mm)，这里统一转换为厘米(cm)。
    """
    coord_series = df.iloc[:, 0].dropna()
    segments = []
    current_segment_points = []
    coord_pattern = re.compile(r'\d+\.\s*\{([\d.-]+),\s*([\d.-]+),\s*([\d.-]+)\}')
    segment_separator_pattern = re.compile(r'\{0;\s*\d+\}')
    for item in coord_series:
        item_str = str(item)
        if segment_separator_pattern.match(item_str):
            if current_segment_points and len(current_segment_points) > 1:
                segments.append(LineString(current_segment_points))
            current_segment_points = []
        else:
            match = coord_pattern.search(item_str)
            if match:
                x, y, z = map(float, match.groups())
                current_segment_points.append((x / 10, y / 10))
    if current_segment_points and len(current_segment_points) > 1:
        segments.append(LineString(current_segment_points))
    return segments


def build_path_graph(lines):
    """
    根据LineString列表构建一个NetworkX图。
    这个版本能够正确处理单条复杂线条的自我相交问题。
    """
    G = nx.Graph()
    all_segments = []
    for line in lines:
        coords = list(line.coords)
        for i in range(len(coords) - 1):
            segment = LineString([coords[i], coords[i + 1]])
            all_segments.append(segment)
    node_coords_set = set()
    for seg in all_segments:
        node_coords_set.add(seg.coords[0])
        node_coords_set.add(seg.coords[1])
    for i in range(len(all_segments)):
        for j in range(i + 1, len(all_segments)):
            if all_segments[i].crosses(all_segments[j]):
                intersection_point = all_segments[i].intersection(all_segments[j])
                if isinstance(intersection_point, Point):
                    node_coords_set.add(intersection_point.coords[0])
    node_list = list(node_coords_set)
    for line in lines:
        nodes_on_line = []
        for node in node_list:
            if line.distance(Point(node)) < 1e-9:
                nodes_on_line.append((line.project(Point(node)), node))
        nodes_on_line.sort()
        for k in range(len(nodes_on_line) - 1):
            start_node, end_node = nodes_on_line[k][1], nodes_on_line[k + 1][1]
            if start_node != end_node:
                distance = Point(start_node).distance(Point(end_node))
                if distance > 1e-9:
                    G.add_edge(start_node, end_node, length=distance)
    return G


def analyze_graph_features(G):
    """
    分析图的关键特征。
    【新版: 修正了转折点的定义，以区分平滑曲线和尖锐拐角】
    """
    # 增加一个'curve_points'类别来存放平滑曲线上的点
    features = {"intersections": [], "sharp_turns": [], "terminals": [], "curve_points": []}

    for node, degree in G.degree():
        if degree == 1:
            features["terminals"].append(node)
        elif degree > 2:
            features["intersections"].append(node)
        elif degree == 2:
            p1, p2 = [n for n in G.neighbors(node)]
            v1 = np.array(p1) - np.array(node)
            v2 = np.array(p2) - np.array(node)

            # 计算角度，并转换为度数以便于理解
            cosine_angle = np.dot(v1, v2) / (np.linalg.norm(v1) * np.linalg.norm(v2))
            angle_rad = np.arccos(np.clip(cosine_angle, -1.0, 1.0))
            angle_deg = np.rad2deg(angle_rad)

            # --- 关键规则修改 ---
            # 规则1: 如果角度小于等于90度，认为是交叉点（符合您之前的定义）
            if angle_deg <= 90:
                features["intersections"].append(node)
            # 规则2: 如果角度小于160度（但大于90度），才认为是真正的“转折点”
            # 您可以调整 160 这个阈值来控制筛选的严格程度。值越小，筛选出的拐角越尖锐。
            elif angle_deg < 160:
                features["sharp_turns"].append(node)
            # 规则3: 否则（角度非常接近180度），认为是平滑曲线上的普通点
            else:
                features["curve_points"].append(node)

    # --- 其他特征计算保持不变 ---
    features["total_length"] = sum(nx.get_edge_attributes(G, 'length').values())
    # ... (这部分函数其余内容无需改动) ...
    if G.number_of_nodes() > 1:
        features["network_density"] = nx.density(G)
    else:
        features["network_density"] = 0
    if G.number_of_edges() > 0:
        features["avg_segment_length"] = features["total_length"] / G.number_of_edges()
    else:
        features["avg_segment_length"] = 0
    features["num_connected_components"] = nx.number_connected_components(G)

    return features


def visualize_graph(G, features, title):
    """
    可视化生成的图模型。
    【新版: 增大了图例和坐标轴的字体大小】
    """
    plt.rcParams['font.sans-serif'] = ['SimHei']
    plt.rcParams['axes.unicode_minus'] = False

    pos = {node: (node[0], node[1]) for node in G.nodes()}
    plt.figure(figsize=(16, 16))

    nx.draw_networkx_edges(G, pos, alpha=0.6, width=1.5, edge_color='gray')

    nx.draw_networkx_nodes(G, pos, nodelist=features['curve_points'], node_color='#cccccc', node_size=10)

    # nx.draw_networkx_nodes(G, pos, nodelist=features['terminals'], node_color='green', node_size=60,
    #                        label=f"路径端点 ({len(features['terminals'])})")
    nx.draw_networkx_nodes(G, pos, nodelist=features['intersections'], node_color='blue', node_size=60,
                           label=f"交叉点 (≤90°) ({len(features['intersections'])})")
    # nx.draw_networkx_nodes(G, pos, nodelist=features['sharp_turns'], node_color='red', node_size=80,
    #                        label=f"转折点 (90°-160°) ({len(features['sharp_turns'])})")

    plt.title(title, fontsize=30)

    # --- 关键修改：在这里为坐标轴标签添加 fontsize 参数 ---
    plt.xlabel("X坐标 (厘米)", fontsize=28)
    plt.ylabel("Y坐标 (厘米)", fontsize=28)

    # --- 关键修改：在这里为图例添加 fontsize 参数 ---
    plt.legend(fontsize=20)

    plt.axis('equal')
    plt.grid(True)

    output_filename = 'zhuozhengyuan_path_graph_09211708.png'
    plt.savefig(output_filename, dpi=300)
    print(f"\n图可视化结果已保存至: {output_filename}")
    plt.close()


# --- 主程序 ---
if __name__ == '__main__':
    # 1. 定义目录和文件名
    data_directory = r'H:\OneDrive\Desktop\F题\赛题F江南古典园林美学特征建模附件资料\1. 拙政园'
    file_name = '4-拙政园数据坐标.xlsx'

    # 2. 拼接成完整路径
    file_path = os.path.join(data_directory, file_name)
    print(f"准备读取的文件路径是: {file_path}")

    try:
        # 使用 pd.read_excel() 正确读取Excel文件的指定工作表
        df = pd.read_excel(file_path, sheet_name='道路')
        print("Excel '道路' 工作表读取成功！")

        road_lines = parse_road_coordinates(df)
        print(f"成功解析出 {len(road_lines)} 条主要几何线条。")

        path_graph = build_path_graph(road_lines)
        print("路径图构建完成。")

        graph_features = analyze_graph_features(path_graph)
        print("图特征分析完成。")

        print("\n--- 拙政园路径网络分析结果 ---")
        print("\n[基础特征]")
        print(f"  - 总路径长度: {graph_features['total_length']:.2f} 厘米")
        print(f"  - 路径端点 数量: {len(graph_features['terminals'])}")
        print(f"  - 交叉点 (≤90°) 数量: {len(graph_features['intersections'])}")
        print(f"  - 转折点 (>90°) 数量: {len(graph_features['sharp_turns'])}")
        print("\n[网络拓扑特征]")
        print(f"  - 网络密度: {graph_features['network_density']:.4f}")
        print(f"  - 平均路径分段长度: {graph_features['avg_segment_length']:.2f} 厘米")
        print(f"  - 连通分量数: {graph_features['num_connected_components']}")
        print("\n[图总体概况]")
        print(f"  - 总节点数量: {path_graph.number_of_nodes()}")
        print(f"  - 总路径分段 (边) 数量: {path_graph.number_of_edges()}")

        visualize_graph(path_graph, graph_features, "拙政园路径网络图分析 (单位: 厘米)")

    except FileNotFoundError:
        print(f"\n错误: 文件未找到。请确认文件名是否正确:\n{file_name}")
    except Exception as e:
        print(f"An error occurred: {e}")