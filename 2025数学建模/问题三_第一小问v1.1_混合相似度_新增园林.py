import pandas as pd
from sklearn.preprocessing import MinMaxScaler
from sklearn.metrics.pairwise import cosine_similarity
import matplotlib.pyplot as plt
import seaborn as sns
from scipy.cluster.hierarchy import dendrogram, linkage
import os
import pandas as pd
from sklearn.preprocessing import MinMaxScaler
from sklearn.metrics.pairwise import cosine_similarity, euclidean_distances
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
# 导入 linkage 和 squareform
from scipy.cluster.hierarchy import dendrogram, linkage
from scipy.spatial.distance import squareform # <--- 新增导入
import os

# --- 用户配置 ---
# 请确保您的Excel文件名与下面的字符串完全一致，包括后缀名 .xlsx
input_excel_file = "相似度矩阵 - 新增园林.xlsx"
w = 0.7

# 文件所在的路径 (桌面)
desktop_path = r"H:\OneDrive\Desktop"

# --- 程序开始 ---

# 组合成完整的文件路径
input_file_path = os.path.join(desktop_path, input_excel_file)
output_csv_path = os.path.join(desktop_path, 'similarity_matrix新园林.csv')
output_heatmap_path = os.path.join(desktop_path, 'similarity_heatmap新园林.png')
output_dendrogram_path = os.path.join(desktop_path, 'dendrogram新园林.png')

# 1. 加载数据
try:
    # --- 这里是关键修改：使用 pd.read_excel 读取 Excel 文件 ---
    df = pd.read_excel(input_file_path)
    print(f"成功从 '{input_file_path}' 读取Excel文件。")
except FileNotFoundError:
    print(f"错误：在您的桌面找不到文件 '{input_excel_file}'。")
    print("请确认文件名是否完全正确（包括.xlsx后缀），并且文件确实在桌面上。")
    exit()
except Exception as e:
    print(f"读取文件时发生错误: {e}")
    print("请确保您已经安装了 'openpyxl' 库 (在CMD中运行 pip install openpyxl)。")
    exit()

# 显示数据前几行以供核对
print("\n原始数据预览:")
print(df.head())

# 2. 数据预处理
try:
    df.set_index('园林名称', inplace=True)
except KeyError:
    print("错误：Excel文件中找不到 '园林名称' 这一列。请检查您的列名是否正确。")
    exit()

# 3. 数据归一化
features = df.columns
scaler = MinMaxScaler()
normalized_data = scaler.fit_transform(df)
normalized_df = pd.DataFrame(normalized_data, columns=features, index=df.index)
print("\n数据归一化完成。")

# # 4. 计算相似度
# similarity_matrix = cosine_similarity(normalized_df)
# similarity_df = pd.DataFrame(similarity_matrix, index=df.index, columns=df.index)
# print("\n相似度矩阵计算完成:")
# print(similarity_df)

# --- 混合相似度计算 (与之前相同) ---
cosine_sim_matrix = cosine_similarity(normalized_df)
euclidean_dist_matrix = euclidean_distances(normalized_df)
normalized_dist = euclidean_dist_matrix / np.max(euclidean_dist_matrix)
euclidean_sim_matrix = 1 - normalized_dist
hybrid_sim_matrix = w * cosine_sim_matrix + (1 - w) * euclidean_sim_matrix
hybrid_similarity_df = pd.DataFrame(hybrid_sim_matrix, index=df.index, columns=df.index)
print(f"\n已使用权重 w={w} 计算混合相似度矩阵。")

# 5. 保存结果到桌面
hybrid_similarity_df.to_csv(output_csv_path, encoding='utf-8-sig')
print(f"\n相似度矩阵已保存到桌面: '{output_csv_path}'")

# --- 可视化 ---

plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

# 绘制并保存热力图
plt.figure(figsize=(12, 10))
sns.heatmap(hybrid_similarity_df, annot=True, cmap='RdBu_r', fmt='.2f', linewidths=.5)
plt.title('园林相似度矩阵 (热力图)', fontsize=16)
plt.xlabel('园林名称', fontsize=12)
plt.ylabel('园林名称', fontsize=12)
plt.xticks(rotation=45, ha='right')
plt.yticks(rotation=0)
plt.tight_layout()
plt.savefig(output_heatmap_path)
print(f"热力图已保存到桌面: '{output_heatmap_path}'")

# 绘制并保存层次聚类树状图
# 设置线条宽度，这里的数值越大，线条越粗
plt.rcParams['lines.linewidth'] = 2.5
linked = linkage(cosine_sim_matrix, 'ward')
plt.figure(figsize=(12, 7))
dendrogram(linked,
            orientation='top',
            labels=hybrid_similarity_df.index.to_list(),
            distance_sort='descending',
            show_leaf_counts=True)
plt.title('园林层次聚类树状图', fontsize=16)
plt.xlabel('园林名称', fontsize=12)
plt.ylabel('距离', fontsize=12)
plt.xticks(rotation=45, ha='right')
plt.tight_layout()
plt.savefig(output_dendrogram_path)
print(f"层次聚类树状图已保存到桌面: '{output_dendrogram_path}'")

print("\n所有任务已完成！")