import pandas as pd
import re
import numpy as np
import os

try:
    from shapely.geometry import Point, LineString
    import networkx as nx
    import matplotlib.pyplot as plt
except ImportError:
    print("关键库未安装，请在您的环境中运行: pip install pandas numpy shapely networkx matplotlib")
    exit()


# --- MODIFICATION 1: Change function to accept a DataFrame instead of a file path ---
def parse_road_coordinates(df):
    """
    解析从Excel工作表读取的道路坐标DataFrame，提取所有线段。
    文件中的坐标单位是毫米(mm)，这里统一转换为米(m)。
    """
    # --- MODIFICATION 2: Remove the incorrect file reading line ---
    # The line `df = pd.read_csv(file_path)` has been removed.

    # The rest of the function remains the same, as it correctly processes the DataFrame.
    coord_series = df.iloc[:, 0].dropna()

    segments = []
    current_segment_points = []

    coord_pattern = re.compile(r'\d+\.\s*\{([\d.-]+),\s*([\d.-]+),\s*([\d.-]+)\}')
    segment_separator_pattern = re.compile(r'\{0;\s*\d+\}')

    for item in coord_series:
        item_str = str(item)
        if segment_separator_pattern.match(item_str):
            if current_segment_points and len(current_segment_points) > 1:
                segments.append(LineString(current_segment_points))
            current_segment_points = []
        else:
            match = coord_pattern.search(item_str)
            if match:
                x, y, z = map(float, match.groups())
                current_segment_points.append((x / 1000, y / 1000))

    if current_segment_points and len(current_segment_points) > 1:
        segments.append(LineString(current_segment_points))

    return segments


def build_path_graph(lines):
    """
    根据LineString列表构建一个NetworkX图。
    这个版本能够正确处理单条复杂线条的自我相交问题。
    """
    G = nx.Graph()
    all_segments = []

    # 步骤1: 将所有输入的大线条分解成由两个点组成的微小线段
    for line in lines:
        coords = list(line.coords)
        for i in range(len(coords) - 1):
            segment = LineString([coords[i], coords[i + 1]])
            all_segments.append(segment)

    # 步骤2: 找出所有的节点坐标，包括线段端点和交叉点
    node_coords_set = set()
    # 首先，所有微小线段的端点都是节点
    for seg in all_segments:
        node_coords_set.add(seg.coords[0])
        node_coords_set.add(seg.coords[1])

    # 其次，计算所有微小线段之间的交叉点
    for i in range(len(all_segments)):
        for j in range(i + 1, len(all_segments)):
            # 使用 .crosses() 来避免仅仅是接触的情况
            if all_segments[i].crosses(all_segments[j]):
                intersection_point = all_segments[i].intersection(all_segments[j])
                if isinstance(intersection_point, Point):
                    node_coords_set.add(intersection_point.coords[0])

    node_list = list(node_coords_set)

    # 步骤3: 使用找到的节点来切分原始线条，并构建图
    for line in lines:
        # 找到所有位于原始线条上的节点
        nodes_on_line = []
        for node in node_list:
            # 使用一个极小的容差来判断点是否在线上
            if line.distance(Point(node)) < 1e-9:
                # 记录 (点在线条上的投影距离, 点的坐标)
                nodes_on_line.append((line.project(Point(node)), node))

        # 按投影距离排序
        nodes_on_line.sort()

        # 在排序后的连续节点之间创建边
        for k in range(len(nodes_on_line) - 1):
            start_node = nodes_on_line[k][1]
            end_node = nodes_on_line[k + 1][1]

            # 确保起点和终点不同
            if start_node != end_node:
                distance = Point(start_node).distance(Point(end_node))
                # 只有当距离大于一个很小的值时才添加边，避免浮点数问题
                if distance > 1e-9:
                    G.add_edge(start_node, end_node, length=distance)

    return G


def analyze_graph_features(G):
    """
    分析图的关键特征。
    """
    features = {"intersections": [], "turns": [], "terminals": []}
    for node, degree in G.degree():
        if degree > 2:
            features["intersections"].append(node)
        elif degree == 1:
            features["terminals"].append(node)
        elif degree == 2:
            p1, p2 = [n for n in G.neighbors(node)]
            v1, v2 = np.array(p1) - np.array(node), np.array(p2) - np.array(node)
            cosine_angle = np.dot(v1, v2) / (np.linalg.norm(v1) * np.linalg.norm(v2))
            angle = np.arccos(np.clip(cosine_angle, -1.0, 1.0))
            if angle < np.deg2rad(135):
                features["turns"].append(node)

    # --- 基础特征 ---
    features["total_length"] = sum(nx.get_edge_attributes(G, 'length').values())

    # --- 新增的关键特征 ---
    # 1. 网络密度
    if G.number_of_nodes() > 1:
        features["network_density"] = nx.density(G)
    else:
        features["network_density"] = 0

    # 2. 平均路径分段长度
    if G.number_of_edges() > 0:
        features["avg_segment_length"] = features["total_length"] / G.number_of_edges()
    else:
        features["avg_segment_length"] = 0

    # 3. 连通分量数
    features["num_connected_components"] = nx.number_connected_components(G)

    return features


def visualize_graph(G, features, title):
    """
    可视化生成的图模型。
    """
    pos = {node: (node[0], node[1]) for node in G.nodes()}
    plt.figure(figsize=(16, 16))

    nx.draw_networkx_edges(G, pos, alpha=0.6, width=1.5, edge_color='gray')

    node_size = 50
    nx.draw_networkx_nodes(G, pos, nodelist=features['terminals'], node_color='green', node_size=node_size,
                           label=f"Terminals ({len(features['terminals'])})")
    nx.draw_networkx_nodes(G, pos, nodelist=features['turns'], node_color='blue', node_size=node_size,
                           label=f"Turns ({len(features['turns'])})")
    nx.draw_networkx_nodes(G, pos, nodelist=features['intersections'], node_color='red', node_size=node_size * 1.5,
                           label=f"Intersections ({len(features['intersections'])})")

    plt.title(title, fontsize=20)
    plt.xlabel("X Coordinate (meters)")
    plt.ylabel("Y Coordinate (meters)")
    plt.legend()
    plt.axis('equal')
    plt.grid(True)

    output_filename = 'zhuozhengyuan_path_graph.png'
    plt.savefig(output_filename)
    print(f"\nGraph visualization saved to {output_filename}")
    plt.close()


# --- 主程序 ---
if __name__ == '__main__':
    # 1. 定义目录和文件名
    data_directory = r'H:\OneDrive\Desktop\F题\赛题F江南古典园林美学特征建模附件资料\1. 拙政园'
    file_name = '4-拙政园数据坐标.xlsx'

    # 2. 拼接成完整路径
    file_path = os.path.join(data_directory, file_name)
    print(f"准备读取的文件路径是: {file_path}")

    try:
        # 使用 pd.read_excel() 正确读取Excel文件的指定工作表
        df = pd.read_excel(file_path, sheet_name='道路')
        print("Excel '道路' 工作表读取成功！")

        # --- MODIFICATION 3: Pass the loaded DataFrame to the function ---
        road_lines = parse_road_coordinates(df)
        print(f"成功解析出 {len(road_lines)} 条道路线段。")

        path_graph = build_path_graph(road_lines)
        print("路径图构建完成。")

        graph_features = analyze_graph_features(path_graph)
        print("图特征分析完成。")

        # --- 打印所有关键特征 ---
        print("\n--- 拙政园路径网络分析结果 ---")
        print("\n[基础特征]")
        print(f"  - 总路径长度: {graph_features['total_length']:.2f} 米")
        print(f"  - 路径端点 数量: {len(graph_features['terminals'])}")
        print(f"  - 明显转折点 数量: {len(graph_features['turns'])}")
        print(f"  - 交叉点 (岔路口) 数量: {len(graph_features['intersections'])}")

        print("\n[网络拓扑特征]")
        print(f"  - 网络密度: {graph_features['network_density']:.4f}")
        print(f"  - 平均路径分段长度: {graph_features['avg_segment_length']:.2f} 米")
        print(f"  - 连通分量数: {graph_features['num_connected_components']}")

        print("\n[图总体概况]")
        print(f"  - 总节点数量: {path_graph.number_of_nodes()}")
        print(f"  - 总路径分段 (边) 数量: {path_graph.number_of_edges()}")

        visualize_graph(path_graph, graph_features, "Zhuozheng Garden Path Network Analysis")

    except FileNotFoundError:
        print(f"\n错误: 文件未找到。请确认路径和文件名是否正确:\n{file_path}")
    except Exception as e:
        print(f"An error occurred: {e}")