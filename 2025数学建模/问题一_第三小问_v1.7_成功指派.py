import pandas as pd
import re
import matplotlib.pyplot as plt
from scipy.spatial.distance import cdist
import heapq
import matplotlib

# 成功写出路线

# --- 配置中文字体 ---
try:
    plt.rcParams['font.sans-serif'] = ['SimHei']
    plt.rcParams['axes.unicode_minus'] = False
except Exception:
    print("警告：未找到'SimHei'字体，图例可能显示不正常。")

# --- 1. 数据加载 ---
SCORE_MATRIX_PATH = r'H:\OneDrive\Desktop\final_scoring_matrix_v4_final.csv'
ROAD_EXCEL_PATH = r'H:\OneDrive\Desktop\F题\赛题F江南古典园林美学特征建模附件资料\1. 拙政园\4-拙政园数据坐标.xlsx'

try:
    print(f"正在从 '{SCORE_MATRIX_PATH}' 加载趣味性评分矩阵...")
    interest_scores_df = pd.read_csv(SCORE_MATRIX_PATH)

    print(f"正在从 '{ROAD_EXCEL_PATH}' 加载园林数据...")
    road_df = pd.read_excel(ROAD_EXCEL_PATH, sheet_name='道路', header=None)

    print("所有必需文件成功加载！")
except FileNotFoundError as e:
    print(f"错误：无法找到文件 {e.filename}。请仔细检查文件路径。")
    exit()
except Exception as e:
    print(f"读取文件时发生错误: {e}")
    exit()


# --- 2. 数据解析与处理 ---
def parse_road_data(df):
    all_points = []
    for index, row in df.iterrows():
        coords_str = str(row.iloc[0]) + str(row.iloc[1])
        points = re.findall(r'\{([-\d\.]+),\s*([-\d\.]+),\s*([-\d\.]+)\}', coords_str)
        for p in points:
            all_points.append((float(p[0]), float(p[1])))

    if not all_points:
        return pd.DataFrame(), pd.DataFrame()

    unique_points = pd.DataFrame(all_points, columns=['x', 'y']).drop_duplicates().reset_index(drop=True)
    unique_points['node_id'] = unique_points.index

    coord_to_id = {
        (round(row.x, 4), round(row.y, 4)): row.node_id
        for _, row in unique_points.iterrows()
    }

    edges = []
    segment_points = []
    for index, row in df.iterrows():
        segment_marker = re.match(r'\s*\{\d+;\d+\}', str(row.iloc[0]))
        coords_str = str(row.iloc[0]) + str(row.iloc[1])
        points = re.findall(r'\{([-\d\.]+),\s*([-\d\.]+),\s*([-\d\.]+)\}', coords_str)

        if segment_marker and segment_points:
            for i in range(len(segment_points) - 1):
                p1, p2 = segment_points[i], segment_points[i + 1]
                node1_id = coord_to_id.get((round(p1[0], 4), round(p1[1], 4)))
                node2_id = coord_to_id.get((round(p2[0], 4), round(p2[1], 4)))
                if node1_id is not None and node2_id is not None and node1_id != node2_id:
                    edges.append(
                        {'source': node1_id, 'target': node2_id, 'start_x': p1[0], 'start_y': p1[1], 'end_x': p2[0],
                         'end_y': p2[1]})
            segment_points = []

        for p in points:
            segment_points.append((float(p[0]), float(p[1])))

    if segment_points:
        for i in range(len(segment_points) - 1):
            p1, p2 = segment_points[i], segment_points[i + 1]
            node1_id = coord_to_id.get((round(p1[0], 4), round(p1[1], 4)))
            node2_id = coord_to_id.get((round(p2[0], 4), round(p2[1], 4)))
            if node1_id is not None and node2_id is not None and node1_id != node2_id:
                edges.append(
                    {'source': node1_id, 'target': node2_id, 'start_x': p1[0], 'start_y': p1[1], 'end_x': p2[0],
                     'end_y': p2[1]})

    return unique_points, pd.DataFrame(edges)


def parse_interest_scores(df):
    parsed_scores = []
    for _, row in df.iterrows():
        start_coords = re.findall(r'\(([-\d\.]+),\s*([-\d\.]+)\)', str(row['Start_Point']))
        end_coords = re.findall(r'\(([-\d\.]+),\s*([-\d\.]+)\)', str(row['End_Point']))
        if start_coords and end_coords:
            parsed_scores.append({'start_x': float(start_coords[0][0]), 'start_y': float(start_coords[0][1]),
                                  'end_x': float(end_coords[0][0]), 'end_y': float(end_coords[0][1]),
                                  'interest_score': float(row['Interest_Score'])})
    return pd.DataFrame(parsed_scores)


print("正在解析道路和趣味性评分数据...")
nodes_df, edges_df = parse_road_data(road_df)
interest_coords_df = parse_interest_scores(interest_scores_df)

if edges_df.empty:
    print("错误：无法构建道路网络，程序终止。")
    exit()
else:
    print(f"成功构建路网，包含 {len(nodes_df)} 个节点和 {len(edges_df)} 条路段。")

print("正在将评分匹配到路网...")
merged_edges = edges_df.copy()
merged_edges['interest_score'] = 0.0
for _, interest_row in interest_coords_df.iterrows():
    interest_start, interest_end = [[interest_row['start_x'], interest_row['start_y']]], [
        [interest_row['end_x'], interest_row['end_y']]]
    dist_start = cdist(interest_start, merged_edges[['start_x', 'start_y']])
    dist_end = cdist(interest_end, merged_edges[['end_x', 'end_y']])
    total_dist = dist_start + dist_end
    if total_dist.size > 0:
        merged_edges.loc[total_dist.argmin(), 'interest_score'] = interest_row['interest_score']

# --- 3. 路径规划 ---
print("正在构建图网络并规划路径...")
graph = {i: [] for i in range(len(nodes_df))}
for _, row in merged_edges.iterrows():
    length = ((row['start_x'] - row['end_x']) ** 2 + (row['start_y'] - row['end_y']) ** 2) ** 0.5
    cost = length / (1 + row['interest_score'] * 10)
    graph[int(row['source'])].append((int(row['target']), cost))
    graph[int(row['target'])].append((int(row['source']), cost))


def dijkstra(graph, start, end):
    queue, seen = [(0, start, [])], set()
    while queue:
        (cost, v, path) = heapq.heappop(queue)
        if v not in seen:
            seen.add(v)
            path = path + [v]
            if v == end: return (cost, path)
            for (next_node, edge_cost) in graph.get(v, []):
                if next_node not in seen: heapq.heappush(queue, (cost + edge_cost, next_node, path))
    return float("inf"), []


min_x, max_x, min_y, max_y = nodes_df['x'].min(), nodes_df['x'].max(), nodes_df['y'].min(), nodes_df['y'].max()
entrance_node_id = cdist([[max_x * 0.9 + min_x * 0.1, min_y]], nodes_df[['x', 'y']]).argmin()
exit_node_id = cdist([[min_x * 0.9 + max_x * 0.1, min_y]], nodes_df[['x', 'y']]).argmin()

cost, tour_path_nodes = dijkstra(graph, entrance_node_id, exit_node_id)

# --- 4. 可视化 (网络图版) ---

if tour_path_nodes:
    print("成功找到最优路径，正在生成网络版地图...")
    fig, ax = plt.subplots(figsize=(15, 15))

    # =================================================================
    # 修改之处：使用 “趣味性评分矩阵” 的数据来绘制背景道路
    # 这将创建一个非常简洁的、由单条线段组成的网络图
    # =================================================================
    print("正在根据趣味性评分数据绘制极简道路背景图...")
    for index, row in interest_coords_df.iterrows():
        ax.plot([row['start_x'], row['end_x']], [row['start_y'], row['end_y']], color='gray', linewidth=2, zorder=1)
    # =================================================================

    # 在地图上绘制推荐路线
    path_coords = [(nodes_df.loc[node_id]['x'], nodes_df.loc[node_id]['y']) for node_id in tour_path_nodes]
    ax.plot(*zip(*path_coords), color='red', linewidth=4, linestyle='-', marker='o', markersize=6, zorder=10,
            label='推荐路线')

    # 标记出入口
    start_coords, end_coords = nodes_df.loc[entrance_node_id], nodes_df.loc[exit_node_id]
    ax.plot(start_coords['x'], start_coords['y'], 'p', color='lime', markersize=15, label='入口', zorder=11,
            markeredgecolor='black')
    ax.plot(end_coords['x'], end_coords['y'], 'h', color='cyan', markersize=15, label='出口', zorder=11,
            markeredgecolor='black')

    ax.set_aspect('equal', adjustable='box')
    plt.title("拙政园趣味性游览路线推荐图 (网络版)", fontsize=20)
    plt.xlabel("X 坐标 (mm)", fontsize=14)
    plt.ylabel("Y 坐标 (mm)", fontsize=14)
    plt.legend(prop={'size': 15})
    # 去掉网格线，让背景更干净
    plt.grid(False)
    # 设置背景颜色
    ax.set_facecolor('#f4f4f4')

    # 使用新的文件名保存
    output_filename = "garden_tour_route_network.png"
    plt.savefig(output_filename, dpi=300, bbox_inches='tight')
    print(f"任务完成！网络版路线图已成功保存为 '{output_filename}'，请在代码同目录下查找。")
else:
    print("在设定的出入口之间未能找到路径。")