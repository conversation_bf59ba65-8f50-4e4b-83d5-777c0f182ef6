import pandas as pd
import numpy as np
import re
import matplotlib.pyplot as plt
import networkx as nx

# 有路径 好像不合理

class TourOptimizer:
    def __init__(self, score_matrix_path, road_excel_path):
        self.road_excel_path = road_excel_path
        self.segments_df = None
        self.graph = nx.Graph()
        self._build_base_graph()
        self._integrate_scores(score_matrix_path)

    def _parse_coord_string(self, s):
        try:
            numbers = [float(num) for num in re.findall(r'-?\d+\.\d+', s)]
            if len(numbers) >= 2: return (round(numbers[0], 2), round(numbers[1], 2))
        except (ValueError, TypeError):
            return None
        return None

    def _build_base_graph(self):
        print("1. 正在构建精确的园区路网图...")
        try:
            road_raw_df = pd.read_excel(self.road_excel_path, sheet_name="道路", header=None, skiprows=1)

            current_polyline = []
            for index, row in road_raw_df.iterrows():
                cell1_str = str(row.iloc[0])
                if cell1_str.startswith('{'):
                    if current_polyline:
                        self._add_polyline_to_graph(current_polyline)
                    current_polyline = []

                for cell in row:
                    if pd.notna(cell):
                        coord = self._parse_coord_string(str(cell))
                        if coord:
                            current_polyline.append(coord)

            if current_polyline:
                self._add_polyline_to_graph(current_polyline)

            print(
                f"   > 路网图构建完成，包含 {self.graph.number_of_nodes()} 个节点和 {self.graph.number_of_edges()} 条边。")
        except Exception as e:
            print(f"构建路网图时发生错误: {e}")

    def _add_polyline_to_graph(self, polyline):
        for i in range(len(polyline) - 1):
            p1, p2 = polyline[i], polyline[i + 1]
            distance = np.linalg.norm(np.array(p1) - np.array(p2))
            self.graph.add_edge(p1, p2, length=distance, score=0.0)

    def _integrate_scores(self, score_matrix_path):
        print("2. 正在将趣味性评分融入路网图...")
        try:
            df = pd.read_csv(score_matrix_path)
            df['Start_Point_Parsed'] = df['Start_Point'].apply(lambda s: self._parse_coord_string(s))
            df['End_Point_Parsed'] = df['End_Point'].apply(lambda s: self._parse_coord_string(s))

            for index, row in df.iterrows():
                start_node, end_node = row['Start_Point_Parsed'], row['End_Point_Parsed']
                score = row['Final_Score']

                if self.graph.has_node(start_node) and self.graph.has_node(end_node):
                    try:
                        path_nodes = nx.shortest_path(self.graph, source=start_node, target=end_node, weight='length')
                        for i in range(len(path_nodes) - 1):
                            self.graph.edges[path_nodes[i], path_nodes[i + 1]]['score'] = max(
                                self.graph.edges[path_nodes[i], path_nodes[i + 1]]['score'], score)
                    except nx.NetworkXNoPath:
                        continue
            print("   > 评分融入完成。")
        except Exception as e:
            print(f"融入评分时发生错误: {e}")

    def find_best_tour(self):
        """第三步：在新路网上寻找最佳路径。"""
        print("3. 正在从'最有趣的地方'开始寻找最佳路径...")

        # 1. 自动定位评分最高的“王牌”路段作为起点
        if self.graph.number_of_edges() == 0:
            print("   > 错误：路网图中没有任何边。")
            return []

        best_edge = max(self.graph.edges(data=True), key=lambda edge: edge[2].get('score', 0.0))
        start_node = best_edge[0]
        print(f"   > 已自动确定最佳起点：{start_node} (该点属于全园评分最高的路段)")

        # 2. 从这个最佳起点出发，使用贪心算法构建路径
        current_path = [start_node]
        visited_edges = set()
        current_node = start_node

        while True:
            candidate_edges = []
            for neighbor in self.graph.neighbors(current_node):
                edge = tuple(sorted((current_node, neighbor)))
                if edge not in visited_edges:
                    edge_data = self.graph.get_edge_data(current_node, neighbor)
                    candidate_edges.append((neighbor, edge_data.get('score', 0.0), edge))

            if not candidate_edges:
                break

            best_next_node, _, best_edge_tuple = max(candidate_edges, key=lambda item: item[1])

            current_path.append(best_next_node)
            visited_edges.add(best_edge_tuple)
            current_node = best_next_node

        return current_path

    def visualize_tour(self, path):
        """可视化最终找到的最佳路径。"""
        if not path:
            print("未能找到有效路径进行可视化。")
            return
        print("4. 正在生成最终的路径可视化图...")

        plt.rcParams['font.sans-serif'] = ['SimHei']
        plt.rcParams['axes.unicode_minus'] = False
        plt.figure(figsize=(16, 16))

        pos = {node: (node[0], node[1]) for node in self.graph.nodes()}
        nx.draw_networkx_edges(self.graph, pos, edge_color='lightgray', width=1.0)

        path_edges = list(zip(path, path[1:]))
        nx.draw_networkx_edges(self.graph, pos, edgelist=path_edges, edge_color='red', width=3.0, label="最优路径")

        start_node, end_node = path[0], path[-1]
        nx.draw_networkx_nodes(self.graph, pos, nodelist=[start_node], node_color='g', node_size=150, label="起点")
        nx.draw_networkx_nodes(self.graph, pos, nodelist=[end_node], node_color='b', node_shape='s', node_size=150,
                               label="终点")

        plt.title('拙政园最优游览路径（最终版）', fontsize=20)
        plt.xlabel('X坐标', fontsize=14);
        plt.ylabel('Y坐标', fontsize=14)
        plt.tick_params(axis='both', which='major', labelsize=12)
        plt.legend()
        plt.grid(True, linestyle=':', alpha=0.6)

        output_image_path = r'H:\OneDrive\Desktop\optimal_tour_path_final.png'
        plt.savefig(output_image_path, dpi=300)
        print(f"   > 路径图已保存到: {output_image_path}")
        plt.show()


# --- 主程序入口 ---
if __name__ == "__main__":
    SCORE_MATRIX_PATH = r'H:\OneDrive\Desktop\final_scoring_matrix_v4_final.csv'
    ROAD_EXCEL_PATH = r'H:\OneDrive\Desktop\F题\赛题F江南古典园林美学特征建模附件资料\1. 拙政园\4-拙政园数据坐标.xlsx'

    optimizer = TourOptimizer(score_matrix_path=SCORE_MATRIX_PATH,
                              road_excel_path=ROAD_EXCEL_PATH)

    best_tour_path = optimizer.find_best_tour()

    if best_tour_path:
        path_score = sum(optimizer.graph.edges[best_tour_path[i], best_tour_path[i + 1]]['score'] for i in
                         range(len(best_tour_path) - 1))
        path_length = sum(optimizer.graph.edges[best_tour_path[i], best_tour_path[i + 1]]['length'] for i in
                          range(len(best_tour_path) - 1))

        print("\n==============================================")
        print("            最终最佳游览路径方案")
        print("==============================================")
        print(f"最佳起点为: {best_tour_path[0]}")
        print(f"路径总长度: {len(best_tour_path) - 1} 个路段, 约 {path_length / 1000:.2f} 米")
        print(f"总趣味性评分: {path_score:.4f}")

        optimizer.visualize_tour(best_tour_path)