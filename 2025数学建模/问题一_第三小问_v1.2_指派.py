import pandas as pd
import numpy as np
import pulp
from pulp import LpProblem, LpMaximize, LpVariable, lpSum, LpStatus
import re
import matplotlib.pyplot as plt

# 求解速度慢

# --- 模块1: 配置与数据加载 ---

# 请确认这是您最终版的评分矩阵文件路径
SCORE_MATRIX_PATH = r'H:\OneDrive\Desktop\final_scoring_matrix_v4_final.csv'
# 园区所有道路的原始坐标文件路径 (Excel文件)
ROAD_EXCEL_PATH = r'H:\OneDrive\Desktop\F题\赛题F江南古典园林美学特征建模附件资料\1. 拙政园\4-拙政园数据坐标.xlsx'
# 您也可以在这里直接指定一个起点Segment的编号，例如0。如果设为None，则由模型自由选择。
DESIGNATED_START_SEGMENT = 0


def parse_coord_string(s):
    """辅助函数，用于从字符串中解析出(x, y)坐标元组。"""
    try:
        numbers = [float(num) for num in re.findall(r'-?\d+\.\d+', s)]
        if len(numbers) >= 2: return (numbers[0], numbers[1])
    except (ValueError, TypeError):
        return None
    return None


def load_and_prepare_data(filepath):
    """加载评分矩阵并准备建模所需的数据。"""
    try:
        df = pd.read_csv(filepath)
        print(f"1. 成功读取评分矩阵文件: {filepath}")
    except FileNotFoundError:
        print(f"错误: 找不到文件 {filepath}。请检查路径。")
        return None, None, None

    df['Start_Point_Parsed'] = df['Start_Point'].apply(lambda s: tuple(map(float, s.strip('()').split(', '))))
    df['End_Point_Parsed'] = df['End_Point'].apply(lambda s: tuple(map(float, s.strip('()').split(', '))))

    num_segments = len(df)
    scores = df['Final_Score'].to_dict()

    connectivity = np.zeros((num_segments, num_segments))
    for i in range(num_segments):
        for j in range(num_segments):
            if i == j: continue
            if np.allclose(df.loc[i, 'End_Point_Parsed'], df.loc[j, 'Start_Point_Parsed']):
                connectivity[i, j] = 1

    print("2. 物理连接性矩阵构建完成。")
    return df, scores, connectivity


# --- 模块2: 建模与求解 ---

def build_and_solve_model(segments_df, scores, connectivity):
    """构建并求解路径优化的MILP模型。"""
    num_segments = len(segments_df)
    S = range(num_segments)

    model = LpProblem("Garden_Tour_Optimization", LpMaximize)
    print("3. 开始构建优化模型...")

    # ******** 此处为关键修正部分 ********
    # 决策变量x的索引是元组 (i, j)
    x = LpVariable.dicts("x", ((i, j) for i in S for j in S), cat='Binary')
    y = LpVariable.dicts("y", S, cat='Binary')
    u = LpVariable.dicts("u", S, lowBound=1, upBound=num_segments, cat='Continuous')

    model += lpSum(scores[i] * y[i] for i in S), "Total_Score"

    # 约束1 & 2: 流量守恒 (使用正确的 x[(i, j)] 索引)
    for k in S:
        model += lpSum(x[(i, k)] for i in S) == y[k], f"Incoming_Flow_{k}"
        model += lpSum(x[(k, j)] for j in S) == y[k], f"Outgoing_Flow_{k}"

    # 约束3: 物理连接性约束
    for i in S:
        for j in S:
            model += x[(i, j)] <= connectivity[i, j], f"Connectivity_{i}_{j}"

    # 约束4: 路径起点约束
    if DESIGNATED_START_SEGMENT is not None:
        model += y[DESIGNATED_START_SEGMENT] == 1, "Designated_Start_Visited"
        model += lpSum(x[(i, DESIGNATED_START_SEGMENT)] for i in S) == 0, "No_Incoming_To_Start"
    else:
        print("   > 警告：未指定起点，模型可能不稳定或找到多个路径。")

    # 约束5: MTZ 消除子回路约束
    for i in S:
        for j in S:
            if i != j:
                model += u[i] - u[j] + 1 <= num_segments * (1 - x[(i, j)]), f"MTZ_{i}_{j}"

    print("4. 模型构建完成，开始求解...")
    solver = pulp.PULP_CBC_CMD(timeLimit=60, msg=1)
    model.solve(solver)

    status = LpStatus[model.status]
    print(f"5. 求解完成！状态: {status}")

    if status == 'Optimal':
        return x, y, model.objective.value()
    else:
        print("   > 未能找到最优解，请检查模型或数据。")
        return None, None, 0


# --- 模块3: 结果解析、展示与可视化 ---

def parse_and_display_results(x, y, total_score, segments_df, road_excel_path):
    """解析模型的解，以可读方式展示路径，并绘制可视化图。"""
    if total_score == 0 or x is None:
        print("\n未能找到最优解，无法展示路径或绘制图。")
        return

    print(f"\n--- 最优游览路径方案 ---")
    print(f"总综合评分: {total_score:.4f}")

    num_segments = len(segments_df)

    start_node = DESIGNATED_START_SEGMENT
    if start_node is None or y[start_node].value() < 0.5:
        print("错误：指定的起点未被包含在最终路径中或未指定起点。")
        return

    # 从起点开始，追踪整条路径
    path_segment_ids = [start_node]
    current_node = start_node

    next_node_map = {}
    for i in range(num_segments):
        for j in range(num_segments):
            # 使用正确的 x[(i, j)] 索引
            if i != j and x[(i, j)].value() > 0.5:
                next_node_map[i] = j

    while current_node in next_node_map:
        next_node = next_node_map[current_node]
        if next_node in path_segment_ids:
            print(f"警告: 检测到子回路在Segment {next_node}。路径提前终止。")
            break
        path_segment_ids.append(next_node)
        current_node = next_node

    print("路径顺序 (按Segment编号):")
    print(" -> ".join(map(str, path_segment_ids)))

    print("\n路径详情:")
    optimal_route_coords = []
    for i, segment_id in enumerate(path_segment_ids):
        segment_info = segments_df.loc[segment_id]
        print(f"  第{i + 1}步: Segment {segment_id} (综合分: {segment_info['Final_Score']:.3f})")
        optimal_route_coords.append(segment_info['Start_Point_Parsed'])
        if i == len(path_segment_ids) - 1:
            optimal_route_coords.append(segment_info['End_Point_Parsed'])

    # --- 可视化部分 ---
    print("\n正在生成路径可视化图...")
    try:
        road_raw_df_all = pd.read_excel(road_excel_path, sheet_name="道路", header=None,
                                        names=['raw_col_1', 'raw_col_2'], dtype=str)
        all_park_points = []
        for idx, row in road_raw_df_all.iterrows():
            for col in ['raw_col_1', 'raw_col_2']:
                coord = parse_coord_string(str(row[col]))
                if coord and (not all_park_points or all_park_points[-1] != coord):
                    all_park_points.append(coord)

        all_park_x = [p[0] for p in all_park_points]
        all_park_y = [p[1] for p in all_park_points]
        optimal_x = [p[0] for p in optimal_route_coords]
        optimal_y = [p[1] for p in optimal_route_coords]

        plt.figure(figsize=(12, 10))
        plt.plot(all_park_x, all_park_y, color='lightgray', linestyle='-', linewidth=0.8, alpha=0.7,
                 label='所有园区道路')
        plt.plot(optimal_x, optimal_y, color='red', linestyle='-', linewidth=3, marker='o', markersize=6,
                 label='最优游览路径')

        if len(optimal_x) > 0:
            plt.plot(optimal_x[0], optimal_y[0], 'go', markersize=10, label='起点')
            plt.text(optimal_x[0], optimal_y[0] + 100, '起点', color='green', fontsize=12)
        if len(optimal_x) > 1:
            plt.plot(optimal_x[-1], optimal_y[-1], 'bs', markersize=10, label='终点')
            plt.text(optimal_x[-1], optimal_y[-1] + 100, '终点', color='blue', fontsize=12)

        plt.title('拙政园最优游览路径')
        plt.xlabel('X坐标')
        plt.ylabel('Y坐标')
        plt.grid(True, linestyle=':', alpha=0.6)
        plt.legend()
        plt.gca().set_aspect('equal', adjustable='box')
        plt.tight_layout()

        output_image_path = r'H:\OneDrive\Desktop\optimal_tour_path.png'
        plt.savefig(output_image_path, dpi=300)
        print(f"路径图已保存到: {output_image_path}")
        plt.show()

    except Exception as e:
        print(f"绘制路径图时发生错误: {e}")


# --- 主程序入口 ---
if __name__ == "__main__":
    segments_df, scores, connectivity = load_and_prepare_data(SCORE_MATRIX_PATH)

    if segments_df is not None:
        x, y, total_score = build_and_solve_model(segments_df, scores, connectivity)

        parse_and_display_results(x, y, total_score, segments_df, ROAD_EXCEL_PATH)