import pandas as pd
import numpy as np
import re
from sklearn.preprocessing import MinMaxScaler

# --- 您的文件路径配置 (无需修改) ---
interest_file_path = r'H:\OneDrive\Desktop\趣味性评分矩阵.xlsx'
road_raw_file_path = r'H:\OneDrive\Desktop\F题\赛题F江南古典园林美学特征建模附件资料\1. 拙政园\4-拙政园数据坐标.xlsx'
output_path = r'H:\OneDrive\Desktop\final_scoring_matrix_v4_final.csv'  # 保存为最终版


def parse_coord_string(s):
    try:
        numbers = [float(num) for num in re.findall(r'-?\d+\.\d+', s)]
        if len(numbers) >= 2: return (numbers[0], numbers[1])
    except (ValueError, TypeError):
        return None
    return None


def calculate_angle(p1, p2, p3):
    v1 = np.array(p1) - np.array(p2)
    v2 = np.array(p3) - np.array(p2)
    mag_v1 = np.linalg.norm(v1)
    mag_v2 = np.linalg.norm(v2)
    if mag_v1 == 0 or mag_v2 == 0: return 0
    cos_angle = np.clip(np.dot(v1, v2) / (mag_v1 * mag_v2), -1.0, 1.0)
    return np.arccos(cos_angle)


print("--- 开始处理 ---")

# --- 第1步：读取并解析原始道路坐标数据 ---
try:
    road_raw_df = pd.read_excel(road_raw_file_path, sheet_name="道路", header=None, names=['raw_col_1', 'raw_col_2'],
                                dtype=str)
    all_points = []
    for index, row in road_raw_df.iterrows():
        for col in ['raw_col_1', 'raw_col_2']:
            coord = parse_coord_string(str(row[col]))
            if coord and (not all_points or all_points[-1] != coord):
                all_points.append(coord)
    # 将所有原始点转换为Numpy数组，为高速距离计算做准备
    all_points_np = np.array(all_points)
    print(f"1. 成功解析出 {len(all_points_np)} 个原始坐标点。")
except Exception as e:
    print(f"读取'道路'工作表时发生错误: {e}")
    exit()

# --- 第2步：读取趣味性评分数据 ---
try:
    interest_df = pd.read_excel(interest_file_path, sheet_name=0)
    interest_df['Start_Point_Tuple'] = interest_df['Start_Point'].apply(
        lambda s: tuple(map(float, s.strip('()').split(', '))))
    interest_df['End_Point_Tuple'] = interest_df['End_Point'].apply(
        lambda s: tuple(map(float, s.strip('()').split(', '))))
    print("2. 成功读取趣味性评分文件。")
except Exception as e:
    print(f"读取趣味性评分文件时发生错误: {e}")
    exit()

# --- 第3步：【最终修正逻辑】 - 使用“最近点匹配”为宏观路径段计算总转折度 ---
print("3. 使用'最近点匹配'算法，为每一条宏观路径段计算其内部的总转折度...")
turn_scores = []
for index, row in interest_df.iterrows():
    start_p = np.array(row['Start_Point_Tuple'])
    end_p = np.array(row['End_Point_Tuple'])

    # 计算距离并找到最近点的索引
    start_dist = np.linalg.norm(all_points_np - start_p, axis=1)
    end_dist = np.linalg.norm(all_points_np - end_p, axis=1)
    start_index = np.argmin(start_dist)
    end_index = np.argmin(end_dist)

    total_turn = 0
    if start_index < end_index:  # 确保顺序正确
        sub_path = all_points[start_index: end_index + 1]
        if len(sub_path) > 2:
            for i in range(1, len(sub_path) - 1):
                angle = calculate_angle(sub_path[i - 1], sub_path[i], sub_path[i + 1])
                total_turn += (np.pi - angle)  # 累加转弯程度

    turn_scores.append(total_turn)

interest_df['Turn'] = turn_scores
print("   > Turn值计算完成。")

# --- 第4步：归一化并计算最终得分 ---
final_df = interest_df.copy()
scaler = MinMaxScaler()
final_df['Normalized_Path_Score'] = scaler.fit_transform(final_df[['Turn']])
final_df['Normalized_Interest_Score'] = scaler.fit_transform(final_df[['Interest_Score']])
final_df['Final_Score'] = 0.5 * final_df['Normalized_Interest_Score'] + 0.5 * final_df['Normalized_Path_Score']
print("4. 完成归一化处理并计算最终综合评分。")

# --- 第5步：保存结果 ---
final_df = final_df.drop(columns=['Start_Point_Tuple', 'End_Point_Tuple'])
final_df = final_df[['Segment', 'Start_Point', 'End_Point', 'Interest_Score', 'Turn', 'Normalized_Interest_Score',
                     'Normalized_Path_Score', 'Final_Score']]
final_df.to_csv(output_path, index=False, encoding='utf-8-sig')

print("\n--- 处理完成 ---")
print(f"最终的评分矩阵已成功保存到:\n   {output_path}")
# print("\n最终结果预览（前5行）:")
# print(final_df.head().to_markdown(index=False))