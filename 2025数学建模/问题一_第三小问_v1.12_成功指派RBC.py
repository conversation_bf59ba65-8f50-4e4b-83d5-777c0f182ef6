import pandas as pd
import re
import matplotlib.pyplot as plt
from scipy.spatial.distance import cdist
import heapq
import numpy as np
import pulp as pl
from collections import deque
import os
from datetime import datetime

# 带灰色线

# --- 尝试导入可选的、用于绘图的库 ---
try:
    from shapely.geometry import Point, LineString
    import networkx as nx

    VISUALIZATION_LIB_AVAILABLE = True
    print("用于精细绘图的'shapely'和'networkx'库已找到。")
except ImportError:
    VISUALIZATION_LIB_AVAILABLE = False
    print("警告：未安装 'shapely' 或 'networkx' 库。背景路网将使用简化方法绘制。")
    print("建议运行: pip install shapely networkx")

# --- 配置中文字体 ---
try:
    plt.rcParams['font.sans-serif'] = ['SimHei']
    plt.rcParams['axes.unicode_minus'] = False
except Exception:
    print("警告：未找到'SimHei'字体，图例可能显示不正常。")

# =================================================================
#  1. 数据加载与路径设置
# =================================================================
# --- 请根据您的文件位置修改这里的路径 ---
SCORE_MATRIX_PATH = r'H:\OneDrive\Desktop\final_scoring_matrix_v4_final.csv'
ROAD_EXCEL_PATH = r'H:\OneDrive\Desktop\F题\赛题F江南古典园林美学特征建模附件资料\1. 拙政园\4-拙政园数据坐标.xlsx'

try:
    print(f"正在从 '{SCORE_MATRIX_PATH}' 加载趣味性评分矩阵...")
    interest_scores_df_raw = pd.read_csv(SCORE_MATRIX_PATH)
    print(f"正在从 '{ROAD_EXCEL_PATH}' 加载园林数据...")
    road_df = pd.read_excel(ROAD_EXCEL_PATH, sheet_name='道路', header=None)
    print("所有必需文件成功加载！")
except FileNotFoundError as e:
    print(f"错误：无法找到文件 {e.filename}。请检查路径是否正确。")
    exit()


# =================================================================
#  2. E-CARGO 核心数据解析函数 (用于路径规划)
# =================================================================
def parse_road_data_for_routing(df):
    all_points = []
    for _, row in df.iterrows():
        coords_str = "".join(str(s) for s in row if pd.notna(s))
        points = re.findall(r'\{([-\d\.]+),\s*([-\d\.]+),\s*([-\d\.]+)\}', coords_str)
        for p in points:
            all_points.append((float(p[0]), float(p[1])))

    if not all_points: return pd.DataFrame(), pd.DataFrame()

    nodes_df = pd.DataFrame(all_points, columns=['x', 'y']).drop_duplicates().reset_index(drop=True)
    nodes_df['node_id'] = nodes_df.index
    coord_to_id = {(round(row.x, 4), round(row.y, 4)): row.node_id for _, row in nodes_df.iterrows()}

    edges = []
    for _, row in df.iterrows():
        coords_str = "".join(str(s) for s in row if pd.notna(s))
        points_in_row = re.findall(r'\{([-\d\.]+),\s*([-\d\.]+),\s*([-\d\.]+)\}', coords_str)
        current_nodes = []
        for p in points_in_row:
            coord = (round(float(p[0]), 4), round(float(p[1]), 4))
            node_id = coord_to_id.get(coord)
            if node_id is not None: current_nodes.append(node_id)
        for i in range(len(current_nodes) - 1):
            u, v = sorted((current_nodes[i], current_nodes[i + 1]))
            if u != v: edges.append({'source': u, 'target': v})

    edges_df = pd.DataFrame(edges).drop_duplicates().reset_index(drop=True)
    return nodes_df, edges_df


def parse_and_map_interest_scores(interest_df, nodes_df, edges_df):
    edges_df['interest_score'] = 0.0
    node_coords_map = {row.node_id: {'x': row.x, 'y': row.y} for _, row in nodes_df.iterrows()}

    for _, row in interest_df.iterrows():
        start_coords_list = re.findall(r'\(([-\d\.]+),\s*([-\d\.]+)\)', str(row['Start_Point']))
        end_coords_list = re.findall(r'\(([-\d\.]+),\s*([-\d\.]+)\)', str(row['End_Point']))

        if start_coords_list and end_coords_list:
            start_coord = (float(start_coords_list[0][0]), float(start_coords_list[0][1]))
            end_coord = (float(end_coords_list[0][0]), float(end_coords_list[0][1]))
            interest_mid_x, interest_mid_y = (start_coord[0] + end_coord[0]) / 2, (start_coord[1] + end_coord[1]) / 2
            min_dist, best_edge_idx = float('inf'), -1
            for idx, edge in edges_df.iterrows():
                p1, p2 = node_coords_map.get(edge['source']), node_coords_map.get(edge['target'])
                if p1 and p2:
                    edge_mid_x, edge_mid_y = (p1['x'] + p2['x']) / 2, (p1['y'] + p2['y']) / 2
                    dist = (edge_mid_x - interest_mid_x) ** 2 + (edge_mid_y - interest_mid_y) ** 2
                    if dist < min_dist: min_dist, best_edge_idx = dist, idx
            if best_edge_idx != -1:
                edges_df.loc[best_edge_idx, 'interest_score'] += float(row['Interest_Score'])
    return edges_df


# =========================================================================
#  3. 用于生成精细背景图的函数 (使用您最初提供的更强大的版本)
# =========================================================================
def parse_road_coordinates_for_background(df):
    """
    使用更复杂但更健壮的逻辑解析道路坐标，以处理数据中可能的分段。
    此函数直接从您早期提供的代码改编而来。
    """
    # Excel可能将数据读取到多个列中，我们先关注第一列
    # 并假设主要数据结构在那里
    coord_series = df.iloc[:, 0].dropna()
    segments = []
    current_segment_points = []

    # 匹配坐标点，例如 {x, y, z}
    coord_pattern = re.compile(r'\{([\d.-]+),\s*([\d.-]+),\s*([\d.-]+)\}')
    # 匹配分段标记，例如 {0;123}，这表示一条路的结束
    segment_separator_pattern = re.compile(r'\{0;\s*\d+\}')

    for item in coord_series:
        item_str = str(item)
        if segment_separator_pattern.search(item_str):
            # 如果遇到分段标记，且当前段有超过1个点，则保存
            if current_segment_points and len(current_segment_points) > 1:
                segments.append(LineString(current_segment_points))
            # 重置点列表以开始新的一段
            current_segment_points = []

        # 在字符串中查找所有坐标点
        points_in_str = re.findall(coord_pattern, item_str)
        for p in points_in_str:
            # 保持原始毫米(mm)单位
            current_segment_points.append((float(p[0]), float(p[1])))

    # 循环结束后，保存最后一段（如果存在）
    if current_segment_points and len(current_segment_points) > 1:
        segments.append(LineString(current_segment_points))

    return segments


def build_path_graph_for_background(lines):
    """
    根据LineString列表构建一个NetworkX图。
    这个版本能够正确处理复杂线条的交叉点。
    此函数直接从您早期提供的代码改编而来。
    """
    G = nx.Graph()
    if not lines: return G

    # 1. 将所有LineString拆分成最小的直线段
    all_segments = []
    for line in lines:
        coords = list(line.coords)
        for i in range(len(coords) - 1):
            segment = LineString([coords[i], coords[i + 1]])
            all_segments.append(segment)

    # 2. 收集所有端点和交叉点作为图的节点
    node_coords_set = set()
    for seg in all_segments:
        node_coords_set.add(seg.coords[0])
        node_coords_set.add(seg.coords[1])

    for i in range(len(all_segments)):
        for j in range(i + 1, len(all_segments)):
            if all_segments[i].crosses(all_segments[j]):
                intersection_point = all_segments[i].intersection(all_segments[j])
                if isinstance(intersection_point, Point):
                    node_coords_set.add(intersection_point.coords[0])

    # 3. 在原始路径上找到所有节点，并连接相邻节点
    node_list = list(node_coords_set)
    for line in lines:
        nodes_on_line = []
        for node in node_list:
            # 检查一个点是否几乎精确地在一条线上
            if line.distance(Point(node)) < 1e-9:
                # 记录点在线上的投影距离和点本身
                nodes_on_line.append((line.project(Point(node)), node))

        # 按投影距离排序，确保顺序正确
        nodes_on_line.sort()

        # 连接在同一条原始路径上相邻的节点
        for k in range(len(nodes_on_line) - 1):
            start_node, end_node = nodes_on_line[k][1], nodes_on_line[k + 1][1]
            if start_node != end_node:
                distance = Point(start_node).distance(Point(end_node))
                if distance > 1e-9:  # 避免添加长度为0的边
                    G.add_edge(start_node, end_node, length=distance)
    return G


# =================================================================
#  4. 核心建模与路径规划流程
# =================================================================
print("\n--- 开始执行E-CARGO建模 ---")
print("1. 正在解析数据用于路径规划...")
nodes_df, edges_df = parse_road_data_for_routing(road_df)
edges_df = parse_and_map_interest_scores(interest_scores_df_raw, nodes_df, edges_df)

if nodes_df.empty or edges_df.empty:
    print("错误：无法从数据中构建有效的道路网络。程序终止。");
    exit()

print(f"路网构建完成：包含 {len(nodes_df)} 个节点和 {len(edges_df)} 条边。")
graph = {i: [] for i in nodes_df['node_id']}
for _, edge in edges_df.iterrows():
    u, v = int(edge['source']), int(edge['target'])
    p1, p2 = nodes_df.loc[u], nodes_df.loc[v]
    dist = np.sqrt((p1['x'] - p2['x']) ** 2 + (p1['y'] - p2['y']) ** 2)
    interest = edge['interest_score']
    graph[u].append({'target': v, 'dist': dist, 'interest': interest})
    graph[v].append({'target': u, 'dist': dist, 'interest': interest})

print("2. 正在分析路网连通性...")
visited, components = set(), []
for node in graph:
    if node not in visited:
        component, q = [], deque([node])
        visited.add(node)
        while q:
            curr = q.popleft();
            component.append(curr)
            for edge in graph.get(curr, []):
                neighbor = edge['target']
                if neighbor not in visited: visited.add(neighbor); q.append(neighbor)
        components.append(component)
if not components:
    print("错误：未能从地图数据构建任何有效的路网图。");
    exit()
largest_component = max(components, key=len)
largest_component_set = set(largest_component)
print(f"路网包含 {len(components)} 个独立区域。已锁定最大区域（含 {len(largest_component)} 个节点）进行规划。")

NUM_ROLES = 8
MIN_ROLE_DISTANCE = 30000
print(f"3. 阶段一：在最大连通区域内筛选 {NUM_ROLES} 个核心景点作为'角色'...")
# ... (rest of the E-CARGO logic remains the same as it was working correctly)
interest_edges = edges_df[edges_df['interest_score'] > 0].copy()
interest_edges = interest_edges[
    interest_edges['source'].isin(largest_component_set) & interest_edges['target'].isin(largest_component_set)]
interest_edges['mid_x'] = interest_edges.apply(
    lambda r: (nodes_df.loc[r['source']]['x'] + nodes_df.loc[r['target']]['x']) / 2, axis=1)
interest_edges['mid_y'] = interest_edges.apply(
    lambda r: (nodes_df.loc[r['source']]['y'] + nodes_df.loc[r['target']]['y']) / 2, axis=1)
interest_edges['role_node_id'] = interest_edges.apply(lambda r: r['source'] if np.random.rand() > 0.5 else r['target'],
                                                      axis=1)
sorted_roles = interest_edges.sort_values(by='interest_score', ascending=False)
selected_roles = []
for _, role in sorted_roles.iterrows():
    if len(selected_roles) >= NUM_ROLES: break
    is_far_enough = True
    current_pos = np.array([role['mid_x'], role['mid_y']])
    for selected_role in selected_roles:
        selected_pos = np.array([selected_role['mid_x'], selected_role['mid_y']])
        if np.linalg.norm(current_pos - selected_pos) < MIN_ROLE_DISTANCE: is_far_enough = False; break
    if is_far_enough: selected_roles.append(role)
if not selected_roles:
    print("错误：未能找到满足条件的核心景点。请尝试减小 MIN_ROLE_DISTANCE 的值。");
    exit()
roles_df = pd.DataFrame(selected_roles)
role_node_ids = [int(x) for x in roles_df['role_node_id']]
print(f"已筛选出 {len(role_node_ids)} 个符合要求的角色。")

valid_nodes_df = nodes_df[nodes_df['node_id'].isin(largest_component_set)]
min_x, max_x, min_y = valid_nodes_df['x'].min(), valid_nodes_df['x'].max(), valid_nodes_df['y'].min()
entrance_idx = cdist([[max_x * 0.9 + min_x * 0.1, min_y]], valid_nodes_df[['x', 'y']]).argmin()
entrance_node_id = int(valid_nodes_df.iloc[entrance_idx].name)
exit_idx = cdist([[min_x * 0.9 + max_x * 0.1, min_y]], valid_nodes_df[['x', 'y']]).argmin()
exit_node_id = int(valid_nodes_df.iloc[exit_idx].name)
print(f"4. 已定义入口: 节点 {entrance_node_id}, 出口: 节点 {exit_node_id}。")

print("5. 阶段二：基于综合成本Q矩阵进行最优路径指派...")


def dijkstra_for_q_matrix(graph, start_node):
    dists = {node: [float('inf'), 0] for node in graph}
    if start_node not in dists: return None
    dists[start_node] = [0, 0];
    pq = [(0, start_node)]
    while pq:
        dist, curr = heapq.heappop(pq)
        if dist > dists[curr][0]: continue
        for edge_info in graph.get(curr, []):
            neighbor, weight, interest = edge_info['target'], edge_info['dist'], edge_info['interest']
            if dists[curr][0] + weight < dists[neighbor][0]:
                dists[neighbor] = [dists[curr][0] + weight, dists[curr][1] + interest]
                heapq.heappush(pq, (dists[neighbor][0], neighbor))
    return dists


critical_nodes = sorted(list(set([entrance_node_id] + role_node_ids + [exit_node_id])))
node_map = {node_id: i for i, node_id in enumerate(critical_nodes)}
n_crit = len(critical_nodes)
dist_matrix = np.full((n_crit, n_crit), np.inf)
interest_matrix = np.full((n_crit, n_crit), 0.0)
print("   - 正在计算关键节点间的距离与趣味性矩阵...")
for i, start_node in enumerate(critical_nodes):
    all_node_dists = dijkstra_for_q_matrix(graph, start_node)
    if all_node_dists is None: continue
    for j, end_node in enumerate(critical_nodes):
        if end_node in all_node_dists:
            dist_matrix[i, j], interest_matrix[i, j] = all_node_dists[end_node]
if np.isinf(dist_matrix).all():
    print("错误: 成本矩阵计算失败，所有关键节点之间似乎都不连通。");
    exit()
max_dist = np.max(dist_matrix[np.isfinite(dist_matrix)]) if np.any(np.isfinite(dist_matrix)) else 1
max_interest = np.max(interest_matrix) if np.max(interest_matrix) > 0 else 1
norm_dist = dist_matrix / max_dist
norm_interest = interest_matrix / max_interest
q_matrix = 0.5 * norm_dist - 0.5 * norm_interest
q_matrix[np.isinf(q_matrix)] = 1e6


def solve_tsp_with_pulp(cost_matrix, start_idx, end_idx):
    num_nodes = len(cost_matrix);
    prob = pl.LpProblem("Garden_Tour_TSP", pl.LpMinimize)
    x = pl.LpVariable.dicts("x", ((i, j) for i in range(num_nodes) for j in range(num_nodes) if i != j), cat='Binary')
    prob += pl.lpSum(cost_matrix[i][j] * x.get((i, j), 0) for i in range(num_nodes) for j in range(num_nodes) if i != j)
    for i in range(num_nodes):
        if i != end_idx: prob += pl.lpSum(x.get((i, j), 0) for j in range(num_nodes) if i != j) == 1
        if i != start_idx: prob += pl.lpSum(x.get((j, i), 0) for j in range(num_nodes) if i != j) == 1
    u = pl.LpVariable.dicts('u', range(num_nodes), lowBound=1, upBound=num_nodes - 1, cat='Continuous')
    for i in range(num_nodes):
        for j in range(num_nodes):
            if i != j and i != start_idx and j != start_idx:
                prob += u[i] - u[j] + (num_nodes - 1) * x.get((i, j), 0) <= num_nodes - 2
    prob.solve(pl.PULP_CBC_CMD(msg=0))
    if prob.status == pl.LpStatusOptimal:
        path, curr = [start_idx], start_idx
        while curr != end_idx and len(path) <= num_nodes:
            found_next = False
            for j in range(num_nodes):
                if (curr, j) in x and x[(curr, j)].varValue > 0.9:
                    path.append(j);
                    curr = j;
                    found_next = True;
                    break
            if not found_next:
                print(f"警告: 从节点 {curr} 出发未能找到下一跳, 路径构建中断。");
                break
        return path
    return None


start_idx, end_idx = node_map[entrance_node_id], node_map.get(exit_node_id, -1)
best_path_indices = solve_tsp_with_pulp(q_matrix, start_idx, end_idx)


def dijkstra_for_physical_path(graph, start_node, end_node):
    distances = {node: float('inf') for node in graph};
    paths = {node: [] for node in graph}
    if start_node not in distances: return []
    distances[start_node] = 0;
    pq = [(0, start_node, [])]
    while pq:
        dist, curr, path = heapq.heappop(pq)
        if dist > distances[curr]: continue
        path = path + [curr]
        if curr == end_node: return path
        for edge_info in graph.get(curr, []):
            neighbor, weight = edge_info['target'], edge_info['dist']
            if distances[curr] + weight < distances[neighbor]:
                distances[neighbor] = distances[curr] + weight
                heapq.heappush(pq, (distances[neighbor], neighbor, path))
    return []


final_tour_nodes = []
if best_path_indices:
    print("6. 已找到最优顺序，正在构建最终的连贯物理路径...")
    best_path_node_ids = [critical_nodes[i] for i in best_path_indices]
    for i in range(len(best_path_node_ids) - 1):
        start, end = best_path_node_ids[i], best_path_node_ids[i + 1]
        path_segment = dijkstra_for_physical_path(graph, start, end)
        if not path_segment:
            print(f"!!! 严重错误：无法找到从节点 {start} 到 {end} 的路径。");
            final_tour_nodes = [];
            break
        final_tour_nodes.extend(path_segment if not final_tour_nodes else path_segment[1:])
else:
    print("错误：未能使用PuLP求解器找到最优访问顺序。")

# =================================================================
#  5. 可视化最终结果
# =================================================================
if final_tour_nodes:
    print("7. 正在生成最终路线图...")
    fig, ax = plt.subplots(figsize=(20, 20))

    if VISUALIZATION_LIB_AVAILABLE:
        print("   - 正在使用 networkx 绘制精细的灰色背景路网...")
        try:
            # Step 1: Parse excel data into shapely LineString objects
            road_lines = parse_road_coordinates_for_background(road_df)
            print(f"   - 已为背景图解析出 {len(road_lines)} 条主要路径线段。")

            # Step 2: Build a graph from these lines, handling intersections
            background_graph = build_path_graph_for_background(road_lines)
            print(
                f"   - 已为背景图构建了包含 {background_graph.number_of_nodes()} 个节点和 {background_graph.number_of_edges()} 条边的网络。")

            # Step 3: Draw the graph if it's not empty
            if background_graph.number_of_edges() > 0:
                pos = {node: (node[0], node[1]) for node in background_graph.nodes()}
                nx.draw_networkx_edges(background_graph, pos, ax=ax, alpha=0.6, width=1.5, edge_color='lightgray')
            else:
                print("   - 警告：构建的背景图为空，不进行绘制。")

        except Exception as e:
            print(f"   - 绘制精细背景时发生未知错误: {e}。将不绘制背景。")
    else:
        print("   - networkx库不可用，跳过精细背景绘制。")

    # --- 绘制推荐路线和关键点 (此部分逻辑不变) ---
    path_coords = [(nodes_df.loc[nid]['x'], nodes_df.loc[nid]['y']) for nid in final_tour_nodes]
    ax.plot(*zip(*path_coords), color='red', linewidth=3.5, linestyle='-', zorder=10, label='推荐路线')
    ax.plot(nodes_df.loc[entrance_node_id]['x'], nodes_df.loc[entrance_node_id]['y'], 'p', color='lime', markersize=20,
            label='入口 (Agent)', zorder=11, markeredgecolor='black')
    ax.plot(nodes_df.loc[exit_node_id]['x'], nodes_df.loc[exit_node_id]['y'], 'h', color='cyan', markersize=20,
            label='出口', zorder=11, markeredgecolor='black')
    ax.plot(nodes_df.loc[role_node_ids]['x'], nodes_df.loc[role_node_ids]['y'], '*', color='gold', markersize=25,
            label='核心景点 (Roles)', zorder=12, markeredgecolor='black')

    ax.set_aspect('equal', adjustable='box')
    ax.set_title("E-CARGO模型游览路线规划 (趣味性与效率均衡)", fontsize=24)
    ax.set_xlabel("X 坐标 (mm)", fontsize=16);
    ax.set_ylabel("Y 坐标 (mm)", fontsize=16)
    ax.tick_params(axis='both', which='major', labelsize=12)
    ax.legend(prop={'size': 15});
    ax.grid(False)

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_filename = f"garden_tour_route_E-CARGO_{timestamp}.png"
    plt.savefig(output_filename, dpi=300, bbox_inches='tight')
    print(f"\n--- 任务完成！已生成最终路线图 '{output_filename}' ---")
    plt.show()

else:
    print("\n由于未能构建完整的路径，程序已结束，未生成路线图。")