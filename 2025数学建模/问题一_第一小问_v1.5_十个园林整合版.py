import pandas as pd
import re
import numpy as np
import os

# 十个园林汇总版+excel输出

# --- 核心依赖库检查 ---
try:
    from shapely.geometry import Point, LineString
    import networkx as nx
    import matplotlib.pyplot as plt
    import openpyxl  # 确保 openpyxl 已安装，用于写入xlsx文件
except ImportError:
    print("关键库未安装，请在您的环境中运行: pip install pandas numpy shapely networkx matplotlib openpyxl")
    exit()

# --- Matplotlib 全局设置 (支持中文) ---
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False


# ==============================================================================
#  核心分析函数 (与之前版本相同)
# ==============================================================================

def parse_road_coordinates(df):
    coord_series = df.iloc[:, 0].dropna()
    segments = []
    current_segment_points = []
    coord_pattern = re.compile(r'\d+\.\s*\{([\d.-]+),\s*([\d.-]+),\s*([\d.-]+)\}')
    segment_separator_pattern = re.compile(r'\{0;\s*\d+\}')
    for item in coord_series:
        item_str = str(item)
        if segment_separator_pattern.match(item_str):
            if current_segment_points and len(current_segment_points) > 1:
                segments.append(LineString(current_segment_points))
            current_segment_points = []
        else:
            match = coord_pattern.search(item_str)
            if match:
                x, y, z = map(float, match.groups())
                current_segment_points.append((x / 10, y / 10))
    if current_segment_points and len(current_segment_points) > 1:
        segments.append(LineString(current_segment_points))
    return segments


def build_path_graph(lines):
    G = nx.Graph()
    all_segments = []
    for line in lines:
        coords = list(line.coords)
        for i in range(len(coords) - 1):
            segment = LineString([coords[i], coords[i + 1]])
            all_segments.append(segment)
    node_coords_set = set()
    for seg in all_segments:
        node_coords_set.add(seg.coords[0])
        node_coords_set.add(seg.coords[1])
    for i in range(len(all_segments)):
        for j in range(i + 1, len(all_segments)):
            if all_segments[i].crosses(all_segments[j]):
                intersection_point = all_segments[i].intersection(all_segments[j])
                if isinstance(intersection_point, Point):
                    node_coords_set.add(intersection_point.coords[0])
    node_list = list(node_coords_set)
    for line in lines:
        nodes_on_line = []
        for node in node_list:
            if line.distance(Point(node)) < 1e-9:
                nodes_on_line.append((line.project(Point(node)), node))
        nodes_on_line.sort()
        for k in range(len(nodes_on_line) - 1):
            start_node, end_node = nodes_on_line[k][1], nodes_on_line[k + 1][1]
            if start_node != end_node:
                distance = Point(start_node).distance(Point(end_node))
                if distance > 1e-9:
                    G.add_edge(start_node, end_node, length=distance)
    return G


def analyze_graph_features(G):
    features = {"intersections": [], "sharp_turns": [], "terminals": [], "curve_points": []}
    for node, degree in G.degree():
        if degree == 1:
            features["terminals"].append(node)
        elif degree > 2:
            features["intersections"].append(node)
        elif degree == 2:
            p1, p2 = [n for n in G.neighbors(node)]
            v1 = np.array(p1) - np.array(node)
            v2 = np.array(p2) - np.array(node)
            cosine_angle = np.dot(v1, v2) / (np.linalg.norm(v1) * np.linalg.norm(v2))
            angle_rad = np.arccos(np.clip(cosine_angle, -1.0, 1.0))
            angle_deg = np.rad2deg(angle_rad)
            if angle_deg <= 90:
                features["intersections"].append(node)
            elif angle_deg < 160:
                features["sharp_turns"].append(node)
            else:
                features["curve_points"].append(node)
    features["total_length"] = sum(nx.get_edge_attributes(G, 'length').values())
    if G.number_of_nodes() > 1:
        features["network_density"] = nx.density(G)
    else:
        features["network_density"] = 0
    if G.number_of_edges() > 0:
        features["avg_segment_length"] = features["total_length"] / G.number_of_edges()
    else:
        features["avg_segment_length"] = 0
    features["num_connected_components"] = nx.number_connected_components(G)
    return features


# ==============================================================================
#  --- 重大修改: 可视化函数 ---
#  现在可以为每个园林生成两张不同的网络图
# ==============================================================================

def visualize_garden_graphs(G, features, garden_name, output_dir):
    """
    为单个园林生成并保存两种路径网络图:
    1. 只显示交叉点
    2. 显示交叉点和转折点
    """
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    pos = {node: (node[0], node[1]) for node in G.nodes()}

    # --- 图 1: 只显示交叉点 ---
    plt.figure(figsize=(16, 16))
    nx.draw_networkx_edges(G, pos, alpha=0.6, width=1.5, edge_color='gray')
    nx.draw_networkx_nodes(G, pos, nodelist=features['curve_points'], node_color='#cccccc', node_size=10)
    nx.draw_networkx_nodes(G, pos, nodelist=features['intersections'], node_color='blue', node_size=60,
                           label=f"交叉点 (≤90°) ({len(features['intersections'])})")

    plt.title(f"{garden_name} 路径网络图 (仅显示交叉点)", fontsize=30)
    plt.xlabel("X坐标 (厘米)", fontsize=28)
    plt.ylabel("Y坐标 (厘米)", fontsize=28)
    plt.legend(fontsize=20)
    plt.axis('equal')
    plt.grid(True)

    output_filename_1 = os.path.join(output_dir, f'{garden_name}_网络图_仅交叉点.png')
    plt.savefig(output_filename_1, dpi=300, bbox_inches='tight')
    print(f"  - 图1已保存: {output_filename_1}")
    plt.close()

    # --- 图 2: 显示交叉点和转折点 ---
    plt.figure(figsize=(16, 16))
    nx.draw_networkx_edges(G, pos, alpha=0.6, width=1.5, edge_color='gray')
    nx.draw_networkx_nodes(G, pos, nodelist=features['curve_points'], node_color='#cccccc', node_size=10)
    nx.draw_networkx_nodes(G, pos, nodelist=features['intersections'], node_color='blue', node_size=60,
                           label=f"交叉点 (≤90°) ({len(features['intersections'])})")
    nx.draw_networkx_nodes(G, pos, nodelist=features['sharp_turns'], node_color='red', node_size=80,
                           label=f"转折点 (90°-160°) ({len(features['sharp_turns'])})")

    plt.title(f"{garden_name} 路径网络图 (显示交叉点与转折点)", fontsize=30)
    plt.xlabel("X坐标 (厘米)", fontsize=28)
    plt.ylabel("Y坐标 (厘米)", fontsize=28)
    plt.legend(fontsize=20)
    plt.axis('equal')
    plt.grid(True)

    output_filename_2 = os.path.join(output_dir, f'{garden_name}_网络图_交叉点与转折点.png')
    plt.savefig(output_filename_2, dpi=300, bbox_inches='tight')
    print(f"  - 图2已保存: {output_filename_2}")
    plt.close()


# ==============================================================================
#  汇总柱状图函数 (与之前版本相同)
# ==============================================================================

def plot_summary_bar_chart(df, metric_col, title, ylabel, output_dir):
    plt.figure(figsize=(14, 8))
    bars = plt.bar(df['name'], df[metric_col], color=plt.cm.viridis(np.linspace(0, 1, len(df))))
    plt.title(title, fontsize=20)
    plt.ylabel(ylabel, fontsize=14)
    plt.xticks(rotation=45, ha='right', fontsize=12)
    plt.grid(axis='y', linestyle='--', alpha=0.7)
    for bar in bars:
        yval = bar.get_height()
        plt.text(bar.get_x() + bar.get_width() / 2.0, yval, f'{yval:.2f}', va='bottom', ha='center', fontsize=10)
    plt.tight_layout()
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    output_filename = os.path.join(output_dir, f'所有园林_{title}.png')
    plt.savefig(output_filename, dpi=300)
    print(f"汇总图表已保存: {output_filename}")
    plt.close()


# ==============================================================================
#  --- 主程序 ---
# ==============================================================================
if __name__ == '__main__':

    # 1. 配置园林数据
    garden_info = {
        '拙政园': ('1. 拙政园', '4-拙政园数据坐标.xlsx'),
        '留园': ('2. 留园', '4-留园数据坐标.xlsx'),
        '寄畅园': ('3. 寄畅园', '4-寄畅园数据坐标.xlsx'),
        '瞻园': ('4. 瞻园', '4-瞻园数据坐标.xlsx'),
        '豫园': ('5. 豫园', '4-豫园数据坐标.xlsx'),
        '秋霞圃': ('6. 秋霞圃', '4-秋霞圃数据坐标.xlsx'),
        '沈园': ('7. 沈园', '4-沈园数据坐标.xlsx'),
        '怡园': ('8. 怡园', '4-怡园数据坐标.xlsx'),
        '耦园': ('9. 耦园', '4-耦园数据坐标.xlsx'),
        '绮园': ('10. 绮园', '4-绮园数据坐标.xlsx'),
    }

    # 2. 定义根目录和输出目录
    base_data_directory = r'H:\OneDrive\Desktop\F题\赛题F江南古典园林美学特征建模附件资料'
    output_directory = r'H:\OneDrive\Desktop\F题\园林分析输出结果'

    all_garden_stats = []

    # 3. 循环处理每个园林
    for name, (folder, filename) in garden_info.items():
        print(f"\n{'=' * 20} 正在处理: {name} {'=' * 20}")
        file_path = os.path.join(base_data_directory, folder, filename)

        try:
            df = pd.read_excel(file_path, sheet_name='道路')
            print(f"成功读取文件: {file_path}")

            road_lines = parse_road_coordinates(df)
            path_graph = build_path_graph(road_lines)
            graph_features = analyze_graph_features(path_graph)

            print(f"\n--- {name} 路径网络分析结果 ---")
            print(f"  - 总路径长度: {graph_features['total_length']:.2f} 厘米")
            print(f"  - 交叉点 (≤90°) 数量: {len(graph_features['intersections'])}")
            print(f"  - 转折点 (90°-160°) 数量: {len(graph_features['sharp_turns'])}")
            print(f"  - 总节点数量: {path_graph.number_of_nodes()}")
            print(f"  - 总路径分段 (边) 数量: {path_graph.number_of_edges()}")

            # 调用新的可视化函数，生成两张图
            visualize_garden_graphs(path_graph, graph_features, name, output_directory)

            stats = {
                'name': name,
                'num_intersections': len(graph_features['intersections']),
                'num_sharp_turns': len(graph_features['sharp_turns']),
                'total_length': graph_features['total_length'],
                'total_nodes': path_graph.number_of_nodes(),
                'total_edges': path_graph.number_of_edges()
            }
            all_garden_stats.append(stats)

        except FileNotFoundError:
            print(f"!! 错误: 文件未找到，请检查路径: {file_path}")
        except Exception as e:
            print(f"!! 处理 {name} 时发生错误: {e}")

    # 4. 所有园林处理完毕，进行汇总
    if all_garden_stats:
        print(f"\n{'=' * 20} 所有园林处理完毕，开始生成汇总报告... {'=' * 20}")

        summary_df = pd.DataFrame(all_garden_stats)

        # 4.1 生成汇总对比柱状图
        plot_summary_bar_chart(summary_df, 'num_intersections', '交叉点数量对比', '数量 (个)', output_directory)
        plot_summary_bar_chart(summary_df, 'num_sharp_turns', '转折点数量对比', '数量 (个)', output_directory)
        plot_summary_bar_chart(summary_df, 'total_length', '总路径长度对比', '长度 (厘米)', output_directory)
        plot_summary_bar_chart(summary_df, 'total_nodes', '总节点数量对比', '数量 (个)', output_directory)
        plot_summary_bar_chart(summary_df, 'total_edges', '总路径分段数量对比', '数量 (个)', output_directory)

        # --- 新增功能: 将结果输出到Excel文件 ---
        print("\n正在生成Excel汇总报告...")
        excel_path = os.path.join(output_directory, '所有园林路径网络分析汇总.xlsx')

        # 为了Excel列名更友好，重命名DataFrame的列
        report_df = summary_df.rename(columns={
            'name': '园林名称',
            'total_length': '总路径长度(cm)',
            'num_intersections': '交叉点数量',
            'num_sharp_turns': '转折点数量',
            'total_nodes': '总节点数量',
            'total_edges': '总路径分段数量'
        })

        # 将总路径长度格式化为两位小数
        report_df['总路径长度(cm)'] = report_df['总路径长度(cm)'].map('{:.2f}'.format)

        try:
            report_df.to_excel(excel_path, index=False, engine='openpyxl')
            print(f"✔ Excel汇总报告已成功保存至: {excel_path}")
        except Exception as e:
            print(f"!! 保存Excel文件时出错: {e}")

        print("\n所有任务已完成！")
    else:
        print("\n没有成功处理任何园林数据，无法生成汇总报告。")