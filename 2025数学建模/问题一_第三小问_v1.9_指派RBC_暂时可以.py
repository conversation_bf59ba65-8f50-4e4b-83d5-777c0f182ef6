import pandas as pd
import re
import matplotlib.pyplot as plt
from scipy.spatial.distance import cdist
import heapq
import numpy as np
import pulp as pl
from collections import deque

# --- 配置中文字体 ---
try:
    plt.rcParams['font.sans-serif'] = ['SimHei']
    plt.rcParams['axes.unicode_minus'] = False
except Exception:
    print("警告：未找到'SimHei'字体，图例可能显示不正常。")

# --- 1. 数据加载 ---
SCORE_MATRIX_PATH = r'H:\OneDrive\Desktop\final_scoring_matrix_v4_final.csv'
ROAD_EXCEL_PATH = r'H:\OneDrive\Desktop\F题\赛题F江南古典园林美学特征建模附件资料\1. 拙政园\4-拙政园数据坐标.xlsx'

try:
    print(f"正在从 '{SCORE_MATRIX_PATH}' 加载趣味性评分矩阵...")
    interest_scores_df_raw = pd.read_csv(SCORE_MATRIX_PATH)
    print(f"正在从 '{ROAD_EXCEL_PATH}' 加载园林数据...")
    road_df = pd.read_excel(ROAD_EXCEL_PATH, sheet_name='道路', header=None)
    print("所有必需文件成功加载！")
except FileNotFoundError as e:
    print(f"错误：无法找到文件 {e.filename}。请检查路径是否正确。")
    exit()


# --- 2. 数据解析与处理 ---
def parse_road_data(df):
    all_points = []
    for _, row in df.iterrows():
        coords_str = str(row.iloc[0]) + str(row.iloc[1])
        points = re.findall(r'\{([-\d\.]+),\s*([-\d\.]+),\s*([-\d\.]+)\}', coords_str)
        for p in points: all_points.append((float(p[0]), float(p[1])))
    if not all_points: return pd.DataFrame(), pd.DataFrame()

    nodes_df = pd.DataFrame(all_points, columns=['x', 'y']).drop_duplicates().reset_index(drop=True)
    nodes_df['node_id'] = nodes_df.index

    edges = []
    for _, row in df.iterrows():
        coords_str = str(row.iloc[0]) + str(row.iloc[1])
        points_in_row = re.findall(r'\{([-\d\.]+),\s*([-\d\.]+),\s*([-\d\.]+)\}', coords_str)
        current_nodes = []
        for p in points_in_row:
            coord = (round(float(p[0]), 4), round(float(p[1]), 4))
            node_id = nodes_df[
                (np.isclose(nodes_df['x'], coord[0])) & (np.isclose(nodes_df['y'], coord[1]))].index.values
            if len(node_id) > 0: current_nodes.append(node_id[0])
        for i in range(len(current_nodes) - 1):
            u, v = sorted((current_nodes[i], current_nodes[i + 1]))
            if u != v: edges.append({'source': u, 'target': v})

    edges_df = pd.DataFrame(edges).drop_duplicates().reset_index(drop=True)
    return nodes_df, edges_df


def parse_and_map_interest_scores(interest_df, nodes_df, edges_df):
    edges_df['interest_score'] = 0.0
    node_coords = nodes_df.set_index('node_id')[['x', 'y']].to_dict('index')

    for _, row in interest_df.iterrows():
        start_coords_list = re.findall(r'\(([-\d\.]+),\s*([-\d\.]+)\)', str(row['Start_Point']))
        end_coords_list = re.findall(r'\(([-\d\.]+),\s*([-\d\.]+)\)', str(row['End_Point']))

        if start_coords_list and end_coords_list:
            start_coord = (float(start_coords_list[0][0]), float(start_coords_list[0][1]))
            end_coord = (float(end_coords_list[0][0]), float(end_coords_list[0][1]))
            min_dist, best_edge_idx = float('inf'), -1
            for idx, edge in edges_df.iterrows():
                p1, p2 = node_coords[edge['source']], node_coords[edge['target']]
                edge_mid_x, edge_mid_y = (p1['x'] + p2['x']) / 2, (p1['y'] + p2['y']) / 2
                interest_mid_x, interest_mid_y = (start_coord[0] + end_coord[0]) / 2, (
                            start_coord[1] + end_coord[1]) / 2
                dist = (edge_mid_x - interest_mid_x) ** 2 + (edge_mid_y - interest_mid_y) ** 2
                if dist < min_dist: min_dist, best_edge_idx = dist, idx
            if best_edge_idx != -1:
                edges_df.loc[best_edge_idx, 'interest_score'] = max(edges_df.loc[best_edge_idx, 'interest_score'],
                                                                    float(row['Interest_Score']))
    return edges_df


# --- 新增：寻找连通分量的函数 ---
def find_connected_components(graph):
    visited, components = set(), []
    for node in graph:
        if node not in visited:
            component, q = [], deque([node])
            visited.add(node)
            while q:
                curr = q.popleft()
                component.append(curr)
                for edge in graph.get(curr, []):
                    neighbor = edge['target']
                    if neighbor not in visited: visited.add(neighbor); q.append(neighbor)
            components.append(component)
    return components


print("正在解析数据...")
nodes_df, edges_df = parse_road_data(road_df)
edges_df = parse_and_map_interest_scores(interest_scores_df_raw, nodes_df, edges_df)

if nodes_df.empty or edges_df.empty:
    print("错误：无法构建道路网络。");
    exit()

print("启动E-CARGO建模与路径规划...")

# --- MODIFIED: 构建完整图并寻找最大连通区域 ---
graph = {i: [] for i in range(len(nodes_df))}
for _, edge in edges_df.iterrows():
    u, v = int(edge['source']), int(edge['target'])
    p1, p2 = nodes_df.loc[u], nodes_df.loc[v]
    dist = np.sqrt((p1['x'] - p2['x']) ** 2 + (p1['y'] - p2['y']) ** 2)
    interest = edge['interest_score']
    graph[u].append({'target': v, 'dist': dist, 'interest': interest})
    graph[v].append({'target': u, 'dist': dist, 'interest': interest})

print("正在分析路网连通性...")
components = find_connected_components(graph)
if not components:
    print("错误：未能从地图数据构建任何有效的路网图。");
    exit()
largest_component = max(components, key=len)
largest_component_set = set(largest_component)
print(f"路网包含 {len(components)} 个独立的区域。已锁定最大的区域，其中包含 {len(largest_component)} 个节点进行规划。")

# =================================================================
#  阶段一：角色选择 (Role Selection) - 确保角色在最大连通区域内
# =================================================================
NUM_ROLES = 8
MIN_ROLE_DISTANCE = 30000
print(f"阶段一：在最大连通区域内筛选 {NUM_ROLES} 个核心景点作为 '角色'...")
interest_edges = edges_df[edges_df['interest_score'] > 0].copy()
interest_edges = interest_edges[interest_edges['source'].isin(largest_component_set)]

interest_edges['mid_x'] = interest_edges.apply(
    lambda r: (nodes_df.loc[r['source']]['x'] + nodes_df.loc[r['target']]['x']) / 2, axis=1)
interest_edges['mid_y'] = interest_edges.apply(
    lambda r: (nodes_df.loc[r['source']]['y'] + nodes_df.loc[r['target']]['y']) / 2, axis=1)
interest_edges['role_node_id'] = interest_edges['source']
sorted_roles = interest_edges.sort_values(by='interest_score', ascending=False)

selected_roles = []
for _, role in sorted_roles.iterrows():
    if len(selected_roles) >= NUM_ROLES: break
    is_far_enough = True
    current_pos = np.array([role['mid_x'], role['mid_y']])
    for selected_role in selected_roles:
        selected_pos = np.array([selected_role['mid_x'], selected_role['mid_y']])
        if np.linalg.norm(current_pos - selected_pos) < MIN_ROLE_DISTANCE: is_far_enough = False; break
    if is_far_enough: selected_roles.append(role)

roles_df = pd.DataFrame(selected_roles)
if roles_df.empty:
    print("错误：在最大连通区域内未能找到足够满足条件的核心景点。");
    exit()
role_node_ids = [int(x) for x in roles_df['role_node_id']]
print(f"已筛选出 {len(role_node_ids)} 个符合要求的角色（核心景点）。")

# --- MODIFIED: 确保出入口也在最大连通区域内 ---
valid_nodes_df = nodes_df[nodes_df['node_id'].isin(largest_component_set)]
min_x, max_x = valid_nodes_df['x'].min(), valid_nodes_df['x'].max()
min_y = valid_nodes_df['y'].min()
entrance_idx = cdist([[max_x * 0.9 + min_x * 0.1, min_y]], valid_nodes_df[['x', 'y']]).argmin()
entrance_node_id = int(valid_nodes_df.iloc[entrance_idx].name)
exit_idx = cdist([[min_x * 0.9 + max_x * 0.1, min_y]], valid_nodes_df[['x', 'y']]).argmin()
exit_node_id = int(valid_nodes_df.iloc[exit_idx].name)

# =================================================================
#  阶段二：最优路径规划 (Agent Assignment)
# =================================================================
print("阶段二：基于综合成本Q进行最优路径指派...")


def dijkstra_for_q_matrix(graph, start_node):
    dists = {node: [float('inf'), 0] for node in graph}
    dists[start_node] = [0, 0]
    pq = [(0, start_node)]
    while pq:
        dist, curr = heapq.heappop(pq)
        if dist > dists[curr][0]: continue
        for edge_info in graph.get(curr, []):
            neighbor, weight, interest = edge_info['target'], edge_info['dist'], edge_info['interest']
            if dists[curr][0] + weight < dists[neighbor][0]:
                dists[neighbor][0] = dists[curr][0] + weight
                dists[neighbor][1] = dists[curr][1] + interest
                heapq.heappush(pq, (dists[neighbor][0], neighbor))
    return dists


critical_nodes = sorted(list(set([entrance_node_id] + role_node_ids + [exit_node_id])))
node_map = {node_id: i for i, node_id in enumerate(critical_nodes)}
n_crit = len(critical_nodes)
dist_matrix = np.full((n_crit, n_crit), float('inf'))
interest_matrix = np.full((n_crit, n_crit), 0.0)

print("正在计算关键节点间的距离与趣味性矩阵...")
for i, start_node in enumerate(critical_nodes):
    dists = dijkstra_for_q_matrix(graph, start_node)
    for j, end_node in enumerate(critical_nodes):
        dist_matrix[i, j], interest_matrix[i, j] = dists[end_node]


def normalize(matrix):
    min_val, max_val = matrix.min(), matrix.max()
    if max_val == min_val: return np.zeros_like(matrix)
    return (matrix - min_val) / (max_val - min_val)


norm_dist, norm_interest = normalize(dist_matrix), normalize(interest_matrix)
q_matrix = 0.5 * norm_dist - 0.5 * norm_interest


def solve_tsp_with_pulp(cost_matrix, start_idx, end_idx):
    num_nodes = len(cost_matrix)
    prob = pl.LpProblem("Garden_Tour_TSP", pl.LpMinimize)
    x = pl.LpVariable.dicts("x", ((i, j) for i in range(num_nodes) for j in range(num_nodes)), cat='Binary')
    prob += pl.lpSum(cost_matrix[i][j] * x[i, j] for i in range(num_nodes) for j in range(num_nodes))
    for i in range(num_nodes):
        if i != end_idx: prob += pl.lpSum(x[i, j] for j in range(num_nodes) if i != j) == 1
        if i != start_idx: prob += pl.lpSum(x[j, i] for j in range(num_nodes) if i != j) == 1
    u = pl.LpVariable.dicts('u', range(num_nodes), lowBound=1, upBound=num_nodes - 1, cat='Continuous')
    for i in range(num_nodes):
        for j in range(num_nodes):
            if i != j and (i != start_idx and j != start_idx):
                prob += u[i] - u[j] + (num_nodes - 1) * x[i, j] <= num_nodes - 2
    prob.solve(pl.PULP_CBC_CMD(msg=0))
    if prob.status == pl.LpStatusOptimal:
        path, curr = [start_idx], start_idx
        while curr != end_idx:
            for j in range(num_nodes):
                if j != curr and x[curr, j].varValue == 1: path.append(j); curr = j; break
        return path
    return None


start_idx, end_idx = node_map[entrance_node_id], node_map[exit_node_id]
best_path_indices = solve_tsp_with_pulp(q_matrix, start_idx, end_idx)


def dijkstra_for_physical_path(graph, start_node):
    distances = {node: float('inf') for node in graph}
    paths = {node: [] for node in graph}
    distances[start_node] = 0
    pq = [(0, start_node, [])]
    while pq:
        dist, curr, path = heapq.heappop(pq)
        if dist > distances[curr]: continue
        path = path + [curr]
        paths[curr] = path
        for edge_info in graph.get(curr, []):
            neighbor, weight = edge_info['target'], edge_info['dist']
            if distances[curr] + weight < distances[neighbor]:
                distances[neighbor] = distances[curr] + weight
                heapq.heappush(pq, (distances[neighbor], neighbor, path))
    return paths


final_tour_nodes = []
if best_path_indices:
    print("已找到最优顺序，正在构建最终的连贯路径...")
    best_path_node_ids = [critical_nodes[i] for i in best_path_indices]
    for i in range(len(best_path_node_ids) - 1):
        start, end = best_path_node_ids[i], best_path_node_ids[i + 1]
        paths = dijkstra_for_physical_path(graph, start)
        path_segment = paths[end]
        if not path_segment:
            print(f"警告：构建路径时未能找到从 {start} 到 {end} 的路径段。");
            break
        if not final_tour_nodes:
            final_tour_nodes.extend(path_segment)
        else:
            final_tour_nodes.extend(path_segment[1:])
else:
    print("未能找到最优访问顺序。")

# --- 4. 可视化 ---
if final_tour_nodes:
    print("正在生成最终路线图...")
    fig, ax = plt.subplots(figsize=(15, 15))

    # --- NEW AND IMPROVED: 直接根据原始Excel数据绘制简洁的道路背景 ---
    print("正在根据原始Excel数据绘制简洁的道路背景...")
    for index, row in road_df.iterrows():
        # 从原始数据行中解析出所有坐标点
        coords_str = str(row.iloc[0]) + str(row.iloc[1])
        points_in_row = re.findall(r'\{([-\d\.]+),\s*([-\d\.]+),\s*([-\d\.]+)\}', coords_str)

        if len(points_in_row) > 1:
            # 将所有点解包成x和y坐标列表
            coords = [(float(p[0]), float(p[1])) for p in points_in_row]
            x_coords, y_coords = zip(*coords)

            # # 将这一行中的所有点连接成一条连续的线
            # ax.plot(x_coords, y_coords, color='lightgray', linewidth=1.5, zorder=1)

    # --- 以下绘制推荐路线和标记点的代码保持不变 ---
    path_coords = [(nodes_df.loc[nid]['x'], nodes_df.loc[nid]['y']) for nid in final_tour_nodes]
    ax.plot(*zip(*path_coords), color='red', linewidth=3.5, linestyle='-', zorder=10, label='推荐路线')

    ax.plot(nodes_df.loc[entrance_node_id]['x'], nodes_df.loc[entrance_node_id]['y'], 'p', color='lime', markersize=20,
            label='入口', zorder=11, markeredgecolor='black')
    ax.plot(nodes_df.loc[exit_node_id]['x'], nodes_df.loc[exit_node_id]['y'], 'h', color='cyan', markersize=20,
            label='出口', zorder=11, markeredgecolor='black')
    ax.plot(nodes_df.loc[role_node_ids]['x'], nodes_df.loc[role_node_ids]['y'], '*', color='gold', markersize=25,
            label='核心景点(角色)', zorder=12, markeredgecolor='black')

    ax.set_aspect('equal', adjustable='box')
    plt.title("E-CARGO模型游览路线 (趣味性+效率)", fontsize=20)
    plt.xlabel("X 坐标 (mm)");
    plt.ylabel("Y 坐标 (mm)")
    plt.legend(prop={'size': 15});
    plt.grid(False)

    output_filename = "garden_tour_route_E-CARGO_simplified_background.png"
    plt.savefig(output_filename, dpi=300, bbox_inches='tight')
    print(f"任务完成！已生成路线图 '{output_filename}'。")

    print("正在尝试显示绘图窗口...")
    plt.show()

else:
    print("\n由于未能构建完整的路径，程序已结束，未生成路线图。")