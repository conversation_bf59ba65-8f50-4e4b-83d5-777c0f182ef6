import pandas as pd
import numpy as np
import re
from sklearn.preprocessing import MinMaxScaler

# 可运行但是Turn值有错误

# --- 已经为您配置好的、您电脑上的文件路径 ---
interest_file_path = r'H:\OneDrive\Desktop\趣味性评分矩阵.xlsx'
road_raw_file_path = r'H:\OneDrive\Desktop\F题\赛题F江南古典园林美学特征建模附件资料\1. 拙政园\4-拙政园数据坐标.xlsx'


def parse_coord_string(s):
    """
    一个辅助函数，用于从复杂的字符串中解析出坐标。
    """
    try:
        numbers = [float(num) for num in re.findall(r'-?\d+\.\d+', s)]
        if len(numbers) >= 2:
            return (numbers[0], numbers[1])
    except (ValueError, TypeError):
        return None
    return None


def calculate_angle(p1, p2, p3):
    """
    计算由三个点p1, p2, p3构成的在p2点的夹角。
    """
    v1 = (p1[0] - p2[0], p1[1] - p2[1])
    v2 = (p3[0] - p2[0], p3[1] - p2[1])

    dot_product = v1[0] * v2[0] + v1[1] * v2[1]
    mag_v1 = np.sqrt(v1[0] ** 2 + v1[1] ** 2)
    mag_v2 = np.sqrt(v2[0] ** 2 + v2[1] ** 2)

    if mag_v1 == 0 or mag_v2 == 0:
        return np.pi

    cos_angle = dot_product / (mag_v1 * mag_v2)
    cos_angle = np.clip(cos_angle, -1.0, 1.0)

    angle = np.arccos(cos_angle)
    return angle


print("--- 开始处理 ---")

# --- 第1步：读取并解析原始道路坐标数据 ---
try:
    road_raw_df = pd.read_excel(road_raw_file_path, sheet_name="道路", header=None, names=['raw_col_1', 'raw_col_2'], dtype=str)
    print(f"1. 成功读取原始道路坐标文件:\n   {road_raw_file_path}")
except FileNotFoundError:
    print(f"错误：在以下路径找不到文件，请检查路径和文件名是否正确:\n   {road_raw_file_path}")
    exit()

all_points = []
for index, row in road_raw_df.iterrows():
    for col in ['raw_col_1', 'raw_col_2']:
        coord = parse_coord_string(str(row[col]))
        if coord and (not all_points or all_points[-1] != coord):
            all_points.append(coord)

print(f"2. 从原始数据中解析出 {len(all_points)} 个坐标点，并构建路径。")

# --- 第2步：计算每个点的转折度（Turn） ---
path_segments = []
for i in range(1, len(all_points) - 1):
    p_prev, p_curr, p_next = all_points[i - 1], all_points[i], all_points[i + 1]
    angle_rad = calculate_angle(p_prev, p_curr, p_next)
    path_segments.append({'Start_Point': p_prev, 'End_Point': p_curr, 'Turn': angle_rad})

if len(all_points) > 1:
    path_segments.append({'Start_Point': all_points[-2], 'End_Point': all_points[-1], 'Turn': np.pi})

turn_df = pd.DataFrame(path_segments)
print(f"3. 成功计算了 {len(turn_df)} 条路径段的转折度（Turn）。")

# --- 第3步：读取趣味性评分数据 ---
try:
    interest_df = pd.read_excel(interest_file_path, sheet_name=0)
    print(f"4. 成功读取趣味性评分文件:\n   {interest_file_path}")
except FileNotFoundError:
    print(f"错误：在以下路径找不到文件，请检查路径和文件名是否正确:\n   {interest_file_path}")
    exit()

interest_df['Start_Point'] = interest_df['Start_Point'].apply(lambda s: tuple(map(float, s.strip('()').split(', '))))
interest_df['End_Point'] = interest_df['End_Point'].apply(lambda s: tuple(map(float, s.strip('()').split(', '))))

# --- 第4步：合并转折度（Turn）和趣味性（Interest_Score） ---
turn_df['Start_Point_rounded'] = turn_df['Start_Point'].apply(lambda p: (round(p[0], 3), round(p[1], 3)))
turn_df['End_Point_rounded'] = turn_df['End_Point'].apply(lambda p: (round(p[0], 3), round(p[1], 3)))
interest_df['Start_Point_rounded'] = interest_df['Start_Point'].apply(lambda p: (round(p[0], 3), round(p[1], 3)))
interest_df['End_Point_rounded'] = interest_df['End_Point'].apply(lambda p: (round(p[0], 3), round(p[1], 3)))

final_df = pd.merge(interest_df, turn_df, on=['Start_Point_rounded', 'End_Point_rounded'], how='left')

final_df = final_df.drop(columns=['Start_Point_rounded', 'End_Point_rounded', 'Start_Point_y', 'End_Point_y'])
final_df = final_df.rename(columns={'Start_Point_x': 'Start_Point', 'End_Point_x': 'End_Point'})
final_df['Turn'] = final_df['Turn'].fillna(np.pi)
print("5. 成功将转折度评分与趣味性评分进行合并。")

# --- 第5步：归一化并计算最终得分 ---
scaler = MinMaxScaler()
final_df['Normalized_Path_Score'] = 1 - scaler.fit_transform(final_df[['Turn']])
final_df['Normalized_Interest_Score'] = scaler.fit_transform(final_df[['Interest_Score']])
final_df['Final_Score'] = 0.5 * final_df['Normalized_Interest_Score'] + 0.5 * final_df['Normalized_Path_Score']
print("6. 完成归一化处理并计算最终综合评分。")

# --- 第6步：保存结果 ---
final_df = final_df[['Segment', 'Start_Point', 'End_Point', 'Interest_Score', 'Turn', 'Normalized_Interest_Score',
                     'Normalized_Path_Score', 'Final_Score']]

# 将最终结果保存到您的桌面
output_path = r'H:\OneDrive\Desktop\final_scoring_matrix.csv'
final_df.to_csv(output_path, index=False, encoding='utf-8-sig')

print("\n--- 处理完成 ---")
print(f"最终的评分矩阵已成功保存到您的桌面:\n   {output_path}")
print("\n最终结果预览（前5行）:")
# print(final_df.head().to_markdown(index=False))