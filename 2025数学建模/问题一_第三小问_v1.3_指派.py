import pandas as pd
import numpy as np
import pulp as pl
import re
import matplotlib.pyplot as plt

# 无解

class TourOptimizer:
    def __init__(self, score_matrix_path, road_excel_path):
        """
        类的构造函数，在实例化时自动加载并预处理数据。
        """
        self.road_excel_path = road_excel_path
        self.segments_df = None
        self.scores = None
        self.connectivity = None
        self._load_and_prepare_data(score_matrix_path)

    def _parse_coord_string(self, s):
        """[内部辅助方法] 从字符串中解析出(x, y)坐标元组。"""
        try:
            numbers = [float(num) for num in re.findall(r'-?\d+\.\d+', s)]
            if len(numbers) >= 2: return (numbers[0], numbers[1])
        except (ValueError, TypeError):
            return None
        return None

    def _load_and_prepare_data(self, filepath):
        """[内部辅助方法] 加载完整的评分矩阵，并构建连接性矩阵。"""
        try:
            df = pd.read_csv(filepath)
            # 移除了预筛选逻辑，直接使用完整的DataFrame
            self.segments_df = df.copy()
            print(f"1. 成功读取评分矩阵，将使用全部 {len(self.segments_df)} 条路径段进行建模。")
        except FileNotFoundError:
            print(f"错误: 找不到文件 {filepath}。")
            return

        self.segments_df['Start_Point_Parsed'] = self.segments_df['Start_Point'].apply(
            lambda s: tuple(map(float, s.strip('()').split(', '))))
        self.segments_df['End_Point_Parsed'] = self.segments_df['End_Point'].apply(
            lambda s: tuple(map(float, s.strip('()').split(', '))))

        num_segments = len(self.segments_df)
        self.scores = self.segments_df['Final_Score'].to_dict()

        # 构建物理连接性矩阵
        self.connectivity = np.zeros((num_segments, num_segments))
        for i in range(num_segments):
            for j in range(num_segments):
                if i != j and np.allclose(self.segments_df.loc[i, 'End_Point_Parsed'],
                                          self.segments_df.loc[j, 'Start_Point_Parsed']):
                    self.connectivity[i, j] = 1

        print("2. 物理连接性矩阵构建完成。")

    def find_optimal_tour(self, designated_start_segment=0, time_limit=300):
        """
        构建并求解路径优化模型。这是核心的求解方法。
        """
        if self.segments_df is None:
            print("错误: 数据未成功加载，无法进行优化。")
            return None, 0

        num_segments = len(self.segments_df)
        S = range(num_segments)

        pro = pl.LpProblem("Garden_Tour_Optimization", pl.LpMaximize)
        print(f"3. 开始构建优化模型 (规模: {num_segments}条路径)...")

        x = pl.LpVariable.dicts("x", ((i, j) for i in S for j in S), cat='Binary')
        y = pl.LpVariable.dicts("y", S, cat='Binary')
        u = pl.LpVariable.dicts("u", S, lowBound=1, upBound=num_segments, cat='Continuous')

        pro += pl.lpSum(self.scores[i] * y[i] for i in S), "Total_Score_Objective"

        for k in S:
            pro += pl.lpSum(x[(i, k)] for i in S) == y[k]
            pro += pl.lpSum(x[(k, j)] for j in S) == y[k]

        for i in S:
            for j in S:
                pro += x[(i, j)] <= self.connectivity[i, j]
                if i != j:
                    pro += u[i] - u[j] + 1 <= num_segments * (1 - x[(i, j)])

        if designated_start_segment is not None:
            pro += y[designated_start_segment] == 1
            pro += pl.lpSum(x[(i, designated_start_segment)] for i in S) == 0

        print(f"4. 模型构建完成，开始求解 (时间上限: {time_limit}秒)...")

        solver = pl.PULP_CBC_CMD(timeLimit=time_limit, msg=1)
        pro.solve(solver)

        status = pl.LpStatus[pro.status]
        print(f"5. 求解完成！状态: {status}")

        if pro.status == pl.LpStatusOptimal:
            total_score = pl.value(pro.objective)
            path = self._parse_path_from_solution(x, y, designated_start_segment)
            return path, total_score
        else:
            print(
                "   > 未能找到最优解。如果状态为'Infeasible'，可能是因为在完整数据中，指定的起点依然无法连接成一条有效路径。")
            return None, 0

    def _parse_path_from_solution(self, x, y, start_node):
        num_segments = len(self.segments_df)
        if start_node is None or y[start_node].value() < 0.5:
            return []
        path = [start_node]
        current_node = start_node
        next_node_map = {i: j for i in range(num_segments) for j in range(num_segments) if
                         i != j and x[(i, j)].value() > 0.5}
        while current_node in next_node_map:
            next_node = next_node_map[current_node]
            if next_node in path: break
            path.append(next_node)
            current_node = next_node
        return path

    def visualize_tour(self, path):
        if not path:
            print("路径为空，无法进行可视化。")
            return
        print("\n正在生成路径可视化图...")
        try:
            optimal_route_coords = []
            for i, segment_idx in enumerate(path):
                segment_info = self.segments_df.loc[segment_idx]
                optimal_route_coords.append(segment_info['Start_Point_Parsed'])
                if i == len(path) - 1:
                    optimal_route_coords.append(segment_info['End_Point_Parsed'])
            road_raw_df = pd.read_excel(self.road_excel_path, sheet_name="道路", header=None,
                                        names=['raw_col_1', 'raw_col_2'], dtype=str)
            all_park_points = [self._parse_coord_string(str(row[col])) for _, row in road_raw_df.iterrows() for col in
                               row if self._parse_coord_string(str(row[col]))]
            all_park_points_unique = []
            [all_park_points_unique.append(p) for p in all_park_points if p not in all_park_points_unique]
            all_park_x = [p[0] for p in all_park_points_unique]
            all_park_y = [p[1] for p in all_park_points_unique]
            optimal_x = [p[0] for p in optimal_route_coords]
            optimal_y = [p[1] for p in optimal_route_coords]
            plt.figure(figsize=(12, 10))
            plt.plot(all_park_x, all_park_y, color='lightgray', linestyle='-', linewidth=0.8, alpha=0.7,
                     label='所有园区道路')
            plt.plot(optimal_x, optimal_y, color='red', linestyle='-', linewidth=3, marker='o', markersize=6,
                     label='最优游览路径')
            if optimal_x:
                plt.plot(optimal_x[0], optimal_y[0], 'go', markersize=12, label='起点')
                plt.plot(optimal_x[-1], optimal_y[-1], 'bs', markersize=12, label='终点')
            plt.title('拙政园最优游览路径')
            plt.xlabel('X坐标')
            plt.ylabel('Y坐标')
            plt.grid(True, linestyle=':', alpha=0.6)
            plt.legend()
            plt.gca().set_aspect('equal', adjustable='box')
            plt.tight_layout()
            output_image_path = r'H:\OneDrive\Desktop\optimal_tour_path_full.png'
            plt.savefig(output_image_path, dpi=300)
            print(f"路径图已保存到: {output_image_path}")
            plt.show()
        except Exception as e:
            print(f"绘制路径图时发生错误: {e}")


# --- 主程序入口 ---
if __name__ == "__main__":
    SCORE_MATRIX_PATH = r'H:\OneDrive\Desktop\final_scoring_matrix_v4_final.csv'
    ROAD_EXCEL_PATH = r'H:\OneDrive\Desktop\F题\赛题F江南古典园林美学特征建模附件资料\1. 拙政园\4-拙政园数据坐标.xlsx'

    # ------------------- 您可以在这里调整参数 -------------------
    # 起点Segment的编号 (在518条路中的原始索引)
    START_SEGMENT_INDEX = 0
    # 求解时间上限（秒）。对于完整数据集，建议设置一个较长的时间
    TIME_LIMIT_SECONDS = 600  # <<<<<< 建议设置为5-10分钟 (300-600秒)
    # ---------------------------------------------------------

    optimizer = TourOptimizer(score_matrix_path=SCORE_MATRIX_PATH,
                              road_excel_path=ROAD_EXCEL_PATH)

    if optimizer.segments_df is not None:
        optimal_path, total_score = optimizer.find_optimal_tour(designated_start_segment=START_SEGMENT_INDEX,
                                                                time_limit=TIME_LIMIT_SECONDS)

        if optimal_path:
            print("\n--- 最优游览路径方案 ---")
            print(f"总综合评分: {total_score:.4f}")
            print("路径顺序 (按原始索引):")
            print(" -> ".join(map(str, optimal_path)))
            optimizer.visualize_tour(optimal_path)