import pandas as pd
import re
import matplotlib.pyplot as plt
from scipy.spatial.distance import cdist
import heapq
import numpy as np
from collections import defaultdict

# --- 配置中文字体 ---
try:
    plt.rcParams['font.sans-serif'] = ['SimHei']
    plt.rcParams['axes.unicode_minus'] = False
except Exception:
    print("警告：未找到'SimHei'字体，图例可能显示不正常。")

# --- 1. 数据加载 ---
SCORE_MATRIX_PATH = r'H:\OneDrive\Desktop\final_scoring_matrix_v4_final.csv'
ROAD_EXCEL_PATH = r'H:\OneDrive\Desktop\F题\赛题F江南古典园林美学特征建模附件资料\1. 拙政园\4-拙政园数据坐标.xlsx'

try:
    print(f"正在从 '{SCORE_MATRIX_PATH}' 加载趣味性评分矩阵...")
    interest_scores_df_raw = pd.read_csv(SCORE_MATRIX_PATH)
    print(f"正在从 '{ROAD_EXCEL_PATH}' 加载园林数据...")
    road_df = pd.read_excel(ROAD_EXCEL_PATH, sheet_name='道路', header=None)
    print("所有必需文件成功加载！")
except FileNotFoundError as e:
    print(f"错误：无法找到文件 {e.filename}。")
    exit()
except Exception as e:
    print(f"读取文件时发生错误: {e}")
    exit()


# --- 2. 数据解析与处理 ---
def parse_road_data(df):
    all_points = []
    for index, row in df.iterrows():
        coords_str = str(row.iloc[0]) + str(row.iloc[1])
        points = re.findall(r'\{([-\d\.]+),\s*([-\d\.]+),\s*([-\d\.]+)\}', coords_str)
        for p in points: all_points.append((float(p[0]), float(p[1])))
    if not all_points: return pd.DataFrame(), pd.DataFrame()
    unique_points = pd.DataFrame(all_points, columns=['x', 'y']).drop_duplicates().reset_index(drop=True)
    unique_points['node_id'] = unique_points.index
    coord_to_id = {(round(row.x, 4), round(row.y, 4)): row.node_id for _, row in unique_points.iterrows()}
    edges, segment_points = [], []
    for index, row in df.iterrows():
        segment_marker = re.match(r'\s*\{\d+;\d+\}', str(row.iloc[0]))
        coords_str = str(row.iloc[0]) + str(row.iloc[1])
        points = re.findall(r'\{([-\d\.]+),\s*([-\d\.]+),\s*([-\d\.]+)\}', coords_str)
        if segment_marker and segment_points:
            for i in range(len(segment_points) - 1):
                p1, p2 = segment_points[i], segment_points[i + 1]
                node1_id, node2_id = coord_to_id.get((round(p1[0], 4), round(p1[1], 4))), coord_to_id.get(
                    (round(p2[0], 4), round(p2[1], 4)))
                if node1_id is not None and node2_id is not None and node1_id != node2_id:
                    edges.append({'source': node1_id, 'target': node2_id})
            segment_points = []
        for p in points: segment_points.append((float(p[0]), float(p[1])))
    if segment_points:
        for i in range(len(segment_points) - 1):
            p1, p2 = segment_points[i], segment_points[i + 1]
            node1_id, node2_id = coord_to_id.get((round(p1[0], 4), round(p1[1], 4))), coord_to_id.get(
                (round(p2[0], 4), round(p2[1], 4)))
            if node1_id is not None and node2_id is not None and node1_id != node2_id:
                edges.append({'source': node1_id, 'target': node2_id})
    return unique_points, pd.DataFrame(edges)


def parse_interest_scores(df):
    parsed_scores = []
    for _, row in df.iterrows():
        start_coords = re.findall(r'\(([-\d\.]+),\s*([-\d\.]+)\)', str(row['Start_Point']))
        end_coords = re.findall(r'\(([-\d\.]+),\s*([-\d\.]+)\)', str(row['End_Point']))
        if start_coords and end_coords:
            parsed_scores.append({'start_x': float(start_coords[0][0]), 'start_y': float(start_coords[0][1]),
                                  'end_x': float(end_coords[0][0]), 'end_y': float(end_coords[0][1]),
                                  'interest_score': float(row['Interest_Score'])})
    return pd.DataFrame(parsed_scores)


print("正在解析数据...")
nodes_df, edges_df = parse_road_data(road_df)
interest_coords_df = parse_interest_scores(interest_scores_df_raw)
if edges_df.empty:
    print("错误：无法构建道路网络，程序终止。")
    exit()
edges_df = edges_df.merge(nodes_df.rename(columns={'node_id': 'source', 'x': 'start_x', 'y': 'start_y'}), on='source')
edges_df = edges_df.merge(nodes_df.rename(columns={'node_id': 'target', 'x': 'end_x', 'y': 'end_y'}), on='target')

# --- 3. 最终优化版路径规划 ---
print("最终优化版路径规划启动 (最近邻 + 路径惩罚)...")

# =================================================================
#  阶段一：选择核心景点 (逻辑不变)
# =================================================================
NUM_POIS = 8
MIN_POI_DISTANCE = 30000
print(f"阶段一：筛选最多 {NUM_POIS} 个空间分散的核心景点...")
sorted_pois = interest_coords_df.sort_values(by='interest_score', ascending=False).copy()
sorted_pois['mid_x'] = (sorted_pois['start_x'] + sorted_pois['end_x']) / 2
sorted_pois['mid_y'] = (sorted_pois['start_y'] + sorted_pois['end_y']) / 2
filtered_pois = []
for index, poi in sorted_pois.iterrows():
    if len(filtered_pois) >= NUM_POIS: break
    is_far_enough = True
    current_pos = np.array([poi['mid_x'], poi['mid_y']])
    for selected_poi in filtered_pois:
        selected_pos = np.array([selected_poi['mid_x'], selected_poi['mid_y']])
        if np.linalg.norm(current_pos - selected_pos) < MIN_POI_DISTANCE:
            is_far_enough = False;
            break
    if is_far_enough: filtered_pois.append(poi.to_dict())
top_pois_df = pd.DataFrame(filtered_pois)
poi_midpoints = top_pois_df[['mid_x', 'mid_y']].values
print(f"已筛选出 {len(poi_midpoints)} 个符合距离要求的核心景点。")
poi_nodes = [cdist([midpoint], nodes_df[['x', 'y']]).argmin() for midpoint in poi_midpoints]
min_x, max_x, min_y, max_y = nodes_df['x'].min(), nodes_df['x'].max(), nodes_df['y'].min(), nodes_df['y'].max()
entrance_node_id = cdist([[max_x * 0.9 + min_x * 0.1, min_y]], nodes_df[['x', 'y']]).argmin()
exit_node_id = cdist([[min_x * 0.9 + max_x * 0.1, min_y]], nodes_df[['x', 'y']]).argmin()

# =================================================================
#  阶段二：使用最近邻策略 + 动态惩罚规划路径
# =================================================================
# 1. 准备工作：构建基础距离图和计算关键点之间的最短距离
distance_graph = {i: [] for i in range(len(nodes_df))}
for _, row in edges_df.iterrows():
    length = np.sqrt((row['start_x'] - row['end_x']) ** 2 + (row['start_y'] - row['end_y']) ** 2)
    distance_graph[int(row['source'])].append((int(row['target']), length))
    distance_graph[int(row['target'])].append((int(row['source']), length))


def dijkstra_shortest_path(graph, start_node):
    distances, paths = {n: float('inf') for n in graph}, {n: [] for n in graph}
    distances[start_node] = 0
    queue = [(0, start_node, [])]
    while queue:
        (cost, v, path) = heapq.heappop(queue)
        if cost > distances[v]: continue
        path = path + [v]
        paths[v] = path
        for (next_node, edge_cost) in graph.get(v, []):
            if cost + edge_cost < distances[next_node]:
                distances[next_node] = cost + edge_cost
                heapq.heappush(queue, (distances[next_node], next_node, path))
    return distances, paths


critical_nodes_list = sorted(list(set([entrance_node_id] + poi_nodes + [exit_node_id])))
node_map = {node_id: i for i, node_id in enumerate(critical_nodes_list)}
inv_node_map = {i: node_id for node_id, i in node_map.items()}
distance_matrix = np.full((len(critical_nodes_list), len(critical_nodes_list)), float('inf'))
print("正在计算关键节点间的最短距离矩阵...")
for i, start_node in enumerate(critical_nodes_list):
    distances, _ = dijkstra_shortest_path(distance_graph, start_node)
    for j, end_node in enumerate(critical_nodes_list):
        distance_matrix[i, j] = distances.get(end_node, float('inf'))

# 2. 决定访问顺序：最近邻贪心算法 (逻辑不变)
print("正在通过最近邻算法寻找连贯的游览顺序...")
start_idx, end_idx = node_map[entrance_node_id], node_map[exit_node_id]
poi_indices = {node_map[pid] for pid in poi_nodes if pid in node_map}
tour_indices = [start_idx]
current_idx = start_idx
while poi_indices:
    nearest_dist, nearest_poi_idx = float('inf'), -1
    for idx in poi_indices:
        if distance_matrix[current_idx, idx] < nearest_dist:
            nearest_dist, nearest_poi_idx = distance_matrix[current_idx, idx], idx
    if nearest_poi_idx != -1:
        tour_indices.append(nearest_poi_idx)
        poi_indices.remove(nearest_poi_idx)
        current_idx = nearest_poi_idx
    else:
        break
tour_indices.append(end_idx)
best_path_node_ids = [inv_node_map[i] for i in tour_indices]

# 3. **核心修改**：拼接最终路径时，加入动态惩罚
print("已找到游览顺序，正在通过动态惩罚构建最终路径以减少重复...")
# ##################################################################
# ## 您可以调整此参数：惩罚因子越大，越倾向于绕远路来避免重复 ##
# ##################################################################
PENALTY_FACTOR = 500.0
print(f"当前重复路线惩罚因子: {PENALTY_FACTOR}")

edge_usage = defaultdict(int)
final_tour_nodes = []

for i in range(len(best_path_node_ids) - 1):
    start_node, end_node = best_path_node_ids[i], best_path_node_ids[i + 1]

    # 基于当前已走过的路，构建一个带惩罚的动态图
    dynamic_graph = {n: [] for n in distance_graph}
    for u, neighbors in distance_graph.items():
        for v, length in neighbors:
            edge_key = tuple(sorted((u, v)))
            penalty = 1 + PENALTY_FACTOR * edge_usage[edge_key]
            dynamic_graph[u].append((v, length * penalty))

    # 在带惩罚的图上寻找最短路径
    distances, paths = dijkstra_shortest_path(dynamic_graph, start_node)
    path_segment = paths.get(end_node)

    # 如果因为惩罚过高导致找不到路，则回退到无惩罚的图上找一次
    if not path_segment:
        print(f"警告：从节点 {start_node} 到 {end_node} 的惩罚路径搜索失败，尝试无惩罚路径。")
        _, paths = dijkstra_shortest_path(distance_graph, start_node)
        path_segment = paths.get(end_node, [])

    # 更新走过的路径计数
    for j in range(len(path_segment) - 1):
        u, v = path_segment[j], path_segment[j + 1]
        edge_key = tuple(sorted((u, v)))
        edge_usage[edge_key] += 1

    # 拼接路径
    if not final_tour_nodes:
        final_tour_nodes.extend(path_segment)
    else:
        final_tour_nodes.extend(path_segment[1:])

# --- 4. 可视化 ---
if final_tour_nodes:
    print("正在生成最终地图...")
    fig, ax = plt.subplots(figsize=(15, 15))
    for _, row in edges_df.iterrows():
        ax.plot([row['start_x'], row['end_x']], [row['start_y'], row['end_y']], color='gray', linewidth=1, zorder=1,
                alpha=0.5)
    path_coords = [(nodes_df.loc[node_id]['x'], nodes_df.loc[node_id]['y']) for node_id in final_tour_nodes]
    ax.plot(*zip(*path_coords), color='red', linewidth=3, linestyle='-', zorder=10, label='推荐路线')
    start_coords, end_coords = nodes_df.loc[entrance_node_id], nodes_df.loc[exit_node_id]
    poi_coords = nodes_df.loc[poi_nodes]
    ax.plot(start_coords['x'], start_coords['y'], 'p', color='lime', markersize=20, label='入口 (Agent起点)', zorder=11,
            markeredgecolor='black')
    ax.plot(end_coords['x'], end_coords['y'], 'h', color='cyan', markersize=20, label='出口', zorder=11,
            markeredgecolor='black')
    ax.plot(poi_coords['x'], poi_coords['y'], '*', color='gold', markersize=20, label='核心景点 (Roles)', zorder=12,
            markeredgecolor='black')
    ax.set_aspect('equal', adjustable='box')
    plt.title("优化游览路线 (最近邻 + 路径惩罚)", fontsize=20)
    plt.xlabel("X 坐标 (mm)", fontsize=14)
    plt.ylabel("Y 坐标 (mm)", fontsize=14)
    plt.legend(prop={'size': 15})
    plt.grid(False)
    ax.set_facecolor('#f4f4f4')
    output_filename = "garden_tour_route_final.png"
    plt.savefig(output_filename, dpi=300, bbox_inches='tight')
    print(f"任务完成！已生成 '{output_filename}'。")
else:
    print("未能生成最终路线图。")