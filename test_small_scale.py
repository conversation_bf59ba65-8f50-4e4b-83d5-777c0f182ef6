#!/usr/bin/env python3
"""
测试小规模版本的无人机喷洒农药代码
"""

import numpy as np
import pulp as pl
from collections import defaultdict
import time
import math
import random
import matplotlib.pyplot as plt
from matplotlib.patches import Patch, Polygon
import pandas as pd
from datetime import datetime
import os
from matplotlib.colors import LinearSegmentedColormap
from matplotlib.path import Path

# 复制关键函数进行小规模测试
def generate_small_test_data():
    """生成小规模测试数据"""
    print("--- 生成小规模测试数据 ---")
    
    num_drones = 2
    num_points = 4
    time_period = 8  # 2天 * 4小时
    num_pesticides = 2
    
    params = {
        'num_drones': num_drones,
        'num_points': num_points, 
        'time_period': time_period,
        'num_pesticides': num_pesticides
    }
    
    # 简化的作物分配
    params['all_crop_assignments'] = {0: 0, 1: 0, 2: 1, 3: 1}
    
    # 简化的坐标
    params['points_coords'] = {
        0: (100, 100),
        1: (200, 100), 
        2: (100, 200),
        3: (200, 200)
    }
    params['base_station_coord'] = (0, 0)
    
    # 无人机参数
    params['La'] = np.array([20, 20])  # 载药量
    params['F_tasks'] = np.array([4, 4])  # 任务数限制
    params['F_time'] = np.array([30, 30])  # 时间限制
    params['zeta_max_flow'] = 6.0
    params['alpha'] = 0.05
    
    # 农艺参数
    zeta_standard = np.zeros((num_points, num_pesticides))
    L = np.zeros((num_points, num_pesticides))
    Q_base = np.zeros((num_points, num_pesticides))
    
    # 前两个点需要农药0，后两个点需要农药1
    zeta_standard[0:2, 0] = 1.5
    L[0:2, 0] = 1.5
    Q_base[0:2, 0] = 0.8
    
    zeta_standard[2:4, 1] = 1.0
    L[2:4, 1] = 1.0
    Q_base[2:4, 1] = 1.0
    
    params['zeta_standard'] = zeta_standard
    params['L'] = L
    params['L_max'] = np.ceil(L * 1.3)
    params['Q_base'] = Q_base
    
    # 时间参数
    params['mu_vec'] = np.array([2, 6])  # 理想时间
    params['delta_vec'] = np.array([2, 2])  # 时间窗口
    
    # 风速
    params['wind_speed'] = np.array([2.0, 2.0, 3.0, 3.0, 2.5, 2.5, 2.0, 2.0])
    
    # 冲突参数
    params['C_conflict'] = [(0, 1)]
    params['Delta_t_conflict'] = 2
    
    # 距离参数
    dist_matrix = np.zeros((num_points, num_points))
    for i in range(num_points):
        for j in range(num_points):
            coord_i = params['points_coords'][i]
            coord_j = params['points_coords'][j]
            dist_matrix[i, j] = math.sqrt((coord_i[0] - coord_j[0])**2 + (coord_i[1] - coord_j[1])**2)
    
    params['dist_matrix'] = dist_matrix
    params['dist_base'] = {}
    for i in range(num_points):
        coord_i = params['points_coords'][i]
        base_coord = params['base_station_coord']
        params['dist_base'][i] = math.sqrt((coord_i[0] - base_coord[0])**2 + (coord_i[1] - base_coord[1])**2)
    
    # VRP参数
    params['V_drone'] = 20
    params['tau_task'] = 2
    params['w1'] = 0.6
    params['w2'] = 0.4
    params['yield_ref'] = 1.0
    params['path_ref'] = 1000
    
    print("--- 小规模测试数据生成完毕 ---")
    return params

def solve_small_moo_vrp(params):
    """小规模多目标VRP求解"""
    print("开始求解小规模多目标VRP...")
    
    num_drones = params['num_drones']
    num_points = params['num_points']
    time_period = params['time_period']
    num_pesticides = params['num_pesticides']
    
    L = params['L']
    La = params['La']
    F_time = params['F_time']
    Q_base = params['Q_base']
    mu_vec = params['mu_vec']
    delta_vec = params['delta_vec']
    zeta_standard = params['zeta_standard']
    wind_speed = params['wind_speed']
    alpha = params['alpha']
    zeta_max_flow = params['zeta_max_flow']
    
    # 生成角色
    roles = []
    Q_j, zeta_j, eta_j = {}, {}, {}
    eta_values = np.maximum(0.1, 1 - alpha * wind_speed)
    
    for p in range(num_points):
        for t in range(time_period):
            for c in range(num_pesticides):
                if L[p, c] > 0:
                    zeta_dispense = zeta_standard[p, c] / eta_values[t]
                    if zeta_dispense <= zeta_max_flow:
                        role_tuple = (p, t, c)
                        roles.append(role_tuple)
                        j = len(roles) - 1
                        g_value = 1 / (np.sqrt(2 * np.pi) * delta_vec[c]) * np.exp(
                            -((t - mu_vec[c]) ** 2) / (2 * delta_vec[c] ** 2))
                        Q_j[j] = Q_base[p, c] * g_value
                        zeta_j[j] = zeta_dispense
                        eta_j[j] = eta_values[t]
    
    num_roles = len(roles)
    print(f"有效角色数量: {num_roles}")
    
    # 创建优化模型
    prob = pl.LpProblem("Small_MOO_VRP", pl.LpMaximize)
    solver = pl.getSolver('GUROBI_CMD', msg=True, timeLimit=60)  # 限制60秒
    
    T = pl.LpVariable.dicts("T", (range(num_drones), range(num_roles)), 0, 1, pl.LpBinary)
    Y = pl.LpVariable.dicts("Y", (range(num_drones), range(num_pesticides)), 0, 1, pl.LpBinary)
    role_nodes = list(range(num_roles)) + ['N']
    X = pl.LpVariable.dicts("X", (range(num_drones), role_nodes, role_nodes), 0, 1, pl.LpBinary)
    
    # 目标函数
    yield_obj = pl.lpSum(Q_j[j] * T[i][j] for i in range(num_drones) for j in range(num_roles))
    path_cost_obj = pl.lpSum(
        params['dist_base'][roles[k][0]] * X[i]['N'][k] for i in range(num_drones) for k in range(num_roles)) + \
                    pl.lpSum(params['dist_matrix'][roles[j][0]][roles[k][0]] * X[i][j][k] 
                            for i in range(num_drones) for j in range(num_roles) for k in range(num_roles) if j != k) + \
                    pl.lpSum(params['dist_base'][roles[j][0]] * X[i][j]['N'] 
                            for i in range(num_drones) for j in range(num_roles))
    
    prob += params['w1'] * (yield_obj / params['yield_ref']) - params['w2'] * (path_cost_obj / params['path_ref'])
    
    # 添加基本约束（简化版）
    for p in range(num_points):
        for c in range(num_pesticides):
            if L[p, c] > 0:
                relevant_roles = [j for j, role in enumerate(roles) if role[0] == p and role[2] == c]
                effective_spray = pl.lpSum(T[i][j] * (zeta_j[j] * eta_j[j]) 
                                         for i in range(num_drones) for j in relevant_roles)
                prob += effective_spray >= L[p, c]
                prob += effective_spray <= params['L_max'][p, c]
    
    # 其他约束
    for i in range(num_drones):
        prob += pl.lpSum(Y[i][c] for c in range(num_pesticides)) <= 1
        for j in range(num_roles):
            c_j = roles[j][2]
            prob += T[i][j] <= Y[i][c_j]
        for t in range(time_period):
            relevant_roles = [j for j, role in enumerate(roles) if role[1] == t]
            prob += pl.lpSum(T[i][j] for j in relevant_roles) <= 1
    
    # VRP约束
    for i in range(num_drones):
        prob += pl.lpSum(X[i]['N'][k] for k in range(num_roles)) <= 1
        for j in range(num_roles):
            prob += pl.lpSum(X[i][k][j] for k in role_nodes if k != j) == T[i][j]
            prob += pl.lpSum(X[i][j][k] for k in role_nodes if k != j) == T[i][j]
            prob += X[i][j][j] == 0
    
    # 求解
    prob.solve(solver)
    
    if pl.value(prob.objective) is not None:
        solution_t = {(i, j): T[i][j].varValue for i in range(num_drones) for j in range(num_roles)}
        solution_x = {(i, j, k): X[i][j][k].varValue 
                     for i in range(num_drones) 
                     for j in list(range(len(roles))) + ['N'] 
                     for k in list(range(len(roles))) + ['N']
                     if j != k and X[i][j][k].varValue > 0.5}
        return solution_t, prob.status, pl.value(prob.objective), roles, solution_x
    else:
        return None, prob.status, None, None, None

def test_path_extraction(solution_x, roles, num_drones):
    """测试路径提取功能"""
    print("\n--- 测试路径提取 ---")
    
    paths = {}
    for i in range(num_drones):
        print(f"提取无人机 {i} 的路径...")
        
        # 创建路径映射
        path_map = {}
        for (drone_id, from_node, to_node), value in solution_x.items():
            if drone_id == i and value > 0.5:
                path_map[from_node] = to_node
                print(f"  路径段: {from_node} -> {to_node}")
        
        # 查找起点
        start_node = path_map.get('N')
        print(f"  起始节点: {start_node}")
        
        if start_node is not None:
            drone_path = ['N', start_node]
            current_node = start_node
            
            # 追溯完整路径
            max_iterations = len(roles) + 2
            iteration_count = 0
            
            while current_node != 'N' and iteration_count < max_iterations:
                next_node = path_map.get(current_node)
                if next_node is not None:
                    drone_path.append(next_node)
                    current_node = next_node
                    iteration_count += 1
                    print(f"    添加节点: {next_node}")
                else:
                    print(f"    路径在节点 {current_node} 处中断")
                    break
            
            paths[i] = drone_path
            print(f"  完整路径: {drone_path}")
            
            # 显示路径详情
            path_details = []
            for node in drone_path:
                if node == 'N':
                    path_details.append("基站")
                else:
                    p, t, c = roles[node]
                    path_details.append(f"P{p}(T{t},C{c})")
            print(f"  路径详情: {' -> '.join(path_details)}")
        else:
            print(f"  无人机 {i} 没有分配任务")
            paths[i] = []
    
    return paths

def main():
    """主测试函数"""
    print("开始小规模测试...")
    
    # 生成测试数据
    params = generate_small_test_data()
    
    # 求解
    solution_t, status, objective, roles, solution_x = solve_small_moo_vrp(params)
    
    if objective is not None:
        print(f"\n求解成功!")
        print(f"状态: {pl.LpStatus[status]}")
        print(f"目标值: {objective}")
        print(f"角色数量: {len(roles)}")
        print(f"路径变量数量: {len(solution_x)}")
        
        # 测试路径提取
        paths = test_path_extraction(solution_x, roles, params['num_drones'])
        
        print("\n--- 测试成功! ---")
        print("修复后的路径提取功能工作正常。")
        
        return True
    else:
        print(f"\n求解失败!")
        print(f"状态: {pl.LpStatus[status]}")
        return False

if __name__ == "__main__":
    main()
