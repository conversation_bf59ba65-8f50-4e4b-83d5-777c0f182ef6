import math
import matplotlib.pyplot as plt
from matplotlib.ticker import MaxNLocator
from math import floor
import numpy as np
import xlrd
import pandas as pd
import pulp as pl
import pandas as pd
from math import cos, sin, acos, asin, pi, atan, fabs, ceil, floor
from collections import defaultdict
import random
import time
from gurobipy import *
import openpyxl
import sys
import os

#2023.9.18 基础版公式+错误连续时间约束..

class Assignment:
    @classmethod
    def Assemblyprocess(cls, Q, L, time_slice):
        row = len(Q)   # 生产线
        col = len(Q[0])   # 零件组装工序

        # build a optimal problem
        pro = pl.LpProblem('Max(connection and coverage )', pl.LpMaximize)
        solver = pl.getSolver('GUROBI_CMD')

        # build variables for the optimal problem
        # i生产线 j零件组装工序 t时间
        lpvars = [[[pl.LpVariable("x" + str(i) + "y" + str(j) + "l" + str(t), lowBound=0, upBound=1, cat='Integer') for
                    j in range(col)] for i in range(row)]for t in range(time_slice)]
        y_start_times = [[pl.LpVariable("l" + str(t) + "y" + str(j), lowBound=0, upBound=1, cat='Integer') for
                          j in range(col) for t in range(time_slice)]]

        y_start_times_values = [[pl.value(var) for var in row] for row in y_start_times]

        # # 创建二进制变量矩阵，表示每个工序在每个时间片是否开始
        # start_matrix = {}
        # for process in processes:
        #     for time_slot in time_slots:
        #         start_matrix[(process, time_slot)] = lp_problem.add_variable(
        #             name=f"Start_{process}_{time_slot}",
        #             var_type=BINARY
        #         )

        # # 更新约束条件：每个工序在某个时间片只能开始一次
        # for process in processes:
        #     lp_problem.add_constraint(sum(start_matrix[(process, time_slot)] for time_slot in time_slots) == 1)
        #
        # # 更新约束条件：每个工序必须在某个时间片开始
        # for process in processes:
        #     lp_problem.add_constraint(sum(start_matrix[(process, time_slot)] for time_slot in time_slots) == 1)

        # build optimal function
        all = pl.LpAffineExpression()
        for t in range(0, time_slice):
            for i in range(0, row):
                for j in range(0, col):
                    all += Q[i][j] * lpvars[t][i][j]
        pro += all

        # build constraint
        # 满足L约束
        for j in range(0, col):
            tempSum = 0
            for i in range(0, row):
                for t in range(0, time_slice):
                    tempSum += lpvars[t][i][j]
            pro += tempSum == L[j]

        # 每条生产线的每个时间片只能给一个零部件组装工序  La约束
        for t in range(0, time_slice):
            for i in range(0, row):
                tempSum = 0
                for j in range(0, col):
                    tempSum += lpvars[t][i][j]
                pro += tempSum <= 1

        # 任意时间，同一零部件组装工序只能指派给一条生产线
        for t in range(0, time_slice):
            for j in range(0, col):
                tempSum = 0
                for i in range(0, row):
                    tempSum += lpvars[t][i][j]
                pro += tempSum <= 1

        # 零部件组装工序时间矩阵
        y_start_times_values = [[0] * col for _ in range(time_slice)]
        for t in range(0, time_slice):
            for j in range(0, col):
                tempSum = 0
                for i in range(0, row):
                    tempSum += lpvars[t][i][j]
                y_start_times_values[t][j] = tempSum

        # 开始时间
        j_start_time = [next((t for t, x in enumerate(col) if x == 1), -1) for col in zip(*y_start_times_values)]

        # 连续时间
        # for j in range(0, col):
        #     tempSum = 0
        #     for t in range(j_start_time[j], j_start_time[j] + L[j] + 1):
        #         for i in range(0, row):
        #             tempSum += lpvars[t][i][j]
        #     pro += tempSum == L[j]

        # # 记录每个j工序执行时间
        # for j in range(0, col):
        #     temp = 0
        #     temp_array =[[0 for t in range(time_slice)] for j in range(col)]
        #     for i in range(0, row):
        #         for t in range(0, time_slice):
        #             tempT = lpvars[t][i][j].varValue
        #             # temp = t * lpvars[t][i][j].varValue
        #             temp = t * tempT
        #             temp_array[j][t] = temp
        #             tempT = 0

        # # 遍历j行再遍历t列，找到每行最小值
        # start_times = [0 for j in range(col)]
        # for j in range(len(temp_array)):
        #     j_start_time = int(100000000000)
        #     for t in range(len(temp_array[j])):
        #         j_start_time = min(temp_array[j][t], j_start_time)
        #     start_times[j] = j_start_time
        #
        # # 连续时间，至少连续 L[j] 个时间片分配给 j
        # for j in range(0, col):
        #     tempSum = 0
        #     for t in range(start_times[j], start_times[j] + L[j] + 1):
        #         for i in range(0, row):
        #             tempSum += lpvars[t][i][j]
        #     pro += tempSum == L[j]

        # 连续时间，至少 L[j] 个时间片分配给 j
        # for j in range(0, col):
        #     for t in range(0, time_slice - L[j] + 1):
        #         tempSum = 0
        #         for tt in range(t, t + L[j]):
        #             for i in range(0, row):
        #                 tempSum += lpvars[tt][i][j]
        #     pro += tempSum == L[j]



        # solve optimal problem
        status = pro.solve(solver)
        print("Assignment Status: ", pl.LpStatus[status])
        print("Final Assignment Result", pl.value(pro.objective))

        # get the result of T matrix
        T = [[[lpvars[t][i][j].varValue for j in range(col)] for i in range(row)] for t in range(time_slice)]
        return T, j_start_time, y_start_times_values

# 生成随机的Q矩阵
def genRandQMat(agentNum, roleNum):
	resMat = np.zeros((agentNum, roleNum))
	for i in range(agentNum):
		for j in range(roleNum):
			resMat[i][j] = round(np.random.random(), 2)
	return resMat

# 归一化后的Q
def getNormalizedQ(Q):
    max = min = Q[0][0]
    row = len(Q)
    column = len(Q[0])
    for i in range(row):
        for j in range(column):
            if (Q[i, j] > max):
                max = Q[i, j]
            if (Q[i, j] < min):
                min = Q[i, j]
    for i in range(row):
        for j in range(column):
            Q[i, j] = round((Q[i, j] - min) / (max - min), 4)
    return Q

if __name__ == '__main__':

    time_slice = 30
    lines = 3
    process = 10
    L = [1,2,3,4,5,1,2,3,4,5]
    Q = genRandQMat(lines,process)
    QMatrix = getNormalizedQ(Q)

    T1,j_start_time,y_start_times_values = Assignment.Assemblyprocess(QMatrix, L, time_slice)
    y_start_times_values = np.array(y_start_times_values)
    print(j_start_time)
    T1 = np.array(T1)

    totalL = [0 for j in range(process)]
    for j in range(process):
        cont = 0
        for t in range(0, time_slice):
            for i in range(lines):
                cont += T1[t][i][j]
        totalL[j] = cont
    print(totalL)


