import math
import matplotlib.pyplot as plt
from matplotlib.ticker import MaxNLocator
from math import floor
import numpy as np
import xlrd
import pandas as pd
import pulp as pl
import pandas as pd
from math import cos, sin, acos, asin, pi, atan, fabs, ceil, floor
from collections import defaultdict
import random
import time
from gurobipy import *
import openpyxl
import sys
import os

#2023.10.6：无法解决连续时间约束，无法存取开始时间并作为约束

class Assignment:
    @classmethod
    def Assemblyprocess(cls, Q, L, time_slice):
        row = len(Q)   # 生产线
        col = len(Q[0])   # 零件组装工序

        # build a optimal problem
        pro = pl.LpProblem('Max(connection and coverage )', pl.LpMaximize)
        solver = pl.getSolver('GUROBI_CMD')

        # build variables for the optimal problem
        # i生产线 j零件组装工序 t时间
        lpvars = [[[pl.LpVariable("x" + str(i) + "y" + str(j) + "l" + str(t), lowBound=0, upBound=1, cat='Integer') for
                    j in range(col)] for i in range(row)]for t in range(time_slice)]
        # 创建二进制变量数组，表示每个j是否有一个tempSum等于L[j]
        has_tempSum_equal_L = [pl.LpVariable("a" + str(a), lowBound = 0, upBound = 1, cat = 'Binary') for a in range(col)]

        # build optimal function
        all = pl.LpAffineExpression()
        for t in range(0, time_slice):
            for i in range(0, row):
                for j in range(0, col):
                    all += Q[i][j] * lpvars[t][i][j]
        pro += all

        # # build constraint
        # # 满足L约束
        # for j in range(0, col):
        #     tempSum = 0
        #     for i in range(0, row):
        #         for t in range(0, time_slice):
        #             tempSum += lpvars[t][i][j]
        #     pro += tempSum == L[j]
        #
        # # 每条生产线的每个时间片只能给一个零部件组装工序  La约束
        # for i in range(0, row):
        #     for t in range(0, time_slice):
        #         tempSum = 0
        #         for j in range(0, col):
        #             tempSum += lpvars[t][i][j]
        #         pro += tempSum <= 1
        #
        # # 任意时间，同一零部件组装工序只能指派给一条生产线
        # for t in range(0, time_slice):
        #     for j in range(0, col):
        #         tempSum = 0
        #         for i in range(0, row):
        #             tempSum += lpvars[t][i][j]
        #         pro += tempSum <= 1

        # math.isnan()
        ################## 连续时间Test2 不可行（约束不起作用）
        # # 连续时间约束
        # for j in range(0, col):
        #     for t in range(0, time_slice - L[j]):
        #         tempSum = 0
        #         for tt in range(t, t + L[j]):
        #             for i in range(0, row):
        #                 tempSum += lpvars[tt][i][j]
        #         pro += tempSum <= L[j] * has_tempSum_equal_L[j]


        ################## 连续时间Test3 不可行（lpvars在未求解出来时有可能是none，无法进行乘法运算记录）

        # 记录开始时间，并存为数组
        temp_array = np.zeros((col, time_slice))
        for j in range(0, col):
            temp = 0
            for t in range(0, time_slice):
                for i in range(0, row):
                    temp += (t + 1) * lpvars[t][i][j].value()  # 有其他约束限制一个工序只能分配给一个生产线
                temp_array[j][t] = temp
        temp_array = np.array(temp_array)
        filtered_matrix = [[element for element in row if element != 0] for row in temp_array]  # 去0
        min_values = [min(sublist) for sublist in filtered_matrix]  # 输出最小值组成数组
        starttime = [x - 1 for x in min_values]  # 转换为从0开始的时间
        #
        # # 连续时间约束
        # for j in range(0, col):
        #     for t in range(starttime[j], starttime[j] + L[j]):
        #         tempSum = 0
        #         for i in range(0, row):
        #             tempSum += lpvars[t][i][j]
        #         pro += tempSum == L[j]


        ################## 连续时间Test1 不可行

        # # 记录每个j工序执行时间
        # for j in range(0, col):
        #     temp = 0
        #     temp_array =[[0 for t in range(time_slice)] for j in range(col)]
        #     for i in range(0, row):
        #         for t in range(0, time_slice):
        #             tempT = lpvars[t][i][j]
        #             # temp = t * lpvars[t][i][j].varValue
        #             temp = (t + 1) * tempT
        #             temp_array[j][t] = temp
        #             tempT = 0
        #
        # # 遍历j行再遍历t列，找到每行最小值
        # start_times = [0 for j in range(col)]
        # for j in range(len(temp_array)):
        #     j_start_time = int(100000000000)
        #     for t in range(len(temp_array[j])):
        #         j_start_time = min(temp_array[j][t], j_start_time)
        #     start_times[j] = j_start_time
        #
        # # 连续时间，至少连续 L[j] 个时间片分配给 j
        # for j in range(0, col):
        #     tempSum = 0
        #     for t in range(start_times[j], start_times[j] + L[j] + 1):
        #         for i in range(0, row):
        #             tempSum += lpvars[t][i][j]
        #     pro += tempSum == L[j]


        # solve optimal problem
        status = pro.solve(solver)
        print("Assignment Status: ", pl.LpStatus[status])
        print("Final Assignment Result", pl.value(pro.objective))

        # get the result of T matrix
        T = [[[lpvars[t][i][j].varValue for j in range(col)] for i in range(row)] for t in range(time_slice)]
        return T

# 生成随机的Q矩阵
def genRandQMat(agentNum, roleNum):
	resMat = np.zeros((agentNum, roleNum))
	for i in range(agentNum):
		for j in range(roleNum):
			resMat[i][j] = round(np.random.random(), 2)
	return resMat

# 归一化后的Q
def getNormalizedQ(Q):
    max = min = Q[0][0]
    row = len(Q)
    column = len(Q[0])
    for i in range(row):
        for j in range(column):
            if (Q[i, j] > max):
                max = Q[i, j]
            if (Q[i, j] < min):
                min = Q[i, j]
    for i in range(row):
        for j in range(column):
            Q[i, j] = round((Q[i, j] - min) / (max - min), 4)
    return Q

if __name__ == '__main__':

    time_slice = 30
    lines = 3
    process = 10
    L = [1,2,3,4,5,1,2,3,4,5]
    # Q = genRandQMat(lines,process)
    # QMatrix = getNormalizedQ(Q)

    QMatrix = [[0,0.1579,0.8632,0.2316,0.4526,0.2632,0.9579,0.5368,0.8,0.9579],
     [0.3895,0.0526,0.5789,0.7368,0.0211,0.0105,0.1684,0.5158,0.2421,0],
    [0.3579,0.1263,0.9579,0.7158,1,0.4421,0.9474,0.2526,0.9053,0.2]]

    # print(QMatrix)

    T1 = Assignment.Assemblyprocess(QMatrix, L, time_slice)
    T1 = np.array(T1)

    # 验证是否满足L[j]
    totalL = [0 for j in range(process)]
    for j in range(process):
        cont = 0
        for t in range(0, time_slice):
            for i in range(lines):
                cont += T1[t][i][j]
        totalL[j] = cont
    print(totalL)

    # 记录j工序被分配的时间t
    Assigntime = [[] for j in range(process)]
    for j in range(process):
        for t in range(time_slice):
            for i in range(lines):
                if T1[t][i][j] == 1:
                    Assigntime[j].append(t + 1)




