import math
import matplotlib.pyplot as plt
from matplotlib.ticker import MaxNLocator
from math import floor
import numpy as np
import xlrd
import pandas as pd
import pulp as pl
import pandas as pd
from math import cos, sin, acos, asin, pi, atan, fabs, ceil, floor
from collections import defaultdict
import random
import time
from gurobipy import *
import openpyxl
import sys
import os

a = [[1,2,3], [3,4,3]]
a = np.array(a)
b = [[[[ 1,  2,  3],[ 4,  5,  6]],[[ 7,  8,  9],[10, 11, 12]]],[[[13, 14, 15],[16, 17, 18]],[[19, 20, 21],[22, 23, 24]]]]
b = np.array(b)
c = a * b
print(c)
