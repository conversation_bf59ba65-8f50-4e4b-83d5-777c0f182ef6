import math
import matplotlib.pyplot as plt
from matplotlib.ticker import MaxNLocator
from math import floor
import numpy as np
import xlrd
import pandas as pd
import pulp as pl
import pandas as pd
from math import cos, sin, acos, asin, pi, atan, fabs, ceil, floor
from collections import defaultdict
import random
import time
from gurobipy import *
import openpyxl
import sys
import os

# 生成随机的ACC矩阵
def c(matLength, times = 3): # times表示ACC矩阵的冲突数
	resMat = np.zeros((matLength, matLength))
	while times:
		i = random.randint(0, matLength-1)
		j = random.randint(0, matLength-1)
		if i != j and resMat[i][j] == 0:
			resMat[i][j] = -1 + 2*np.random.random()
			times -= 1
	return resMat

# 获取处理后的ACC矩阵 (type = 1 正关系 type = -1 负关系)
def genRelationMat(relationMat, type = 1): # times表示ACC矩阵的冲突数
	resMat = cp.deepcopy(relationMat)
	if type == 1:
		resMat[resMat < 0] = 0
	else:
		resMat[resMat > 0] = 0
	return resMat

if __name__ == '__main__':
	jobseq = 2
	component = 3
	matLength = jobseq * component
	resMat = c(matLength, times = 2)

	# relationMat = genRandRelationMat(matLength)

