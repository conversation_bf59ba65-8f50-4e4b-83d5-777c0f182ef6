import random
import matplotlib.pyplot as plt

# 定义工序数量和生产线数量
num_jobs = 10
num_lines = 2

# 定义每条生产线的容量，以确保不会挤在同一条线上
line_capacity = num_jobs // num_lines

# 生成随机工序数据，包括工序、开始时间、持续时间和生产线
jobs = []
lines = [[] for _ in range(num_lines)]  # 为每条生产线创建一个列表来存储工序

# 创建时间关系矩阵，初始化为None
time_relationships = [[None for _ in range(num_jobs)] for _ in range(num_jobs)]

i = 0  # 工序计数器
while i < num_jobs:  # 生成指定数量的工序
    start_time = random.randint(0, 50)  # 随机生成开始时间
    duration = random.randint(5, 20)  # 随机生成持续时间

    # 随机选择生产线，但确保不会挤满同一条线
    line = random.randint(0, num_lines - 1)

    if len(lines[line]) < line_capacity:
        # 检查该生产线在该时间段内是否已存在工序
        conflict = False
        for j in lines[line]:
            if (start_time < j[0] + j[1]) and (start_time + duration > j[0]):
                conflict = True
                break

        if not conflict:
            # 如果没有时间冲突，添加工序
            jobs.append((i, start_time, duration, line))
            lines[line].append((start_time, duration))
            i += 1


# # 随机选择一些工序对来表示时间关系
# for _ in range(5):
#     job1, job2 = random.sample(range(num_jobs), 2)
#     time_relationships[job1][job2] = "A -> B"  # 顺序关系
#     time_relationships[job2][job1] = "B -> A"  # 逆序关系
#
# for _ in range(5):
#     job1, job2 = random.sample(range(num_jobs), 2)
#     time_relationships[job1][job2] = "A || B"  # 并行关系
#     time_relationships[job2][job1] = "A || B"  # 并行关系

# 输出随机生成的工序数据和时间关系
print("随机生成的工序数据：")
for job in jobs:
    print(f"工序{job[0]}：开始时间={job[1]}, 持续时间={job[2]}, 生产线={job[3]}")

print("\n时间关系矩阵：")
for i in range(num_jobs):
    for j in range(num_jobs):
        if time_relationships[i][j]:
            print(f"工序{jobs[i][0]} 和 工序{jobs[j][0]} 的关系：{time_relationships[i][j]}")

# 准备绘制甘特图的数据
schedule_data = []
for job in jobs:
    job_id, start_time, duration, line = job
    end_time = start_time + duration
    schedule_data.append((job_id, line, start_time, end_time))

# 绘制生产排程甘特图
fig, ax = plt.subplots(figsize=(10, 6))

for job_id, line, start_time, end_time in schedule_data:
    plt.barh(y=line, left=start_time, width=end_time - start_time, height=0.5, label=f'Job {job_id+1}', align='center')

plt.rcParams['font.sans-serif'] = ['SimHei']
plt.yticks(range(1, num_lines+1), [f'Line {line}' for line in range(1, num_lines+1)])
plt.xlabel('时间')
plt.ylabel('生产线')
plt.title('生产排程甘特图')
plt.legend()

plt.show()
