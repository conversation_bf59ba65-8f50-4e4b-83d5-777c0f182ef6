import random

# 定义工序数量、生产线数量和时间关系列表
num_jobs = 20
num_lines = 4  # 可根据需要调整生产线数量
time_relationships = []

# 生成随机工序数据，包括工序、开始时间、持续时间和生产线
jobs = []
lines = [[] for _ in range(num_lines)]  # 为每条生产线创建一个列表来存储工序

# 创建时间关系矩阵，初始化为None
time_relationships = [[None for _ in range(num_jobs)] for _ in range(num_jobs)]

i = 0  # 工序计数器
while i < num_jobs:  # 生成指定数量的工序
    start_time = random.randint(0, 50)  # 随机生成开始时间
    duration = random.randint(5, 20)  # 随机生成持续时间

    # 随机选择生产线，确保不会挤满同一条线
    line = random.randint(0, num_lines - 1)

    if len(lines[line]) < (num_jobs // num_lines):  # 确保每条线的工序数量大致相同
        # 检查该生产线在该时间段内是否已存在工序
        conflict = False
        for j in lines[line]:
            if (start_time < j[0] + j[1]) and (start_time + duration > j[0]):
                conflict = True
                break

        if not conflict:
            # 如果没有时间冲突，添加工序
            jobs.append((i, start_time, duration, line))
            lines[line].append((start_time, duration))

            # 随机选择一种时间关系
            relationship = random.choice(["A -> B", "B -> A", "A && B", "B && A", "A < B", "B < A", "A <= B", "B <= A", "A > B", "B > A", "A >= B", "B >= A", "A == B"])

            # 更新时间关系矩阵，考虑工序的时间
            if relationship == "A -> B":
                # A工序完成后间隔一段时间B工序才能开始
                job1, job2 = random.sample(range(i), 2)
                time_relationships[job1][job2] = relationship
            elif relationship == "B -> A":
                # B工序完成后间隔一段时间A工序才能开始
                job1, job2 = random.sample(range(i), 2)
                time_relationships[job2][job1] = "A -> B"
            elif relationship == "A && B":
                # A工序和B工序同时开始
                job1, job2 = random.sample(range(i), 2)
                time_relationships[job1][job2] = relationship
                time_relationships[job2][job1] = relationship
            elif relationship == "B && A":
                # B工序和A工序同时开始
                job1, job2 = random.sample(range(i), 2)
                time_relationships[job1][job2] = "A && B"
                time_relationships[job2][job1] = "A && B"
            elif relationship == "A < B":
                # A工序的开始时间在B工序开始时间之前，A工序结束时间在B工序结束时间之前
                job1, job2 = random.sample(range(i), 2)
                if jobs[job1][1] < jobs[job2][1] and jobs[job1][1] + jobs[job1][2] < jobs[job2][1]:
                    time_relationships[job1][job2] = relationship
            elif relationship == "B < A":
                # B工序的开始时间在A工序开始时间之前，B工序结束时间在A工序结束时间之前
                job1, job2 = random.sample(range(i), 2)
                if jobs[job2][1] < jobs[job1][1] and jobs[job2][1] + jobs[job2][2] < jobs[job1][1]:
                    time_relationships[job2][job1] = "A < B"
            elif relationship == "A <= B":
                # A工序的开始时间在B工序开始时间之前，A工序结束时间在B工序结束时间之前或同时
                job1, job2 = random.sample(range(i), 2)
                if jobs[job1][1] < jobs[job2][1] and jobs[job1][1] + jobs[job1][2] <= jobs[job2][1]:
                    time_relationships[job1][job2] = relationship
            elif relationship == "B <= A":
                # B工序的开始时间在A工序开始时间之前，B工序结束时间在A工序结束时间之前或同时
                job1, job2 = random.sample(range(i), 2)
                if jobs[job2][1] < jobs[job1][1] and jobs[job2][1] + jobs[job2][2] <= jobs[job1][1]:
                    time_relationships[job2][job1] = "A <= B"
            elif relationship == "A > B":
                # A工序的开始时间在B工序开始时间之后，A工序结束时间在B工序结束时间之后
                job1, job2 = random.sample(range(i), 2)
                if jobs[job1][1] > jobs[job2][1] and jobs[job1][1] + jobs[job1][2] > jobs[job2][1] + jobs[job2][2]:
                    time_relationships[job1][job2] = relationship
            elif relationship == "B > A":
                # B工序的开始时间在A工序开始时间之后，B工序结束时间在A工序结束时间之后
                job1, job2 = random.sample(range(i), 2)
                if jobs[job2][1] > jobs[job1][1] and jobs[job2][1] + jobs[job2][2] > jobs[job1][1] + jobs[job1][2]:
                    time_relationships[job2][job1] = "A > B"
            elif relationship == "A == B":
                # A和B工序同时开始同时结束
                job1, job2 = random.sample(range(i), 2)
                if jobs[job1][1] == jobs[job2][1] and jobs[job1][1] + jobs[job1][2] == jobs[job2][1] + jobs[job2][2]:
                    time_relationships[job1][job2] = relationship
                    time_relationships[job2][job1] = relationship

            i += 1

# 输出生成的工序数据和时间关系
print("生成的工序数据：")
for job in jobs:
    print(f"工序{job[0]}：开始时间={job[1]}, 持续时间={job[2]}, 生产线={job[3]}")

print("\n时间关系矩阵：")
for i in range(num_jobs):
    for j in range(num_jobs):
        if time_relationships[i][j]:
            print(f"工序{jobs[i][0]} 和 工序{jobs[j][0]} 的关系：{time_relationships[i][j]}")
