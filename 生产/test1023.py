import numpy as np

# build variables for the optimal problem
# i生产线 j零件组装工序 t时间
lpvars = [[[pl.LpVariable("x" + str(i) + "y" + str(j) + "l" + str(t), lowBound=0, upBound=1, cat='Integer') for
            j in range(col)] for i in range(row)] for t in range(time_slice)]
start_time = pl.LpVariable.dicts("Start_Time", range(col), lowBound=0, cat=pl.LpContinuous)

# 添加额外的二进制变量，用于指示任务是否被分配
task_assigned = [pl.LpVariable(f"Task_Assigned_{j}", cat=pl.LpBinary) for j in range(col)]

# build optimal function
all = pl.LpAffineExpression()
for t in range(0, time_slice):
    for i in range(0, row):
        for j in range(0, col):
            all += Q[i][j] * lpvars[t][i][j]
pro += all

# 添加约束：每个工序任务要么分配到一个生产线，要么任务未分配
for j in range(col):
    pro += pl.lpSum([lpvars[i][j][t] for i in range(row) for t in range(time_slice)]) == task_assigned[j]

# 添加约束：如果任务被分配，计算开始时间
for j in range(col):
    min_expression = pl.LpAffineExpression([(lpvars[i][j][t], t) for i in range(row) for t in range(time_slice)])
    pro += start_time[j] >= min_expression - (1 - task_assigned[j]) * time_slice