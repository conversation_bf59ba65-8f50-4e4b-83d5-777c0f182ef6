import math
import matplotlib.pyplot as plt
from matplotlib.ticker import MaxNLocator
from math import floor
import numpy as np
import xlrd
import pandas as pd
import pulp as pl
import pandas as pd
from math import cos, sin, acos, asin, pi, atan, fabs, ceil, floor
from collections import defaultdict
import random
import time
from gurobipy import *
import openpyxl
import sys
import os

#2023.10.12：将开始时间变量作为一个新的变量（不可行：数组的问题+求min大概率求到的是T=0的时候的t）

class Assignment:
    @classmethod
    def Assemblyprocess(cls, Q, L, time_slice):
        row = len(Q)   # 生产线
        col = len(Q[0])   # 零件组装工序

        # build a optimal problem
        pro = pl.LpProblem('Max(connection and coverage)', pl.LpMaximize)
        solver = pl.getSolver('GUROBI_CMD')

        # build variables for the optimal problem
        # i生产线 j零件组装工序 t时间
        lpvars = [[[pl.LpVariable("x" + str(i) + "y" + str(j) + "l" + str(t), lowBound=0, upBound=1, cat='Integer') for
                    j in range(col)] for i in range(row)]for t in range(time_slice)]
        s_vars = [pl.LpVariable("a" + str(j), lowBound=0, upBound = time_slice, cat='Integer')for j in range(col)]

        # build optimal function
        all = pl.LpAffineExpression()
        for t in range(0, time_slice):
            for i in range(0, row):
                for j in range(0, col):
                    all += Q[i][j] * lpvars[t][i][j]
        pro += all

        # # 开始时间
        # sub_all = pl.LppAffineExpression()
        # for j in range(col):
        #     for t in range(time_slice):
        #         tempSum = 0
        #         for i in range(row):
        #             tempSum +=
        #
        # sub_pro = pl.LpProblem('Min_s_vars', pl.LpMinimize)
        # sub_pro += pl.lpSum(s_vars)

        # build constraint
        # 满足L约束
        for j in range(0, col):
            tempSum = 0
            for i in range(0, row):
                for t in range(0, time_slice):
                    tempSum += lpvars[t][i][j]
            pro += tempSum == L[j]

        # 每条生产线的每个时间片只能给一个零部件组装工序  La约束
        for i in range(0, row):
            for t in range(0, time_slice):
                tempSum = 0
                for j in range(0, col):
                    tempSum += lpvars[t][i][j]
                pro += tempSum <= 1

        # 任意时间，同一零部件组装工序只能指派给一条生产线
        for t in range(0, time_slice):
            for j in range(0, col):
                tempSum = 0
                for i in range(0, row):
                    tempSum += lpvars[t][i][j]
                pro += tempSum <= 1

        # # 开始时间不能越界 约束 （s+c<=p）
        # for j in range(0, col):
        #     temp = time_slice - L[j]
        #     pro += temp >= s_vars[j]
        #
        # # 开始时间与指派矩阵结合
        # for j in range(0, col):
        #     for t in range(0, time_slice):
        #         tempSum = 0
        #         for i in range(0, row):
        #             tempSum += lpvars[t][i][j]
        #         pro += s_vars[j] >= tempSum * t



        # solve optimal problem
        status_sub = pro.solve(solver)

        status = pro.solve(solver)
        print("Assignment Status: ", pl.LpStatus[status])
        print("Final Assignment Result", pl.value(pro.objective))

        # get the result of T matrix
        T = [[[lpvars[t][i][j].varValue for j in range(col)] for i in range(row)] for t in range(time_slice)]
        return T

# 生成随机的Q矩阵
def genRandQMat(agentNum, roleNum):
	resMat = np.zeros((agentNum, roleNum))
	for i in range(agentNum):
		for j in range(roleNum):
			resMat[i][j] = round(np.random.random(), 2)
	return resMat

# 归一化后的Q
def getNormalizedQ(Q):
    max = min = Q[0][0]
    row = len(Q)
    column = len(Q[0])
    for i in range(row):
        for j in range(column):
            if (Q[i, j] > max):
                max = Q[i, j]
            if (Q[i, j] < min):
                min = Q[i, j]
    for i in range(row):
        for j in range(column):
            Q[i, j] = round((Q[i, j] - min) / (max - min), 4)
    return Q

if __name__ == '__main__':

    time_slice = 30
    lines = 3
    process = 10
    L = [1,2,3,4,5,1,2,3,4,5]
    # Q = genRandQMat(lines,process)
    # QMatrix = getNormalizedQ(Q)
    c_time = [1,2,1,2,1,1,2,1,2,1]

    QMatrix = [[0,0.1579,0.8632,0.2316,0.4526,0.2632,0.9579,0.5368,0.8,0.9579],
     [0.3895,0.0526,0.5789,0.7368,0.0211,0.0105,0.1684,0.5158,0.2421,0],
    [0.3579,0.1263,0.9579,0.7158,1,0.4421,0.9474,0.2526,0.9053,0.2]]

    # print(QMatrix)

    T1 = Assignment.Assemblyprocess(QMatrix, L, time_slice)
    T1 = np.array(T1)

    # 验证是否满足L[j]
    totalL = [0 for j in range(process)]
    for j in range(process):
        cont = 0
        for t in range(0, time_slice):
            for i in range(lines):
                cont += T1[t][i][j]
        totalL[j] = cont
    print(totalL)

    # 记录j工序被分配的时间t
    Assigntime = [[] for j in range(process)]
    for j in range(process):
        for t in range(time_slice):
            for i in range(lines):
                if T1[t][i][j] == 1:
                    Assigntime[j].append(t + 1)




