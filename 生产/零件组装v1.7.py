import math
import matplotlib.pyplot as plt
from matplotlib.ticker import MaxNLocator
from math import floor
import numpy as np
import xlrd
import pandas as pd
import pulp as pl
import pandas as pd
from math import cos, sin, acos, asin, pi, atan, fabs, ceil, floor
from collections import defaultdict
import random
import time
from gurobipy import *
import openpyxl
import sys
import os
from pulp import lpSum

#2023.10.27：新尝试两个表示开始时间的方法

class Assignment:
    @classmethod
    def Assemblyprocess(cls, Q, L, time_slice):
        row = len(Q)   # 生产线
        col = len(Q[0])   # 零件组装工序

        # build a optimal problem
        pro = pl.LpProblem('Max(connection and coverage )', pl.LpMaximize)
        solver = pl.getSolver('GUROBI_CMD')

        # build variables for the optimal problem
        # i生产线 j零件组装工序 t时间
        lpvars = [[[pl.LpVariable("x" + str(i) + "y" + str(j) + "l" + str(t), lowBound=0, upBound=1, cat='Integer') for
                    j in range(col)] for i in range(row)]for t in range(time_slice)]
        # 创建二进制变量，表示每个工序是否在每个时间片执行
        job_active = pl.LpVariable.dicts("job_active", ((j, t) for j in range(col) for t in range(time_slice)),
                                      cat='Binary')

        # build optimal function
        all = pl.LpAffineExpression()
        for t in range(0, time_slice):
            for i in range(0, row):
                for j in range(0, col):
                    all += Q[i][j] * lpvars[t][i][j]
        pro += all

        # build constraint
        # 满足L约束
        # for j in range(0, col):
        #     tempSum = 0
        #     for i in range(0, row):
        #         for t in range(0, time_slice):
        #             tempSum += lpvars[t][i][j]
        #     pro += tempSum == L[j]

        # # 每条生产线的每个时间片只能给一个零部件组装工序  类似La约束
        # for i in range(0, row):
        #     for t in range(0, time_slice):
        #         tempSum = 0
        #         for j in range(0, col):
        #             tempSum += lpvars[t][i][j]
        #         pro += tempSum <= 1

        # 任意时间，同一零部件组装工序只能指派给一条生产线
        for t in range(0, time_slice):
            for j in range(0, col):
                tempSum = 0
                for i in range(0, row):
                    tempSum += lpvars[t][i][j]
                pro += tempSum <= 1

        # # 记录每个j工序执行时间
        # for j in range(0, col):
        #     temp = 0
        #     temp_array =[[0 for t in range(time_slice)] for j in range(col)]
        #     for i in range(0, row):
        #         for t in range(0, time_slice):
        #             tempT = lpvars[t][i][j]
        #             temp = (t + 1) * tempT
        #             temp_array[j][t] = temp
        #             tempT = 0
        #
        # # 遍历j行再遍历t列，找到每行最小值
        # start_times = [0 for j in range(col)]
        # for j in range(len(temp_array)):
        #     j_start_time = int(100000000000)
        #     for t in range(len(temp_array[j])):
        #         j_start_time = min(temp_array[j][t], j_start_time)
        #     start_times[j] = j_start_time
        #
        # # 连续时间，至少连续 L[j] 个时间片分配给 j
        # for j in range(0, col):
        #     tempSum = 0
        #     for t in range(start_times[j], start_times[j] + L[j] + 1):
        #         for i in range(0, row):
        #             tempSum += lpvars[t][i][j]
        #     pro += tempSum >= L[j]

        # # 记录每个j工序执行时间
        # temp_array = [[0 for t in range(time_slice)] for j in range(col)]
        # for j in range(0, col):
        #     for t in range(0, time_slice):
        #         if lpvars[t][i][j] == 1:
        #             temp_array[j][t] = pl.lpSum(lpvars[t][i][j] * (t + 1) for i in range(row))
        #
        # # 找到每行最小值
        # start_times = [min(temp_array[j]) for j in range(col)]
        #
        # # 连续时间，至少连续 L[j] 个时间片分配给 j
        # for j in range(0, col):
        #     for t in range(start_times[j], start_times[j] + L[j] + 1):
        #         pro += pl.lpSum(lpvars[t][i][j] for i in range(row)) >= 1


        # # 添加约束：最早开始时间是正确的
        # for j in range(col):
        #     for t in range(time_slice):
        #         pro += lpSum(start_time_vars[(j, tt)] for tt in range(t + 1)) >= start_timearray[j]
        #
        # # 检查最早开始时间
        # for j in range(col):
        #     for t in range(time_slice):
        #         for i in range(row):
        #             if lpvars[t][i][j] == 1:
        #                 # 如果工序 j 在时间 t 开始执行并且 t 比当前记录的最早开始时间小，更新最早开始时间
        #                 start_timearray[j] = min(start_timearray[j], t)

        # 添加约束：如果某工序在某时间片执行，那么至少有一个任务在该时间片执行
        for j in range(col):
            for t in range(time_slice):
                if lpvars[t][i][j] == 1:
                    pro += job_active[(j, t)] <= lpSum(lpvars[t][i][j] for i in range(row))

        # 添加约束：确保每个工序具有至少 L[j] 个连续的时间片
        for j in range(col):
            for t in range(time_slice - L[j] + 1):
                pro += lpSum(job_active[(j, t + k)] for k in range(L[j])) >= L[j]

        # solve optimal problem
        status = pro.solve(solver)
        print("Assignment Status: ", pl.LpStatus[status])
        print("Final Assignment Result", pl.value(pro.objective))

        # get the result of T matrix
        T = [[[lpvars[t][i][j].varValue for j in range(col)] for i in range(row)] for t in range(time_slice)]
        # Start_time = [start_time[j].varValue for j in range(col)] --10.23
        return T

# 生成随机的Q矩阵
def genRandQMat(agentNum, roleNum):
	resMat = np.zeros((agentNum, roleNum))
	for i in range(agentNum):
		for j in range(roleNum):
			resMat[i][j] = round(np.random.random(), 2)
	return resMat

# 归一化后的Q
def getNormalizedQ(Q):
    max = min = Q[0][0]
    row = len(Q)
    column = len(Q[0])
    for i in range(row):
        for j in range(column):
            if (Q[i, j] > max):
                max = Q[i, j]
            if (Q[i, j] < min):
                min = Q[i, j]
    for i in range(row):
        for j in range(column):
            Q[i, j] = round((Q[i, j] - min) / (max - min), 4)
    return Q

if __name__ == '__main__':

    time_slice = 20
    lines = 3
    process = 10
    L = [1,2,3,4,5,1,2,3,4,5]
    # Q = genRandQMat(lines,process)
    # QMatrix = getNormalizedQ(Q)

    QMatrix = [[0,0.1579,0.8632,0.2316,0.4526,0.2632,0.9579,0.5368,0.8,0.9579],
     [0.3895,0.0526,0.5789,0.7368,0.0211,0.0105,0.1684,0.5158,0.2421,0],
    [0.3579,0.1263,0.9579,0.7158,1,0.4421,0.9474,0.2526,0.9053,0.2]]

    # print(QMatrix)

    # T1, Start_time = Assignment.Assemblyprocess(QMatrix, L, time_slice)   --10.23
    T1 = Assignment.Assemblyprocess(QMatrix, L, time_slice)
    T1 = np.array(T1)
    # start_time = np.array(Start_time) --10.23

    # 验证是否满足L[j]
    totalL = [0 for j in range(process)]
    for j in range(process):
        cont = 0
        for t in range(0, time_slice):
            for i in range(lines):
                cont += T1[t][i][j]
        totalL[j] = cont
    print(totalL)

    # 记录j工序被分配的时间t
    Assigntime = [[] for j in range(process)]
    for j in range(process):
        for t in range(time_slice):
            for i in range(lines):
                if T1[t][i][j] == 1:
                    Assigntime[j].append(t)




