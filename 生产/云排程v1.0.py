import math
import matplotlib.pyplot as plt
from matplotlib.ticker import MaxNLocator
from math import floor
import numpy as np
import xlrd
import pandas as pd
import pulp as pl
import pandas as pd
from math import cos, sin, acos, asin, pi, atan, fabs, ceil, floor
from collections import defaultdict
import random
import time
from gurobipy import *
import openpyxl
import sys
import os
from pulp import lpSum
import networkx as nx

#2023.12.12：

class Assignment:
    @classmethod
    def Assemblytask(cls, Q, L, time_slice, upper_lower_matrix, same_layer_matrix, I):
        row = len(Q)   # 云服务器
        col = len(Q[0])   # 任务

        # build a optimal problem
        pro = pl.LpProblem('Max(connection and coverage )', pl.LpMaximize)

        # build variables for the optimal problem
        # i云服务器 j任务 t时间
        lpvars = [[[pl.LpVariable("x" + str(i) + "y" + str(j) + "l" + str(t), lowBound=0, upBound=1, cat='Integer') for
                    j in range(col)] for i in range(row)]for t in range(time_slice)]

        # 定义开始时间数组 s
        s = [pl.LpVariable("y" + str(j), lowBound=0) for j in range(col)]

        # build optimal function
        all = pl.LpAffineExpression()
        for t in range(0, time_slice):
            for i in range(0, row):
                for j in range(0, col):
                    all += Q[i][j] * lpvars[t][i][j]
        pro += all

        # build constraint
        # 满足L约束
        for j in range(0, col):
            tempSum = 0
            for i in range(0, row):
                for t in range(0, time_slice):
                    tempSum += lpvars[t][i][j]
            pro += tempSum == L[j]

        # 开始时间 (存取最小时间)
        for j in range(0, col):
            for t in range(0, time_slice):
                pro += s[j] >= lpSum(lpvars[t][i][j] * t for i in range(row))


        # # 连续时间
        # for j in range(0, col):
        #     tempSum = 0
        #     tempidle = 0
        #     for i in range(0, row):
        #         for t in range(s[j], s[j] + L[j]):
        #             tempSum += lpvars[t][i][j]
        #             tempidle += I[i][t]
        #     pro += tempSum == tempidle


        # solve optimal problem
        status = pro.solve()
        print("Assignment Status: ", pl.LpStatus[status])
        print("Final Assignment Result", pl.value(pro.objective))

        # get the result of T matrix
        T = [[[lpvars[t][i][j].varValue for j in range(col)] for i in range(row)] for t in range(time_slice)]

        # 输出s的值，并将它们存储在一个新的列表中
        s_values = [pl.value(var) for var in s]
        print("Values of s:", s_values)

        return T, s_values


# 生成随机的Q矩阵
def genRandQMat(agentNum, roleNum):
	resMat = np.zeros((agentNum, roleNum))
	for i in range(agentNum):
		for j in range(roleNum):
			resMat[i][j] = round(np.random.random(), 2)
	return resMat

# 归一化后的Q
def getNormalizedQ(Q):
    max = min = Q[0][0]
    row = len(Q)
    column = len(Q[0])
    for i in range(row):
        for j in range(column):
            if (Q[i, j] > max):
                max = Q[i, j]
            if (Q[i, j] < min):
                min = Q[i, j]
    for i in range(row):
        for j in range(column):
            Q[i, j] = round((Q[i, j] - min) / (max - min), 4)
    return Q

# 随机生成云服务器空闲时间矩阵
def generate_idletime_matrix(i, t):
    # 计算矩阵中0的数量
    num_zeros = int(0.2 * i * t)
    num_ones = i * t - num_zeros
    # 创建一个包含所需数量的0和1的数组
    arr = np.array([0]*num_zeros + [1]*num_ones)
    # 随机打乱数组
    np.random.shuffle(arr)
    # 将一维数组重塑为i行t列的矩阵
    matrix = arr.reshape(i, t)
    return matrix

# 生成随机颜色列表，长度与作业数量相同
def generate_random_colors(num_jobs):
    random.seed()  # 重新设置随机数生成器的种子
    colors = []
    for _ in range(num_jobs):
        color = "#{:02x}{:02x}{:02x}".format(random.randint(0, 255), random.randint(0, 255), random.randint(0, 255))
        colors.append(color)
    return colors
#
# # 生成任务上下级矩阵、同级矩阵
# def generate_adjacency_matrices(num_task, num_layers):
#     # 生成上下级矩阵
#     upper_lower_matrix = np.zeros((num_task, num_task), dtype=int)
#     for i in range(num_task):
#         for j in range(i + 1, num_task):
#             upper_lower_matrix[i, j] = np.random.randint(0, 2)  # 随机生成0或1
#             # 确保不出现自环，因此对称部分也设置为0
#             upper_lower_matrix[j, i] = 0
#
#     # 生成同级矩阵
#     same_layer_matrix = np.zeros((num_layers, num_task, num_task), dtype=int)
#     for layer in range(num_layers):
#         for i in range(num_task):
#             for j in range(i + 1, num_task):
#                 same_layer_matrix[layer, i, j] = np.random.randint(0, 2)  # 随机生成0或1
#                 # 确保不出现自环，因此对称部分也设置为0
#                 same_layer_matrix[layer, j, i] = 0
#
#     return upper_lower_matrix, same_layer_matrix
#
# # 生成任务树状图
# def create_tree_graph(upper_lower_matrix, same_layer_matrix):
#     G = nx.DiGraph()
#
#     num_task = len(upper_lower_matrix)
#     num_layers = len(same_layer_matrix)
#
#     for i in range(num_task):
#         G.add_node(f"task {i}")
#
#     for i in range(num_task):
#         for j in range(num_task):
#             if upper_lower_matrix[i, j] == 1:
#                 G.add_edge(f"task {i}", f"task {j}")
#
#     for layer in range(num_layers):
#         for i in range(num_task):
#             for j in range(num_task):
#                 if same_layer_matrix[layer, i, j] == 1 and i != j:
#                     G.add_edge(f"task {i}", f"task {j}")
#
#     return G
#
# def plot_hierarchy_tree(G):
#     pos = nx.spring_layout(G, seed=42)  # 定义布局
#
#     nx.draw(G, pos, with_labels=True, node_size=500, node_color="skyblue", font_size=10, font_color="black", font_weight="bold")
#     plt.title("工序关系树状图")
#     plt.show()
#
#
# def create_hierarchy_graph():
#     # 创建一个空的有向图
#     G = nx.DiGraph()
#
#     # 定义各个层次的节点
#     layer1 = ["A", "B", "C", "D"]
#     layer2 = ["E", "F"]
#     layer3 = ["G"]
#
#     # 添加节点和边，构建上下级关系
#     for parent, children in zip(layer1, layer2):
#         G.add_node(parent)
#         for child in children:
#             G.add_node(child)
#             G.add_edge(parent, child)
#
#     for parent, children in zip(layer2, layer3):
#         G.add_node(parent)
#         for child in children:
#             G.add_node(child)
#             G.add_edge(parent, child)
#
#     # 添加同级关系
#     same_layer_edges = [("A", "B"), ("C", "D"), ("E", "F")]
#     G.add_edges_from(same_layer_edges)
#
#     return G

if __name__ == '__main__':

    time_slice = 20
    server = 3
    task = 8
    num_layers = 3
    # L = [1,2,3,4,5,1,2,3,4,5]
    L = [2,2,3,5,1,6,8,3]
    # Q = genRandQMat(server,task)
    # QMatrix = getNormalizedQ(Q)
    # print(QMatrix)

    QMatrix = [[0.28420, 0.98950, 0.96840, 0.76840, 0.58950, 0.61050, 0.89470, 1.00000],
    [0.00000, 0.97890, 0.57890, 0.35790, 0.61050, 0.67370, 0.60000, 0.15790],
    [0.97890, 0.61050, 0.58950, 0.47370, 0.12630, 0.41050, 0.46320, 0.78950]]

    # QMatrix = [[0,0.1579,0.8632,0.2316,0.4526,0.2632,0.9579,0.5368,0.8,0.9579],
    #  [0.3895,0.0526,0.5789,0.7368,0.0211,0.0105,0.1684,0.5158,0.2421,0],
    # [0.3579,0.1263,0.9579,0.7158,1,0.4421,0.9474,0.2526,0.9053,0.2]]

    # 上下级矩阵
    # upper_lower_matrix, same_layer_matrix = generate_adjacency_matrices(task, num_layers)
    # upper_lower_matrix = np.array(upper_lower_matrix)
    # same_layer_matrix = np.array(same_layer_matrix)

    # 上下级矩阵
    upper_lower_matrix = [[0,0,0,0,0,0,0,0],
                          [0,0,0,0,0,0,0,0],
                          [0,0,0,0,0,0,0,0],
                          [0,0,0,0,0,0,0,0],
                          [0,0,0,0,0,0,0,0],
                          [1,1,0,0,0,0,0,0],
                          [0,0,1,1,1,0,0,0],
                          [0,0,0,0,0,1,1,0]]
    # 同级矩阵
    same_layer_matrix = [[0,1,1,1,1,0,0,0],
                         [1,0,1,1,1,0,0,0],
                         [1,1,0,1,1,0,0,0],
                         [1,1,1,0,1,0,0,0],
                         [1,1,1,1,0,0,0,0],
                         [0,0,0,0,0,0,1,0],
                         [0,0,0,0,0,1,0,0],
                         [0,0,0,0,0,0,0,0]]

    I = generate_idletime_matrix(server, task)

    start_1 = time.perf_counter()
    T1,S1 = Assignment.Assemblytask(QMatrix, L, time_slice, upper_lower_matrix, same_layer_matrix, I)
    total_1 = time.perf_counter() - start_1
    print(total_1)

    T1 = np.array(T1)
    S1 = np.array(S1)
    # start_time = np.array(Start_time) --10.23

    # 验证是否满足L[j]
    totalL = [0 for j in range(task)]
    for j in range(task):
        cont = 0
        for t in range(0, time_slice):
            for i in range(server):
                cont += T1[t][i][j]
        totalL[j] = cont
    print(totalL)

    # 记录j工序被分配的时间t
    Assigntime = [[] for j in range(task)]

    for j in range(task):
        for t in range(time_slice):
            for i in range(server):
                if T1[t][i][j] == 1:
                    Assigntime[j].append(t)

    # 记录j工序在哪一条生产线
    Assignline = [0 for j in range(task)]
    for j in range(task):
        for i in range(server):
            # 初始化一个列表，用于存储工序 j 被分配到生产线 i 的时间片
            assigned_time_slots = []
            for t in range(time_slice):
                if T1[t][i][j] == 1:
                    assigned_time_slots.append(t)
            # 如果工序 j 在生产线 i 上有分配，则将其记录下来
            if assigned_time_slots:
                Assignline[j] = i

    # 准备绘制甘特图的数据
    schedule_data = []

    jobs = []
    for j in range(task):
        start_time = min(Assigntime[j])
        duration = L[j]
        line = Assignline[j]
        jobs.append((j, start_time, duration, line))

    for job in jobs:
        job_id, start_time, duration, line = job
        end_time = start_time + duration
        schedule_data.append((job_id, line, start_time, end_time))

    colors = generate_random_colors(task)

    # # 绘制生产排程甘特图(理想)
    # fig, ax = plt.subplots(figsize=(10, 6))
    #
    # for job_id, line, start_time, end_time in schedule_data:
    #     plt.barh(y=line, left=start_time, width=end_time - start_time, height=0.5, label=f'Job {job_id + 1}',
    #              align='center')
    #     # color = colors[job_id]  # 使用与作业ID对应的随机颜色
    #
    # plt.rcParams['font.sans-serif'] = ['SimHei']
    # plt.yticks(range(1, server + 1), [f'Line {line}' for line in range(1, server + 1)])
    # plt.xlabel('时间')
    # plt.ylabel('生产线')
    # plt.title('生产排程甘特图')
    # plt.legend()
    #
    # plt.show()


    # # 绘制任务树状图v1.0
    # G = create_tree_graph(upper_lower_matrix, same_layer_matrix)
    # pos = nx.spring_layout(G, seed=42)  # 定义布局
    # nx.draw(G, pos, with_labels=True, node_size=500, node_color="skyblue", font_size=10, font_color="black",
    #         font_weight="bold")
    # plt.title("工序关系树状图")
    # plt.show()

    # # 绘制树状图v2.0
    # G = create_hierarchy_graph()
    # plot_hierarchy_tree(G)

    # 设置甘特图的参数（实际）
    fig, ax = plt.subplots(figsize=(10, 6))
    # 绘制甘特图
    for j in range(task):
        for t in Assigntime[j]:
            ax.barh(j, 1, left=t, color=colors[j], edgecolor='k')

    plt.rcParams['font.sans-serif'] = ['SimHei']
    # 设置 Y 轴标签
    plt.yticks(range(task), [f'任务{j}' for j in range(task)])

    # 设置 X 轴标签
    plt.xlabel('时间')

    # 显示甘特图
    plt.title('生产调度甘特图')
    plt.grid(axis='x')
    plt.show()
