import random
import matplotlib.pyplot as plt

# 定义工序数量和生产线数量
num_jobs = 10
num_lines = 8

# 生成随机工序数据，包括工序、开始时间、持续时间和生产线
jobs = []
for i in range(num_jobs):
    start_time = random.randint(0, 50)  # 随机生成开始时间
    duration = random.randint(5, 20)   # 随机生成持续时间
    line = random.randint(1, num_lines)  # 随机生成生产线编号
    jobs.append((i, start_time, duration, line))

# 创建时间关系矩阵，初始化为None
time_relationships = [[None for _ in range(num_jobs)] for _ in range(num_jobs)]

# 随机选择一些工序对来表示时间关系
for _ in range(5):
    job1, job2 = random.sample(range(num_jobs), 2)
    time_relationships[job1][job2] = "A -> B"  # 顺序关系
    time_relationships[job2][job1] = "B -> A"  # 逆序关系

# 准备绘制甘特图的数据
schedule_data = []
for job in jobs:
    job_id, start_time, duration, line = job
    end_time = start_time + duration
    schedule_data.append((job_id, line, start_time, end_time))

# 绘制生产排程甘特图
fig, ax = plt.subplots(figsize=(10, 6))

for job_id, line, start_time, end_time in schedule_data:
    plt.barh(y=line, left=start_time, width=end_time - start_time, height=0.5, label=f'Job {job_id+1}', align='center')

plt.rcParams['font.sans-serif'] = ['SimHei']
plt.yticks(range(1, num_lines+1), [f'Line {line}' for line in range(1, num_lines+1)])
plt.xlabel('时间')
plt.ylabel('生产线')
plt.title('生产排程甘特图')
plt.legend()

plt.show()
