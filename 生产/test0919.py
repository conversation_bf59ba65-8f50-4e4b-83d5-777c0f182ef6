import numpy as np

col = 2
row = 2
time_slice = 2
# L = [1,2]
T = [[[1, 0],[1, 0]],[[0, 1],[1, 0]]]
row = len(T[0])
col = len(T[0][0])
T = np.array(T)
# T = np.array(T)
# for j in range(0, col):
#     for t in range(0, time_slice - L[j] + 1):
#         tempSum = 0
#         for tt in range(t, t + L[j]):
#             for i in range(0, row):
#                 tempSum += T[tt][i][j]

# y_start_times = [[0 for j in range(col)] for t in range(time_slice)]
# y_start_times = np.array(y_start_times)
# for t in range(time_slice):
#     for j in range(col):
#         print(y_start_times[t][j])
#
# for t in range(0, time_slice):
#     for j in range(0, col):
#         tempSum = 0
#         for i in range(0, row):
#             tempSum += T[t][i][j]
#         y_start_times[t][j] = tempSum
#         print(tempSum)

# y_start_times_values = [[0] * col for _ in range(time_slice)]
# # print(y_start_times_values)
# for t in range(0, time_slice):
#     for j in range(0, col):
#         tempSum = 0
#         for i in range(0, row):
#             tempSum += T[t][i][j]
#         y_start_times_values[t][j] = tempSum
# print(y_start_times_values)
# # 开始时间
# j_start_time = [next((t for t, x in enumerate(col) if x == 1), -1) for col in zip(*y_start_times_values)]
# print(j_start_time)

# temp_array =[[0 for t in range(time_slice)] for j in range(col)]
# for j in range(0, col):
#     temp = 0
#     for i in range(0, row):
#         for t in range(0, time_slice):
#             temp = (t + 1) * T[t][i][j]
#             temp_array[t][j] = temp
# temp_array = np.array(temp_array)
# non_zero_elements = temp_array[temp_array != 0]

temp_array = np.zeros((col, time_slice))
for j in range(0, col):
    temp = 0
    for t in range(0, time_slice):
        for i in range(0, row):
            temp += (t + 1) * T[t][i][j]    #有其他约束限制一个工序只能分配给一个生产线
        temp_array[j][t] = temp
filtered_matrix = [[element for element in row if element != 0] for row in temp_array]
min_values = [min(sublist) for sublist in filtered_matrix]


# temp_array = np.zeros((col, time_slice))
#
# # 填充 temp_array
# for j in range(0, col):
#     for t in range(0, time_slice):
#         temp = 0
#         for i in range(0, row):
#             temp += (t + 1) * T[t][i][j]
#         temp_array[j][t] = temp