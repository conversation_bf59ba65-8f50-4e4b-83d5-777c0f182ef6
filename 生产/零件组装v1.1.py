import math
import matplotlib.pyplot as plt
from matplotlib.ticker import MaxNLocator
from math import floor
import numpy as np
import xlrd
import pandas as pd
import pulp as pl
import pandas as pd
from math import cos, sin, acos, asin, pi, atan, fabs, ceil, floor
from collections import defaultdict
import random
import time
from gurobipy import *
import openpyxl
import sys
import os

#2023.9.21  修改连续时间约束 may be ok?

class Assignment:
    @classmethod
    def Assemblyprocess(cls, Q, L, time_slice):
        row = len(Q)   # 生产线
        col = len(Q[0])   # 零件组装工序

        # build a optimal problem
        pro = pl.LpProblem('Max(connection and coverage )', pl.LpMaximize)
        solver = pl.getSolver('GUROBI_CMD')

        # build variables for the optimal problem
        # i生产线 j零件组装工序 t时间
        lpvars = [[[pl.LpVariable("x" + str(i) + "y" + str(j) + "l" + str(t), lowBound=0, upBound=1, cat='Integer') for
                    j in range(col)] for i in range(row)]for t in range(time_slice)]
        # 创建二进制变量数组，表示每个j是否有一个tempSum等于L[j]
        has_tempSum_equal_L = [pl.LpVariable("y" + str(j), lowBound = 0, upBound = 1, cat = 'Binary') for j in range(col)]

        # build optimal function
        all = pl.LpAffineExpression()
        for t in range(0, time_slice):
            for i in range(0, row):
                for j in range(0, col):
                    all += Q[i][j] * lpvars[t][i][j]
        pro += all

        # build constraint
        # 满足L约束
        # for j in range(0, col):
        #     tempSum = 0
        #     for i in range(0, row):
        #         for t in range(0, time_slice):
        #             tempSum += lpvars[t][i][j]
        #     pro += tempSum == L[j]

        # 每条生产线的每个时间片只能给一个零部件组装工序  La约束
        for t in range(0, time_slice):
            for i in range(0, row):
                tempSum = 0
                for j in range(0, col):
                    tempSum += lpvars[t][i][j]
                pro += tempSum <= 1

        # 任意时间，同一零部件组装工序只能指派给一条生产线
        for t in range(0, time_slice):
            for j in range(0, col):
                tempSum = 0
                for i in range(0, row):
                    tempSum += lpvars[t][i][j]
                pro += tempSum <= 1

        # # 零部件组装工序时间矩阵
        # y_start_times_values = [[0] * col for _ in range(time_slice)]
        # for t in range(0, time_slice):
        #     for j in range(0, col):
        #         tempSum = 0
        #         for i in range(0, row):
        #             tempSum += lpvars[t][i][j]
        #         y_start_times_values[t][j] = tempSum
        #
        # # 开始时间
        # j_start_time = [next((t for t, x in enumerate(col) if x == 1), -1) for col in zip(*y_start_times_values)]
        #
        # # 连续时间
        # # for j in range(0, col):
        # #     tempSum = 0
        # #     for t in range(j_start_time[j], j_start_time[j] + L[j] + 1):
        # #         for i in range(0, row):
        # #             tempSum += lpvars[t][i][j]
        # #     pro += tempSum == L[j]

        # 零部件组装工序时间矩阵 开始时间 连续时间约束
        # y_start_times_values = [[0] * col for _ in range(time_slice)]
        # for j in range(0, col):
        #     for t in range(0, time_slice):
        #         tempSum = 0
        #         for i in range(0, row):
        #             tempSum += lpvars[t][i][j]
        #         y_start_times_values[t][j] = tempSum
        #     j_start_time = [next((t for t, x in enumerate(col) if x == 1), -1) for col in zip(*y_start_times_values)]
        #     temp = 0
        #     for tt in range(j_start_time[j], j_start_time[j] + L[j] + 1):
        #         for i in range(0, row):
        #             temp += lpvars[tt][i][j]
        #     pro += temp == L[j]

        # 连续时间，至少 L[j] 个时间片分配给 j
        # for j in range(0, col):
        #     for t in range(0, time_slice - L[j] + 1):
        #         tempSum = 0
        #         for tt in range(t, t + L[j]):
        #             for i in range(0, row):
        #                 tempSum += lpvars[tt][i][j]
        #         pro += tempSum == L[j]

        # 连续时间  至少 L[j] 个时间片分配给 j
        for j in range(col):
            tempSum = 0
            for t in range(0, time_slice - L[j] + 1):
                for tt in range(t, t + L[j]):
                    for i in range(row):
                        tempSum += lpvars[tt][i][j]
            # 添加约束，如果tempSum等于L[j]，则has_tempSum_equal_L[j]为1
            constraint_name = f'constraint_tempSum_{j}_t_{t}'  # 确保唯一性
            pro += pl.LpConstraint(e=tempSum - L[j] * has_tempSum_equal_L[j],
                                           sense=pl.LpConstraintEQ,
                                           rhs=0,
                                           name=constraint_name)

        # 确保每个j至少存在一个时间段，其中tempSum等于L[j]
        for j in range(col):
            pro += has_tempSum_equal_L[j] == 1

        # solve optimal problem
        status = pro.solve(solver)
        print("Assignment Status: ", pl.LpStatus[status])
        print("Final Assignment Result", pl.value(pro.objective))

        # get the result of T matrix
        T = [[[lpvars[t][i][j].varValue for j in range(col)] for i in range(row)] for t in range(time_slice)]
        return T

# 生成随机的Q矩阵
def genRandQMat(agentNum, roleNum):
	resMat = np.zeros((agentNum, roleNum))
	for i in range(agentNum):
		for j in range(roleNum):
			resMat[i][j] = round(np.random.random(), 2)
	return resMat

# 归一化后的Q
def getNormalizedQ(Q):
    max = min = Q[0][0]
    row = len(Q)
    column = len(Q[0])
    for i in range(row):
        for j in range(column):
            if (Q[i, j] > max):
                max = Q[i, j]
            if (Q[i, j] < min):
                min = Q[i, j]
    for i in range(row):
        for j in range(column):
            Q[i, j] = round((Q[i, j] - min) / (max - min), 4)
    return Q

if __name__ == '__main__':

    time_slice = 60
    lines = 3
    process = 10
    L = [1,2,3,4,5,1,2,3,4,5]
    Q = genRandQMat(lines,process)
    QMatrix = getNormalizedQ(Q)

    T1 = Assignment.Assemblyprocess(QMatrix, L, time_slice)
    T1 = np.array(T1)

    totalL = [0 for j in range(process)]
    for j in range(process):
        cont = 0
        for t in range(0, time_slice):
            for i in range(lines):
                cont += T1[t][i][j]
        totalL[j] = cont
    print(totalL)


