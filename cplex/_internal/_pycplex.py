# This file was automatically generated by SWIG (http://www.swig.org).
# Version 3.0.2
#
# Do not make changes to this file unless you know what you are doing--modify
# the SWIG interface file instead.





from sys import version_info
if version_info >= (2,6,0):
    def swig_import_helper():
        from os.path import dirname
        import imp
        fp = None
        try:
            fp, pathname, description = imp.find_module('_pycplex_platform', [dirname(__file__)])
        except ImportError:
            import _pycplex_platform
            return _pycplex_platform
        if fp is not None:
            try:
                _mod = imp.load_module('_pycplex_platform', fp, pathname, description)
            finally:
                fp.close()
            return _mod
    _pycplex_platform = swig_import_helper()
    del swig_import_helper
else:
    import _pycplex_platform
del version_info
try:
    _swig_property = property
except NameError:
    pass # Python < 2.2 doesn't have 'property'.
def _swig_setattr_nondynamic(self,class_type,name,value,static=1):
    if (name == "thisown"): return self.this.own(value)
    if (name == "this"):
        if type(value).__name__ == 'SwigPyObject':
            self.__dict__[name] = value
            return
    method = class_type.__swig_setmethods__.get(name,None)
    if method: return method(self,value)
    if (not static):
        self.__dict__[name] = value
    else:
        raise AttributeError("You cannot add attributes to %s" % self)

def _swig_setattr(self,class_type,name,value):
    return _swig_setattr_nondynamic(self,class_type,name,value,0)

def _swig_getattr(self,class_type,name):
    if (name == "thisown"): return self.this.own()
    method = class_type.__swig_getmethods__.get(name,None)
    if method: return method(self)
    raise AttributeError(name)

def _swig_repr(self):
    try: strthis = "proxy of " + self.this.__repr__()
    except: strthis = ""
    return "<%s.%s; %s >" % (self.__class__.__module__, self.__class__.__name__, strthis,)

try:
    _object = object
    _newclass = 1
except AttributeError:
    class _object : pass
    _newclass = 0


CPX_H = _pycplex_platform.CPX_H
CPXBAR_H = _pycplex_platform.CPXBAR_H
CPXMIP_H = _pycplex_platform.CPXMIP_H
CPXGC_H = _pycplex_platform.CPXGC_H
CPXNET_H = _pycplex_platform.CPXNET_H
CPXQP_H = _pycplex_platform.CPXQP_H
CPXSOCP_H = _pycplex_platform.CPXSOCP_H
CPX_FEATURES_H = _pycplex_platform.CPX_FEATURES_H
CPX_FEATURE_REMOTE_OBJECT = _pycplex_platform.CPX_FEATURE_REMOTE_OBJECT
CPX_FEATURE_DISTRIBUTED_MIP = _pycplex_platform.CPX_FEATURE_DISTRIBUTED_MIP
CPX_CPXAUTOINTTYPES_H_H = _pycplex_platform.CPX_CPXAUTOINTTYPES_H_H
CPXBYTE_DEFINED = _pycplex_platform.CPXBYTE_DEFINED
CPXINT_DEFINED = _pycplex_platform.CPXINT_DEFINED
CPXLONG_DEFINED = _pycplex_platform.CPXLONG_DEFINED
CPXSHORT_DEFINED = _pycplex_platform.CPXSHORT_DEFINED
CPXULONG_DEFINED = _pycplex_platform.CPXULONG_DEFINED
CPX_STR_PARAM_MAX = _pycplex_platform.CPX_STR_PARAM_MAX
class cpxiodevice(_object):
    __swig_setmethods__ = {}
    __setattr__ = lambda self, name, value: _swig_setattr(self, cpxiodevice, name, value)
    __swig_getmethods__ = {}
    __getattr__ = lambda self, name: _swig_getattr(self, cpxiodevice, name)
    __repr__ = _swig_repr
    __swig_setmethods__["cpxiodev_eof"] = _pycplex_platform.cpxiodevice_cpxiodev_eof_set
    __swig_getmethods__["cpxiodev_eof"] = _pycplex_platform.cpxiodevice_cpxiodev_eof_get
    if _newclass:cpxiodev_eof = _swig_property(_pycplex_platform.cpxiodevice_cpxiodev_eof_get, _pycplex_platform.cpxiodevice_cpxiodev_eof_set)
    __swig_setmethods__["cpxiodev_error"] = _pycplex_platform.cpxiodevice_cpxiodev_error_set
    __swig_getmethods__["cpxiodev_error"] = _pycplex_platform.cpxiodevice_cpxiodev_error_get
    if _newclass:cpxiodev_error = _swig_property(_pycplex_platform.cpxiodevice_cpxiodev_error_get, _pycplex_platform.cpxiodevice_cpxiodev_error_set)
    __swig_setmethods__["cpxiodev_rewind"] = _pycplex_platform.cpxiodevice_cpxiodev_rewind_set
    __swig_getmethods__["cpxiodev_rewind"] = _pycplex_platform.cpxiodevice_cpxiodev_rewind_get
    if _newclass:cpxiodev_rewind = _swig_property(_pycplex_platform.cpxiodevice_cpxiodev_rewind_get, _pycplex_platform.cpxiodevice_cpxiodev_rewind_set)
    __swig_setmethods__["cpxiodev_flush"] = _pycplex_platform.cpxiodevice_cpxiodev_flush_set
    __swig_getmethods__["cpxiodev_flush"] = _pycplex_platform.cpxiodevice_cpxiodev_flush_get
    if _newclass:cpxiodev_flush = _swig_property(_pycplex_platform.cpxiodevice_cpxiodev_flush_get, _pycplex_platform.cpxiodevice_cpxiodev_flush_set)
    __swig_setmethods__["cpxiodev_close"] = _pycplex_platform.cpxiodevice_cpxiodev_close_set
    __swig_getmethods__["cpxiodev_close"] = _pycplex_platform.cpxiodevice_cpxiodev_close_get
    if _newclass:cpxiodev_close = _swig_property(_pycplex_platform.cpxiodevice_cpxiodev_close_get, _pycplex_platform.cpxiodevice_cpxiodev_close_set)
    __swig_setmethods__["cpxiodev_putc"] = _pycplex_platform.cpxiodevice_cpxiodev_putc_set
    __swig_getmethods__["cpxiodev_putc"] = _pycplex_platform.cpxiodevice_cpxiodev_putc_get
    if _newclass:cpxiodev_putc = _swig_property(_pycplex_platform.cpxiodevice_cpxiodev_putc_get, _pycplex_platform.cpxiodevice_cpxiodev_putc_set)
    __swig_setmethods__["cpxiodev_puts"] = _pycplex_platform.cpxiodevice_cpxiodev_puts_set
    __swig_getmethods__["cpxiodev_puts"] = _pycplex_platform.cpxiodevice_cpxiodev_puts_get
    if _newclass:cpxiodev_puts = _swig_property(_pycplex_platform.cpxiodevice_cpxiodev_puts_get, _pycplex_platform.cpxiodevice_cpxiodev_puts_set)
    __swig_setmethods__["cpxiodev_read"] = _pycplex_platform.cpxiodevice_cpxiodev_read_set
    __swig_getmethods__["cpxiodev_read"] = _pycplex_platform.cpxiodevice_cpxiodev_read_get
    if _newclass:cpxiodev_read = _swig_property(_pycplex_platform.cpxiodevice_cpxiodev_read_get, _pycplex_platform.cpxiodevice_cpxiodev_read_set)
    __swig_setmethods__["cpxiodev_write"] = _pycplex_platform.cpxiodevice_cpxiodev_write_set
    __swig_getmethods__["cpxiodev_write"] = _pycplex_platform.cpxiodevice_cpxiodev_write_get
    if _newclass:cpxiodev_write = _swig_property(_pycplex_platform.cpxiodevice_cpxiodev_write_get, _pycplex_platform.cpxiodevice_cpxiodev_write_set)
    def __init__(self): 
        this = _pycplex_platform.new_cpxiodevice()
        try: self.this.append(this)
        except: self.this = this
    __swig_destroy__ = _pycplex_platform.delete_cpxiodevice
    __del__ = lambda self : None;
cpxiodevice_swigregister = _pycplex_platform.cpxiodevice_swigregister
cpxiodevice_swigregister(cpxiodevice)
cvar = _pycplex_platform.cvar
CPX_NULL = cvar.CPX_NULL
ext_name = cvar.ext_name

CPX_VERSION = _pycplex_platform.CPX_VERSION
CPX_VERSION_VERSION = _pycplex_platform.CPX_VERSION_VERSION
CPX_VERSION_RELEASE = _pycplex_platform.CPX_VERSION_RELEASE
CPX_VERSION_MODIFICATION = _pycplex_platform.CPX_VERSION_MODIFICATION
CPX_VERSION_FIX = _pycplex_platform.CPX_VERSION_FIX
CPX_INFBOUND = _pycplex_platform.CPX_INFBOUND
CPX_MINBOUND = _pycplex_platform.CPX_MINBOUND
CPX_PARAMTYPE_NONE = _pycplex_platform.CPX_PARAMTYPE_NONE
CPX_PARAMTYPE_INT = _pycplex_platform.CPX_PARAMTYPE_INT
CPX_PARAMTYPE_DOUBLE = _pycplex_platform.CPX_PARAMTYPE_DOUBLE
CPX_PARAMTYPE_STRING = _pycplex_platform.CPX_PARAMTYPE_STRING
CPX_PARAMTYPE_LONG = _pycplex_platform.CPX_PARAMTYPE_LONG
CPX_NO_SOLN = _pycplex_platform.CPX_NO_SOLN
CPX_AUTO_SOLN = _pycplex_platform.CPX_AUTO_SOLN
CPX_BASIC_SOLN = _pycplex_platform.CPX_BASIC_SOLN
CPX_NONBASIC_SOLN = _pycplex_platform.CPX_NONBASIC_SOLN
CPX_PRIMAL_SOLN = _pycplex_platform.CPX_PRIMAL_SOLN
CPX_PRECOL_LOW = _pycplex_platform.CPX_PRECOL_LOW
CPX_PRECOL_UP = _pycplex_platform.CPX_PRECOL_UP
CPX_PRECOL_FIX = _pycplex_platform.CPX_PRECOL_FIX
CPX_PRECOL_AGG = _pycplex_platform.CPX_PRECOL_AGG
CPX_PRECOL_OTHER = _pycplex_platform.CPX_PRECOL_OTHER
CPX_PREROW_RED = _pycplex_platform.CPX_PREROW_RED
CPX_PREROW_AGG = _pycplex_platform.CPX_PREROW_AGG
CPX_PREROW_OTHER = _pycplex_platform.CPX_PREROW_OTHER
CPX_AUTO = _pycplex_platform.CPX_AUTO
CPX_ON = _pycplex_platform.CPX_ON
CPX_OFF = _pycplex_platform.CPX_OFF
CPX_MAX = _pycplex_platform.CPX_MAX
CPX_MIN = _pycplex_platform.CPX_MIN
CPX_DATACHECK_OFF = _pycplex_platform.CPX_DATACHECK_OFF
CPX_DATACHECK_WARN = _pycplex_platform.CPX_DATACHECK_WARN
CPX_DATACHECK_ASSIST = _pycplex_platform.CPX_DATACHECK_ASSIST
CPX_PPRIIND_PARTIAL = _pycplex_platform.CPX_PPRIIND_PARTIAL
CPX_PPRIIND_AUTO = _pycplex_platform.CPX_PPRIIND_AUTO
CPX_PPRIIND_DEVEX = _pycplex_platform.CPX_PPRIIND_DEVEX
CPX_PPRIIND_STEEP = _pycplex_platform.CPX_PPRIIND_STEEP
CPX_PPRIIND_STEEPQSTART = _pycplex_platform.CPX_PPRIIND_STEEPQSTART
CPX_PPRIIND_FULL = _pycplex_platform.CPX_PPRIIND_FULL
CPX_DPRIIND_AUTO = _pycplex_platform.CPX_DPRIIND_AUTO
CPX_DPRIIND_FULL = _pycplex_platform.CPX_DPRIIND_FULL
CPX_DPRIIND_STEEP = _pycplex_platform.CPX_DPRIIND_STEEP
CPX_DPRIIND_FULLSTEEP = _pycplex_platform.CPX_DPRIIND_FULLSTEEP
CPX_DPRIIND_STEEPQSTART = _pycplex_platform.CPX_DPRIIND_STEEPQSTART
CPX_DPRIIND_DEVEX = _pycplex_platform.CPX_DPRIIND_DEVEX
CPX_PARALLEL_DETERMINISTIC = _pycplex_platform.CPX_PARALLEL_DETERMINISTIC
CPX_PARALLEL_AUTO = _pycplex_platform.CPX_PARALLEL_AUTO
CPX_PARALLEL_OPPORTUNISTIC = _pycplex_platform.CPX_PARALLEL_OPPORTUNISTIC
CPX_WRITELEVEL_AUTO = _pycplex_platform.CPX_WRITELEVEL_AUTO
CPX_WRITELEVEL_ALLVARS = _pycplex_platform.CPX_WRITELEVEL_ALLVARS
CPX_WRITELEVEL_DISCRETEVARS = _pycplex_platform.CPX_WRITELEVEL_DISCRETEVARS
CPX_WRITELEVEL_NONZEROVARS = _pycplex_platform.CPX_WRITELEVEL_NONZEROVARS
CPX_WRITELEVEL_NONZERODISCRETEVARS = _pycplex_platform.CPX_WRITELEVEL_NONZERODISCRETEVARS
CPX_OPTIMALITYTARGET_AUTO = _pycplex_platform.CPX_OPTIMALITYTARGET_AUTO
CPX_OPTIMALITYTARGET_OPTIMALCONVEX = _pycplex_platform.CPX_OPTIMALITYTARGET_OPTIMALCONVEX
CPX_OPTIMALITYTARGET_FIRSTORDER = _pycplex_platform.CPX_OPTIMALITYTARGET_FIRSTORDER
CPX_OPTIMALITYTARGET_OPTIMALGLOBAL = _pycplex_platform.CPX_OPTIMALITYTARGET_OPTIMALGLOBAL
CPX_ALG_NONE = _pycplex_platform.CPX_ALG_NONE
CPX_ALG_AUTOMATIC = _pycplex_platform.CPX_ALG_AUTOMATIC
CPX_ALG_PRIMAL = _pycplex_platform.CPX_ALG_PRIMAL
CPX_ALG_DUAL = _pycplex_platform.CPX_ALG_DUAL
CPX_ALG_NET = _pycplex_platform.CPX_ALG_NET
CPX_ALG_BARRIER = _pycplex_platform.CPX_ALG_BARRIER
CPX_ALG_SIFTING = _pycplex_platform.CPX_ALG_SIFTING
CPX_ALG_CONCURRENT = _pycplex_platform.CPX_ALG_CONCURRENT
CPX_ALG_BAROPT = _pycplex_platform.CPX_ALG_BAROPT
CPX_ALG_PIVOTIN = _pycplex_platform.CPX_ALG_PIVOTIN
CPX_ALG_PIVOTOUT = _pycplex_platform.CPX_ALG_PIVOTOUT
CPX_ALG_PIVOT = _pycplex_platform.CPX_ALG_PIVOT
CPX_ALG_FEASOPT = _pycplex_platform.CPX_ALG_FEASOPT
CPX_ALG_MIP = _pycplex_platform.CPX_ALG_MIP
CPX_ALG_BENDERS = _pycplex_platform.CPX_ALG_BENDERS
CPX_ALG_MULTIOBJ = _pycplex_platform.CPX_ALG_MULTIOBJ
CPX_ALG_ROBUST = _pycplex_platform.CPX_ALG_ROBUST
CPX_AT_LOWER = _pycplex_platform.CPX_AT_LOWER
CPX_BASIC = _pycplex_platform.CPX_BASIC
CPX_AT_UPPER = _pycplex_platform.CPX_AT_UPPER
CPX_FREE_SUPER = _pycplex_platform.CPX_FREE_SUPER
CPX_NO_VARIABLE = _pycplex_platform.CPX_NO_VARIABLE
CPX_CONTINUOUS = _pycplex_platform.CPX_CONTINUOUS
CPX_BINARY = _pycplex_platform.CPX_BINARY
CPX_INTEGER = _pycplex_platform.CPX_INTEGER
CPX_SEMICONT = _pycplex_platform.CPX_SEMICONT
CPX_SEMIINT = _pycplex_platform.CPX_SEMIINT
CPX_PREREDUCE_PRIMALANDDUAL = _pycplex_platform.CPX_PREREDUCE_PRIMALANDDUAL
CPX_PREREDUCE_DUALONLY = _pycplex_platform.CPX_PREREDUCE_DUALONLY
CPX_PREREDUCE_PRIMALONLY = _pycplex_platform.CPX_PREREDUCE_PRIMALONLY
CPX_PREREDUCE_NOPRIMALORDUAL = _pycplex_platform.CPX_PREREDUCE_NOPRIMALORDUAL
CPX_CONFLICT_EXCLUDED = _pycplex_platform.CPX_CONFLICT_EXCLUDED
CPX_CONFLICT_POSSIBLE_MEMBER = _pycplex_platform.CPX_CONFLICT_POSSIBLE_MEMBER
CPX_CONFLICT_POSSIBLE_LB = _pycplex_platform.CPX_CONFLICT_POSSIBLE_LB
CPX_CONFLICT_POSSIBLE_UB = _pycplex_platform.CPX_CONFLICT_POSSIBLE_UB
CPX_CONFLICT_MEMBER = _pycplex_platform.CPX_CONFLICT_MEMBER
CPX_CONFLICT_LB = _pycplex_platform.CPX_CONFLICT_LB
CPX_CONFLICT_UB = _pycplex_platform.CPX_CONFLICT_UB
CPX_CONFLICTALG_AUTO = _pycplex_platform.CPX_CONFLICTALG_AUTO
CPX_CONFLICTALG_FAST = _pycplex_platform.CPX_CONFLICTALG_FAST
CPX_CONFLICTALG_PROPAGATE = _pycplex_platform.CPX_CONFLICTALG_PROPAGATE
CPX_CONFLICTALG_PRESOLVE = _pycplex_platform.CPX_CONFLICTALG_PRESOLVE
CPX_CONFLICTALG_IIS = _pycplex_platform.CPX_CONFLICTALG_IIS
CPX_CONFLICTALG_LIMITSOLVE = _pycplex_platform.CPX_CONFLICTALG_LIMITSOLVE
CPX_CONFLICTALG_SOLVE = _pycplex_platform.CPX_CONFLICTALG_SOLVE
CPXPROB_LP = _pycplex_platform.CPXPROB_LP
CPXPROB_MILP = _pycplex_platform.CPXPROB_MILP
CPXPROB_FIXEDMILP = _pycplex_platform.CPXPROB_FIXEDMILP
CPXPROB_NODELP = _pycplex_platform.CPXPROB_NODELP
CPXPROB_QP = _pycplex_platform.CPXPROB_QP
CPXPROB_MIQP = _pycplex_platform.CPXPROB_MIQP
CPXPROB_FIXEDMIQP = _pycplex_platform.CPXPROB_FIXEDMIQP
CPXPROB_NODEQP = _pycplex_platform.CPXPROB_NODEQP
CPXPROB_QCP = _pycplex_platform.CPXPROB_QCP
CPXPROB_MIQCP = _pycplex_platform.CPXPROB_MIQCP
CPXPROB_NODEQCP = _pycplex_platform.CPXPROB_NODEQCP
CPX_LPREADER_LEGACY = _pycplex_platform.CPX_LPREADER_LEGACY
CPX_LPREADER_NEW = _pycplex_platform.CPX_LPREADER_NEW
CPX_PARAM_ALL_MIN = _pycplex_platform.CPX_PARAM_ALL_MIN
CPX_PARAM_ALL_MAX = _pycplex_platform.CPX_PARAM_ALL_MAX
CPX_CALLBACK_PRIMAL = _pycplex_platform.CPX_CALLBACK_PRIMAL
CPX_CALLBACK_DUAL = _pycplex_platform.CPX_CALLBACK_DUAL
CPX_CALLBACK_NETWORK = _pycplex_platform.CPX_CALLBACK_NETWORK
CPX_CALLBACK_PRIMAL_CROSSOVER = _pycplex_platform.CPX_CALLBACK_PRIMAL_CROSSOVER
CPX_CALLBACK_DUAL_CROSSOVER = _pycplex_platform.CPX_CALLBACK_DUAL_CROSSOVER
CPX_CALLBACK_BARRIER = _pycplex_platform.CPX_CALLBACK_BARRIER
CPX_CALLBACK_PRESOLVE = _pycplex_platform.CPX_CALLBACK_PRESOLVE
CPX_CALLBACK_QPBARRIER = _pycplex_platform.CPX_CALLBACK_QPBARRIER
CPX_CALLBACK_QPSIMPLEX = _pycplex_platform.CPX_CALLBACK_QPSIMPLEX
CPX_CALLBACK_TUNING = _pycplex_platform.CPX_CALLBACK_TUNING
CPX_CALLBACK_INFO_PRIMAL_OBJ = _pycplex_platform.CPX_CALLBACK_INFO_PRIMAL_OBJ
CPX_CALLBACK_INFO_DUAL_OBJ = _pycplex_platform.CPX_CALLBACK_INFO_DUAL_OBJ
CPX_CALLBACK_INFO_PRIMAL_INFMEAS = _pycplex_platform.CPX_CALLBACK_INFO_PRIMAL_INFMEAS
CPX_CALLBACK_INFO_DUAL_INFMEAS = _pycplex_platform.CPX_CALLBACK_INFO_DUAL_INFMEAS
CPX_CALLBACK_INFO_PRIMAL_FEAS = _pycplex_platform.CPX_CALLBACK_INFO_PRIMAL_FEAS
CPX_CALLBACK_INFO_DUAL_FEAS = _pycplex_platform.CPX_CALLBACK_INFO_DUAL_FEAS
CPX_CALLBACK_INFO_ITCOUNT = _pycplex_platform.CPX_CALLBACK_INFO_ITCOUNT
CPX_CALLBACK_INFO_CROSSOVER_PPUSH = _pycplex_platform.CPX_CALLBACK_INFO_CROSSOVER_PPUSH
CPX_CALLBACK_INFO_CROSSOVER_PEXCH = _pycplex_platform.CPX_CALLBACK_INFO_CROSSOVER_PEXCH
CPX_CALLBACK_INFO_CROSSOVER_DPUSH = _pycplex_platform.CPX_CALLBACK_INFO_CROSSOVER_DPUSH
CPX_CALLBACK_INFO_CROSSOVER_DEXCH = _pycplex_platform.CPX_CALLBACK_INFO_CROSSOVER_DEXCH
CPX_CALLBACK_INFO_CROSSOVER_SBCNT = _pycplex_platform.CPX_CALLBACK_INFO_CROSSOVER_SBCNT
CPX_CALLBACK_INFO_PRESOLVE_ROWSGONE = _pycplex_platform.CPX_CALLBACK_INFO_PRESOLVE_ROWSGONE
CPX_CALLBACK_INFO_PRESOLVE_COLSGONE = _pycplex_platform.CPX_CALLBACK_INFO_PRESOLVE_COLSGONE
CPX_CALLBACK_INFO_PRESOLVE_AGGSUBST = _pycplex_platform.CPX_CALLBACK_INFO_PRESOLVE_AGGSUBST
CPX_CALLBACK_INFO_PRESOLVE_COEFFS = _pycplex_platform.CPX_CALLBACK_INFO_PRESOLVE_COEFFS
CPX_CALLBACK_INFO_USER_PROBLEM = _pycplex_platform.CPX_CALLBACK_INFO_USER_PROBLEM
CPX_CALLBACK_INFO_TUNING_PROGRESS = _pycplex_platform.CPX_CALLBACK_INFO_TUNING_PROGRESS
CPX_CALLBACK_INFO_ENDTIME = _pycplex_platform.CPX_CALLBACK_INFO_ENDTIME
CPX_CALLBACK_INFO_ITCOUNT_LONG = _pycplex_platform.CPX_CALLBACK_INFO_ITCOUNT_LONG
CPX_CALLBACK_INFO_CROSSOVER_PPUSH_LONG = _pycplex_platform.CPX_CALLBACK_INFO_CROSSOVER_PPUSH_LONG
CPX_CALLBACK_INFO_CROSSOVER_PEXCH_LONG = _pycplex_platform.CPX_CALLBACK_INFO_CROSSOVER_PEXCH_LONG
CPX_CALLBACK_INFO_CROSSOVER_DPUSH_LONG = _pycplex_platform.CPX_CALLBACK_INFO_CROSSOVER_DPUSH_LONG
CPX_CALLBACK_INFO_CROSSOVER_DEXCH_LONG = _pycplex_platform.CPX_CALLBACK_INFO_CROSSOVER_DEXCH_LONG
CPX_CALLBACK_INFO_PRESOLVE_AGGSUBST_LONG = _pycplex_platform.CPX_CALLBACK_INFO_PRESOLVE_AGGSUBST_LONG
CPX_CALLBACK_INFO_PRESOLVE_COEFFS_LONG = _pycplex_platform.CPX_CALLBACK_INFO_PRESOLVE_COEFFS_LONG
CPX_CALLBACK_INFO_ENDDETTIME = _pycplex_platform.CPX_CALLBACK_INFO_ENDDETTIME
CPX_CALLBACK_INFO_STARTTIME = _pycplex_platform.CPX_CALLBACK_INFO_STARTTIME
CPX_CALLBACK_INFO_STARTDETTIME = _pycplex_platform.CPX_CALLBACK_INFO_STARTDETTIME
CPX_TUNE_AVERAGE = _pycplex_platform.CPX_TUNE_AVERAGE
CPX_TUNE_MINMAX = _pycplex_platform.CPX_TUNE_MINMAX
CPX_TUNE_ABORT = _pycplex_platform.CPX_TUNE_ABORT
CPX_TUNE_TILIM = _pycplex_platform.CPX_TUNE_TILIM
CPX_TUNE_DETTILIM = _pycplex_platform.CPX_TUNE_DETTILIM
CPX_FEASOPT_MIN_SUM = _pycplex_platform.CPX_FEASOPT_MIN_SUM
CPX_FEASOPT_OPT_SUM = _pycplex_platform.CPX_FEASOPT_OPT_SUM
CPX_FEASOPT_MIN_INF = _pycplex_platform.CPX_FEASOPT_MIN_INF
CPX_FEASOPT_OPT_INF = _pycplex_platform.CPX_FEASOPT_OPT_INF
CPX_FEASOPT_MIN_QUAD = _pycplex_platform.CPX_FEASOPT_MIN_QUAD
CPX_FEASOPT_OPT_QUAD = _pycplex_platform.CPX_FEASOPT_OPT_QUAD
CPX_BENDERSSTRATEGY_OFF = _pycplex_platform.CPX_BENDERSSTRATEGY_OFF
CPX_BENDERSSTRATEGY_AUTO = _pycplex_platform.CPX_BENDERSSTRATEGY_AUTO
CPX_BENDERSSTRATEGY_USER = _pycplex_platform.CPX_BENDERSSTRATEGY_USER
CPX_BENDERSSTRATEGY_WORKERS = _pycplex_platform.CPX_BENDERSSTRATEGY_WORKERS
CPX_BENDERSSTRATEGY_FULL = _pycplex_platform.CPX_BENDERSSTRATEGY_FULL
CPX_ANNOTATIONDATA_LONG = _pycplex_platform.CPX_ANNOTATIONDATA_LONG
CPX_ANNOTATIONDATA_DOUBLE = _pycplex_platform.CPX_ANNOTATIONDATA_DOUBLE
CPX_ANNOTATIONOBJ_OBJ = _pycplex_platform.CPX_ANNOTATIONOBJ_OBJ
CPX_ANNOTATIONOBJ_COL = _pycplex_platform.CPX_ANNOTATIONOBJ_COL
CPX_ANNOTATIONOBJ_ROW = _pycplex_platform.CPX_ANNOTATIONOBJ_ROW
CPX_ANNOTATIONOBJ_SOS = _pycplex_platform.CPX_ANNOTATIONOBJ_SOS
CPX_ANNOTATIONOBJ_IND = _pycplex_platform.CPX_ANNOTATIONOBJ_IND
CPX_ANNOTATIONOBJ_QC = _pycplex_platform.CPX_ANNOTATIONOBJ_QC
CPX_ANNOTATIONOBJ_LAST = _pycplex_platform.CPX_ANNOTATIONOBJ_LAST
CPXIIS_COMPLETE = _pycplex_platform.CPXIIS_COMPLETE
CPXIIS_PARTIAL = _pycplex_platform.CPXIIS_PARTIAL
CPXIIS_AT_LOWER = _pycplex_platform.CPXIIS_AT_LOWER
CPXIIS_FIXED = _pycplex_platform.CPXIIS_FIXED
CPXIIS_AT_UPPER = _pycplex_platform.CPXIIS_AT_UPPER
CPX_BARORDER_AUTO = _pycplex_platform.CPX_BARORDER_AUTO
CPX_BARORDER_AMD = _pycplex_platform.CPX_BARORDER_AMD
CPX_BARORDER_AMF = _pycplex_platform.CPX_BARORDER_AMF
CPX_BARORDER_ND = _pycplex_platform.CPX_BARORDER_ND
CPX_MIPEMPHASIS_BALANCED = _pycplex_platform.CPX_MIPEMPHASIS_BALANCED
CPX_MIPEMPHASIS_FEASIBILITY = _pycplex_platform.CPX_MIPEMPHASIS_FEASIBILITY
CPX_MIPEMPHASIS_OPTIMALITY = _pycplex_platform.CPX_MIPEMPHASIS_OPTIMALITY
CPX_MIPEMPHASIS_BESTBOUND = _pycplex_platform.CPX_MIPEMPHASIS_BESTBOUND
CPX_MIPEMPHASIS_HIDDENFEAS = _pycplex_platform.CPX_MIPEMPHASIS_HIDDENFEAS
CPX_TYPE_VAR = _pycplex_platform.CPX_TYPE_VAR
CPX_TYPE_SOS1 = _pycplex_platform.CPX_TYPE_SOS1
CPX_TYPE_SOS2 = _pycplex_platform.CPX_TYPE_SOS2
CPX_TYPE_USER = _pycplex_platform.CPX_TYPE_USER
CPX_TYPE_ANY = _pycplex_platform.CPX_TYPE_ANY
CPX_VARSEL_MININFEAS = _pycplex_platform.CPX_VARSEL_MININFEAS
CPX_VARSEL_DEFAULT = _pycplex_platform.CPX_VARSEL_DEFAULT
CPX_VARSEL_MAXINFEAS = _pycplex_platform.CPX_VARSEL_MAXINFEAS
CPX_VARSEL_PSEUDO = _pycplex_platform.CPX_VARSEL_PSEUDO
CPX_VARSEL_STRONG = _pycplex_platform.CPX_VARSEL_STRONG
CPX_VARSEL_PSEUDOREDUCED = _pycplex_platform.CPX_VARSEL_PSEUDOREDUCED
CPX_NODESEL_DFS = _pycplex_platform.CPX_NODESEL_DFS
CPX_NODESEL_BESTBOUND = _pycplex_platform.CPX_NODESEL_BESTBOUND
CPX_NODESEL_BESTEST = _pycplex_platform.CPX_NODESEL_BESTEST
CPX_NODESEL_BESTEST_ALT = _pycplex_platform.CPX_NODESEL_BESTEST_ALT
CPX_MIPORDER_COST = _pycplex_platform.CPX_MIPORDER_COST
CPX_MIPORDER_BOUNDS = _pycplex_platform.CPX_MIPORDER_BOUNDS
CPX_MIPORDER_SCALEDCOST = _pycplex_platform.CPX_MIPORDER_SCALEDCOST
CPX_BRANCH_GLOBAL = _pycplex_platform.CPX_BRANCH_GLOBAL
CPX_BRANCH_DOWN = _pycplex_platform.CPX_BRANCH_DOWN
CPX_BRANCH_UP = _pycplex_platform.CPX_BRANCH_UP
CPX_BRDIR_DOWN = _pycplex_platform.CPX_BRDIR_DOWN
CPX_BRDIR_AUTO = _pycplex_platform.CPX_BRDIR_AUTO
CPX_BRDIR_UP = _pycplex_platform.CPX_BRDIR_UP
CPX_CUT_COVER = _pycplex_platform.CPX_CUT_COVER
CPX_CUT_GUBCOVER = _pycplex_platform.CPX_CUT_GUBCOVER
CPX_CUT_FLOWCOVER = _pycplex_platform.CPX_CUT_FLOWCOVER
CPX_CUT_CLIQUE = _pycplex_platform.CPX_CUT_CLIQUE
CPX_CUT_FRAC = _pycplex_platform.CPX_CUT_FRAC
CPX_CUT_MIR = _pycplex_platform.CPX_CUT_MIR
CPX_CUT_FLOWPATH = _pycplex_platform.CPX_CUT_FLOWPATH
CPX_CUT_DISJ = _pycplex_platform.CPX_CUT_DISJ
CPX_CUT_IMPLBD = _pycplex_platform.CPX_CUT_IMPLBD
CPX_CUT_ZEROHALF = _pycplex_platform.CPX_CUT_ZEROHALF
CPX_CUT_MCF = _pycplex_platform.CPX_CUT_MCF
CPX_CUT_LOCALCOVER = _pycplex_platform.CPX_CUT_LOCALCOVER
CPX_CUT_TIGHTEN = _pycplex_platform.CPX_CUT_TIGHTEN
CPX_CUT_OBJDISJ = _pycplex_platform.CPX_CUT_OBJDISJ
CPX_CUT_LANDP = _pycplex_platform.CPX_CUT_LANDP
CPX_CUT_USER = _pycplex_platform.CPX_CUT_USER
CPX_CUT_TABLE = _pycplex_platform.CPX_CUT_TABLE
CPX_CUT_SOLNPOOL = _pycplex_platform.CPX_CUT_SOLNPOOL
CPX_CUT_LOCALIMPLBD = _pycplex_platform.CPX_CUT_LOCALIMPLBD
CPX_CUT_BQP = _pycplex_platform.CPX_CUT_BQP
CPX_CUT_RLT = _pycplex_platform.CPX_CUT_RLT
CPX_CUT_BENDERS = _pycplex_platform.CPX_CUT_BENDERS
CPX_CUT_NUM_TYPES = _pycplex_platform.CPX_CUT_NUM_TYPES
CPX_MIPSEARCH_AUTO = _pycplex_platform.CPX_MIPSEARCH_AUTO
CPX_MIPSEARCH_TRADITIONAL = _pycplex_platform.CPX_MIPSEARCH_TRADITIONAL
CPX_MIPSEARCH_DYNAMIC = _pycplex_platform.CPX_MIPSEARCH_DYNAMIC
CPX_MIPKAPPA_OFF = _pycplex_platform.CPX_MIPKAPPA_OFF
CPX_MIPKAPPA_AUTO = _pycplex_platform.CPX_MIPKAPPA_AUTO
CPX_MIPKAPPA_SAMPLE = _pycplex_platform.CPX_MIPKAPPA_SAMPLE
CPX_MIPKAPPA_FULL = _pycplex_platform.CPX_MIPKAPPA_FULL
CPX_MIPSTART_AUTO = _pycplex_platform.CPX_MIPSTART_AUTO
CPX_MIPSTART_CHECKFEAS = _pycplex_platform.CPX_MIPSTART_CHECKFEAS
CPX_MIPSTART_SOLVEFIXED = _pycplex_platform.CPX_MIPSTART_SOLVEFIXED
CPX_MIPSTART_SOLVEMIP = _pycplex_platform.CPX_MIPSTART_SOLVEMIP
CPX_MIPSTART_REPAIR = _pycplex_platform.CPX_MIPSTART_REPAIR
CPX_MIPSTART_NOCHECK = _pycplex_platform.CPX_MIPSTART_NOCHECK
CPX_CALLBACK_MIP = _pycplex_platform.CPX_CALLBACK_MIP
CPX_CALLBACK_MIP_BRANCH = _pycplex_platform.CPX_CALLBACK_MIP_BRANCH
CPX_CALLBACK_MIP_NODE = _pycplex_platform.CPX_CALLBACK_MIP_NODE
CPX_CALLBACK_MIP_HEURISTIC = _pycplex_platform.CPX_CALLBACK_MIP_HEURISTIC
CPX_CALLBACK_MIP_SOLVE = _pycplex_platform.CPX_CALLBACK_MIP_SOLVE
CPX_CALLBACK_MIP_CUT_LOOP = _pycplex_platform.CPX_CALLBACK_MIP_CUT_LOOP
CPX_CALLBACK_MIP_PROBE = _pycplex_platform.CPX_CALLBACK_MIP_PROBE
CPX_CALLBACK_MIP_FRACCUT = _pycplex_platform.CPX_CALLBACK_MIP_FRACCUT
CPX_CALLBACK_MIP_DISJCUT = _pycplex_platform.CPX_CALLBACK_MIP_DISJCUT
CPX_CALLBACK_MIP_FLOWMIR = _pycplex_platform.CPX_CALLBACK_MIP_FLOWMIR
CPX_CALLBACK_MIP_INCUMBENT_NODESOLN = _pycplex_platform.CPX_CALLBACK_MIP_INCUMBENT_NODESOLN
CPX_CALLBACK_MIP_DELETENODE = _pycplex_platform.CPX_CALLBACK_MIP_DELETENODE
CPX_CALLBACK_MIP_BRANCH_NOSOLN = _pycplex_platform.CPX_CALLBACK_MIP_BRANCH_NOSOLN
CPX_CALLBACK_MIP_CUT_LAST = _pycplex_platform.CPX_CALLBACK_MIP_CUT_LAST
CPX_CALLBACK_MIP_CUT_FEAS = _pycplex_platform.CPX_CALLBACK_MIP_CUT_FEAS
CPX_CALLBACK_MIP_CUT_UNBD = _pycplex_platform.CPX_CALLBACK_MIP_CUT_UNBD
CPX_CALLBACK_MIP_INCUMBENT_HEURSOLN = _pycplex_platform.CPX_CALLBACK_MIP_INCUMBENT_HEURSOLN
CPX_CALLBACK_MIP_INCUMBENT_USERSOLN = _pycplex_platform.CPX_CALLBACK_MIP_INCUMBENT_USERSOLN
CPX_CALLBACK_MIP_INCUMBENT_MIPSTART = _pycplex_platform.CPX_CALLBACK_MIP_INCUMBENT_MIPSTART
CPX_CALLBACK_INFO_BEST_INTEGER = _pycplex_platform.CPX_CALLBACK_INFO_BEST_INTEGER
CPX_CALLBACK_INFO_BEST_REMAINING = _pycplex_platform.CPX_CALLBACK_INFO_BEST_REMAINING
CPX_CALLBACK_INFO_NODE_COUNT = _pycplex_platform.CPX_CALLBACK_INFO_NODE_COUNT
CPX_CALLBACK_INFO_NODES_LEFT = _pycplex_platform.CPX_CALLBACK_INFO_NODES_LEFT
CPX_CALLBACK_INFO_MIP_ITERATIONS = _pycplex_platform.CPX_CALLBACK_INFO_MIP_ITERATIONS
CPX_CALLBACK_INFO_CUTOFF = _pycplex_platform.CPX_CALLBACK_INFO_CUTOFF
CPX_CALLBACK_INFO_CLIQUE_COUNT = _pycplex_platform.CPX_CALLBACK_INFO_CLIQUE_COUNT
CPX_CALLBACK_INFO_COVER_COUNT = _pycplex_platform.CPX_CALLBACK_INFO_COVER_COUNT
CPX_CALLBACK_INFO_MIP_FEAS = _pycplex_platform.CPX_CALLBACK_INFO_MIP_FEAS
CPX_CALLBACK_INFO_FLOWCOVER_COUNT = _pycplex_platform.CPX_CALLBACK_INFO_FLOWCOVER_COUNT
CPX_CALLBACK_INFO_GUBCOVER_COUNT = _pycplex_platform.CPX_CALLBACK_INFO_GUBCOVER_COUNT
CPX_CALLBACK_INFO_IMPLBD_COUNT = _pycplex_platform.CPX_CALLBACK_INFO_IMPLBD_COUNT
CPX_CALLBACK_INFO_PROBE_PHASE = _pycplex_platform.CPX_CALLBACK_INFO_PROBE_PHASE
CPX_CALLBACK_INFO_PROBE_PROGRESS = _pycplex_platform.CPX_CALLBACK_INFO_PROBE_PROGRESS
CPX_CALLBACK_INFO_FRACCUT_COUNT = _pycplex_platform.CPX_CALLBACK_INFO_FRACCUT_COUNT
CPX_CALLBACK_INFO_FRACCUT_PROGRESS = _pycplex_platform.CPX_CALLBACK_INFO_FRACCUT_PROGRESS
CPX_CALLBACK_INFO_DISJCUT_COUNT = _pycplex_platform.CPX_CALLBACK_INFO_DISJCUT_COUNT
CPX_CALLBACK_INFO_DISJCUT_PROGRESS = _pycplex_platform.CPX_CALLBACK_INFO_DISJCUT_PROGRESS
CPX_CALLBACK_INFO_FLOWPATH_COUNT = _pycplex_platform.CPX_CALLBACK_INFO_FLOWPATH_COUNT
CPX_CALLBACK_INFO_MIRCUT_COUNT = _pycplex_platform.CPX_CALLBACK_INFO_MIRCUT_COUNT
CPX_CALLBACK_INFO_FLOWMIR_PROGRESS = _pycplex_platform.CPX_CALLBACK_INFO_FLOWMIR_PROGRESS
CPX_CALLBACK_INFO_ZEROHALFCUT_COUNT = _pycplex_platform.CPX_CALLBACK_INFO_ZEROHALFCUT_COUNT
CPX_CALLBACK_INFO_MY_THREAD_NUM = _pycplex_platform.CPX_CALLBACK_INFO_MY_THREAD_NUM
CPX_CALLBACK_INFO_USER_THREADS = _pycplex_platform.CPX_CALLBACK_INFO_USER_THREADS
CPX_CALLBACK_INFO_MIP_REL_GAP = _pycplex_platform.CPX_CALLBACK_INFO_MIP_REL_GAP
CPX_CALLBACK_INFO_MCFCUT_COUNT = _pycplex_platform.CPX_CALLBACK_INFO_MCFCUT_COUNT
CPX_CALLBACK_INFO_KAPPA_STABLE = _pycplex_platform.CPX_CALLBACK_INFO_KAPPA_STABLE
CPX_CALLBACK_INFO_KAPPA_SUSPICIOUS = _pycplex_platform.CPX_CALLBACK_INFO_KAPPA_SUSPICIOUS
CPX_CALLBACK_INFO_KAPPA_UNSTABLE = _pycplex_platform.CPX_CALLBACK_INFO_KAPPA_UNSTABLE
CPX_CALLBACK_INFO_KAPPA_ILLPOSED = _pycplex_platform.CPX_CALLBACK_INFO_KAPPA_ILLPOSED
CPX_CALLBACK_INFO_KAPPA_MAX = _pycplex_platform.CPX_CALLBACK_INFO_KAPPA_MAX
CPX_CALLBACK_INFO_KAPPA_ATTENTION = _pycplex_platform.CPX_CALLBACK_INFO_KAPPA_ATTENTION
CPX_CALLBACK_INFO_LANDPCUT_COUNT = _pycplex_platform.CPX_CALLBACK_INFO_LANDPCUT_COUNT
CPX_CALLBACK_INFO_USERCUT_COUNT = _pycplex_platform.CPX_CALLBACK_INFO_USERCUT_COUNT
CPX_CALLBACK_INFO_TABLECUT_COUNT = _pycplex_platform.CPX_CALLBACK_INFO_TABLECUT_COUNT
CPX_CALLBACK_INFO_SOLNPOOLCUT_COUNT = _pycplex_platform.CPX_CALLBACK_INFO_SOLNPOOLCUT_COUNT
CPX_CALLBACK_INFO_BENDERS_COUNT = _pycplex_platform.CPX_CALLBACK_INFO_BENDERS_COUNT
CPX_CALLBACK_INFO_NODE_COUNT_LONG = _pycplex_platform.CPX_CALLBACK_INFO_NODE_COUNT_LONG
CPX_CALLBACK_INFO_NODES_LEFT_LONG = _pycplex_platform.CPX_CALLBACK_INFO_NODES_LEFT_LONG
CPX_CALLBACK_INFO_MIP_ITERATIONS_LONG = _pycplex_platform.CPX_CALLBACK_INFO_MIP_ITERATIONS_LONG
CPX_CALLBACK_INFO_LAZY_SOURCE = _pycplex_platform.CPX_CALLBACK_INFO_LAZY_SOURCE
CPX_CALLBACK_INFO_NODE_SIINF = _pycplex_platform.CPX_CALLBACK_INFO_NODE_SIINF
CPX_CALLBACK_INFO_NODE_NIINF = _pycplex_platform.CPX_CALLBACK_INFO_NODE_NIINF
CPX_CALLBACK_INFO_NODE_ESTIMATE = _pycplex_platform.CPX_CALLBACK_INFO_NODE_ESTIMATE
CPX_CALLBACK_INFO_NODE_DEPTH = _pycplex_platform.CPX_CALLBACK_INFO_NODE_DEPTH
CPX_CALLBACK_INFO_NODE_OBJVAL = _pycplex_platform.CPX_CALLBACK_INFO_NODE_OBJVAL
CPX_CALLBACK_INFO_NODE_TYPE = _pycplex_platform.CPX_CALLBACK_INFO_NODE_TYPE
CPX_CALLBACK_INFO_NODE_VAR = _pycplex_platform.CPX_CALLBACK_INFO_NODE_VAR
CPX_CALLBACK_INFO_NODE_SOS = _pycplex_platform.CPX_CALLBACK_INFO_NODE_SOS
CPX_CALLBACK_INFO_NODE_SEQNUM = _pycplex_platform.CPX_CALLBACK_INFO_NODE_SEQNUM
CPX_CALLBACK_INFO_NODE_USERHANDLE = _pycplex_platform.CPX_CALLBACK_INFO_NODE_USERHANDLE
CPX_CALLBACK_INFO_NODE_NODENUM = _pycplex_platform.CPX_CALLBACK_INFO_NODE_NODENUM
CPX_CALLBACK_INFO_NODE_SEQNUM_LONG = _pycplex_platform.CPX_CALLBACK_INFO_NODE_SEQNUM_LONG
CPX_CALLBACK_INFO_NODE_NODENUM_LONG = _pycplex_platform.CPX_CALLBACK_INFO_NODE_NODENUM_LONG
CPX_CALLBACK_INFO_NODE_DEPTH_LONG = _pycplex_platform.CPX_CALLBACK_INFO_NODE_DEPTH_LONG
CPX_CALLBACK_INFO_SOS_TYPE = _pycplex_platform.CPX_CALLBACK_INFO_SOS_TYPE
CPX_CALLBACK_INFO_SOS_SIZE = _pycplex_platform.CPX_CALLBACK_INFO_SOS_SIZE
CPX_CALLBACK_INFO_SOS_IS_FEASIBLE = _pycplex_platform.CPX_CALLBACK_INFO_SOS_IS_FEASIBLE
CPX_CALLBACK_INFO_SOS_MEMBER_INDEX = _pycplex_platform.CPX_CALLBACK_INFO_SOS_MEMBER_INDEX
CPX_CALLBACK_INFO_SOS_MEMBER_REFVAL = _pycplex_platform.CPX_CALLBACK_INFO_SOS_MEMBER_REFVAL
CPX_CALLBACK_INFO_SOS_NUM = _pycplex_platform.CPX_CALLBACK_INFO_SOS_NUM
CPX_CALLBACK_INFO_IC_NUM = _pycplex_platform.CPX_CALLBACK_INFO_IC_NUM
CPX_CALLBACK_INFO_IC_IMPLYING_VAR = _pycplex_platform.CPX_CALLBACK_INFO_IC_IMPLYING_VAR
CPX_CALLBACK_INFO_IC_IMPLIED_VAR = _pycplex_platform.CPX_CALLBACK_INFO_IC_IMPLIED_VAR
CPX_CALLBACK_INFO_IC_SENSE = _pycplex_platform.CPX_CALLBACK_INFO_IC_SENSE
CPX_CALLBACK_INFO_IC_COMPL = _pycplex_platform.CPX_CALLBACK_INFO_IC_COMPL
CPX_CALLBACK_INFO_IC_RHS = _pycplex_platform.CPX_CALLBACK_INFO_IC_RHS
CPX_CALLBACK_INFO_IC_IS_FEASIBLE = _pycplex_platform.CPX_CALLBACK_INFO_IC_IS_FEASIBLE
CPX_INCUMBENT_ID = _pycplex_platform.CPX_INCUMBENT_ID
CPX_RAMPUP_DISABLED = _pycplex_platform.CPX_RAMPUP_DISABLED
CPX_RAMPUP_AUTO = _pycplex_platform.CPX_RAMPUP_AUTO
CPX_RAMPUP_DYNAMIC = _pycplex_platform.CPX_RAMPUP_DYNAMIC
CPX_RAMPUP_INFINITE = _pycplex_platform.CPX_RAMPUP_INFINITE
CPX_CALLBACK_DEFAULT = _pycplex_platform.CPX_CALLBACK_DEFAULT
CPX_CALLBACK_FAIL = _pycplex_platform.CPX_CALLBACK_FAIL
CPX_CALLBACK_SET = _pycplex_platform.CPX_CALLBACK_SET
CPX_CALLBACK_ABORT_CUT_LOOP = _pycplex_platform.CPX_CALLBACK_ABORT_CUT_LOOP
CPX_USECUT_FORCE = _pycplex_platform.CPX_USECUT_FORCE
CPX_USECUT_PURGE = _pycplex_platform.CPX_USECUT_PURGE
CPX_USECUT_FILTER = _pycplex_platform.CPX_USECUT_FILTER
CPX_INTEGER_FEASIBLE = _pycplex_platform.CPX_INTEGER_FEASIBLE
CPX_INTEGER_INFEASIBLE = _pycplex_platform.CPX_INTEGER_INFEASIBLE
CPX_IMPLIED_INTEGER_FEASIBLE = _pycplex_platform.CPX_IMPLIED_INTEGER_FEASIBLE
CPX_CON_LOWER_BOUND = _pycplex_platform.CPX_CON_LOWER_BOUND
CPX_CON_UPPER_BOUND = _pycplex_platform.CPX_CON_UPPER_BOUND
CPX_CON_LINEAR = _pycplex_platform.CPX_CON_LINEAR
CPX_CON_QUADRATIC = _pycplex_platform.CPX_CON_QUADRATIC
CPX_CON_SOS = _pycplex_platform.CPX_CON_SOS
CPX_CON_INDICATOR = _pycplex_platform.CPX_CON_INDICATOR
CPX_CON_PWL = _pycplex_platform.CPX_CON_PWL
CPX_CON_ABS = _pycplex_platform.CPX_CON_ABS
CPX_CON_MINEXPR = _pycplex_platform.CPX_CON_MINEXPR
CPX_CON_MAXEXPR = _pycplex_platform.CPX_CON_MAXEXPR
CPX_CON_LAST_CONTYPE = _pycplex_platform.CPX_CON_LAST_CONTYPE
CPX_INDICATOR_IF = _pycplex_platform.CPX_INDICATOR_IF
CPX_INDICATOR_ONLYIF = _pycplex_platform.CPX_INDICATOR_ONLYIF
CPX_INDICATOR_IFANDONLYIF = _pycplex_platform.CPX_INDICATOR_IFANDONLYIF
CPXNET_NO_DISPLAY_OBJECTIVE = _pycplex_platform.CPXNET_NO_DISPLAY_OBJECTIVE
CPXNET_TRUE_OBJECTIVE = _pycplex_platform.CPXNET_TRUE_OBJECTIVE
CPXNET_PENALIZED_OBJECTIVE = _pycplex_platform.CPXNET_PENALIZED_OBJECTIVE
CPXNET_PRICE_AUTO = _pycplex_platform.CPXNET_PRICE_AUTO
CPXNET_PRICE_PARTIAL = _pycplex_platform.CPXNET_PRICE_PARTIAL
CPXNET_PRICE_MULT_PART = _pycplex_platform.CPXNET_PRICE_MULT_PART
CPXNET_PRICE_SORT_MULT_PART = _pycplex_platform.CPXNET_PRICE_SORT_MULT_PART
CPX_NETFIND_PURE = _pycplex_platform.CPX_NETFIND_PURE
CPX_NETFIND_REFLECT = _pycplex_platform.CPX_NETFIND_REFLECT
CPX_NETFIND_SCALE = _pycplex_platform.CPX_NETFIND_SCALE
CPX_QCPDUALS_NO = _pycplex_platform.CPX_QCPDUALS_NO
CPX_QCPDUALS_IFPOSSIBLE = _pycplex_platform.CPX_QCPDUALS_IFPOSSIBLE
CPX_QCPDUALS_FORCE = _pycplex_platform.CPX_QCPDUALS_FORCE
CPX_CPXAUTOCONSTANTS_H_H = _pycplex_platform.CPX_CPXAUTOCONSTANTS_H_H
CPX_BENDERS_ANNOTATION = _pycplex_platform.CPX_BENDERS_ANNOTATION
CPX_BENDERS_MASTERVALUE = _pycplex_platform.CPX_BENDERS_MASTERVALUE
CPX_BIGINT = _pycplex_platform.CPX_BIGINT
CPX_BIGLONG = _pycplex_platform.CPX_BIGLONG
CPX_CALLBACKCONTEXT_CANDIDATE = _pycplex_platform.CPX_CALLBACKCONTEXT_CANDIDATE
CPX_CALLBACKCONTEXT_GLOBAL_PROGRESS = _pycplex_platform.CPX_CALLBACKCONTEXT_GLOBAL_PROGRESS
CPX_CALLBACKCONTEXT_LOCAL_PROGRESS = _pycplex_platform.CPX_CALLBACKCONTEXT_LOCAL_PROGRESS
CPX_CALLBACKCONTEXT_RELAXATION = _pycplex_platform.CPX_CALLBACKCONTEXT_RELAXATION
CPX_CALLBACKCONTEXT_THREAD_DOWN = _pycplex_platform.CPX_CALLBACKCONTEXT_THREAD_DOWN
CPX_CALLBACKCONTEXT_THREAD_UP = _pycplex_platform.CPX_CALLBACKCONTEXT_THREAD_UP
CPX_DUAL_OBJ = _pycplex_platform.CPX_DUAL_OBJ
CPX_EXACT_KAPPA = _pycplex_platform.CPX_EXACT_KAPPA
CPX_KAPPA = _pycplex_platform.CPX_KAPPA
CPX_KAPPA_ATTENTION = _pycplex_platform.CPX_KAPPA_ATTENTION
CPX_KAPPA_ILLPOSED = _pycplex_platform.CPX_KAPPA_ILLPOSED
CPX_KAPPA_MAX = _pycplex_platform.CPX_KAPPA_MAX
CPX_KAPPA_STABLE = _pycplex_platform.CPX_KAPPA_STABLE
CPX_KAPPA_SUSPICIOUS = _pycplex_platform.CPX_KAPPA_SUSPICIOUS
CPX_KAPPA_UNSTABLE = _pycplex_platform.CPX_KAPPA_UNSTABLE
CPX_LAZYCONSTRAINTCALLBACK_HEUR = _pycplex_platform.CPX_LAZYCONSTRAINTCALLBACK_HEUR
CPX_LAZYCONSTRAINTCALLBACK_MIPSTART = _pycplex_platform.CPX_LAZYCONSTRAINTCALLBACK_MIPSTART
CPX_LAZYCONSTRAINTCALLBACK_NODE = _pycplex_platform.CPX_LAZYCONSTRAINTCALLBACK_NODE
CPX_MAX_COMP_SLACK = _pycplex_platform.CPX_MAX_COMP_SLACK
CPX_MAX_DUAL_INFEAS = _pycplex_platform.CPX_MAX_DUAL_INFEAS
CPX_MAX_DUAL_RESIDUAL = _pycplex_platform.CPX_MAX_DUAL_RESIDUAL
CPX_MAX_INDSLACK_INFEAS = _pycplex_platform.CPX_MAX_INDSLACK_INFEAS
CPX_MAX_INT_INFEAS = _pycplex_platform.CPX_MAX_INT_INFEAS
CPX_MAX_PI = _pycplex_platform.CPX_MAX_PI
CPX_MAX_PRIMAL_INFEAS = _pycplex_platform.CPX_MAX_PRIMAL_INFEAS
CPX_MAX_PRIMAL_RESIDUAL = _pycplex_platform.CPX_MAX_PRIMAL_RESIDUAL
CPX_MAX_PWLSLACK_INFEAS = _pycplex_platform.CPX_MAX_PWLSLACK_INFEAS
CPX_MAX_QCPRIMAL_RESIDUAL = _pycplex_platform.CPX_MAX_QCPRIMAL_RESIDUAL
CPX_MAX_QCSLACK = _pycplex_platform.CPX_MAX_QCSLACK
CPX_MAX_QCSLACK_INFEAS = _pycplex_platform.CPX_MAX_QCSLACK_INFEAS
CPX_MAX_RED_COST = _pycplex_platform.CPX_MAX_RED_COST
CPX_MAX_SCALED_DUAL_INFEAS = _pycplex_platform.CPX_MAX_SCALED_DUAL_INFEAS
CPX_MAX_SCALED_DUAL_RESIDUAL = _pycplex_platform.CPX_MAX_SCALED_DUAL_RESIDUAL
CPX_MAX_SCALED_PI = _pycplex_platform.CPX_MAX_SCALED_PI
CPX_MAX_SCALED_PRIMAL_INFEAS = _pycplex_platform.CPX_MAX_SCALED_PRIMAL_INFEAS
CPX_MAX_SCALED_PRIMAL_RESIDUAL = _pycplex_platform.CPX_MAX_SCALED_PRIMAL_RESIDUAL
CPX_MAX_SCALED_RED_COST = _pycplex_platform.CPX_MAX_SCALED_RED_COST
CPX_MAX_SCALED_SLACK = _pycplex_platform.CPX_MAX_SCALED_SLACK
CPX_MAX_SCALED_X = _pycplex_platform.CPX_MAX_SCALED_X
CPX_MAX_SLACK = _pycplex_platform.CPX_MAX_SLACK
CPX_MAX_X = _pycplex_platform.CPX_MAX_X
CPX_MULTIOBJ_BARITCNT = _pycplex_platform.CPX_MULTIOBJ_BARITCNT
CPX_MULTIOBJ_BESTOBJVAL = _pycplex_platform.CPX_MULTIOBJ_BESTOBJVAL
CPX_MULTIOBJ_BLEND = _pycplex_platform.CPX_MULTIOBJ_BLEND
CPX_MULTIOBJ_DEGCNT = _pycplex_platform.CPX_MULTIOBJ_DEGCNT
CPX_MULTIOBJ_DETTIME = _pycplex_platform.CPX_MULTIOBJ_DETTIME
CPX_MULTIOBJ_DEXCH = _pycplex_platform.CPX_MULTIOBJ_DEXCH
CPX_MULTIOBJ_DPUSH = _pycplex_platform.CPX_MULTIOBJ_DPUSH
CPX_MULTIOBJ_ERROR = _pycplex_platform.CPX_MULTIOBJ_ERROR
CPX_MULTIOBJ_ITCNT = _pycplex_platform.CPX_MULTIOBJ_ITCNT
CPX_MULTIOBJ_METHOD = _pycplex_platform.CPX_MULTIOBJ_METHOD
CPX_MULTIOBJ_NODECNT = _pycplex_platform.CPX_MULTIOBJ_NODECNT
CPX_MULTIOBJ_NODELEFTCNT = _pycplex_platform.CPX_MULTIOBJ_NODELEFTCNT
CPX_MULTIOBJ_OBJVAL = _pycplex_platform.CPX_MULTIOBJ_OBJVAL
CPX_MULTIOBJ_PEXCH = _pycplex_platform.CPX_MULTIOBJ_PEXCH
CPX_MULTIOBJ_PHASE1CNT = _pycplex_platform.CPX_MULTIOBJ_PHASE1CNT
CPX_MULTIOBJ_PPUSH = _pycplex_platform.CPX_MULTIOBJ_PPUSH
CPX_MULTIOBJ_PRIORITY = _pycplex_platform.CPX_MULTIOBJ_PRIORITY
CPX_MULTIOBJ_SIFTITCNT = _pycplex_platform.CPX_MULTIOBJ_SIFTITCNT
CPX_MULTIOBJ_SIFTPHASE1CNT = _pycplex_platform.CPX_MULTIOBJ_SIFTPHASE1CNT
CPX_MULTIOBJ_STATUS = _pycplex_platform.CPX_MULTIOBJ_STATUS
CPX_MULTIOBJ_TIME = _pycplex_platform.CPX_MULTIOBJ_TIME
CPX_NO_PRIORITY_CHANGE = _pycplex_platform.CPX_NO_PRIORITY_CHANGE
CPX_OBJ_GAP = _pycplex_platform.CPX_OBJ_GAP
CPX_PRIMAL_OBJ = _pycplex_platform.CPX_PRIMAL_OBJ
CPX_SOLNPOOL_DIV = _pycplex_platform.CPX_SOLNPOOL_DIV
CPX_SOLNPOOL_FIFO = _pycplex_platform.CPX_SOLNPOOL_FIFO
CPX_SOLNPOOL_FILTER_DIVERSITY = _pycplex_platform.CPX_SOLNPOOL_FILTER_DIVERSITY
CPX_SOLNPOOL_FILTER_RANGE = _pycplex_platform.CPX_SOLNPOOL_FILTER_RANGE
CPX_SOLNPOOL_OBJ = _pycplex_platform.CPX_SOLNPOOL_OBJ
CPX_STAT_ABORT_DETTIME_LIM = _pycplex_platform.CPX_STAT_ABORT_DETTIME_LIM
CPX_STAT_ABORT_DUAL_OBJ_LIM = _pycplex_platform.CPX_STAT_ABORT_DUAL_OBJ_LIM
CPX_STAT_ABORT_IT_LIM = _pycplex_platform.CPX_STAT_ABORT_IT_LIM
CPX_STAT_ABORT_OBJ_LIM = _pycplex_platform.CPX_STAT_ABORT_OBJ_LIM
CPX_STAT_ABORT_PRIM_OBJ_LIM = _pycplex_platform.CPX_STAT_ABORT_PRIM_OBJ_LIM
CPX_STAT_ABORT_TIME_LIM = _pycplex_platform.CPX_STAT_ABORT_TIME_LIM
CPX_STAT_ABORT_USER = _pycplex_platform.CPX_STAT_ABORT_USER
CPX_STAT_BENDERS_MASTER_UNBOUNDED = _pycplex_platform.CPX_STAT_BENDERS_MASTER_UNBOUNDED
CPX_STAT_BENDERS_NUM_BEST = _pycplex_platform.CPX_STAT_BENDERS_NUM_BEST
CPX_STAT_CONFLICT_ABORT_CONTRADICTION = _pycplex_platform.CPX_STAT_CONFLICT_ABORT_CONTRADICTION
CPX_STAT_CONFLICT_ABORT_DETTIME_LIM = _pycplex_platform.CPX_STAT_CONFLICT_ABORT_DETTIME_LIM
CPX_STAT_CONFLICT_ABORT_IT_LIM = _pycplex_platform.CPX_STAT_CONFLICT_ABORT_IT_LIM
CPX_STAT_CONFLICT_ABORT_MEM_LIM = _pycplex_platform.CPX_STAT_CONFLICT_ABORT_MEM_LIM
CPX_STAT_CONFLICT_ABORT_NODE_LIM = _pycplex_platform.CPX_STAT_CONFLICT_ABORT_NODE_LIM
CPX_STAT_CONFLICT_ABORT_OBJ_LIM = _pycplex_platform.CPX_STAT_CONFLICT_ABORT_OBJ_LIM
CPX_STAT_CONFLICT_ABORT_TIME_LIM = _pycplex_platform.CPX_STAT_CONFLICT_ABORT_TIME_LIM
CPX_STAT_CONFLICT_ABORT_USER = _pycplex_platform.CPX_STAT_CONFLICT_ABORT_USER
CPX_STAT_CONFLICT_FEASIBLE = _pycplex_platform.CPX_STAT_CONFLICT_FEASIBLE
CPX_STAT_CONFLICT_MINIMAL = _pycplex_platform.CPX_STAT_CONFLICT_MINIMAL
CPX_STAT_FEASIBLE = _pycplex_platform.CPX_STAT_FEASIBLE
CPX_STAT_FEASIBLE_RELAXED_INF = _pycplex_platform.CPX_STAT_FEASIBLE_RELAXED_INF
CPX_STAT_FEASIBLE_RELAXED_QUAD = _pycplex_platform.CPX_STAT_FEASIBLE_RELAXED_QUAD
CPX_STAT_FEASIBLE_RELAXED_SUM = _pycplex_platform.CPX_STAT_FEASIBLE_RELAXED_SUM
CPX_STAT_FIRSTORDER = _pycplex_platform.CPX_STAT_FIRSTORDER
CPX_STAT_INFEASIBLE = _pycplex_platform.CPX_STAT_INFEASIBLE
CPX_STAT_INForUNBD = _pycplex_platform.CPX_STAT_INForUNBD
CPX_STAT_MULTIOBJ_INFEASIBLE = _pycplex_platform.CPX_STAT_MULTIOBJ_INFEASIBLE
CPX_STAT_MULTIOBJ_INForUNBD = _pycplex_platform.CPX_STAT_MULTIOBJ_INForUNBD
CPX_STAT_MULTIOBJ_NON_OPTIMAL = _pycplex_platform.CPX_STAT_MULTIOBJ_NON_OPTIMAL
CPX_STAT_MULTIOBJ_OPTIMAL = _pycplex_platform.CPX_STAT_MULTIOBJ_OPTIMAL
CPX_STAT_MULTIOBJ_STOPPED = _pycplex_platform.CPX_STAT_MULTIOBJ_STOPPED
CPX_STAT_MULTIOBJ_UNBOUNDED = _pycplex_platform.CPX_STAT_MULTIOBJ_UNBOUNDED
CPX_STAT_NUM_BEST = _pycplex_platform.CPX_STAT_NUM_BEST
CPX_STAT_OPTIMAL = _pycplex_platform.CPX_STAT_OPTIMAL
CPX_STAT_OPTIMAL_FACE_UNBOUNDED = _pycplex_platform.CPX_STAT_OPTIMAL_FACE_UNBOUNDED
CPX_STAT_OPTIMAL_INFEAS = _pycplex_platform.CPX_STAT_OPTIMAL_INFEAS
CPX_STAT_OPTIMAL_RELAXED_INF = _pycplex_platform.CPX_STAT_OPTIMAL_RELAXED_INF
CPX_STAT_OPTIMAL_RELAXED_QUAD = _pycplex_platform.CPX_STAT_OPTIMAL_RELAXED_QUAD
CPX_STAT_OPTIMAL_RELAXED_SUM = _pycplex_platform.CPX_STAT_OPTIMAL_RELAXED_SUM
CPX_STAT_UNBOUNDED = _pycplex_platform.CPX_STAT_UNBOUNDED
CPX_SUM_COMP_SLACK = _pycplex_platform.CPX_SUM_COMP_SLACK
CPX_SUM_DUAL_INFEAS = _pycplex_platform.CPX_SUM_DUAL_INFEAS
CPX_SUM_DUAL_RESIDUAL = _pycplex_platform.CPX_SUM_DUAL_RESIDUAL
CPX_SUM_INDSLACK_INFEAS = _pycplex_platform.CPX_SUM_INDSLACK_INFEAS
CPX_SUM_INT_INFEAS = _pycplex_platform.CPX_SUM_INT_INFEAS
CPX_SUM_PI = _pycplex_platform.CPX_SUM_PI
CPX_SUM_PRIMAL_INFEAS = _pycplex_platform.CPX_SUM_PRIMAL_INFEAS
CPX_SUM_PRIMAL_RESIDUAL = _pycplex_platform.CPX_SUM_PRIMAL_RESIDUAL
CPX_SUM_PWLSLACK_INFEAS = _pycplex_platform.CPX_SUM_PWLSLACK_INFEAS
CPX_SUM_QCPRIMAL_RESIDUAL = _pycplex_platform.CPX_SUM_QCPRIMAL_RESIDUAL
CPX_SUM_QCSLACK = _pycplex_platform.CPX_SUM_QCSLACK
CPX_SUM_QCSLACK_INFEAS = _pycplex_platform.CPX_SUM_QCSLACK_INFEAS
CPX_SUM_RED_COST = _pycplex_platform.CPX_SUM_RED_COST
CPX_SUM_SCALED_DUAL_INFEAS = _pycplex_platform.CPX_SUM_SCALED_DUAL_INFEAS
CPX_SUM_SCALED_DUAL_RESIDUAL = _pycplex_platform.CPX_SUM_SCALED_DUAL_RESIDUAL
CPX_SUM_SCALED_PI = _pycplex_platform.CPX_SUM_SCALED_PI
CPX_SUM_SCALED_PRIMAL_INFEAS = _pycplex_platform.CPX_SUM_SCALED_PRIMAL_INFEAS
CPX_SUM_SCALED_PRIMAL_RESIDUAL = _pycplex_platform.CPX_SUM_SCALED_PRIMAL_RESIDUAL
CPX_SUM_SCALED_RED_COST = _pycplex_platform.CPX_SUM_SCALED_RED_COST
CPX_SUM_SCALED_SLACK = _pycplex_platform.CPX_SUM_SCALED_SLACK
CPX_SUM_SCALED_X = _pycplex_platform.CPX_SUM_SCALED_X
CPX_SUM_SLACK = _pycplex_platform.CPX_SUM_SLACK
CPX_SUM_X = _pycplex_platform.CPX_SUM_X
CPXERR_ABORT_STRONGBRANCH = _pycplex_platform.CPXERR_ABORT_STRONGBRANCH
CPXERR_ADJ_SIGN_QUAD = _pycplex_platform.CPXERR_ADJ_SIGN_QUAD
CPXERR_ADJ_SIGN_SENSE = _pycplex_platform.CPXERR_ADJ_SIGN_SENSE
CPXERR_ADJ_SIGNS = _pycplex_platform.CPXERR_ADJ_SIGNS
CPXERR_ARC_INDEX_RANGE = _pycplex_platform.CPXERR_ARC_INDEX_RANGE
CPXERR_ARRAY_BAD_SOS_TYPE = _pycplex_platform.CPXERR_ARRAY_BAD_SOS_TYPE
CPXERR_ARRAY_NOT_ASCENDING = _pycplex_platform.CPXERR_ARRAY_NOT_ASCENDING
CPXERR_ARRAY_TOO_LONG = _pycplex_platform.CPXERR_ARRAY_TOO_LONG
CPXERR_BAD_ARGUMENT = _pycplex_platform.CPXERR_BAD_ARGUMENT
CPXERR_BAD_BOUND_SENSE = _pycplex_platform.CPXERR_BAD_BOUND_SENSE
CPXERR_BAD_BOUND_TYPE = _pycplex_platform.CPXERR_BAD_BOUND_TYPE
CPXERR_BAD_CHAR = _pycplex_platform.CPXERR_BAD_CHAR
CPXERR_BAD_CTYPE = _pycplex_platform.CPXERR_BAD_CTYPE
CPXERR_BAD_DECOMPOSITION = _pycplex_platform.CPXERR_BAD_DECOMPOSITION
CPXERR_BAD_DIRECTION = _pycplex_platform.CPXERR_BAD_DIRECTION
CPXERR_BAD_EXPO_RANGE = _pycplex_platform.CPXERR_BAD_EXPO_RANGE
CPXERR_BAD_EXPONENT = _pycplex_platform.CPXERR_BAD_EXPONENT
CPXERR_BAD_FILETYPE = _pycplex_platform.CPXERR_BAD_FILETYPE
CPXERR_BAD_ID = _pycplex_platform.CPXERR_BAD_ID
CPXERR_BAD_INDCONSTR = _pycplex_platform.CPXERR_BAD_INDCONSTR
CPXERR_BAD_INDICATOR = _pycplex_platform.CPXERR_BAD_INDICATOR
CPXERR_BAD_INDTYPE = _pycplex_platform.CPXERR_BAD_INDTYPE
CPXERR_BAD_LAZY_UCUT = _pycplex_platform.CPXERR_BAD_LAZY_UCUT
CPXERR_BAD_LUB = _pycplex_platform.CPXERR_BAD_LUB
CPXERR_BAD_METHOD = _pycplex_platform.CPXERR_BAD_METHOD
CPXERR_BAD_MULTIOBJ_ATTR = _pycplex_platform.CPXERR_BAD_MULTIOBJ_ATTR
CPXERR_BAD_NUMBER = _pycplex_platform.CPXERR_BAD_NUMBER
CPXERR_BAD_OBJ_SENSE = _pycplex_platform.CPXERR_BAD_OBJ_SENSE
CPXERR_BAD_PARAM_NAME = _pycplex_platform.CPXERR_BAD_PARAM_NAME
CPXERR_BAD_PARAM_NUM = _pycplex_platform.CPXERR_BAD_PARAM_NUM
CPXERR_BAD_PIVOT = _pycplex_platform.CPXERR_BAD_PIVOT
CPXERR_BAD_PRIORITY = _pycplex_platform.CPXERR_BAD_PRIORITY
CPXERR_BAD_PROB_TYPE = _pycplex_platform.CPXERR_BAD_PROB_TYPE
CPXERR_BAD_ROW_ID = _pycplex_platform.CPXERR_BAD_ROW_ID
CPXERR_BAD_SECTION_BOUNDS = _pycplex_platform.CPXERR_BAD_SECTION_BOUNDS
CPXERR_BAD_SECTION_ENDATA = _pycplex_platform.CPXERR_BAD_SECTION_ENDATA
CPXERR_BAD_SECTION_QMATRIX = _pycplex_platform.CPXERR_BAD_SECTION_QMATRIX
CPXERR_BAD_SENSE = _pycplex_platform.CPXERR_BAD_SENSE
CPXERR_BAD_SOS_TYPE = _pycplex_platform.CPXERR_BAD_SOS_TYPE
CPXERR_BAD_STATUS = _pycplex_platform.CPXERR_BAD_STATUS
CPXERR_BAS_FILE_SHORT = _pycplex_platform.CPXERR_BAS_FILE_SHORT
CPXERR_BAS_FILE_SIZE = _pycplex_platform.CPXERR_BAS_FILE_SIZE
CPXERR_BENDERS_MASTER_SOLVE = _pycplex_platform.CPXERR_BENDERS_MASTER_SOLVE
CPXERR_CALLBACK = _pycplex_platform.CPXERR_CALLBACK
CPXERR_CALLBACK_INCONSISTENT = _pycplex_platform.CPXERR_CALLBACK_INCONSISTENT
CPXERR_CAND_NOT_POINT = _pycplex_platform.CPXERR_CAND_NOT_POINT
CPXERR_CAND_NOT_RAY = _pycplex_platform.CPXERR_CAND_NOT_RAY
CPXERR_CNTRL_IN_NAME = _pycplex_platform.CPXERR_CNTRL_IN_NAME
CPXERR_COL_INDEX_RANGE = _pycplex_platform.CPXERR_COL_INDEX_RANGE
CPXERR_COL_REPEAT_PRINT = _pycplex_platform.CPXERR_COL_REPEAT_PRINT
CPXERR_COL_REPEATS = _pycplex_platform.CPXERR_COL_REPEATS
CPXERR_COL_ROW_REPEATS = _pycplex_platform.CPXERR_COL_ROW_REPEATS
CPXERR_COL_UNKNOWN = _pycplex_platform.CPXERR_COL_UNKNOWN
CPXERR_CONFLICT_UNSTABLE = _pycplex_platform.CPXERR_CONFLICT_UNSTABLE
CPXERR_COUNT_OVERLAP = _pycplex_platform.CPXERR_COUNT_OVERLAP
CPXERR_COUNT_RANGE = _pycplex_platform.CPXERR_COUNT_RANGE
CPXERR_CPUBINDING_FAILURE = _pycplex_platform.CPXERR_CPUBINDING_FAILURE
CPXERR_DBL_MAX = _pycplex_platform.CPXERR_DBL_MAX
CPXERR_DECOMPRESSION = _pycplex_platform.CPXERR_DECOMPRESSION
CPXERR_DETTILIM_STRONGBRANCH = _pycplex_platform.CPXERR_DETTILIM_STRONGBRANCH
CPXERR_DUP_ENTRY = _pycplex_platform.CPXERR_DUP_ENTRY
CPXERR_DYNFUNC = _pycplex_platform.CPXERR_DYNFUNC
CPXERR_DYNLOAD = _pycplex_platform.CPXERR_DYNLOAD
CPXERR_ENCODING_CONVERSION = _pycplex_platform.CPXERR_ENCODING_CONVERSION
CPXERR_EXTRA_BV_BOUND = _pycplex_platform.CPXERR_EXTRA_BV_BOUND
CPXERR_EXTRA_FR_BOUND = _pycplex_platform.CPXERR_EXTRA_FR_BOUND
CPXERR_EXTRA_FX_BOUND = _pycplex_platform.CPXERR_EXTRA_FX_BOUND
CPXERR_EXTRA_INTEND = _pycplex_platform.CPXERR_EXTRA_INTEND
CPXERR_EXTRA_INTORG = _pycplex_platform.CPXERR_EXTRA_INTORG
CPXERR_EXTRA_SOSEND = _pycplex_platform.CPXERR_EXTRA_SOSEND
CPXERR_EXTRA_SOSORG = _pycplex_platform.CPXERR_EXTRA_SOSORG
CPXERR_FAIL_OPEN_READ = _pycplex_platform.CPXERR_FAIL_OPEN_READ
CPXERR_FAIL_OPEN_WRITE = _pycplex_platform.CPXERR_FAIL_OPEN_WRITE
CPXERR_FILE_ENTRIES = _pycplex_platform.CPXERR_FILE_ENTRIES
CPXERR_FILE_FORMAT = _pycplex_platform.CPXERR_FILE_FORMAT
CPXERR_FILE_IO = _pycplex_platform.CPXERR_FILE_IO
CPXERR_FILTER_VARIABLE_TYPE = _pycplex_platform.CPXERR_FILTER_VARIABLE_TYPE
CPXERR_ILL_DEFINED_PWL = _pycplex_platform.CPXERR_ILL_DEFINED_PWL
CPXERR_IN_INFOCALLBACK = _pycplex_platform.CPXERR_IN_INFOCALLBACK
CPXERR_INDEX_NOT_BASIC = _pycplex_platform.CPXERR_INDEX_NOT_BASIC
CPXERR_INDEX_RANGE = _pycplex_platform.CPXERR_INDEX_RANGE
CPXERR_INDEX_RANGE_HIGH = _pycplex_platform.CPXERR_INDEX_RANGE_HIGH
CPXERR_INDEX_RANGE_LOW = _pycplex_platform.CPXERR_INDEX_RANGE_LOW
CPXERR_INT_TOO_BIG = _pycplex_platform.CPXERR_INT_TOO_BIG
CPXERR_INT_TOO_BIG_INPUT = _pycplex_platform.CPXERR_INT_TOO_BIG_INPUT
CPXERR_INVALID_NUMBER = _pycplex_platform.CPXERR_INVALID_NUMBER
CPXERR_LIMITS_TOO_BIG = _pycplex_platform.CPXERR_LIMITS_TOO_BIG
CPXERR_LINE_TOO_LONG = _pycplex_platform.CPXERR_LINE_TOO_LONG
CPXERR_LO_BOUND_REPEATS = _pycplex_platform.CPXERR_LO_BOUND_REPEATS
CPXERR_LOCK_CREATE = _pycplex_platform.CPXERR_LOCK_CREATE
CPXERR_LP_NOT_IN_ENVIRONMENT = _pycplex_platform.CPXERR_LP_NOT_IN_ENVIRONMENT
CPXERR_LP_PARSE = _pycplex_platform.CPXERR_LP_PARSE
CPXERR_MASTER_SOLVE = _pycplex_platform.CPXERR_MASTER_SOLVE
CPXERR_MIPSEARCH_WITH_CALLBACKS = _pycplex_platform.CPXERR_MIPSEARCH_WITH_CALLBACKS
CPXERR_MISS_SOS_TYPE = _pycplex_platform.CPXERR_MISS_SOS_TYPE
CPXERR_MSG_NO_CHANNEL = _pycplex_platform.CPXERR_MSG_NO_CHANNEL
CPXERR_MSG_NO_FILEPTR = _pycplex_platform.CPXERR_MSG_NO_FILEPTR
CPXERR_MSG_NO_FUNCTION = _pycplex_platform.CPXERR_MSG_NO_FUNCTION
CPXERR_MULTIOBJ_SUBPROB_SOLVE = _pycplex_platform.CPXERR_MULTIOBJ_SUBPROB_SOLVE
CPXERR_MULTIPLE_PROBS_IN_REMOTE_ENVIRONMENT = _pycplex_platform.CPXERR_MULTIPLE_PROBS_IN_REMOTE_ENVIRONMENT
CPXERR_NAME_CREATION = _pycplex_platform.CPXERR_NAME_CREATION
CPXERR_NAME_NOT_FOUND = _pycplex_platform.CPXERR_NAME_NOT_FOUND
CPXERR_NAME_TOO_LONG = _pycplex_platform.CPXERR_NAME_TOO_LONG
CPXERR_NAN = _pycplex_platform.CPXERR_NAN
CPXERR_NEED_OPT_SOLN = _pycplex_platform.CPXERR_NEED_OPT_SOLN
CPXERR_NEGATIVE_SURPLUS = _pycplex_platform.CPXERR_NEGATIVE_SURPLUS
CPXERR_NET_DATA = _pycplex_platform.CPXERR_NET_DATA
CPXERR_NET_FILE_SHORT = _pycplex_platform.CPXERR_NET_FILE_SHORT
CPXERR_NO_BARRIER_SOLN = _pycplex_platform.CPXERR_NO_BARRIER_SOLN
CPXERR_NO_BASIC_SOLN = _pycplex_platform.CPXERR_NO_BASIC_SOLN
CPXERR_NO_BASIS = _pycplex_platform.CPXERR_NO_BASIS
CPXERR_NO_BOUND_SENSE = _pycplex_platform.CPXERR_NO_BOUND_SENSE
CPXERR_NO_BOUND_TYPE = _pycplex_platform.CPXERR_NO_BOUND_TYPE
CPXERR_NO_COLUMNS_SECTION = _pycplex_platform.CPXERR_NO_COLUMNS_SECTION
CPXERR_NO_CONFLICT = _pycplex_platform.CPXERR_NO_CONFLICT
CPXERR_NO_DECOMPOSITION = _pycplex_platform.CPXERR_NO_DECOMPOSITION
CPXERR_NO_DUAL_SOLN = _pycplex_platform.CPXERR_NO_DUAL_SOLN
CPXERR_NO_ENDATA = _pycplex_platform.CPXERR_NO_ENDATA
CPXERR_NO_ENVIRONMENT = _pycplex_platform.CPXERR_NO_ENVIRONMENT
CPXERR_NO_FILENAME = _pycplex_platform.CPXERR_NO_FILENAME
CPXERR_NO_ID = _pycplex_platform.CPXERR_NO_ID
CPXERR_NO_ID_FIRST = _pycplex_platform.CPXERR_NO_ID_FIRST
CPXERR_NO_INT_X = _pycplex_platform.CPXERR_NO_INT_X
CPXERR_NO_KAPPASTATS = _pycplex_platform.CPXERR_NO_KAPPASTATS
CPXERR_NO_LU_FACTOR = _pycplex_platform.CPXERR_NO_LU_FACTOR
CPXERR_NO_MEMORY = _pycplex_platform.CPXERR_NO_MEMORY
CPXERR_NO_MIPSTART = _pycplex_platform.CPXERR_NO_MIPSTART
CPXERR_NO_NAME_SECTION = _pycplex_platform.CPXERR_NO_NAME_SECTION
CPXERR_NO_NAMES = _pycplex_platform.CPXERR_NO_NAMES
CPXERR_NO_NORMS = _pycplex_platform.CPXERR_NO_NORMS
CPXERR_NO_NUMBER = _pycplex_platform.CPXERR_NO_NUMBER
CPXERR_NO_NUMBER_BOUND = _pycplex_platform.CPXERR_NO_NUMBER_BOUND
CPXERR_NO_NUMBER_FIRST = _pycplex_platform.CPXERR_NO_NUMBER_FIRST
CPXERR_NO_OBJ_NAME = _pycplex_platform.CPXERR_NO_OBJ_NAME
CPXERR_NO_OBJ_SENSE = _pycplex_platform.CPXERR_NO_OBJ_SENSE
CPXERR_NO_OBJECTIVE = _pycplex_platform.CPXERR_NO_OBJECTIVE
CPXERR_NO_OP_OR_SENSE = _pycplex_platform.CPXERR_NO_OP_OR_SENSE
CPXERR_NO_OPERATOR = _pycplex_platform.CPXERR_NO_OPERATOR
CPXERR_NO_ORDER = _pycplex_platform.CPXERR_NO_ORDER
CPXERR_NO_PROBLEM = _pycplex_platform.CPXERR_NO_PROBLEM
CPXERR_NO_QP_OPERATOR = _pycplex_platform.CPXERR_NO_QP_OPERATOR
CPXERR_NO_QUAD_EXP = _pycplex_platform.CPXERR_NO_QUAD_EXP
CPXERR_NO_RHS_COEFF = _pycplex_platform.CPXERR_NO_RHS_COEFF
CPXERR_NO_RHS_IN_OBJ = _pycplex_platform.CPXERR_NO_RHS_IN_OBJ
CPXERR_NO_ROW_NAME = _pycplex_platform.CPXERR_NO_ROW_NAME
CPXERR_NO_ROW_SENSE = _pycplex_platform.CPXERR_NO_ROW_SENSE
CPXERR_NO_ROWS_SECTION = _pycplex_platform.CPXERR_NO_ROWS_SECTION
CPXERR_NO_SENSIT = _pycplex_platform.CPXERR_NO_SENSIT
CPXERR_NO_SOLN = _pycplex_platform.CPXERR_NO_SOLN
CPXERR_NO_SOLNPOOL = _pycplex_platform.CPXERR_NO_SOLNPOOL
CPXERR_NO_SOS = _pycplex_platform.CPXERR_NO_SOS
CPXERR_NO_TREE = _pycplex_platform.CPXERR_NO_TREE
CPXERR_NO_VECTOR_SOLN = _pycplex_platform.CPXERR_NO_VECTOR_SOLN
CPXERR_NODE_INDEX_RANGE = _pycplex_platform.CPXERR_NODE_INDEX_RANGE
CPXERR_NODE_ON_DISK = _pycplex_platform.CPXERR_NODE_ON_DISK
CPXERR_NOT_DUAL_UNBOUNDED = _pycplex_platform.CPXERR_NOT_DUAL_UNBOUNDED
CPXERR_NOT_FIXED = _pycplex_platform.CPXERR_NOT_FIXED
CPXERR_NOT_FOR_BENDERS = _pycplex_platform.CPXERR_NOT_FOR_BENDERS
CPXERR_NOT_FOR_MIP = _pycplex_platform.CPXERR_NOT_FOR_MIP
CPXERR_NOT_FOR_MULTIOBJ = _pycplex_platform.CPXERR_NOT_FOR_MULTIOBJ
CPXERR_NOT_FOR_QCP = _pycplex_platform.CPXERR_NOT_FOR_QCP
CPXERR_NOT_FOR_QP = _pycplex_platform.CPXERR_NOT_FOR_QP
CPXERR_NOT_MILPCLASS = _pycplex_platform.CPXERR_NOT_MILPCLASS
CPXERR_NOT_MIN_COST_FLOW = _pycplex_platform.CPXERR_NOT_MIN_COST_FLOW
CPXERR_NOT_MIP = _pycplex_platform.CPXERR_NOT_MIP
CPXERR_NOT_MIQPCLASS = _pycplex_platform.CPXERR_NOT_MIQPCLASS
CPXERR_NOT_ONE_PROBLEM = _pycplex_platform.CPXERR_NOT_ONE_PROBLEM
CPXERR_NOT_QP = _pycplex_platform.CPXERR_NOT_QP
CPXERR_NOT_SAV_FILE = _pycplex_platform.CPXERR_NOT_SAV_FILE
CPXERR_NOT_UNBOUNDED = _pycplex_platform.CPXERR_NOT_UNBOUNDED
CPXERR_NULL_POINTER = _pycplex_platform.CPXERR_NULL_POINTER
CPXERR_ORDER_BAD_DIRECTION = _pycplex_platform.CPXERR_ORDER_BAD_DIRECTION
CPXERR_OVERFLOW = _pycplex_platform.CPXERR_OVERFLOW
CPXERR_PARAM_INCOMPATIBLE = _pycplex_platform.CPXERR_PARAM_INCOMPATIBLE
CPXERR_PARAM_TOO_BIG = _pycplex_platform.CPXERR_PARAM_TOO_BIG
CPXERR_PARAM_TOO_SMALL = _pycplex_platform.CPXERR_PARAM_TOO_SMALL
CPXERR_PRESLV_ABORT = _pycplex_platform.CPXERR_PRESLV_ABORT
CPXERR_PRESLV_BAD_PARAM = _pycplex_platform.CPXERR_PRESLV_BAD_PARAM
CPXERR_PRESLV_BASIS_MEM = _pycplex_platform.CPXERR_PRESLV_BASIS_MEM
CPXERR_PRESLV_COPYORDER = _pycplex_platform.CPXERR_PRESLV_COPYORDER
CPXERR_PRESLV_COPYSOS = _pycplex_platform.CPXERR_PRESLV_COPYSOS
CPXERR_PRESLV_CRUSHFORM = _pycplex_platform.CPXERR_PRESLV_CRUSHFORM
CPXERR_PRESLV_DETTIME_LIM = _pycplex_platform.CPXERR_PRESLV_DETTIME_LIM
CPXERR_PRESLV_DUAL = _pycplex_platform.CPXERR_PRESLV_DUAL
CPXERR_PRESLV_FAIL_BASIS = _pycplex_platform.CPXERR_PRESLV_FAIL_BASIS
CPXERR_PRESLV_INF = _pycplex_platform.CPXERR_PRESLV_INF
CPXERR_PRESLV_INForUNBD = _pycplex_platform.CPXERR_PRESLV_INForUNBD
CPXERR_PRESLV_NO_BASIS = _pycplex_platform.CPXERR_PRESLV_NO_BASIS
CPXERR_PRESLV_NO_PROB = _pycplex_platform.CPXERR_PRESLV_NO_PROB
CPXERR_PRESLV_SOLN_MIP = _pycplex_platform.CPXERR_PRESLV_SOLN_MIP
CPXERR_PRESLV_SOLN_QP = _pycplex_platform.CPXERR_PRESLV_SOLN_QP
CPXERR_PRESLV_START_LP = _pycplex_platform.CPXERR_PRESLV_START_LP
CPXERR_PRESLV_TIME_LIM = _pycplex_platform.CPXERR_PRESLV_TIME_LIM
CPXERR_PRESLV_UNBD = _pycplex_platform.CPXERR_PRESLV_UNBD
CPXERR_PRESLV_UNCRUSHFORM = _pycplex_platform.CPXERR_PRESLV_UNCRUSHFORM
CPXERR_PRIIND = _pycplex_platform.CPXERR_PRIIND
CPXERR_PRM_DATA = _pycplex_platform.CPXERR_PRM_DATA
CPXERR_PRM_HEADER = _pycplex_platform.CPXERR_PRM_HEADER
CPXERR_PROTOCOL = _pycplex_platform.CPXERR_PROTOCOL
CPXERR_Q_DIVISOR = _pycplex_platform.CPXERR_Q_DIVISOR
CPXERR_Q_DUP_ENTRY = _pycplex_platform.CPXERR_Q_DUP_ENTRY
CPXERR_Q_NOT_INDEF = _pycplex_platform.CPXERR_Q_NOT_INDEF
CPXERR_Q_NOT_POS_DEF = _pycplex_platform.CPXERR_Q_NOT_POS_DEF
CPXERR_Q_NOT_SYMMETRIC = _pycplex_platform.CPXERR_Q_NOT_SYMMETRIC
CPXERR_QCP_SENSE = _pycplex_platform.CPXERR_QCP_SENSE
CPXERR_QCP_SENSE_FILE = _pycplex_platform.CPXERR_QCP_SENSE_FILE
CPXERR_QUAD_EXP_NOT_2 = _pycplex_platform.CPXERR_QUAD_EXP_NOT_2
CPXERR_QUAD_IN_ROW = _pycplex_platform.CPXERR_QUAD_IN_ROW
CPXERR_RANGE_SECTION_ORDER = _pycplex_platform.CPXERR_RANGE_SECTION_ORDER
CPXERR_RESTRICTED_VERSION = _pycplex_platform.CPXERR_RESTRICTED_VERSION
CPXERR_RHS_IN_OBJ = _pycplex_platform.CPXERR_RHS_IN_OBJ
CPXERR_RIM_REPEATS = _pycplex_platform.CPXERR_RIM_REPEATS
CPXERR_RIM_ROW_REPEATS = _pycplex_platform.CPXERR_RIM_ROW_REPEATS
CPXERR_RIMNZ_REPEATS = _pycplex_platform.CPXERR_RIMNZ_REPEATS
CPXERR_ROW_INDEX_RANGE = _pycplex_platform.CPXERR_ROW_INDEX_RANGE
CPXERR_ROW_REPEAT_PRINT = _pycplex_platform.CPXERR_ROW_REPEAT_PRINT
CPXERR_ROW_REPEATS = _pycplex_platform.CPXERR_ROW_REPEATS
CPXERR_ROW_UNKNOWN = _pycplex_platform.CPXERR_ROW_UNKNOWN
CPXERR_SAV_FILE_DATA = _pycplex_platform.CPXERR_SAV_FILE_DATA
CPXERR_SAV_FILE_VALUE = _pycplex_platform.CPXERR_SAV_FILE_VALUE
CPXERR_SAV_FILE_WRITE = _pycplex_platform.CPXERR_SAV_FILE_WRITE
CPXERR_SBASE_ILLEGAL = _pycplex_platform.CPXERR_SBASE_ILLEGAL
CPXERR_SBASE_INCOMPAT = _pycplex_platform.CPXERR_SBASE_INCOMPAT
CPXERR_SINGULAR = _pycplex_platform.CPXERR_SINGULAR
CPXERR_STR_PARAM_TOO_LONG = _pycplex_platform.CPXERR_STR_PARAM_TOO_LONG
CPXERR_SUBPROB_SOLVE = _pycplex_platform.CPXERR_SUBPROB_SOLVE
CPXERR_SYNCPRIM_CREATE = _pycplex_platform.CPXERR_SYNCPRIM_CREATE
CPXERR_SYSCALL = _pycplex_platform.CPXERR_SYSCALL
CPXERR_THREAD_FAILED = _pycplex_platform.CPXERR_THREAD_FAILED
CPXERR_TILIM_CONDITION_NO = _pycplex_platform.CPXERR_TILIM_CONDITION_NO
CPXERR_TILIM_STRONGBRANCH = _pycplex_platform.CPXERR_TILIM_STRONGBRANCH
CPXERR_TOO_MANY_COEFFS = _pycplex_platform.CPXERR_TOO_MANY_COEFFS
CPXERR_TOO_MANY_COLS = _pycplex_platform.CPXERR_TOO_MANY_COLS
CPXERR_TOO_MANY_RIMNZ = _pycplex_platform.CPXERR_TOO_MANY_RIMNZ
CPXERR_TOO_MANY_RIMS = _pycplex_platform.CPXERR_TOO_MANY_RIMS
CPXERR_TOO_MANY_ROWS = _pycplex_platform.CPXERR_TOO_MANY_ROWS
CPXERR_TOO_MANY_THREADS = _pycplex_platform.CPXERR_TOO_MANY_THREADS
CPXERR_TREE_MEMORY_LIMIT = _pycplex_platform.CPXERR_TREE_MEMORY_LIMIT
CPXERR_TUNE_MIXED = _pycplex_platform.CPXERR_TUNE_MIXED
CPXERR_UNIQUE_WEIGHTS = _pycplex_platform.CPXERR_UNIQUE_WEIGHTS
CPXERR_UNSUPPORTED_CONSTRAINT_TYPE = _pycplex_platform.CPXERR_UNSUPPORTED_CONSTRAINT_TYPE
CPXERR_UNSUPPORTED_OPERATION = _pycplex_platform.CPXERR_UNSUPPORTED_OPERATION
CPXERR_UP_BOUND_REPEATS = _pycplex_platform.CPXERR_UP_BOUND_REPEATS
CPXERR_WORK_FILE_OPEN = _pycplex_platform.CPXERR_WORK_FILE_OPEN
CPXERR_WORK_FILE_READ = _pycplex_platform.CPXERR_WORK_FILE_READ
CPXERR_WORK_FILE_WRITE = _pycplex_platform.CPXERR_WORK_FILE_WRITE
CPXERR_XMLPARSE = _pycplex_platform.CPXERR_XMLPARSE
CPXMESSAGEBUFSIZE = _pycplex_platform.CPXMESSAGEBUFSIZE
CPXMI_BIGM_COEF = _pycplex_platform.CPXMI_BIGM_COEF
CPXMI_BIGM_TO_IND = _pycplex_platform.CPXMI_BIGM_TO_IND
CPXMI_BIGM_VARBOUND = _pycplex_platform.CPXMI_BIGM_VARBOUND
CPXMI_CANCEL_TOL = _pycplex_platform.CPXMI_CANCEL_TOL
CPXMI_EPGAP_LARGE = _pycplex_platform.CPXMI_EPGAP_LARGE
CPXMI_EPGAP_OBJOFFSET = _pycplex_platform.CPXMI_EPGAP_OBJOFFSET
CPXMI_FEAS_TOL = _pycplex_platform.CPXMI_FEAS_TOL
CPXMI_FRACTION_SCALING = _pycplex_platform.CPXMI_FRACTION_SCALING
CPXMI_IND_NZ_LARGE_NUM = _pycplex_platform.CPXMI_IND_NZ_LARGE_NUM
CPXMI_IND_NZ_SMALL_NUM = _pycplex_platform.CPXMI_IND_NZ_SMALL_NUM
CPXMI_IND_RHS_LARGE_NUM = _pycplex_platform.CPXMI_IND_RHS_LARGE_NUM
CPXMI_IND_RHS_SMALL_NUM = _pycplex_platform.CPXMI_IND_RHS_SMALL_NUM
CPXMI_KAPPA_ILLPOSED = _pycplex_platform.CPXMI_KAPPA_ILLPOSED
CPXMI_KAPPA_SUSPICIOUS = _pycplex_platform.CPXMI_KAPPA_SUSPICIOUS
CPXMI_KAPPA_UNSTABLE = _pycplex_platform.CPXMI_KAPPA_UNSTABLE
CPXMI_LB_LARGE_NUM = _pycplex_platform.CPXMI_LB_LARGE_NUM
CPXMI_LB_SMALL_NUM = _pycplex_platform.CPXMI_LB_SMALL_NUM
CPXMI_LC_NZ_LARGE_NUM = _pycplex_platform.CPXMI_LC_NZ_LARGE_NUM
CPXMI_LC_NZ_SMALL_NUM = _pycplex_platform.CPXMI_LC_NZ_SMALL_NUM
CPXMI_LC_RHS_LARGE_NUM = _pycplex_platform.CPXMI_LC_RHS_LARGE_NUM
CPXMI_LC_RHS_SMALL_NUM = _pycplex_platform.CPXMI_LC_RHS_SMALL_NUM
CPXMI_MULTIOBJ_COEFFS = _pycplex_platform.CPXMI_MULTIOBJ_COEFFS
CPXMI_MULTIOBJ_LARGE_NUM = _pycplex_platform.CPXMI_MULTIOBJ_LARGE_NUM
CPXMI_MULTIOBJ_MIX = _pycplex_platform.CPXMI_MULTIOBJ_MIX
CPXMI_MULTIOBJ_OPT_TOL = _pycplex_platform.CPXMI_MULTIOBJ_OPT_TOL
CPXMI_MULTIOBJ_SMALL_NUM = _pycplex_platform.CPXMI_MULTIOBJ_SMALL_NUM
CPXMI_NZ_LARGE_NUM = _pycplex_platform.CPXMI_NZ_LARGE_NUM
CPXMI_NZ_SMALL_NUM = _pycplex_platform.CPXMI_NZ_SMALL_NUM
CPXMI_OBJ_LARGE_NUM = _pycplex_platform.CPXMI_OBJ_LARGE_NUM
CPXMI_OBJ_SMALL_NUM = _pycplex_platform.CPXMI_OBJ_SMALL_NUM
CPXMI_OPT_TOL = _pycplex_platform.CPXMI_OPT_TOL
CPXMI_QC_LINNZ_LARGE_NUM = _pycplex_platform.CPXMI_QC_LINNZ_LARGE_NUM
CPXMI_QC_LINNZ_SMALL_NUM = _pycplex_platform.CPXMI_QC_LINNZ_SMALL_NUM
CPXMI_QC_QNZ_LARGE_NUM = _pycplex_platform.CPXMI_QC_QNZ_LARGE_NUM
CPXMI_QC_QNZ_SMALL_NUM = _pycplex_platform.CPXMI_QC_QNZ_SMALL_NUM
CPXMI_QC_RHS_LARGE_NUM = _pycplex_platform.CPXMI_QC_RHS_LARGE_NUM
CPXMI_QC_RHS_SMALL_NUM = _pycplex_platform.CPXMI_QC_RHS_SMALL_NUM
CPXMI_QOBJ_LARGE_NUM = _pycplex_platform.CPXMI_QOBJ_LARGE_NUM
CPXMI_QOBJ_SMALL_NUM = _pycplex_platform.CPXMI_QOBJ_SMALL_NUM
CPXMI_QOPT_TOL = _pycplex_platform.CPXMI_QOPT_TOL
CPXMI_RHS_LARGE_NUM = _pycplex_platform.CPXMI_RHS_LARGE_NUM
CPXMI_RHS_SMALL_NUM = _pycplex_platform.CPXMI_RHS_SMALL_NUM
CPXMI_SAMECOEFF_COL = _pycplex_platform.CPXMI_SAMECOEFF_COL
CPXMI_SAMECOEFF_IND = _pycplex_platform.CPXMI_SAMECOEFF_IND
CPXMI_SAMECOEFF_LAZY = _pycplex_platform.CPXMI_SAMECOEFF_LAZY
CPXMI_SAMECOEFF_MULTIOBJ = _pycplex_platform.CPXMI_SAMECOEFF_MULTIOBJ
CPXMI_SAMECOEFF_OBJ = _pycplex_platform.CPXMI_SAMECOEFF_OBJ
CPXMI_SAMECOEFF_QLIN = _pycplex_platform.CPXMI_SAMECOEFF_QLIN
CPXMI_SAMECOEFF_QUAD = _pycplex_platform.CPXMI_SAMECOEFF_QUAD
CPXMI_SAMECOEFF_RHS = _pycplex_platform.CPXMI_SAMECOEFF_RHS
CPXMI_SAMECOEFF_ROW = _pycplex_platform.CPXMI_SAMECOEFF_ROW
CPXMI_SAMECOEFF_UCUT = _pycplex_platform.CPXMI_SAMECOEFF_UCUT
CPXMI_SINGLE_PRECISION = _pycplex_platform.CPXMI_SINGLE_PRECISION
CPXMI_SYMMETRY_BREAKING_INEQ = _pycplex_platform.CPXMI_SYMMETRY_BREAKING_INEQ
CPXMI_UB_LARGE_NUM = _pycplex_platform.CPXMI_UB_LARGE_NUM
CPXMI_UB_SMALL_NUM = _pycplex_platform.CPXMI_UB_SMALL_NUM
CPXMI_UC_NZ_LARGE_NUM = _pycplex_platform.CPXMI_UC_NZ_LARGE_NUM
CPXMI_UC_NZ_SMALL_NUM = _pycplex_platform.CPXMI_UC_NZ_SMALL_NUM
CPXMI_UC_RHS_LARGE_NUM = _pycplex_platform.CPXMI_UC_RHS_LARGE_NUM
CPXMI_UC_RHS_SMALL_NUM = _pycplex_platform.CPXMI_UC_RHS_SMALL_NUM
CPXMI_WIDE_COEFF_RANGE = _pycplex_platform.CPXMI_WIDE_COEFF_RANGE
CPXMIP_ABORT_FEAS = _pycplex_platform.CPXMIP_ABORT_FEAS
CPXMIP_ABORT_INFEAS = _pycplex_platform.CPXMIP_ABORT_INFEAS
CPXMIP_ABORT_RELAXATION_UNBOUNDED = _pycplex_platform.CPXMIP_ABORT_RELAXATION_UNBOUNDED
CPXMIP_ABORT_RELAXED = _pycplex_platform.CPXMIP_ABORT_RELAXED
CPXMIP_BENDERS_MASTER_UNBOUNDED = _pycplex_platform.CPXMIP_BENDERS_MASTER_UNBOUNDED
CPXMIP_DETTIME_LIM_FEAS = _pycplex_platform.CPXMIP_DETTIME_LIM_FEAS
CPXMIP_DETTIME_LIM_INFEAS = _pycplex_platform.CPXMIP_DETTIME_LIM_INFEAS
CPXMIP_FAIL_FEAS = _pycplex_platform.CPXMIP_FAIL_FEAS
CPXMIP_FAIL_FEAS_NO_TREE = _pycplex_platform.CPXMIP_FAIL_FEAS_NO_TREE
CPXMIP_FAIL_INFEAS = _pycplex_platform.CPXMIP_FAIL_INFEAS
CPXMIP_FAIL_INFEAS_NO_TREE = _pycplex_platform.CPXMIP_FAIL_INFEAS_NO_TREE
CPXMIP_FEASIBLE = _pycplex_platform.CPXMIP_FEASIBLE
CPXMIP_FEASIBLE_RELAXED_INF = _pycplex_platform.CPXMIP_FEASIBLE_RELAXED_INF
CPXMIP_FEASIBLE_RELAXED_QUAD = _pycplex_platform.CPXMIP_FEASIBLE_RELAXED_QUAD
CPXMIP_FEASIBLE_RELAXED_SUM = _pycplex_platform.CPXMIP_FEASIBLE_RELAXED_SUM
CPXMIP_INFEASIBLE = _pycplex_platform.CPXMIP_INFEASIBLE
CPXMIP_INForUNBD = _pycplex_platform.CPXMIP_INForUNBD
CPXMIP_MEM_LIM_FEAS = _pycplex_platform.CPXMIP_MEM_LIM_FEAS
CPXMIP_MEM_LIM_INFEAS = _pycplex_platform.CPXMIP_MEM_LIM_INFEAS
CPXMIP_NODE_LIM_FEAS = _pycplex_platform.CPXMIP_NODE_LIM_FEAS
CPXMIP_NODE_LIM_INFEAS = _pycplex_platform.CPXMIP_NODE_LIM_INFEAS
CPXMIP_OPTIMAL = _pycplex_platform.CPXMIP_OPTIMAL
CPXMIP_OPTIMAL_INFEAS = _pycplex_platform.CPXMIP_OPTIMAL_INFEAS
CPXMIP_OPTIMAL_POPULATED = _pycplex_platform.CPXMIP_OPTIMAL_POPULATED
CPXMIP_OPTIMAL_POPULATED_TOL = _pycplex_platform.CPXMIP_OPTIMAL_POPULATED_TOL
CPXMIP_OPTIMAL_RELAXED_INF = _pycplex_platform.CPXMIP_OPTIMAL_RELAXED_INF
CPXMIP_OPTIMAL_RELAXED_QUAD = _pycplex_platform.CPXMIP_OPTIMAL_RELAXED_QUAD
CPXMIP_OPTIMAL_RELAXED_SUM = _pycplex_platform.CPXMIP_OPTIMAL_RELAXED_SUM
CPXMIP_OPTIMAL_TOL = _pycplex_platform.CPXMIP_OPTIMAL_TOL
CPXMIP_POPULATESOL_LIM = _pycplex_platform.CPXMIP_POPULATESOL_LIM
CPXMIP_SOL_LIM = _pycplex_platform.CPXMIP_SOL_LIM
CPXMIP_TIME_LIM_FEAS = _pycplex_platform.CPXMIP_TIME_LIM_FEAS
CPXMIP_TIME_LIM_INFEAS = _pycplex_platform.CPXMIP_TIME_LIM_INFEAS
CPXMIP_UNBOUNDED = _pycplex_platform.CPXMIP_UNBOUNDED
CPX_CPXAUTOENUMS_H_H = _pycplex_platform.CPX_CPXAUTOENUMS_H_H
CPXCALLBACKINFO_THREADID = _pycplex_platform.CPXCALLBACKINFO_THREADID
CPXCALLBACKINFO_NODECOUNT = _pycplex_platform.CPXCALLBACKINFO_NODECOUNT
CPXCALLBACKINFO_ITCOUNT = _pycplex_platform.CPXCALLBACKINFO_ITCOUNT
CPXCALLBACKINFO_BEST_SOL = _pycplex_platform.CPXCALLBACKINFO_BEST_SOL
CPXCALLBACKINFO_BEST_BND = _pycplex_platform.CPXCALLBACKINFO_BEST_BND
CPXCALLBACKINFO_THREADS = _pycplex_platform.CPXCALLBACKINFO_THREADS
CPXCALLBACKINFO_FEASIBLE = _pycplex_platform.CPXCALLBACKINFO_FEASIBLE
CPXCALLBACKINFO_TIME = _pycplex_platform.CPXCALLBACKINFO_TIME
CPXCALLBACKINFO_DETTIME = _pycplex_platform.CPXCALLBACKINFO_DETTIME
CPXCALLBACKSOLUTION_NOCHECK = _pycplex_platform.CPXCALLBACKSOLUTION_NOCHECK
CPXCALLBACKSOLUTION_CHECKFEAS = _pycplex_platform.CPXCALLBACKSOLUTION_CHECKFEAS
CPXCALLBACKSOLUTION_PROPAGATE = _pycplex_platform.CPXCALLBACKSOLUTION_PROPAGATE
CPXCALLBACKSOLUTION_SOLVE = _pycplex_platform.CPXCALLBACKSOLUTION_SOLVE
CPXINFO_BYTE = _pycplex_platform.CPXINFO_BYTE
CPXINFO_SHORT = _pycplex_platform.CPXINFO_SHORT
CPXINFO_INT = _pycplex_platform.CPXINFO_INT
CPXINFO_LONG = _pycplex_platform.CPXINFO_LONG
CPXINFO_DOUBLE = _pycplex_platform.CPXINFO_DOUBLE
CPXPUBLICPARAMS_H = _pycplex_platform.CPXPUBLICPARAMS_H
CPX_PARAM_ADVIND = _pycplex_platform.CPX_PARAM_ADVIND
CPX_PARAM_AGGFILL = _pycplex_platform.CPX_PARAM_AGGFILL
CPX_PARAM_AGGIND = _pycplex_platform.CPX_PARAM_AGGIND
CPX_PARAM_CLOCKTYPE = _pycplex_platform.CPX_PARAM_CLOCKTYPE
CPX_PARAM_CRAIND = _pycplex_platform.CPX_PARAM_CRAIND
CPX_PARAM_DEPIND = _pycplex_platform.CPX_PARAM_DEPIND
CPX_PARAM_DPRIIND = _pycplex_platform.CPX_PARAM_DPRIIND
CPX_PARAM_PRICELIM = _pycplex_platform.CPX_PARAM_PRICELIM
CPX_PARAM_EPMRK = _pycplex_platform.CPX_PARAM_EPMRK
CPX_PARAM_EPOPT = _pycplex_platform.CPX_PARAM_EPOPT
CPX_PARAM_EPPER = _pycplex_platform.CPX_PARAM_EPPER
CPX_PARAM_EPRHS = _pycplex_platform.CPX_PARAM_EPRHS
CPX_PARAM_SIMDISPLAY = _pycplex_platform.CPX_PARAM_SIMDISPLAY
CPX_PARAM_ITLIM = _pycplex_platform.CPX_PARAM_ITLIM
CPX_PARAM_ROWREADLIM = _pycplex_platform.CPX_PARAM_ROWREADLIM
CPX_PARAM_NETFIND = _pycplex_platform.CPX_PARAM_NETFIND
CPX_PARAM_COLREADLIM = _pycplex_platform.CPX_PARAM_COLREADLIM
CPX_PARAM_NZREADLIM = _pycplex_platform.CPX_PARAM_NZREADLIM
CPX_PARAM_OBJLLIM = _pycplex_platform.CPX_PARAM_OBJLLIM
CPX_PARAM_OBJULIM = _pycplex_platform.CPX_PARAM_OBJULIM
CPX_PARAM_PERIND = _pycplex_platform.CPX_PARAM_PERIND
CPX_PARAM_PERLIM = _pycplex_platform.CPX_PARAM_PERLIM
CPX_PARAM_PPRIIND = _pycplex_platform.CPX_PARAM_PPRIIND
CPX_PARAM_PREIND = _pycplex_platform.CPX_PARAM_PREIND
CPX_PARAM_REINV = _pycplex_platform.CPX_PARAM_REINV
CPX_PARAM_SCAIND = _pycplex_platform.CPX_PARAM_SCAIND
CPX_PARAM_SCRIND = _pycplex_platform.CPX_PARAM_SCRIND
CPX_PARAM_SINGLIM = _pycplex_platform.CPX_PARAM_SINGLIM
CPX_PARAM_TILIM = _pycplex_platform.CPX_PARAM_TILIM
CPX_PARAM_PREDUAL = _pycplex_platform.CPX_PARAM_PREDUAL
CPX_PARAM_PREPASS = _pycplex_platform.CPX_PARAM_PREPASS
CPX_PARAM_DATACHECK = _pycplex_platform.CPX_PARAM_DATACHECK
CPX_PARAM_REDUCE = _pycplex_platform.CPX_PARAM_REDUCE
CPX_PARAM_PRELINEAR = _pycplex_platform.CPX_PARAM_PRELINEAR
CPX_PARAM_LPMETHOD = _pycplex_platform.CPX_PARAM_LPMETHOD
CPX_PARAM_QPMETHOD = _pycplex_platform.CPX_PARAM_QPMETHOD
CPX_PARAM_WORKDIR = _pycplex_platform.CPX_PARAM_WORKDIR
CPX_PARAM_WORKMEM = _pycplex_platform.CPX_PARAM_WORKMEM
CPX_PARAM_THREADS = _pycplex_platform.CPX_PARAM_THREADS
CPX_PARAM_CONFLICTALG = _pycplex_platform.CPX_PARAM_CONFLICTALG
CPX_PARAM_CONFLICTDISPLAY = _pycplex_platform.CPX_PARAM_CONFLICTDISPLAY
CPX_PARAM_SIFTDISPLAY = _pycplex_platform.CPX_PARAM_SIFTDISPLAY
CPX_PARAM_SIFTALG = _pycplex_platform.CPX_PARAM_SIFTALG
CPX_PARAM_SIFTITLIM = _pycplex_platform.CPX_PARAM_SIFTITLIM
CPX_PARAM_MPSLONGNUM = _pycplex_platform.CPX_PARAM_MPSLONGNUM
CPX_PARAM_MEMORYEMPHASIS = _pycplex_platform.CPX_PARAM_MEMORYEMPHASIS
CPX_PARAM_NUMERICALEMPHASIS = _pycplex_platform.CPX_PARAM_NUMERICALEMPHASIS
CPX_PARAM_FEASOPTMODE = _pycplex_platform.CPX_PARAM_FEASOPTMODE
CPX_PARAM_PARALLELMODE = _pycplex_platform.CPX_PARAM_PARALLELMODE
CPX_PARAM_TUNINGMEASURE = _pycplex_platform.CPX_PARAM_TUNINGMEASURE
CPX_PARAM_TUNINGREPEAT = _pycplex_platform.CPX_PARAM_TUNINGREPEAT
CPX_PARAM_TUNINGTILIM = _pycplex_platform.CPX_PARAM_TUNINGTILIM
CPX_PARAM_TUNINGDISPLAY = _pycplex_platform.CPX_PARAM_TUNINGDISPLAY
CPX_PARAM_WRITELEVEL = _pycplex_platform.CPX_PARAM_WRITELEVEL
CPX_PARAM_RANDOMSEED = _pycplex_platform.CPX_PARAM_RANDOMSEED
CPX_PARAM_DETTILIM = _pycplex_platform.CPX_PARAM_DETTILIM
CPX_PARAM_FILEENCODING = _pycplex_platform.CPX_PARAM_FILEENCODING
CPX_PARAM_APIENCODING = _pycplex_platform.CPX_PARAM_APIENCODING
CPX_PARAM_OPTIMALITYTARGET = _pycplex_platform.CPX_PARAM_OPTIMALITYTARGET
CPX_PARAM_CLONELOG = _pycplex_platform.CPX_PARAM_CLONELOG
CPX_PARAM_TUNINGDETTILIM = _pycplex_platform.CPX_PARAM_TUNINGDETTILIM
CPX_PARAM_CPUMASK = _pycplex_platform.CPX_PARAM_CPUMASK
CPX_PARAM_SOLUTIONTYPE = _pycplex_platform.CPX_PARAM_SOLUTIONTYPE
CPX_PARAM_WARNLIM = _pycplex_platform.CPX_PARAM_WARNLIM
CPX_PARAM_SIFTSIM = _pycplex_platform.CPX_PARAM_SIFTSIM
CPX_PARAM_DYNAMICROWS = _pycplex_platform.CPX_PARAM_DYNAMICROWS
CPX_PARAM_RECORD = _pycplex_platform.CPX_PARAM_RECORD
CPX_PARAM_PARAMDISPLAY = _pycplex_platform.CPX_PARAM_PARAMDISPLAY
CPX_PARAM_FOLDING = _pycplex_platform.CPX_PARAM_FOLDING
CPX_PARAM_WORKERALG = _pycplex_platform.CPX_PARAM_WORKERALG
CPX_PARAM_BENDERSSTRATEGY = _pycplex_platform.CPX_PARAM_BENDERSSTRATEGY
CPX_PARAM_BENDERSFEASCUTTOL = _pycplex_platform.CPX_PARAM_BENDERSFEASCUTTOL
CPX_PARAM_BENDERSOPTCUTTOL = _pycplex_platform.CPX_PARAM_BENDERSOPTCUTTOL
CPX_PARAM_MULTIOBJDISPLAY = _pycplex_platform.CPX_PARAM_MULTIOBJDISPLAY
CPX_PARAM_BRDIR = _pycplex_platform.CPX_PARAM_BRDIR
CPX_PARAM_BTTOL = _pycplex_platform.CPX_PARAM_BTTOL
CPX_PARAM_CLIQUES = _pycplex_platform.CPX_PARAM_CLIQUES
CPX_PARAM_COEREDIND = _pycplex_platform.CPX_PARAM_COEREDIND
CPX_PARAM_COVERS = _pycplex_platform.CPX_PARAM_COVERS
CPX_PARAM_CUTLO = _pycplex_platform.CPX_PARAM_CUTLO
CPX_PARAM_CUTUP = _pycplex_platform.CPX_PARAM_CUTUP
CPX_PARAM_EPAGAP = _pycplex_platform.CPX_PARAM_EPAGAP
CPX_PARAM_EPGAP = _pycplex_platform.CPX_PARAM_EPGAP
CPX_PARAM_EPINT = _pycplex_platform.CPX_PARAM_EPINT
CPX_PARAM_MIPDISPLAY = _pycplex_platform.CPX_PARAM_MIPDISPLAY
CPX_PARAM_MIPINTERVAL = _pycplex_platform.CPX_PARAM_MIPINTERVAL
CPX_PARAM_INTSOLLIM = _pycplex_platform.CPX_PARAM_INTSOLLIM
CPX_PARAM_NODEFILEIND = _pycplex_platform.CPX_PARAM_NODEFILEIND
CPX_PARAM_NODELIM = _pycplex_platform.CPX_PARAM_NODELIM
CPX_PARAM_NODESEL = _pycplex_platform.CPX_PARAM_NODESEL
CPX_PARAM_OBJDIF = _pycplex_platform.CPX_PARAM_OBJDIF
CPX_PARAM_MIPORDIND = _pycplex_platform.CPX_PARAM_MIPORDIND
CPX_PARAM_RELOBJDIF = _pycplex_platform.CPX_PARAM_RELOBJDIF
CPX_PARAM_STARTALG = _pycplex_platform.CPX_PARAM_STARTALG
CPX_PARAM_SUBALG = _pycplex_platform.CPX_PARAM_SUBALG
CPX_PARAM_TRELIM = _pycplex_platform.CPX_PARAM_TRELIM
CPX_PARAM_VARSEL = _pycplex_platform.CPX_PARAM_VARSEL
CPX_PARAM_BNDSTRENIND = _pycplex_platform.CPX_PARAM_BNDSTRENIND
CPX_PARAM_HEURFREQ = _pycplex_platform.CPX_PARAM_HEURFREQ
CPX_PARAM_MIPORDTYPE = _pycplex_platform.CPX_PARAM_MIPORDTYPE
CPX_PARAM_CUTSFACTOR = _pycplex_platform.CPX_PARAM_CUTSFACTOR
CPX_PARAM_RELAXPREIND = _pycplex_platform.CPX_PARAM_RELAXPREIND
CPX_PARAM_PRESLVND = _pycplex_platform.CPX_PARAM_PRESLVND
CPX_PARAM_BBINTERVAL = _pycplex_platform.CPX_PARAM_BBINTERVAL
CPX_PARAM_FLOWCOVERS = _pycplex_platform.CPX_PARAM_FLOWCOVERS
CPX_PARAM_IMPLBD = _pycplex_platform.CPX_PARAM_IMPLBD
CPX_PARAM_PROBE = _pycplex_platform.CPX_PARAM_PROBE
CPX_PARAM_GUBCOVERS = _pycplex_platform.CPX_PARAM_GUBCOVERS
CPX_PARAM_STRONGCANDLIM = _pycplex_platform.CPX_PARAM_STRONGCANDLIM
CPX_PARAM_STRONGITLIM = _pycplex_platform.CPX_PARAM_STRONGITLIM
CPX_PARAM_FRACCAND = _pycplex_platform.CPX_PARAM_FRACCAND
CPX_PARAM_FRACCUTS = _pycplex_platform.CPX_PARAM_FRACCUTS
CPX_PARAM_FRACPASS = _pycplex_platform.CPX_PARAM_FRACPASS
CPX_PARAM_FLOWPATHS = _pycplex_platform.CPX_PARAM_FLOWPATHS
CPX_PARAM_MIRCUTS = _pycplex_platform.CPX_PARAM_MIRCUTS
CPX_PARAM_DISJCUTS = _pycplex_platform.CPX_PARAM_DISJCUTS
CPX_PARAM_AGGCUTLIM = _pycplex_platform.CPX_PARAM_AGGCUTLIM
CPX_PARAM_MIPCBREDLP = _pycplex_platform.CPX_PARAM_MIPCBREDLP
CPX_PARAM_CUTPASS = _pycplex_platform.CPX_PARAM_CUTPASS
CPX_PARAM_MIPEMPHASIS = _pycplex_platform.CPX_PARAM_MIPEMPHASIS
CPX_PARAM_SYMMETRY = _pycplex_platform.CPX_PARAM_SYMMETRY
CPX_PARAM_DIVETYPE = _pycplex_platform.CPX_PARAM_DIVETYPE
CPX_PARAM_RINSHEUR = _pycplex_platform.CPX_PARAM_RINSHEUR
CPX_PARAM_LBHEUR = _pycplex_platform.CPX_PARAM_LBHEUR
CPX_PARAM_REPEATPRESOLVE = _pycplex_platform.CPX_PARAM_REPEATPRESOLVE
CPX_PARAM_PROBETIME = _pycplex_platform.CPX_PARAM_PROBETIME
CPX_PARAM_POLISHTIME = _pycplex_platform.CPX_PARAM_POLISHTIME
CPX_PARAM_REPAIRTRIES = _pycplex_platform.CPX_PARAM_REPAIRTRIES
CPX_PARAM_EPLIN = _pycplex_platform.CPX_PARAM_EPLIN
CPX_PARAM_EPRELAX = _pycplex_platform.CPX_PARAM_EPRELAX
CPX_PARAM_FPHEUR = _pycplex_platform.CPX_PARAM_FPHEUR
CPX_PARAM_EACHCUTLIM = _pycplex_platform.CPX_PARAM_EACHCUTLIM
CPX_PARAM_SOLNPOOLCAPACITY = _pycplex_platform.CPX_PARAM_SOLNPOOLCAPACITY
CPX_PARAM_SOLNPOOLREPLACE = _pycplex_platform.CPX_PARAM_SOLNPOOLREPLACE
CPX_PARAM_SOLNPOOLGAP = _pycplex_platform.CPX_PARAM_SOLNPOOLGAP
CPX_PARAM_SOLNPOOLAGAP = _pycplex_platform.CPX_PARAM_SOLNPOOLAGAP
CPX_PARAM_SOLNPOOLINTENSITY = _pycplex_platform.CPX_PARAM_SOLNPOOLINTENSITY
CPX_PARAM_POPULATELIM = _pycplex_platform.CPX_PARAM_POPULATELIM
CPX_PARAM_MIPSEARCH = _pycplex_platform.CPX_PARAM_MIPSEARCH
CPX_PARAM_MIQCPSTRAT = _pycplex_platform.CPX_PARAM_MIQCPSTRAT
CPX_PARAM_ZEROHALFCUTS = _pycplex_platform.CPX_PARAM_ZEROHALFCUTS
CPX_PARAM_POLISHAFTEREPAGAP = _pycplex_platform.CPX_PARAM_POLISHAFTEREPAGAP
CPX_PARAM_POLISHAFTEREPGAP = _pycplex_platform.CPX_PARAM_POLISHAFTEREPGAP
CPX_PARAM_POLISHAFTERNODE = _pycplex_platform.CPX_PARAM_POLISHAFTERNODE
CPX_PARAM_POLISHAFTERINTSOL = _pycplex_platform.CPX_PARAM_POLISHAFTERINTSOL
CPX_PARAM_POLISHAFTERTIME = _pycplex_platform.CPX_PARAM_POLISHAFTERTIME
CPX_PARAM_MCFCUTS = _pycplex_platform.CPX_PARAM_MCFCUTS
CPX_PARAM_MIPKAPPASTATS = _pycplex_platform.CPX_PARAM_MIPKAPPASTATS
CPX_PARAM_AUXROOTTHREADS = _pycplex_platform.CPX_PARAM_AUXROOTTHREADS
CPX_PARAM_INTSOLFILEPREFIX = _pycplex_platform.CPX_PARAM_INTSOLFILEPREFIX
CPX_PARAM_PROBEDETTIME = _pycplex_platform.CPX_PARAM_PROBEDETTIME
CPX_PARAM_POLISHAFTERDETTIME = _pycplex_platform.CPX_PARAM_POLISHAFTERDETTIME
CPX_PARAM_LANDPCUTS = _pycplex_platform.CPX_PARAM_LANDPCUTS
CPX_PARAM_RAMPUPDURATION = _pycplex_platform.CPX_PARAM_RAMPUPDURATION
CPX_PARAM_RAMPUPDETTILIM = _pycplex_platform.CPX_PARAM_RAMPUPDETTILIM
CPX_PARAM_RAMPUPTILIM = _pycplex_platform.CPX_PARAM_RAMPUPTILIM
CPX_PARAM_LOCALIMPLBD = _pycplex_platform.CPX_PARAM_LOCALIMPLBD
CPX_PARAM_BQPCUTS = _pycplex_platform.CPX_PARAM_BQPCUTS
CPX_PARAM_RLTCUTS = _pycplex_platform.CPX_PARAM_RLTCUTS
CPX_PARAM_SUBMIPSTARTALG = _pycplex_platform.CPX_PARAM_SUBMIPSTARTALG
CPX_PARAM_SUBMIPSUBALG = _pycplex_platform.CPX_PARAM_SUBMIPSUBALG
CPX_PARAM_SUBMIPSCAIND = _pycplex_platform.CPX_PARAM_SUBMIPSCAIND
CPX_PARAM_SUBMIPNODELIMIT = _pycplex_platform.CPX_PARAM_SUBMIPNODELIMIT
CPX_PARAM_BAREPCOMP = _pycplex_platform.CPX_PARAM_BAREPCOMP
CPX_PARAM_BARGROWTH = _pycplex_platform.CPX_PARAM_BARGROWTH
CPX_PARAM_BAROBJRNG = _pycplex_platform.CPX_PARAM_BAROBJRNG
CPX_PARAM_BARALG = _pycplex_platform.CPX_PARAM_BARALG
CPX_PARAM_BARCOLNZ = _pycplex_platform.CPX_PARAM_BARCOLNZ
CPX_PARAM_BARDISPLAY = _pycplex_platform.CPX_PARAM_BARDISPLAY
CPX_PARAM_BARITLIM = _pycplex_platform.CPX_PARAM_BARITLIM
CPX_PARAM_BARMAXCOR = _pycplex_platform.CPX_PARAM_BARMAXCOR
CPX_PARAM_BARORDER = _pycplex_platform.CPX_PARAM_BARORDER
CPX_PARAM_BARSTARTALG = _pycplex_platform.CPX_PARAM_BARSTARTALG
CPX_PARAM_BARCROSSALG = _pycplex_platform.CPX_PARAM_BARCROSSALG
CPX_PARAM_BARQCPEPCOMP = _pycplex_platform.CPX_PARAM_BARQCPEPCOMP
CPX_PARAM_QPNZREADLIM = _pycplex_platform.CPX_PARAM_QPNZREADLIM
CPX_PARAM_CALCQCPDUALS = _pycplex_platform.CPX_PARAM_CALCQCPDUALS
CPX_PARAM_QPMAKEPSDIND = _pycplex_platform.CPX_PARAM_QPMAKEPSDIND
CPX_PARAM_QTOLININD = _pycplex_platform.CPX_PARAM_QTOLININD
CPX_PARAM_NETITLIM = _pycplex_platform.CPX_PARAM_NETITLIM
CPX_PARAM_NETEPOPT = _pycplex_platform.CPX_PARAM_NETEPOPT
CPX_PARAM_NETEPRHS = _pycplex_platform.CPX_PARAM_NETEPRHS
CPX_PARAM_NETPPRIIND = _pycplex_platform.CPX_PARAM_NETPPRIIND
CPX_PARAM_NETDISPLAY = _pycplex_platform.CPX_PARAM_NETDISPLAY
CPX_CPXAUTOTYPES_H_H = _pycplex_platform.CPX_CPXAUTOTYPES_H_H
CPX_CPXAUTOSTRUCTS_H_H = _pycplex_platform.CPX_CPXAUTOSTRUCTS_H_H
class cpxdeserializer(_object):
    __swig_setmethods__ = {}
    __setattr__ = lambda self, name, value: _swig_setattr(self, cpxdeserializer, name, value)
    __swig_getmethods__ = {}
    __getattr__ = lambda self, name: _swig_getattr(self, cpxdeserializer, name)
    __repr__ = _swig_repr
    __swig_setmethods__["getbyte"] = _pycplex_platform.cpxdeserializer_getbyte_set
    __swig_getmethods__["getbyte"] = _pycplex_platform.cpxdeserializer_getbyte_get
    if _newclass:getbyte = _swig_property(_pycplex_platform.cpxdeserializer_getbyte_get, _pycplex_platform.cpxdeserializer_getbyte_set)
    __swig_setmethods__["getshort"] = _pycplex_platform.cpxdeserializer_getshort_set
    __swig_getmethods__["getshort"] = _pycplex_platform.cpxdeserializer_getshort_get
    if _newclass:getshort = _swig_property(_pycplex_platform.cpxdeserializer_getshort_get, _pycplex_platform.cpxdeserializer_getshort_set)
    __swig_setmethods__["getint"] = _pycplex_platform.cpxdeserializer_getint_set
    __swig_getmethods__["getint"] = _pycplex_platform.cpxdeserializer_getint_get
    if _newclass:getint = _swig_property(_pycplex_platform.cpxdeserializer_getint_get, _pycplex_platform.cpxdeserializer_getint_set)
    __swig_setmethods__["getlong"] = _pycplex_platform.cpxdeserializer_getlong_set
    __swig_getmethods__["getlong"] = _pycplex_platform.cpxdeserializer_getlong_get
    if _newclass:getlong = _swig_property(_pycplex_platform.cpxdeserializer_getlong_get, _pycplex_platform.cpxdeserializer_getlong_set)
    __swig_setmethods__["getfloat"] = _pycplex_platform.cpxdeserializer_getfloat_set
    __swig_getmethods__["getfloat"] = _pycplex_platform.cpxdeserializer_getfloat_get
    if _newclass:getfloat = _swig_property(_pycplex_platform.cpxdeserializer_getfloat_get, _pycplex_platform.cpxdeserializer_getfloat_set)
    __swig_setmethods__["getdouble"] = _pycplex_platform.cpxdeserializer_getdouble_set
    __swig_getmethods__["getdouble"] = _pycplex_platform.cpxdeserializer_getdouble_get
    if _newclass:getdouble = _swig_property(_pycplex_platform.cpxdeserializer_getdouble_get, _pycplex_platform.cpxdeserializer_getdouble_set)
    __swig_setmethods__["getbytes"] = _pycplex_platform.cpxdeserializer_getbytes_set
    __swig_getmethods__["getbytes"] = _pycplex_platform.cpxdeserializer_getbytes_get
    if _newclass:getbytes = _swig_property(_pycplex_platform.cpxdeserializer_getbytes_get, _pycplex_platform.cpxdeserializer_getbytes_set)
    __swig_setmethods__["getshorts"] = _pycplex_platform.cpxdeserializer_getshorts_set
    __swig_getmethods__["getshorts"] = _pycplex_platform.cpxdeserializer_getshorts_get
    if _newclass:getshorts = _swig_property(_pycplex_platform.cpxdeserializer_getshorts_get, _pycplex_platform.cpxdeserializer_getshorts_set)
    __swig_setmethods__["getints"] = _pycplex_platform.cpxdeserializer_getints_set
    __swig_getmethods__["getints"] = _pycplex_platform.cpxdeserializer_getints_get
    if _newclass:getints = _swig_property(_pycplex_platform.cpxdeserializer_getints_get, _pycplex_platform.cpxdeserializer_getints_set)
    __swig_setmethods__["getlongs"] = _pycplex_platform.cpxdeserializer_getlongs_set
    __swig_getmethods__["getlongs"] = _pycplex_platform.cpxdeserializer_getlongs_get
    if _newclass:getlongs = _swig_property(_pycplex_platform.cpxdeserializer_getlongs_get, _pycplex_platform.cpxdeserializer_getlongs_set)
    __swig_setmethods__["getfloats"] = _pycplex_platform.cpxdeserializer_getfloats_set
    __swig_getmethods__["getfloats"] = _pycplex_platform.cpxdeserializer_getfloats_get
    if _newclass:getfloats = _swig_property(_pycplex_platform.cpxdeserializer_getfloats_get, _pycplex_platform.cpxdeserializer_getfloats_set)
    __swig_setmethods__["getdoubles"] = _pycplex_platform.cpxdeserializer_getdoubles_set
    __swig_getmethods__["getdoubles"] = _pycplex_platform.cpxdeserializer_getdoubles_get
    if _newclass:getdoubles = _swig_property(_pycplex_platform.cpxdeserializer_getdoubles_get, _pycplex_platform.cpxdeserializer_getdoubles_set)
    def __init__(self): 
        this = _pycplex_platform.new_cpxdeserializer()
        try: self.this.append(this)
        except: self.this = this
    __swig_destroy__ = _pycplex_platform.delete_cpxdeserializer
    __del__ = lambda self : None;
cpxdeserializer_swigregister = _pycplex_platform.cpxdeserializer_swigregister
cpxdeserializer_swigregister(cpxdeserializer)

class cpxserializer(_object):
    __swig_setmethods__ = {}
    __setattr__ = lambda self, name, value: _swig_setattr(self, cpxserializer, name, value)
    __swig_getmethods__ = {}
    __getattr__ = lambda self, name: _swig_getattr(self, cpxserializer, name)
    __repr__ = _swig_repr
    __swig_setmethods__["addbyte"] = _pycplex_platform.cpxserializer_addbyte_set
    __swig_getmethods__["addbyte"] = _pycplex_platform.cpxserializer_addbyte_get
    if _newclass:addbyte = _swig_property(_pycplex_platform.cpxserializer_addbyte_get, _pycplex_platform.cpxserializer_addbyte_set)
    __swig_setmethods__["addshort"] = _pycplex_platform.cpxserializer_addshort_set
    __swig_getmethods__["addshort"] = _pycplex_platform.cpxserializer_addshort_get
    if _newclass:addshort = _swig_property(_pycplex_platform.cpxserializer_addshort_get, _pycplex_platform.cpxserializer_addshort_set)
    __swig_setmethods__["addint"] = _pycplex_platform.cpxserializer_addint_set
    __swig_getmethods__["addint"] = _pycplex_platform.cpxserializer_addint_get
    if _newclass:addint = _swig_property(_pycplex_platform.cpxserializer_addint_get, _pycplex_platform.cpxserializer_addint_set)
    __swig_setmethods__["addlong"] = _pycplex_platform.cpxserializer_addlong_set
    __swig_getmethods__["addlong"] = _pycplex_platform.cpxserializer_addlong_get
    if _newclass:addlong = _swig_property(_pycplex_platform.cpxserializer_addlong_get, _pycplex_platform.cpxserializer_addlong_set)
    __swig_setmethods__["addfloat"] = _pycplex_platform.cpxserializer_addfloat_set
    __swig_getmethods__["addfloat"] = _pycplex_platform.cpxserializer_addfloat_get
    if _newclass:addfloat = _swig_property(_pycplex_platform.cpxserializer_addfloat_get, _pycplex_platform.cpxserializer_addfloat_set)
    __swig_setmethods__["adddouble"] = _pycplex_platform.cpxserializer_adddouble_set
    __swig_getmethods__["adddouble"] = _pycplex_platform.cpxserializer_adddouble_get
    if _newclass:adddouble = _swig_property(_pycplex_platform.cpxserializer_adddouble_get, _pycplex_platform.cpxserializer_adddouble_set)
    __swig_setmethods__["addbytes"] = _pycplex_platform.cpxserializer_addbytes_set
    __swig_getmethods__["addbytes"] = _pycplex_platform.cpxserializer_addbytes_get
    if _newclass:addbytes = _swig_property(_pycplex_platform.cpxserializer_addbytes_get, _pycplex_platform.cpxserializer_addbytes_set)
    __swig_setmethods__["addshorts"] = _pycplex_platform.cpxserializer_addshorts_set
    __swig_getmethods__["addshorts"] = _pycplex_platform.cpxserializer_addshorts_get
    if _newclass:addshorts = _swig_property(_pycplex_platform.cpxserializer_addshorts_get, _pycplex_platform.cpxserializer_addshorts_set)
    __swig_setmethods__["addints"] = _pycplex_platform.cpxserializer_addints_set
    __swig_getmethods__["addints"] = _pycplex_platform.cpxserializer_addints_get
    if _newclass:addints = _swig_property(_pycplex_platform.cpxserializer_addints_get, _pycplex_platform.cpxserializer_addints_set)
    __swig_setmethods__["addlongs"] = _pycplex_platform.cpxserializer_addlongs_set
    __swig_getmethods__["addlongs"] = _pycplex_platform.cpxserializer_addlongs_get
    if _newclass:addlongs = _swig_property(_pycplex_platform.cpxserializer_addlongs_get, _pycplex_platform.cpxserializer_addlongs_set)
    __swig_setmethods__["addfloats"] = _pycplex_platform.cpxserializer_addfloats_set
    __swig_getmethods__["addfloats"] = _pycplex_platform.cpxserializer_addfloats_get
    if _newclass:addfloats = _swig_property(_pycplex_platform.cpxserializer_addfloats_get, _pycplex_platform.cpxserializer_addfloats_set)
    __swig_setmethods__["adddoubles"] = _pycplex_platform.cpxserializer_adddoubles_set
    __swig_getmethods__["adddoubles"] = _pycplex_platform.cpxserializer_adddoubles_get
    if _newclass:adddoubles = _swig_property(_pycplex_platform.cpxserializer_adddoubles_get, _pycplex_platform.cpxserializer_adddoubles_set)
    def __init__(self): 
        this = _pycplex_platform.new_cpxserializer()
        try: self.this.append(this)
        except: self.this = this
    __swig_destroy__ = _pycplex_platform.delete_cpxserializer
    __del__ = lambda self : None;
cpxserializer_swigregister = _pycplex_platform.cpxserializer_swigregister
cpxserializer_swigregister(cpxserializer)

CPX_CPLEXX_H = _pycplex_platform.CPX_CPLEXX_H
CPX_APIMODEL_SMALL = _pycplex_platform.CPX_APIMODEL_SMALL
CPX_APIMODEL_LARGE = _pycplex_platform.CPX_APIMODEL_LARGE
CPX_APIMODEL = _pycplex_platform.CPX_APIMODEL

def lpcallbackfuncwrap(*args):
  return _pycplex_platform.lpcallbackfuncwrap(*args)
lpcallbackfuncwrap = _pycplex_platform.lpcallbackfuncwrap

def netcallbackfuncwrap(*args):
  return _pycplex_platform.netcallbackfuncwrap(*args)
netcallbackfuncwrap = _pycplex_platform.netcallbackfuncwrap

def tuningcallbackfuncwrap(*args):
  return _pycplex_platform.tuningcallbackfuncwrap(*args)
tuningcallbackfuncwrap = _pycplex_platform.tuningcallbackfuncwrap

def infocallbackfuncwrap(*args):
  return _pycplex_platform.infocallbackfuncwrap(*args)
infocallbackfuncwrap = _pycplex_platform.infocallbackfuncwrap

def mipcallbackfuncwrap(*args):
  return _pycplex_platform.mipcallbackfuncwrap(*args)
mipcallbackfuncwrap = _pycplex_platform.mipcallbackfuncwrap

def branchcallbackfuncwrap(*args):
  return _pycplex_platform.branchcallbackfuncwrap(*args)
branchcallbackfuncwrap = _pycplex_platform.branchcallbackfuncwrap

def lazyconcallbackfuncwrap(*args):
  return _pycplex_platform.lazyconcallbackfuncwrap(*args)
lazyconcallbackfuncwrap = _pycplex_platform.lazyconcallbackfuncwrap

def usercutcallbackfuncwrap(*args):
  return _pycplex_platform.usercutcallbackfuncwrap(*args)
usercutcallbackfuncwrap = _pycplex_platform.usercutcallbackfuncwrap

def nodecallbackfuncwrap(*args):
  return _pycplex_platform.nodecallbackfuncwrap(*args)
nodecallbackfuncwrap = _pycplex_platform.nodecallbackfuncwrap

def heuristiccallbackfuncwrap(*args):
  return _pycplex_platform.heuristiccallbackfuncwrap(*args)
heuristiccallbackfuncwrap = _pycplex_platform.heuristiccallbackfuncwrap

def incumbentcallbackfuncwrap(*args):
  return _pycplex_platform.incumbentcallbackfuncwrap(*args)
incumbentcallbackfuncwrap = _pycplex_platform.incumbentcallbackfuncwrap

def solvecallbackfuncwrap(*args):
  return _pycplex_platform.solvecallbackfuncwrap(*args)
solvecallbackfuncwrap = _pycplex_platform.solvecallbackfuncwrap

def deletenodecallbackfuncwrap(*args):
  return _pycplex_platform.deletenodecallbackfuncwrap(*args)
deletenodecallbackfuncwrap = _pycplex_platform.deletenodecallbackfuncwrap

def setpydel(*args):
  return _pycplex_platform.setpydel(*args)
setpydel = _pycplex_platform.setpydel

def cpxpygenericcallbackfuncwrap(*args):
  return _pycplex_platform.cpxpygenericcallbackfuncwrap(*args)
cpxpygenericcallbackfuncwrap = _pycplex_platform.cpxpygenericcallbackfuncwrap

def cpxpymodelasstcallbackfuncwrap(*args):
  return _pycplex_platform.cpxpymodelasstcallbackfuncwrap(*args)
cpxpymodelasstcallbackfuncwrap = _pycplex_platform.cpxpymodelasstcallbackfuncwrap

def messagewrap(*args):
  return _pycplex_platform.messagewrap(*args)
messagewrap = _pycplex_platform.messagewrap

def setpyterminate(*args):
  return _pycplex_platform.setpyterminate(*args)
setpyterminate = _pycplex_platform.setpyterminate

def unset_py_terminator():
  return _pycplex_platform.unset_py_terminator()
unset_py_terminator = _pycplex_platform.unset_py_terminator

def set_py_terminator():
  return _pycplex_platform.set_py_terminator()
set_py_terminator = _pycplex_platform.set_py_terminator

def new_native_int():
  return _pycplex_platform.new_native_int()
new_native_int = _pycplex_platform.new_native_int

def delete_native_int(*args):
  return _pycplex_platform.delete_native_int(*args)
delete_native_int = _pycplex_platform.delete_native_int

def set_native_int(*args):
  return _pycplex_platform.set_native_int(*args)
set_native_int = _pycplex_platform.set_native_int

def get_native_int(*args):
  return _pycplex_platform.get_native_int(*args)
get_native_int = _pycplex_platform.get_native_int

def setterminate(*args):
  return _pycplex_platform.setterminate(*args)
setterminate = _pycplex_platform.setterminate

def set_status_checker(*args):
  return _pycplex_platform.set_status_checker(*args)
set_status_checker = _pycplex_platform.set_status_checker

def fast_getcallbackinfo(*args):
  return _pycplex_platform.fast_getcallbackinfo(*args)
fast_getcallbackinfo = _pycplex_platform.fast_getcallbackinfo

def cb_geterrorstring(*args):
  return _pycplex_platform.cb_geterrorstring(*args)
cb_geterrorstring = _pycplex_platform.cb_geterrorstring

def cb_getcolindex(*args):
  return _pycplex_platform.cb_getcolindex(*args)
cb_getcolindex = _pycplex_platform.cb_getcolindex

def cb_getrowindex(*args):
  return _pycplex_platform.cb_getrowindex(*args)
cb_getrowindex = _pycplex_platform.cb_getrowindex

def cb_getqconstrindex(*args):
  return _pycplex_platform.cb_getqconstrindex(*args)
cb_getqconstrindex = _pycplex_platform.cb_getqconstrindex

def cb_getsosindex(*args):
  return _pycplex_platform.cb_getsosindex(*args)
cb_getsosindex = _pycplex_platform.cb_getsosindex

def cb_getnumcols(*args):
  return _pycplex_platform.cb_getnumcols(*args)
cb_getnumcols = _pycplex_platform.cb_getnumcols

def cb_getnumrows(*args):
  return _pycplex_platform.cb_getnumrows(*args)
cb_getnumrows = _pycplex_platform.cb_getnumrows

def cb_getnumqconstrs(*args):
  return _pycplex_platform.cb_getnumqconstrs(*args)
cb_getnumqconstrs = _pycplex_platform.cb_getnumqconstrs

def cb_getnumsos(*args):
  return _pycplex_platform.cb_getnumsos(*args)
cb_getnumsos = _pycplex_platform.cb_getnumsos

def cb_gettime(*args):
  return _pycplex_platform.cb_gettime(*args)
cb_gettime = _pycplex_platform.cb_gettime

def cb_getdettime(*args):
  return _pycplex_platform.cb_getdettime(*args)
cb_getdettime = _pycplex_platform.cb_getdettime

def cb_getstat(*args):
  return _pycplex_platform.cb_getstat(*args)
cb_getstat = _pycplex_platform.cb_getstat

def cb_solninfo(*args):
  return _pycplex_platform.cb_solninfo(*args)
cb_solninfo = _pycplex_platform.cb_solninfo

def cb_primopt(*args):
  return _pycplex_platform.cb_primopt(*args)
cb_primopt = _pycplex_platform.cb_primopt

def cb_dualopt(*args):
  return _pycplex_platform.cb_dualopt(*args)
cb_dualopt = _pycplex_platform.cb_dualopt

def cb_hybbaropt(*args):
  return _pycplex_platform.cb_hybbaropt(*args)
cb_hybbaropt = _pycplex_platform.cb_hybbaropt

def cb_hybnetopt(*args):
  return _pycplex_platform.cb_hybnetopt(*args)
cb_hybnetopt = _pycplex_platform.cb_hybnetopt

def cb_copystart(*args):
  return _pycplex_platform.cb_copystart(*args)
cb_copystart = _pycplex_platform.cb_copystart

def cb_chgbds(*args):
  return _pycplex_platform.cb_chgbds(*args)
cb_chgbds = _pycplex_platform.cb_chgbds

def cb_slackfromx(*args):
  return _pycplex_platform.cb_slackfromx(*args)
cb_slackfromx = _pycplex_platform.cb_slackfromx

def cb_qconstrslackfromx(*args):
  return _pycplex_platform.cb_qconstrslackfromx(*args)
cb_qconstrslackfromx = _pycplex_platform.cb_qconstrslackfromx

def cb_crushx(*args):
  return _pycplex_platform.cb_crushx(*args)
cb_crushx = _pycplex_platform.cb_crushx

def cb_crushpi(*args):
  return _pycplex_platform.cb_crushpi(*args)
cb_crushpi = _pycplex_platform.cb_crushpi

def cb_getobj(*args):
  return _pycplex_platform.cb_getobj(*args)
cb_getobj = _pycplex_platform.cb_getobj

def cb_getprestat_c(*args):
  return _pycplex_platform.cb_getprestat_c(*args)
cb_getprestat_c = _pycplex_platform.cb_getprestat_c
class cb_struct(_object):
    __swig_setmethods__ = {}
    __setattr__ = lambda self, name, value: _swig_setattr(self, cb_struct, name, value)
    __swig_getmethods__ = {}
    __getattr__ = lambda self, name: _swig_getattr(self, cb_struct, name)
    __repr__ = _swig_repr
    __swig_setmethods__["env"] = _pycplex_platform.cb_struct_env_set
    __swig_getmethods__["env"] = _pycplex_platform.cb_struct_env_get
    if _newclass:env = _swig_property(_pycplex_platform.cb_struct_env_get, _pycplex_platform.cb_struct_env_set)
    __swig_setmethods__["cbdata"] = _pycplex_platform.cb_struct_cbdata_set
    __swig_getmethods__["cbdata"] = _pycplex_platform.cb_struct_cbdata_get
    if _newclass:cbdata = _swig_property(_pycplex_platform.cb_struct_cbdata_get, _pycplex_platform.cb_struct_cbdata_set)
    __swig_setmethods__["wherefrom"] = _pycplex_platform.cb_struct_wherefrom_set
    __swig_getmethods__["wherefrom"] = _pycplex_platform.cb_struct_wherefrom_get
    if _newclass:wherefrom = _swig_property(_pycplex_platform.cb_struct_wherefrom_get, _pycplex_platform.cb_struct_wherefrom_set)
    def __init__(self): 
        this = _pycplex_platform.new_cb_struct()
        try: self.this.append(this)
        except: self.this = this
    __swig_destroy__ = _pycplex_platform.delete_cb_struct
    __del__ = lambda self : None;
cb_struct_swigregister = _pycplex_platform.cb_struct_swigregister
cb_struct_swigregister(cb_struct)


def get_wherefrom(*args):
  return _pycplex_platform.get_wherefrom(*args)
get_wherefrom = _pycplex_platform.get_wherefrom

def delpydel(*args):
  return _pycplex_platform.delpydel(*args)
delpydel = _pycplex_platform.delpydel
CPX_CPLEXE_H = _pycplex_platform.CPX_CPLEXE_H
CPXE_H = _pycplex_platform.CPXE_H
BARE_H = _pycplex_platform.BARE_H
MIPE_H = _pycplex_platform.MIPE_H
CPXAUTOE_H = _pycplex_platform.CPXAUTOE_H
CPX_AUTOES_H = _pycplex_platform.CPX_AUTOES_H
CPX_AUTOEL_H = _pycplex_platform.CPX_AUTOEL_H
CPX_AUTOEX_H = _pycplex_platform.CPX_AUTOEX_H
class cpxpyiodevice(_object):
    __swig_setmethods__ = {}
    __setattr__ = lambda self, name, value: _swig_setattr(self, cpxpyiodevice, name, value)
    __swig_getmethods__ = {}
    __getattr__ = lambda self, name: _swig_getattr(self, cpxpyiodevice, name)
    __repr__ = _swig_repr
    __swig_setmethods__["dev"] = _pycplex_platform.cpxpyiodevice_dev_set
    __swig_getmethods__["dev"] = _pycplex_platform.cpxpyiodevice_dev_get
    if _newclass:dev = _swig_property(_pycplex_platform.cpxpyiodevice_dev_get, _pycplex_platform.cpxpyiodevice_dev_set)
    __swig_setmethods__["stream"] = _pycplex_platform.cpxpyiodevice_stream_set
    __swig_getmethods__["stream"] = _pycplex_platform.cpxpyiodevice_stream_get
    if _newclass:stream = _swig_property(_pycplex_platform.cpxpyiodevice_stream_get, _pycplex_platform.cpxpyiodevice_stream_set)
    def __init__(self): 
        this = _pycplex_platform.new_cpxpyiodevice()
        try: self.this.append(this)
        except: self.this = this
    __swig_destroy__ = _pycplex_platform.delete_cpxpyiodevice
    __del__ = lambda self : None;
cpxpyiodevice_swigregister = _pycplex_platform.cpxpyiodevice_swigregister
cpxpyiodevice_swigregister(cpxpyiodevice)


def init_callback_lock():
  return _pycplex_platform.init_callback_lock()
init_callback_lock = _pycplex_platform.init_callback_lock

def finit_callback_lock(*args):
  return _pycplex_platform.finit_callback_lock(*args)
finit_callback_lock = _pycplex_platform.finit_callback_lock

def CPXPyObject_AsInt(*args):
  return _pycplex_platform.CPXPyObject_AsInt(*args)
CPXPyObject_AsInt = _pycplex_platform.CPXPyObject_AsInt

def CPXPyObject_AsChar(*args):
  return _pycplex_platform.CPXPyObject_AsChar(*args)
CPXPyObject_AsChar = _pycplex_platform.CPXPyObject_AsChar

def CPXPyObject_AsCPXSIZE(*args):
  return _pycplex_platform.CPXPyObject_AsCPXSIZE(*args)
CPXPyObject_AsCPXSIZE = _pycplex_platform.CPXPyObject_AsCPXSIZE

def CPXPyObject_AsCPXLONG(*args):
  return _pycplex_platform.CPXPyObject_AsCPXLONG(*args)
CPXPyObject_AsCPXLONG = _pycplex_platform.CPXPyObject_AsCPXLONG

def CPXPyObject_AsCPXCNT(*args):
  return _pycplex_platform.CPXPyObject_AsCPXCNT(*args)
CPXPyObject_AsCPXCNT = _pycplex_platform.CPXPyObject_AsCPXCNT

def CPXPyObject_AsDouble(*args):
  return _pycplex_platform.CPXPyObject_AsDouble(*args)
CPXPyObject_AsDouble = _pycplex_platform.CPXPyObject_AsDouble

def CPXPyIODevInit(*args):
  return _pycplex_platform.CPXPyIODevInit(*args)
CPXPyIODevInit = _pycplex_platform.CPXPyIODevInit

def CPXXdualopt(*args):
  return _pycplex_platform.CPXXdualopt(*args)
CPXXdualopt = _pycplex_platform.CPXXdualopt

def CPXXembwrite(*args):
  return _pycplex_platform.CPXXembwrite(*args)
CPXXembwrite = _pycplex_platform.CPXXembwrite

def CPXXfeasoptext(*args):
  return _pycplex_platform.CPXXfeasoptext(*args)
CPXXfeasoptext = _pycplex_platform.CPXXfeasoptext

def CPXXhybnetopt(*args):
  return _pycplex_platform.CPXXhybnetopt(*args)
CPXXhybnetopt = _pycplex_platform.CPXXhybnetopt

def CPXXlpopt(*args):
  return _pycplex_platform.CPXXlpopt(*args)
CPXXlpopt = _pycplex_platform.CPXXlpopt

def CPXXpivotin(*args):
  return _pycplex_platform.CPXXpivotin(*args)
CPXXpivotin = _pycplex_platform.CPXXpivotin

def CPXXpivotout(*args):
  return _pycplex_platform.CPXXpivotout(*args)
CPXXpivotout = _pycplex_platform.CPXXpivotout

def CPXXpreslvwrite(*args):
  return _pycplex_platform.CPXXpreslvwrite(*args)
CPXXpreslvwrite = _pycplex_platform.CPXXpreslvwrite

def CPXXpresolve(*args):
  return _pycplex_platform.CPXXpresolve(*args)
CPXXpresolve = _pycplex_platform.CPXXpresolve

def CPXXprimopt(*args):
  return _pycplex_platform.CPXXprimopt(*args)
CPXXprimopt = _pycplex_platform.CPXXprimopt

def CPXXrefineconflictext(*args):
  return _pycplex_platform.CPXXrefineconflictext(*args)
CPXXrefineconflictext = _pycplex_platform.CPXXrefineconflictext

def CPXXsiftopt(*args):
  return _pycplex_platform.CPXXsiftopt(*args)
CPXXsiftopt = _pycplex_platform.CPXXsiftopt

def CPXXstrongbranch(*args):
  return _pycplex_platform.CPXXstrongbranch(*args)
CPXXstrongbranch = _pycplex_platform.CPXXstrongbranch

def CPXXbaropt(*args):
  return _pycplex_platform.CPXXbaropt(*args)
CPXXbaropt = _pycplex_platform.CPXXbaropt

def CPXXhybbaropt(*args):
  return _pycplex_platform.CPXXhybbaropt(*args)
CPXXhybbaropt = _pycplex_platform.CPXXhybbaropt

def CPXXqpindefcertificate(*args):
  return _pycplex_platform.CPXXqpindefcertificate(*args)
CPXXqpindefcertificate = _pycplex_platform.CPXXqpindefcertificate

def CPXXqpopt(*args):
  return _pycplex_platform.CPXXqpopt(*args)
CPXXqpopt = _pycplex_platform.CPXXqpopt

def CPXXmipopt(*args):
  return _pycplex_platform.CPXXmipopt(*args)
CPXXmipopt = _pycplex_platform.CPXXmipopt

def CPXXpopulate(*args):
  return _pycplex_platform.CPXXpopulate(*args)
CPXXpopulate = _pycplex_platform.CPXXpopulate

def CPXXrefinemipstartconflictext(*args):
  return _pycplex_platform.CPXXrefinemipstartconflictext(*args)
CPXXrefinemipstartconflictext = _pycplex_platform.CPXXrefinemipstartconflictext

def CPXXtuneparam(*args):
  return _pycplex_platform.CPXXtuneparam(*args)
CPXXtuneparam = _pycplex_platform.CPXXtuneparam

def CPXXtuneparamprobset(*args):
  return _pycplex_platform.CPXXtuneparamprobset(*args)
CPXXtuneparamprobset = _pycplex_platform.CPXXtuneparamprobset

def CPXErunseeds(*args):
  return _pycplex_platform.CPXErunseeds(*args)
CPXErunseeds = _pycplex_platform.CPXErunseeds

def CPXXwriteannotations(*args):
  return _pycplex_platform.CPXXwriteannotations(*args)
CPXXwriteannotations = _pycplex_platform.CPXXwriteannotations

def CPXXwritebendersannotation(*args):
  return _pycplex_platform.CPXXwritebendersannotation(*args)
CPXXwritebendersannotation = _pycplex_platform.CPXXwritebendersannotation

def CPXXclpwrite(*args):
  return _pycplex_platform.CPXXclpwrite(*args)
CPXXclpwrite = _pycplex_platform.CPXXclpwrite

def CPXXwriteprob(*args):
  return _pycplex_platform.CPXXwriteprob(*args)
CPXXwriteprob = _pycplex_platform.CPXXwriteprob

def CPXXmbasewrite(*args):
  return _pycplex_platform.CPXXmbasewrite(*args)
CPXXmbasewrite = _pycplex_platform.CPXXmbasewrite

def CPXXsolwrite(*args):
  return _pycplex_platform.CPXXsolwrite(*args)
CPXXsolwrite = _pycplex_platform.CPXXsolwrite

def CPXXsolwritesolnpool(*args):
  return _pycplex_platform.CPXXsolwritesolnpool(*args)
CPXXsolwritesolnpool = _pycplex_platform.CPXXsolwritesolnpool

def CPXXsolwritesolnpoolall(*args):
  return _pycplex_platform.CPXXsolwritesolnpoolall(*args)
CPXXsolwritesolnpoolall = _pycplex_platform.CPXXsolwritesolnpoolall

def CPXXdperwrite(*args):
  return _pycplex_platform.CPXXdperwrite(*args)
CPXXdperwrite = _pycplex_platform.CPXXdperwrite

def CPXXpperwrite(*args):
  return _pycplex_platform.CPXXpperwrite(*args)
CPXXpperwrite = _pycplex_platform.CPXXpperwrite

def CPXXdualwrite(*args):
  return _pycplex_platform.CPXXdualwrite(*args)
CPXXdualwrite = _pycplex_platform.CPXXdualwrite

def CPXXwriteparam(*args):
  return _pycplex_platform.CPXXwriteparam(*args)
CPXXwriteparam = _pycplex_platform.CPXXwriteparam

def CPXXordwrite(*args):
  return _pycplex_platform.CPXXordwrite(*args)
CPXXordwrite = _pycplex_platform.CPXXordwrite

def CPXXwritemipstarts(*args):
  return _pycplex_platform.CPXXwritemipstarts(*args)
CPXXwritemipstarts = _pycplex_platform.CPXXwritemipstarts

def CPXXfltwrite(*args):
  return _pycplex_platform.CPXXfltwrite(*args)
CPXXfltwrite = _pycplex_platform.CPXXfltwrite

def CPXXreadcopyannotations(*args):
  return _pycplex_platform.CPXXreadcopyannotations(*args)
CPXXreadcopyannotations = _pycplex_platform.CPXXreadcopyannotations

def CPXXreadcopyprob(*args):
  return _pycplex_platform.CPXXreadcopyprob(*args)
CPXXreadcopyprob = _pycplex_platform.CPXXreadcopyprob

def CPXXreadcopybase(*args):
  return _pycplex_platform.CPXXreadcopybase(*args)
CPXXreadcopybase = _pycplex_platform.CPXXreadcopybase

def CPXXreadcopysol(*args):
  return _pycplex_platform.CPXXreadcopysol(*args)
CPXXreadcopysol = _pycplex_platform.CPXXreadcopysol

def CPXXreadcopyparam(*args):
  return _pycplex_platform.CPXXreadcopyparam(*args)
CPXXreadcopyparam = _pycplex_platform.CPXXreadcopyparam

def CPXXreadcopyorder(*args):
  return _pycplex_platform.CPXXreadcopyorder(*args)
CPXXreadcopyorder = _pycplex_platform.CPXXreadcopyorder

def CPXXreadcopysolnpoolfilters(*args):
  return _pycplex_platform.CPXXreadcopysolnpoolfilters(*args)
CPXXreadcopysolnpoolfilters = _pycplex_platform.CPXXreadcopysolnpoolfilters

def CPXXreadcopymipstarts(*args):
  return _pycplex_platform.CPXXreadcopymipstarts(*args)
CPXXreadcopymipstarts = _pycplex_platform.CPXXreadcopymipstarts

def CPXXmultiobjopt(*args):
  return _pycplex_platform.CPXXmultiobjopt(*args)
CPXXmultiobjopt = _pycplex_platform.CPXXmultiobjopt

def pack_env_lp_ptr(*args):
  return _pycplex_platform.pack_env_lp_ptr(*args)
pack_env_lp_ptr = _pycplex_platform.pack_env_lp_ptr

def Pylolmat_to_CHBmat(*args):
  return _pycplex_platform.Pylolmat_to_CHBmat(*args)
Pylolmat_to_CHBmat = _pycplex_platform.Pylolmat_to_CHBmat

def free_CHBmat(*args):
  return _pycplex_platform.free_CHBmat(*args)
free_CHBmat = _pycplex_platform.free_CHBmat

def int_list_to_C_array(*args):
  return _pycplex_platform.int_list_to_C_array(*args)
int_list_to_C_array = _pycplex_platform.int_list_to_C_array

def double_list_to_C_array(*args):
  return _pycplex_platform.double_list_to_C_array(*args)
double_list_to_C_array = _pycplex_platform.double_list_to_C_array

def free_int_C_array(*args):
  return _pycplex_platform.free_int_C_array(*args)
free_int_C_array = _pycplex_platform.free_int_C_array

def free_double_C_array(*args):
  return _pycplex_platform.free_double_C_array(*args)
free_double_C_array = _pycplex_platform.free_double_C_array

def CPXXcreateprob(*args):
  return _pycplex_platform.CPXXcreateprob(*args)
CPXXcreateprob = _pycplex_platform.CPXXcreateprob

def CPXXcloneprob(*args):
  return _pycplex_platform.CPXXcloneprob(*args)
CPXXcloneprob = _pycplex_platform.CPXXcloneprob

def CPXXcopylpwnames(*args):
  return _pycplex_platform.CPXXcopylpwnames(*args)
CPXXcopylpwnames = _pycplex_platform.CPXXcopylpwnames

def CPXXcopyobjname(*args):
  return _pycplex_platform.CPXXcopyobjname(*args)
CPXXcopyobjname = _pycplex_platform.CPXXcopyobjname

def CPXXcopybase(*args):
  return _pycplex_platform.CPXXcopybase(*args)
CPXXcopybase = _pycplex_platform.CPXXcopybase

def CPXXcleanup(*args):
  return _pycplex_platform.CPXXcleanup(*args)
CPXXcleanup = _pycplex_platform.CPXXcleanup

def CPXXcopystart(*args):
  return _pycplex_platform.CPXXcopystart(*args)
CPXXcopystart = _pycplex_platform.CPXXcopystart

def CPXXfreeprob(*args):
  return _pycplex_platform.CPXXfreeprob(*args)
CPXXfreeprob = _pycplex_platform.CPXXfreeprob

def CPXXcopynettolp(*args):
  return _pycplex_platform.CPXXcopynettolp(*args)
CPXXcopynettolp = _pycplex_platform.CPXXcopynettolp

def CPXNETextract(*args):
  return _pycplex_platform.CPXNETextract(*args)
CPXNETextract = _pycplex_platform.CPXNETextract

def CPXXpivot(*args):
  return _pycplex_platform.CPXXpivot(*args)
CPXXpivot = _pycplex_platform.CPXXpivot

def CPXXsolninfo(*args):
  return _pycplex_platform.CPXXsolninfo(*args)
CPXXsolninfo = _pycplex_platform.CPXXsolninfo

def CPXXgetstat(*args):
  return _pycplex_platform.CPXXgetstat(*args)
CPXXgetstat = _pycplex_platform.CPXXgetstat

def CPXXgetstatstring(*args):
  return _pycplex_platform.CPXXgetstatstring(*args)
CPXXgetstatstring = _pycplex_platform.CPXXgetstatstring

def CPXXgetmethod(*args):
  return _pycplex_platform.CPXXgetmethod(*args)
CPXXgetmethod = _pycplex_platform.CPXXgetmethod

def CPXXgetobjval(*args):
  return _pycplex_platform.CPXXgetobjval(*args)
CPXXgetobjval = _pycplex_platform.CPXXgetobjval

def CPXXgetx(*args):
  return _pycplex_platform.CPXXgetx(*args)
CPXXgetx = _pycplex_platform.CPXXgetx

def CPXXgetax(*args):
  return _pycplex_platform.CPXXgetax(*args)
CPXXgetax = _pycplex_platform.CPXXgetax

def CPXXgetpi(*args):
  return _pycplex_platform.CPXXgetpi(*args)
CPXXgetpi = _pycplex_platform.CPXXgetpi

def CPXXgetslack(*args):
  return _pycplex_platform.CPXXgetslack(*args)
CPXXgetslack = _pycplex_platform.CPXXgetslack

def CPXXgetrowinfeas(*args):
  return _pycplex_platform.CPXXgetrowinfeas(*args)
CPXXgetrowinfeas = _pycplex_platform.CPXXgetrowinfeas

def CPXXgetcolinfeas(*args):
  return _pycplex_platform.CPXXgetcolinfeas(*args)
CPXXgetcolinfeas = _pycplex_platform.CPXXgetcolinfeas

def CPXXgetdj(*args):
  return _pycplex_platform.CPXXgetdj(*args)
CPXXgetdj = _pycplex_platform.CPXXgetdj

def CPXXgetgrad(*args):
  return _pycplex_platform.CPXXgetgrad(*args)
CPXXgetgrad = _pycplex_platform.CPXXgetgrad

def CPXXgetijdiv(*args):
  return _pycplex_platform.CPXXgetijdiv(*args)
CPXXgetijdiv = _pycplex_platform.CPXXgetijdiv

def CPXXgetbase(*args):
  return _pycplex_platform.CPXXgetbase(*args)
CPXXgetbase = _pycplex_platform.CPXXgetbase

def CPXXgetitcnt(*args):
  return _pycplex_platform.CPXXgetitcnt(*args)
CPXXgetitcnt = _pycplex_platform.CPXXgetitcnt

def CPXXgetphase1cnt(*args):
  return _pycplex_platform.CPXXgetphase1cnt(*args)
CPXXgetphase1cnt = _pycplex_platform.CPXXgetphase1cnt

def CPXXgetsiftitcnt(*args):
  return _pycplex_platform.CPXXgetsiftitcnt(*args)
CPXXgetsiftitcnt = _pycplex_platform.CPXXgetsiftitcnt

def CPXXgetsiftphase1cnt(*args):
  return _pycplex_platform.CPXXgetsiftphase1cnt(*args)
CPXXgetsiftphase1cnt = _pycplex_platform.CPXXgetsiftphase1cnt

def CPXXgetbaritcnt(*args):
  return _pycplex_platform.CPXXgetbaritcnt(*args)
CPXXgetbaritcnt = _pycplex_platform.CPXXgetbaritcnt

def CPXXgetcrossppushcnt(*args):
  return _pycplex_platform.CPXXgetcrossppushcnt(*args)
CPXXgetcrossppushcnt = _pycplex_platform.CPXXgetcrossppushcnt

def CPXXgetcrosspexchcnt(*args):
  return _pycplex_platform.CPXXgetcrosspexchcnt(*args)
CPXXgetcrosspexchcnt = _pycplex_platform.CPXXgetcrosspexchcnt

def CPXXgetcrossdpushcnt(*args):
  return _pycplex_platform.CPXXgetcrossdpushcnt(*args)
CPXXgetcrossdpushcnt = _pycplex_platform.CPXXgetcrossdpushcnt

def CPXXgetcrossdexchcnt(*args):
  return _pycplex_platform.CPXXgetcrossdexchcnt(*args)
CPXXgetcrossdexchcnt = _pycplex_platform.CPXXgetcrossdexchcnt

def CPXXgetpsbcnt(*args):
  return _pycplex_platform.CPXXgetpsbcnt(*args)
CPXXgetpsbcnt = _pycplex_platform.CPXXgetpsbcnt

def CPXXgetdsbcnt(*args):
  return _pycplex_platform.CPXXgetdsbcnt(*args)
CPXXgetdsbcnt = _pycplex_platform.CPXXgetdsbcnt

def CPXXgetdblquality(*args):
  return _pycplex_platform.CPXXgetdblquality(*args)
CPXXgetdblquality = _pycplex_platform.CPXXgetdblquality

def CPXXgetsolnpooldblquality(*args):
  return _pycplex_platform.CPXXgetsolnpooldblquality(*args)
CPXXgetsolnpooldblquality = _pycplex_platform.CPXXgetsolnpooldblquality

def CPXXgetintquality(*args):
  return _pycplex_platform.CPXXgetintquality(*args)
CPXXgetintquality = _pycplex_platform.CPXXgetintquality

def CPXXgetsolnpoolintquality(*args):
  return _pycplex_platform.CPXXgetsolnpoolintquality(*args)
CPXXgetsolnpoolintquality = _pycplex_platform.CPXXgetsolnpoolintquality

def CPXXrhssa(*args):
  return _pycplex_platform.CPXXrhssa(*args)
CPXXrhssa = _pycplex_platform.CPXXrhssa

def CPXXboundsa(*args):
  return _pycplex_platform.CPXXboundsa(*args)
CPXXboundsa = _pycplex_platform.CPXXboundsa

def CPXXobjsa(*args):
  return _pycplex_platform.CPXXobjsa(*args)
CPXXobjsa = _pycplex_platform.CPXXobjsa

def CPXXgetconflictext(*args):
  return _pycplex_platform.CPXXgetconflictext(*args)
CPXXgetconflictext = _pycplex_platform.CPXXgetconflictext

def CPXXnewrows(*args):
  return _pycplex_platform.CPXXnewrows(*args)
CPXXnewrows = _pycplex_platform.CPXXnewrows

def CPXXaddrows(*args):
  return _pycplex_platform.CPXXaddrows(*args)
CPXXaddrows = _pycplex_platform.CPXXaddrows

def CPXXnewcols(*args):
  return _pycplex_platform.CPXXnewcols(*args)
CPXXnewcols = _pycplex_platform.CPXXnewcols

def CPXXaddcols(*args):
  return _pycplex_platform.CPXXaddcols(*args)
CPXXaddcols = _pycplex_platform.CPXXaddcols

def CPXXdelrows(*args):
  return _pycplex_platform.CPXXdelrows(*args)
CPXXdelrows = _pycplex_platform.CPXXdelrows

def CPXXdelcols(*args):
  return _pycplex_platform.CPXXdelcols(*args)
CPXXdelcols = _pycplex_platform.CPXXdelcols

def CPXXchgrowname(*args):
  return _pycplex_platform.CPXXchgrowname(*args)
CPXXchgrowname = _pycplex_platform.CPXXchgrowname

def CPXXchgcolname(*args):
  return _pycplex_platform.CPXXchgcolname(*args)
CPXXchgcolname = _pycplex_platform.CPXXchgcolname

def CPXXdelnames(*args):
  return _pycplex_platform.CPXXdelnames(*args)
CPXXdelnames = _pycplex_platform.CPXXdelnames

def CPXXchgprobname(*args):
  return _pycplex_platform.CPXXchgprobname(*args)
CPXXchgprobname = _pycplex_platform.CPXXchgprobname

def CPXXchgcoeflist(*args):
  return _pycplex_platform.CPXXchgcoeflist(*args)
CPXXchgcoeflist = _pycplex_platform.CPXXchgcoeflist

def CPXXchgbds(*args):
  return _pycplex_platform.CPXXchgbds(*args)
CPXXchgbds = _pycplex_platform.CPXXchgbds

def CPXXchgobj(*args):
  return _pycplex_platform.CPXXchgobj(*args)
CPXXchgobj = _pycplex_platform.CPXXchgobj

def CPXXchgrhs(*args):
  return _pycplex_platform.CPXXchgrhs(*args)
CPXXchgrhs = _pycplex_platform.CPXXchgrhs

def CPXXchgrngval(*args):
  return _pycplex_platform.CPXXchgrngval(*args)
CPXXchgrngval = _pycplex_platform.CPXXchgrngval

def CPXXchgsense(*args):
  return _pycplex_platform.CPXXchgsense(*args)
CPXXchgsense = _pycplex_platform.CPXXchgsense

def CPXXchgobjsen(*args):
  return _pycplex_platform.CPXXchgobjsen(*args)
CPXXchgobjsen = _pycplex_platform.CPXXchgobjsen

def CPXXchgprobtype(*args):
  return _pycplex_platform.CPXXchgprobtype(*args)
CPXXchgprobtype = _pycplex_platform.CPXXchgprobtype

def CPXXchgprobtypesolnpool(*args):
  return _pycplex_platform.CPXXchgprobtypesolnpool(*args)
CPXXchgprobtypesolnpool = _pycplex_platform.CPXXchgprobtypesolnpool

def CPXXcompletelp(*args):
  return _pycplex_platform.CPXXcompletelp(*args)
CPXXcompletelp = _pycplex_platform.CPXXcompletelp

def CPXXpreaddrows(*args):
  return _pycplex_platform.CPXXpreaddrows(*args)
CPXXpreaddrows = _pycplex_platform.CPXXpreaddrows

def CPXXprechgobj(*args):
  return _pycplex_platform.CPXXprechgobj(*args)
CPXXprechgobj = _pycplex_platform.CPXXprechgobj

def CPXXgetnumcols(*args):
  return _pycplex_platform.CPXXgetnumcols(*args)
CPXXgetnumcols = _pycplex_platform.CPXXgetnumcols

def CPXXgetnumrows(*args):
  return _pycplex_platform.CPXXgetnumrows(*args)
CPXXgetnumrows = _pycplex_platform.CPXXgetnumrows

def CPXXgetnumnz(*args):
  return _pycplex_platform.CPXXgetnumnz(*args)
CPXXgetnumnz = _pycplex_platform.CPXXgetnumnz

def CPXXgetobjsen(*args):
  return _pycplex_platform.CPXXgetobjsen(*args)
CPXXgetobjsen = _pycplex_platform.CPXXgetobjsen

def CPXXgetobj(*args):
  return _pycplex_platform.CPXXgetobj(*args)
CPXXgetobj = _pycplex_platform.CPXXgetobj

def CPXXgetrhs(*args):
  return _pycplex_platform.CPXXgetrhs(*args)
CPXXgetrhs = _pycplex_platform.CPXXgetrhs

def CPXXgetsense(*args):
  return _pycplex_platform.CPXXgetsense(*args)
CPXXgetsense = _pycplex_platform.CPXXgetsense

def CPXXgetcols(*args):
  return _pycplex_platform.CPXXgetcols(*args)
CPXXgetcols = _pycplex_platform.CPXXgetcols

def CPXXgetrows(*args):
  return _pycplex_platform.CPXXgetrows(*args)
CPXXgetrows = _pycplex_platform.CPXXgetrows

def CPXXgetlb(*args):
  return _pycplex_platform.CPXXgetlb(*args)
CPXXgetlb = _pycplex_platform.CPXXgetlb

def CPXXgetub(*args):
  return _pycplex_platform.CPXXgetub(*args)
CPXXgetub = _pycplex_platform.CPXXgetub

def CPXXgetrngval(*args):
  return _pycplex_platform.CPXXgetrngval(*args)
CPXXgetrngval = _pycplex_platform.CPXXgetrngval

def CPXXgetprobname(*args):
  return _pycplex_platform.CPXXgetprobname(*args)
CPXXgetprobname = _pycplex_platform.CPXXgetprobname

def CPXXgetobjname(*args):
  return _pycplex_platform.CPXXgetobjname(*args)
CPXXgetobjname = _pycplex_platform.CPXXgetobjname

def CPXXgetcolname(*args):
  return _pycplex_platform.CPXXgetcolname(*args)
CPXXgetcolname = _pycplex_platform.CPXXgetcolname

def CPXXgetrowname(*args):
  return _pycplex_platform.CPXXgetrowname(*args)
CPXXgetrowname = _pycplex_platform.CPXXgetrowname

def CPXXgetcoef(*args):
  return _pycplex_platform.CPXXgetcoef(*args)
CPXXgetcoef = _pycplex_platform.CPXXgetcoef

def CPXXgetrowindex(*args):
  return _pycplex_platform.CPXXgetrowindex(*args)
CPXXgetrowindex = _pycplex_platform.CPXXgetrowindex

def CPXXgetcolindex(*args):
  return _pycplex_platform.CPXXgetcolindex(*args)
CPXXgetcolindex = _pycplex_platform.CPXXgetcolindex

def CPXXgetprobtype(*args):
  return _pycplex_platform.CPXXgetprobtype(*args)
CPXXgetprobtype = _pycplex_platform.CPXXgetprobtype

def CPXXsetintparam(*args):
  return _pycplex_platform.CPXXsetintparam(*args)
CPXXsetintparam = _pycplex_platform.CPXXsetintparam

def CPXXsetlongparam(*args):
  return _pycplex_platform.CPXXsetlongparam(*args)
CPXXsetlongparam = _pycplex_platform.CPXXsetlongparam

def CPXXsetdblparam(*args):
  return _pycplex_platform.CPXXsetdblparam(*args)
CPXXsetdblparam = _pycplex_platform.CPXXsetdblparam

def CPXXsetstrparam(*args):
  return _pycplex_platform.CPXXsetstrparam(*args)
CPXXsetstrparam = _pycplex_platform.CPXXsetstrparam

def CPXXgetintparam(*args):
  return _pycplex_platform.CPXXgetintparam(*args)
CPXXgetintparam = _pycplex_platform.CPXXgetintparam

def CPXXgetlongparam(*args):
  return _pycplex_platform.CPXXgetlongparam(*args)
CPXXgetlongparam = _pycplex_platform.CPXXgetlongparam

def CPXXgetdblparam(*args):
  return _pycplex_platform.CPXXgetdblparam(*args)
CPXXgetdblparam = _pycplex_platform.CPXXgetdblparam

def CPXXgetstrparam(*args):
  return _pycplex_platform.CPXXgetstrparam(*args)
CPXXgetstrparam = _pycplex_platform.CPXXgetstrparam

def CPXXinfointparam(*args):
  return _pycplex_platform.CPXXinfointparam(*args)
CPXXinfointparam = _pycplex_platform.CPXXinfointparam

def CPXXinfolongparam(*args):
  return _pycplex_platform.CPXXinfolongparam(*args)
CPXXinfolongparam = _pycplex_platform.CPXXinfolongparam

def CPXXinfodblparam(*args):
  return _pycplex_platform.CPXXinfodblparam(*args)
CPXXinfodblparam = _pycplex_platform.CPXXinfodblparam

def CPXXinfostrparam(*args):
  return _pycplex_platform.CPXXinfostrparam(*args)
CPXXinfostrparam = _pycplex_platform.CPXXinfostrparam

def CPXXgetparamtype(*args):
  return _pycplex_platform.CPXXgetparamtype(*args)
CPXXgetparamtype = _pycplex_platform.CPXXgetparamtype

def CPXXEfixparam(*args):
  return _pycplex_platform.CPXXEfixparam(*args)
CPXXEfixparam = _pycplex_platform.CPXXEfixparam

def CPXXversion(*args):
  return _pycplex_platform.CPXXversion(*args)
CPXXversion = _pycplex_platform.CPXXversion

def CPXXversionnumber(*args):
  return _pycplex_platform.CPXXversionnumber(*args)
CPXXversionnumber = _pycplex_platform.CPXXversionnumber

def CPXXopenCPLEX(*args):
  return _pycplex_platform.CPXXopenCPLEX(*args)
CPXXopenCPLEX = _pycplex_platform.CPXXopenCPLEX

def CPXXcloseCPLEX(*args):
  return _pycplex_platform.CPXXcloseCPLEX(*args)
CPXXcloseCPLEX = _pycplex_platform.CPXXcloseCPLEX

def CPXXgetchannels(*args):
  return _pycplex_platform.CPXXgetchannels(*args)
CPXXgetchannels = _pycplex_platform.CPXXgetchannels

def CPXXaddfuncdest(*args):
  return _pycplex_platform.CPXXaddfuncdest(*args)
CPXXaddfuncdest = _pycplex_platform.CPXXaddfuncdest

def CPXXdelfuncdest(*args):
  return _pycplex_platform.CPXXdelfuncdest(*args)
CPXXdelfuncdest = _pycplex_platform.CPXXdelfuncdest

def CPXXgeterrorstring(*args):
  return _pycplex_platform.CPXXgeterrorstring(*args)
CPXXgeterrorstring = _pycplex_platform.CPXXgeterrorstring

def CPXXsetlpcallbackfunc(*args):
  return _pycplex_platform.CPXXsetlpcallbackfunc(*args)
CPXXsetlpcallbackfunc = _pycplex_platform.CPXXsetlpcallbackfunc

def CPXXsetnetcallbackfunc(*args):
  return _pycplex_platform.CPXXsetnetcallbackfunc(*args)
CPXXsetnetcallbackfunc = _pycplex_platform.CPXXsetnetcallbackfunc

def CPXXsettuningcallbackfunc(*args):
  return _pycplex_platform.CPXXsettuningcallbackfunc(*args)
CPXXsettuningcallbackfunc = _pycplex_platform.CPXXsettuningcallbackfunc

def CPXXsetterminate(*args):
  return _pycplex_platform.CPXXsetterminate(*args)
CPXXsetterminate = _pycplex_platform.CPXXsetterminate

def CPXXgetbhead(*args):
  return _pycplex_platform.CPXXgetbhead(*args)
CPXXgetbhead = _pycplex_platform.CPXXgetbhead

def CPXXbinvcol(*args):
  return _pycplex_platform.CPXXbinvcol(*args)
CPXXbinvcol = _pycplex_platform.CPXXbinvcol

def CPXXbinvrow(*args):
  return _pycplex_platform.CPXXbinvrow(*args)
CPXXbinvrow = _pycplex_platform.CPXXbinvrow

def CPXXbinvacol(*args):
  return _pycplex_platform.CPXXbinvacol(*args)
CPXXbinvacol = _pycplex_platform.CPXXbinvacol

def CPXXbinvarow(*args):
  return _pycplex_platform.CPXXbinvarow(*args)
CPXXbinvarow = _pycplex_platform.CPXXbinvarow

def CPXXftran(*args):
  return _pycplex_platform.CPXXftran(*args)
CPXXftran = _pycplex_platform.CPXXftran

def CPXXbtran(*args):
  return _pycplex_platform.CPXXbtran(*args)
CPXXbtran = _pycplex_platform.CPXXbtran

def CPXXgetijrow(*args):
  return _pycplex_platform.CPXXgetijrow(*args)
CPXXgetijrow = _pycplex_platform.CPXXgetijrow

def CPXXgetray(*args):
  return _pycplex_platform.CPXXgetray(*args)
CPXXgetray = _pycplex_platform.CPXXgetray

def CPXXmdleave(*args):
  return _pycplex_platform.CPXXmdleave(*args)
CPXXmdleave = _pycplex_platform.CPXXmdleave

def CPXXdualfarkas(*args):
  return _pycplex_platform.CPXXdualfarkas(*args)
CPXXdualfarkas = _pycplex_platform.CPXXdualfarkas

def CPXXchgobjoffset(*args):
  return _pycplex_platform.CPXXchgobjoffset(*args)
CPXXchgobjoffset = _pycplex_platform.CPXXchgobjoffset

def CPXXgetobjoffset(*args):
  return _pycplex_platform.CPXXgetobjoffset(*args)
CPXXgetobjoffset = _pycplex_platform.CPXXgetobjoffset

def CPXXgetbasednorms(*args):
  return _pycplex_platform.CPXXgetbasednorms(*args)
CPXXgetbasednorms = _pycplex_platform.CPXXgetbasednorms

def CPXXgetdnorms(*args):
  return _pycplex_platform.CPXXgetdnorms(*args)
CPXXgetdnorms = _pycplex_platform.CPXXgetdnorms

def CPXXgetpnorms(*args):
  return _pycplex_platform.CPXXgetpnorms(*args)
CPXXgetpnorms = _pycplex_platform.CPXXgetpnorms

def CPXXtightenbds(*args):
  return _pycplex_platform.CPXXtightenbds(*args)
CPXXtightenbds = _pycplex_platform.CPXXtightenbds

def CPXXbasicpresolve(*args):
  return _pycplex_platform.CPXXbasicpresolve(*args)
CPXXbasicpresolve = _pycplex_platform.CPXXbasicpresolve

def CPXXslackfromx(*args):
  return _pycplex_platform.CPXXslackfromx(*args)
CPXXslackfromx = _pycplex_platform.CPXXslackfromx

def CPXXdjfrompi(*args):
  return _pycplex_platform.CPXXdjfrompi(*args)
CPXXdjfrompi = _pycplex_platform.CPXXdjfrompi

def CPXXqpdjfrompi(*args):
  return _pycplex_platform.CPXXqpdjfrompi(*args)
CPXXqpdjfrompi = _pycplex_platform.CPXXqpdjfrompi

def CPXXfreepresolve(*args):
  return _pycplex_platform.CPXXfreepresolve(*args)
CPXXfreepresolve = _pycplex_platform.CPXXfreepresolve

def CPXXgetredlp(*args):
  return _pycplex_platform.CPXXgetredlp(*args)
CPXXgetredlp = _pycplex_platform.CPXXgetredlp

def CPXXcrushx(*args):
  return _pycplex_platform.CPXXcrushx(*args)
CPXXcrushx = _pycplex_platform.CPXXcrushx

def CPXXuncrushx(*args):
  return _pycplex_platform.CPXXuncrushx(*args)
CPXXuncrushx = _pycplex_platform.CPXXuncrushx

def CPXXcrushpi(*args):
  return _pycplex_platform.CPXXcrushpi(*args)
CPXXcrushpi = _pycplex_platform.CPXXcrushpi

def CPXXuncrushpi(*args):
  return _pycplex_platform.CPXXuncrushpi(*args)
CPXXuncrushpi = _pycplex_platform.CPXXuncrushpi

def CPXXcrushform(*args):
  return _pycplex_platform.CPXXcrushform(*args)
CPXXcrushform = _pycplex_platform.CPXXcrushform

def CPXXuncrushform(*args):
  return _pycplex_platform.CPXXuncrushform(*args)
CPXXuncrushform = _pycplex_platform.CPXXuncrushform

def CPXXgetprestat(*args):
  return _pycplex_platform.CPXXgetprestat(*args)
CPXXgetprestat = _pycplex_platform.CPXXgetprestat

def CPXXcopyprotected(*args):
  return _pycplex_platform.CPXXcopyprotected(*args)
CPXXcopyprotected = _pycplex_platform.CPXXcopyprotected

def CPXXgetprotected(*args):
  return _pycplex_platform.CPXXgetprotected(*args)
CPXXgetprotected = _pycplex_platform.CPXXgetprotected

def CPXXgettime(*args):
  return _pycplex_platform.CPXXgettime(*args)
CPXXgettime = _pycplex_platform.CPXXgettime

def CPXXgetdettime(*args):
  return _pycplex_platform.CPXXgetdettime(*args)
CPXXgetdettime = _pycplex_platform.CPXXgetdettime

def CPXXcopyorder(*args):
  return _pycplex_platform.CPXXcopyorder(*args)
CPXXcopyorder = _pycplex_platform.CPXXcopyorder

def CPXXchgmipstarts(*args):
  return _pycplex_platform.CPXXchgmipstarts(*args)
CPXXchgmipstarts = _pycplex_platform.CPXXchgmipstarts

def CPXXaddmipstarts(*args):
  return _pycplex_platform.CPXXaddmipstarts(*args)
CPXXaddmipstarts = _pycplex_platform.CPXXaddmipstarts

def CPXXdelmipstarts(*args):
  return _pycplex_platform.CPXXdelmipstarts(*args)
CPXXdelmipstarts = _pycplex_platform.CPXXdelmipstarts

def CPXXdistmipopt(*args):
  return _pycplex_platform.CPXXdistmipopt(*args)
CPXXdistmipopt = _pycplex_platform.CPXXdistmipopt

def CPXXcopyvmconfig(*args):
  return _pycplex_platform.CPXXcopyvmconfig(*args)
CPXXcopyvmconfig = _pycplex_platform.CPXXcopyvmconfig

def CPXXreadcopyvmconfig(*args):
  return _pycplex_platform.CPXXreadcopyvmconfig(*args)
CPXXreadcopyvmconfig = _pycplex_platform.CPXXreadcopyvmconfig

def CPXXdelvmconfig(*args):
  return _pycplex_platform.CPXXdelvmconfig(*args)
CPXXdelvmconfig = _pycplex_platform.CPXXdelvmconfig

def CPXEhasvmconfig(*args):
  return _pycplex_platform.CPXEhasvmconfig(*args)
CPXEhasvmconfig = _pycplex_platform.CPXEhasvmconfig

def CPXXgetmipitcnt(*args):
  return _pycplex_platform.CPXXgetmipitcnt(*args)
CPXXgetmipitcnt = _pycplex_platform.CPXXgetmipitcnt

def CPXXgetbestobjval(*args):
  return _pycplex_platform.CPXXgetbestobjval(*args)
CPXXgetbestobjval = _pycplex_platform.CPXXgetbestobjval

def CPXXgetmiprelgap(*args):
  return _pycplex_platform.CPXXgetmiprelgap(*args)
CPXXgetmiprelgap = _pycplex_platform.CPXXgetmiprelgap

def CPXXgetcutoff(*args):
  return _pycplex_platform.CPXXgetcutoff(*args)
CPXXgetcutoff = _pycplex_platform.CPXXgetcutoff

def CPXXgetnodecnt(*args):
  return _pycplex_platform.CPXXgetnodecnt(*args)
CPXXgetnodecnt = _pycplex_platform.CPXXgetnodecnt

def CPXXgetnodeleftcnt(*args):
  return _pycplex_platform.CPXXgetnodeleftcnt(*args)
CPXXgetnodeleftcnt = _pycplex_platform.CPXXgetnodeleftcnt

def CPXXgetnodeint(*args):
  return _pycplex_platform.CPXXgetnodeint(*args)
CPXXgetnodeint = _pycplex_platform.CPXXgetnodeint

def CPXXgetnumcuts(*args):
  return _pycplex_platform.CPXXgetnumcuts(*args)
CPXXgetnumcuts = _pycplex_platform.CPXXgetnumcuts

def CPXXgetnummipstarts(*args):
  return _pycplex_platform.CPXXgetnummipstarts(*args)
CPXXgetnummipstarts = _pycplex_platform.CPXXgetnummipstarts

def CPXXgetmipstarts(*args):
  return _pycplex_platform.CPXXgetmipstarts(*args)
CPXXgetmipstarts = _pycplex_platform.CPXXgetmipstarts

def CPXXgetmipstartname(*args):
  return _pycplex_platform.CPXXgetmipstartname(*args)
CPXXgetmipstartname = _pycplex_platform.CPXXgetmipstartname

def CPXXgetmipstartindex(*args):
  return _pycplex_platform.CPXXgetmipstartindex(*args)
CPXXgetmipstartindex = _pycplex_platform.CPXXgetmipstartindex

def CPXXgetsubstat(*args):
  return _pycplex_platform.CPXXgetsubstat(*args)
CPXXgetsubstat = _pycplex_platform.CPXXgetsubstat

def CPXXchgctype(*args):
  return _pycplex_platform.CPXXchgctype(*args)
CPXXchgctype = _pycplex_platform.CPXXchgctype

def CPXXaddsos(*args):
  return _pycplex_platform.CPXXaddsos(*args)
CPXXaddsos = _pycplex_platform.CPXXaddsos

def CPXXdelsos(*args):
  return _pycplex_platform.CPXXdelsos(*args)
CPXXdelsos = _pycplex_platform.CPXXdelsos

def CPXXgetctype(*args):
  return _pycplex_platform.CPXXgetctype(*args)
CPXXgetctype = _pycplex_platform.CPXXgetctype

def CPXXgetnumsos(*args):
  return _pycplex_platform.CPXXgetnumsos(*args)
CPXXgetnumsos = _pycplex_platform.CPXXgetnumsos

def CPXXgetsos(*args):
  return _pycplex_platform.CPXXgetsos(*args)
CPXXgetsos = _pycplex_platform.CPXXgetsos

def CPXXgetsosname(*args):
  return _pycplex_platform.CPXXgetsosname(*args)
CPXXgetsosname = _pycplex_platform.CPXXgetsosname

def CPXXgetsosindex(*args):
  return _pycplex_platform.CPXXgetsosindex(*args)
CPXXgetsosindex = _pycplex_platform.CPXXgetsosindex

def CPXXgetsosinfeas(*args):
  return _pycplex_platform.CPXXgetsosinfeas(*args)
CPXXgetsosinfeas = _pycplex_platform.CPXXgetsosinfeas

def CPXXaddindconstraints(*args):
  return _pycplex_platform.CPXXaddindconstraints(*args)
CPXXaddindconstraints = _pycplex_platform.CPXXaddindconstraints

def CPXXgetindconstraints(*args):
  return _pycplex_platform.CPXXgetindconstraints(*args)
CPXXgetindconstraints = _pycplex_platform.CPXXgetindconstraints

def CPXXgetnumindconstrs(*args):
  return _pycplex_platform.CPXXgetnumindconstrs(*args)
CPXXgetnumindconstrs = _pycplex_platform.CPXXgetnumindconstrs

def CPXXgetindconstrindex(*args):
  return _pycplex_platform.CPXXgetindconstrindex(*args)
CPXXgetindconstrindex = _pycplex_platform.CPXXgetindconstrindex

def CPXXgetindconstrname(*args):
  return _pycplex_platform.CPXXgetindconstrname(*args)
CPXXgetindconstrname = _pycplex_platform.CPXXgetindconstrname

def CPXXgetindconstrslack(*args):
  return _pycplex_platform.CPXXgetindconstrslack(*args)
CPXXgetindconstrslack = _pycplex_platform.CPXXgetindconstrslack

def CPXXindconstrslackfromx(*args):
  return _pycplex_platform.CPXXindconstrslackfromx(*args)
CPXXindconstrslackfromx = _pycplex_platform.CPXXindconstrslackfromx

def CPXXgetindconstrinfeas(*args):
  return _pycplex_platform.CPXXgetindconstrinfeas(*args)
CPXXgetindconstrinfeas = _pycplex_platform.CPXXgetindconstrinfeas

def CPXXdelindconstrs(*args):
  return _pycplex_platform.CPXXdelindconstrs(*args)
CPXXdelindconstrs = _pycplex_platform.CPXXdelindconstrs

def CPXXgetnumint(*args):
  return _pycplex_platform.CPXXgetnumint(*args)
CPXXgetnumint = _pycplex_platform.CPXXgetnumint

def CPXXgetnumbin(*args):
  return _pycplex_platform.CPXXgetnumbin(*args)
CPXXgetnumbin = _pycplex_platform.CPXXgetnumbin

def CPXXgetnumsemicont(*args):
  return _pycplex_platform.CPXXgetnumsemicont(*args)
CPXXgetnumsemicont = _pycplex_platform.CPXXgetnumsemicont

def CPXXgetnumsemiint(*args):
  return _pycplex_platform.CPXXgetnumsemiint(*args)
CPXXgetnumsemiint = _pycplex_platform.CPXXgetnumsemiint

def CPXXgetorder(*args):
  return _pycplex_platform.CPXXgetorder(*args)
CPXXgetorder = _pycplex_platform.CPXXgetorder

def CPXXgetsolnpoolnumfilters(*args):
  return _pycplex_platform.CPXXgetsolnpoolnumfilters(*args)
CPXXgetsolnpoolnumfilters = _pycplex_platform.CPXXgetsolnpoolnumfilters

def CPXXaddsolnpooldivfilter(*args):
  return _pycplex_platform.CPXXaddsolnpooldivfilter(*args)
CPXXaddsolnpooldivfilter = _pycplex_platform.CPXXaddsolnpooldivfilter

def CPXXaddsolnpoolrngfilter(*args):
  return _pycplex_platform.CPXXaddsolnpoolrngfilter(*args)
CPXXaddsolnpoolrngfilter = _pycplex_platform.CPXXaddsolnpoolrngfilter

def CPXXgetsolnpoolfiltertype(*args):
  return _pycplex_platform.CPXXgetsolnpoolfiltertype(*args)
CPXXgetsolnpoolfiltertype = _pycplex_platform.CPXXgetsolnpoolfiltertype

def CPXXgetsolnpooldivfilter(*args):
  return _pycplex_platform.CPXXgetsolnpooldivfilter(*args)
CPXXgetsolnpooldivfilter = _pycplex_platform.CPXXgetsolnpooldivfilter

def CPXXgetsolnpoolrngfilter(*args):
  return _pycplex_platform.CPXXgetsolnpoolrngfilter(*args)
CPXXgetsolnpoolrngfilter = _pycplex_platform.CPXXgetsolnpoolrngfilter

def CPXXgetsolnpoolfiltername(*args):
  return _pycplex_platform.CPXXgetsolnpoolfiltername(*args)
CPXXgetsolnpoolfiltername = _pycplex_platform.CPXXgetsolnpoolfiltername

def CPXXgetsolnpoolfilterindex(*args):
  return _pycplex_platform.CPXXgetsolnpoolfilterindex(*args)
CPXXgetsolnpoolfilterindex = _pycplex_platform.CPXXgetsolnpoolfilterindex

def CPXXdelsolnpoolfilters(*args):
  return _pycplex_platform.CPXXdelsolnpoolfilters(*args)
CPXXdelsolnpoolfilters = _pycplex_platform.CPXXdelsolnpoolfilters

def CPXXgetsolnpoolnumsolns(*args):
  return _pycplex_platform.CPXXgetsolnpoolnumsolns(*args)
CPXXgetsolnpoolnumsolns = _pycplex_platform.CPXXgetsolnpoolnumsolns

def CPXXgetsolnpoolnumreplaced(*args):
  return _pycplex_platform.CPXXgetsolnpoolnumreplaced(*args)
CPXXgetsolnpoolnumreplaced = _pycplex_platform.CPXXgetsolnpoolnumreplaced

def CPXXgetsolnpoolmeanobjval(*args):
  return _pycplex_platform.CPXXgetsolnpoolmeanobjval(*args)
CPXXgetsolnpoolmeanobjval = _pycplex_platform.CPXXgetsolnpoolmeanobjval

def CPXXgetsolnpoolobjval(*args):
  return _pycplex_platform.CPXXgetsolnpoolobjval(*args)
CPXXgetsolnpoolobjval = _pycplex_platform.CPXXgetsolnpoolobjval

def CPXXgetsolnpoolx(*args):
  return _pycplex_platform.CPXXgetsolnpoolx(*args)
CPXXgetsolnpoolx = _pycplex_platform.CPXXgetsolnpoolx

def CPXXgetsolnpoolslack(*args):
  return _pycplex_platform.CPXXgetsolnpoolslack(*args)
CPXXgetsolnpoolslack = _pycplex_platform.CPXXgetsolnpoolslack

def CPXXgetsolnpoolqconstrslack(*args):
  return _pycplex_platform.CPXXgetsolnpoolqconstrslack(*args)
CPXXgetsolnpoolqconstrslack = _pycplex_platform.CPXXgetsolnpoolqconstrslack

def CPXXgetsolnpoolsolnname(*args):
  return _pycplex_platform.CPXXgetsolnpoolsolnname(*args)
CPXXgetsolnpoolsolnname = _pycplex_platform.CPXXgetsolnpoolsolnname

def CPXXgetsolnpoolsolnindex(*args):
  return _pycplex_platform.CPXXgetsolnpoolsolnindex(*args)
CPXXgetsolnpoolsolnindex = _pycplex_platform.CPXXgetsolnpoolsolnindex

def CPXXdelsolnpoolsolns(*args):
  return _pycplex_platform.CPXXdelsolnpoolsolns(*args)
CPXXdelsolnpoolsolns = _pycplex_platform.CPXXdelsolnpoolsolns

def CPXXsetinfocallbackfunc(*args):
  return _pycplex_platform.CPXXsetinfocallbackfunc(*args)
CPXXsetinfocallbackfunc = _pycplex_platform.CPXXsetinfocallbackfunc

def CPXXsetmipcallbackfunc(*args):
  return _pycplex_platform.CPXXsetmipcallbackfunc(*args)
CPXXsetmipcallbackfunc = _pycplex_platform.CPXXsetmipcallbackfunc

def CPXXsetbranchcallbackfunc(*args):
  return _pycplex_platform.CPXXsetbranchcallbackfunc(*args)
CPXXsetbranchcallbackfunc = _pycplex_platform.CPXXsetbranchcallbackfunc

def CPXXsetbranchnosolncallbackfunc(*args):
  return _pycplex_platform.CPXXsetbranchnosolncallbackfunc(*args)
CPXXsetbranchnosolncallbackfunc = _pycplex_platform.CPXXsetbranchnosolncallbackfunc

def CPXXsetlazyconstraintcallbackfunc(*args):
  return _pycplex_platform.CPXXsetlazyconstraintcallbackfunc(*args)
CPXXsetlazyconstraintcallbackfunc = _pycplex_platform.CPXXsetlazyconstraintcallbackfunc

def CPXXsetusercutcallbackfunc(*args):
  return _pycplex_platform.CPXXsetusercutcallbackfunc(*args)
CPXXsetusercutcallbackfunc = _pycplex_platform.CPXXsetusercutcallbackfunc

def CPXXsetnodecallbackfunc(*args):
  return _pycplex_platform.CPXXsetnodecallbackfunc(*args)
CPXXsetnodecallbackfunc = _pycplex_platform.CPXXsetnodecallbackfunc

def CPXXsetheuristiccallbackfunc(*args):
  return _pycplex_platform.CPXXsetheuristiccallbackfunc(*args)
CPXXsetheuristiccallbackfunc = _pycplex_platform.CPXXsetheuristiccallbackfunc

def CPXXsetincumbentcallbackfunc(*args):
  return _pycplex_platform.CPXXsetincumbentcallbackfunc(*args)
CPXXsetincumbentcallbackfunc = _pycplex_platform.CPXXsetincumbentcallbackfunc

def CPXXsetsolvecallbackfunc(*args):
  return _pycplex_platform.CPXXsetsolvecallbackfunc(*args)
CPXXsetsolvecallbackfunc = _pycplex_platform.CPXXsetsolvecallbackfunc

def CPXXgetcallbacknodeinfo(*args):
  return _pycplex_platform.CPXXgetcallbacknodeinfo(*args)
CPXXgetcallbacknodeinfo = _pycplex_platform.CPXXgetcallbacknodeinfo

def CPXXcallbacksetuserhandle(*args):
  return _pycplex_platform.CPXXcallbacksetuserhandle(*args)
CPXXcallbacksetuserhandle = _pycplex_platform.CPXXcallbacksetuserhandle

def CPXXcallbacksetnodeuserhandle(*args):
  return _pycplex_platform.CPXXcallbacksetnodeuserhandle(*args)
CPXXcallbacksetnodeuserhandle = _pycplex_platform.CPXXcallbacksetnodeuserhandle

def CPXXgetcallbackseqinfo(*args):
  return _pycplex_platform.CPXXgetcallbackseqinfo(*args)
CPXXgetcallbackseqinfo = _pycplex_platform.CPXXgetcallbackseqinfo

def CPXXgetcallbacksosinfo(*args):
  return _pycplex_platform.CPXXgetcallbacksosinfo(*args)
CPXXgetcallbacksosinfo = _pycplex_platform.CPXXgetcallbacksosinfo

def CPXXgetcallbackindicatorinfo(*args):
  return _pycplex_platform.CPXXgetcallbackindicatorinfo(*args)
CPXXgetcallbackindicatorinfo = _pycplex_platform.CPXXgetcallbackindicatorinfo

def CPXXcutcallbackadd(*args):
  return _pycplex_platform.CPXXcutcallbackadd(*args)
CPXXcutcallbackadd = _pycplex_platform.CPXXcutcallbackadd

def CPXXcutcallbackaddlocal(*args):
  return _pycplex_platform.CPXXcutcallbackaddlocal(*args)
CPXXcutcallbackaddlocal = _pycplex_platform.CPXXcutcallbackaddlocal

def CPXXbranchcallbackbranchgeneral(*args):
  return _pycplex_platform.CPXXbranchcallbackbranchgeneral(*args)
CPXXbranchcallbackbranchgeneral = _pycplex_platform.CPXXbranchcallbackbranchgeneral

def CPXXbranchcallbackbranchasCPLEX(*args):
  return _pycplex_platform.CPXXbranchcallbackbranchasCPLEX(*args)
CPXXbranchcallbackbranchasCPLEX = _pycplex_platform.CPXXbranchcallbackbranchasCPLEX

def CPXXgetcallbacknodex(*args):
  return _pycplex_platform.CPXXgetcallbacknodex(*args)
CPXXgetcallbacknodex = _pycplex_platform.CPXXgetcallbacknodex

def CPXXgetcallbacknodeobjval(*args):
  return _pycplex_platform.CPXXgetcallbacknodeobjval(*args)
CPXXgetcallbacknodeobjval = _pycplex_platform.CPXXgetcallbacknodeobjval

def CPXXgetcallbackorder(*args):
  return _pycplex_platform.CPXXgetcallbackorder(*args)
CPXXgetcallbackorder = _pycplex_platform.CPXXgetcallbackorder

def CPXXgetcallbackpseudocosts(*args):
  return _pycplex_platform.CPXXgetcallbackpseudocosts(*args)
CPXXgetcallbackpseudocosts = _pycplex_platform.CPXXgetcallbackpseudocosts

def CPXXgetcallbackincumbent(*args):
  return _pycplex_platform.CPXXgetcallbackincumbent(*args)
CPXXgetcallbackincumbent = _pycplex_platform.CPXXgetcallbackincumbent

def CPXXgetcallbacknodeintfeas(*args):
  return _pycplex_platform.CPXXgetcallbacknodeintfeas(*args)
CPXXgetcallbacknodeintfeas = _pycplex_platform.CPXXgetcallbacknodeintfeas

def CPXXgetcallbackgloballb(*args):
  return _pycplex_platform.CPXXgetcallbackgloballb(*args)
CPXXgetcallbackgloballb = _pycplex_platform.CPXXgetcallbackgloballb

def CPXXgetcallbackglobalub(*args):
  return _pycplex_platform.CPXXgetcallbackglobalub(*args)
CPXXgetcallbackglobalub = _pycplex_platform.CPXXgetcallbackglobalub

def CPXXgetcallbacknodelb(*args):
  return _pycplex_platform.CPXXgetcallbacknodelb(*args)
CPXXgetcallbacknodelb = _pycplex_platform.CPXXgetcallbacknodelb

def CPXXgetcallbacknodeub(*args):
  return _pycplex_platform.CPXXgetcallbacknodeub(*args)
CPXXgetcallbacknodeub = _pycplex_platform.CPXXgetcallbacknodeub

def CPXXgetcallbacknodestat(*args):
  return _pycplex_platform.CPXXgetcallbacknodestat(*args)
CPXXgetcallbacknodestat = _pycplex_platform.CPXXgetcallbacknodestat

def CPXXgetcallbackbranchconstraints(*args):
  return _pycplex_platform.CPXXgetcallbackbranchconstraints(*args)
CPXXgetcallbackbranchconstraints = _pycplex_platform.CPXXgetcallbackbranchconstraints

def CPXXaddusercuts(*args):
  return _pycplex_platform.CPXXaddusercuts(*args)
CPXXaddusercuts = _pycplex_platform.CPXXaddusercuts

def CPXXaddlazyconstraints(*args):
  return _pycplex_platform.CPXXaddlazyconstraints(*args)
CPXXaddlazyconstraints = _pycplex_platform.CPXXaddlazyconstraints

def CPXXfreeusercuts(*args):
  return _pycplex_platform.CPXXfreeusercuts(*args)
CPXXfreeusercuts = _pycplex_platform.CPXXfreeusercuts

def CPXXfreelazyconstraints(*args):
  return _pycplex_platform.CPXXfreelazyconstraints(*args)
CPXXfreelazyconstraints = _pycplex_platform.CPXXfreelazyconstraints

def CPXXcopyquad(*args):
  return _pycplex_platform.CPXXcopyquad(*args)
CPXXcopyquad = _pycplex_platform.CPXXcopyquad

def CPXXcopyqpsep(*args):
  return _pycplex_platform.CPXXcopyqpsep(*args)
CPXXcopyqpsep = _pycplex_platform.CPXXcopyqpsep

def CPXXchgqpcoef(*args):
  return _pycplex_platform.CPXXchgqpcoef(*args)
CPXXchgqpcoef = _pycplex_platform.CPXXchgqpcoef

def CPXXgetnumqpnz(*args):
  return _pycplex_platform.CPXXgetnumqpnz(*args)
CPXXgetnumqpnz = _pycplex_platform.CPXXgetnumqpnz

def CPXXgetnumquad(*args):
  return _pycplex_platform.CPXXgetnumquad(*args)
CPXXgetnumquad = _pycplex_platform.CPXXgetnumquad

def CPXXgetqpcoef(*args):
  return _pycplex_platform.CPXXgetqpcoef(*args)
CPXXgetqpcoef = _pycplex_platform.CPXXgetqpcoef

def CPXXgetquad(*args):
  return _pycplex_platform.CPXXgetquad(*args)
CPXXgetquad = _pycplex_platform.CPXXgetquad

def CPXXaddqconstr(*args):
  return _pycplex_platform.CPXXaddqconstr(*args)
CPXXaddqconstr = _pycplex_platform.CPXXaddqconstr

def CPXXdelqconstrs(*args):
  return _pycplex_platform.CPXXdelqconstrs(*args)
CPXXdelqconstrs = _pycplex_platform.CPXXdelqconstrs

def CPXXgetnumqconstrs(*args):
  return _pycplex_platform.CPXXgetnumqconstrs(*args)
CPXXgetnumqconstrs = _pycplex_platform.CPXXgetnumqconstrs

def CPXXgetqconstrindex(*args):
  return _pycplex_platform.CPXXgetqconstrindex(*args)
CPXXgetqconstrindex = _pycplex_platform.CPXXgetqconstrindex

def CPXXgetqconstr(*args):
  return _pycplex_platform.CPXXgetqconstr(*args)
CPXXgetqconstr = _pycplex_platform.CPXXgetqconstr

def CPXXgetqconstrname(*args):
  return _pycplex_platform.CPXXgetqconstrname(*args)
CPXXgetqconstrname = _pycplex_platform.CPXXgetqconstrname

def CPXXgetqconstrslack(*args):
  return _pycplex_platform.CPXXgetqconstrslack(*args)
CPXXgetqconstrslack = _pycplex_platform.CPXXgetqconstrslack

def CPXXqconstrslackfromx(*args):
  return _pycplex_platform.CPXXqconstrslackfromx(*args)
CPXXqconstrslackfromx = _pycplex_platform.CPXXqconstrslackfromx

def CPXXgetqconstrinfeas(*args):
  return _pycplex_platform.CPXXgetqconstrinfeas(*args)
CPXXgetqconstrinfeas = _pycplex_platform.CPXXgetqconstrinfeas

def CPXXgetxqxax(*args):
  return _pycplex_platform.CPXXgetxqxax(*args)
CPXXgetxqxax = _pycplex_platform.CPXXgetxqxax

def CPXXgetqconstrdslack(*args):
  return _pycplex_platform.CPXXgetqconstrdslack(*args)
CPXXgetqconstrdslack = _pycplex_platform.CPXXgetqconstrdslack

def CPXXnewlongannotation(*args):
  return _pycplex_platform.CPXXnewlongannotation(*args)
CPXXnewlongannotation = _pycplex_platform.CPXXnewlongannotation

def CPXXnewdblannotation(*args):
  return _pycplex_platform.CPXXnewdblannotation(*args)
CPXXnewdblannotation = _pycplex_platform.CPXXnewdblannotation

def CPXXdellongannotations(*args):
  return _pycplex_platform.CPXXdellongannotations(*args)
CPXXdellongannotations = _pycplex_platform.CPXXdellongannotations

def CPXXdeldblannotations(*args):
  return _pycplex_platform.CPXXdeldblannotations(*args)
CPXXdeldblannotations = _pycplex_platform.CPXXdeldblannotations

def CPXXgetlongannotationindex(*args):
  return _pycplex_platform.CPXXgetlongannotationindex(*args)
CPXXgetlongannotationindex = _pycplex_platform.CPXXgetlongannotationindex

def CPXXgetdblannotationindex(*args):
  return _pycplex_platform.CPXXgetdblannotationindex(*args)
CPXXgetdblannotationindex = _pycplex_platform.CPXXgetdblannotationindex

def CPXXgetlongannotationname(*args):
  return _pycplex_platform.CPXXgetlongannotationname(*args)
CPXXgetlongannotationname = _pycplex_platform.CPXXgetlongannotationname

def CPXXgetdblannotationname(*args):
  return _pycplex_platform.CPXXgetdblannotationname(*args)
CPXXgetdblannotationname = _pycplex_platform.CPXXgetdblannotationname

def CPXXgetnumlongannotations(*args):
  return _pycplex_platform.CPXXgetnumlongannotations(*args)
CPXXgetnumlongannotations = _pycplex_platform.CPXXgetnumlongannotations

def CPXXgetnumdblannotations(*args):
  return _pycplex_platform.CPXXgetnumdblannotations(*args)
CPXXgetnumdblannotations = _pycplex_platform.CPXXgetnumdblannotations

def CPXXgetlongannotationdefval(*args):
  return _pycplex_platform.CPXXgetlongannotationdefval(*args)
CPXXgetlongannotationdefval = _pycplex_platform.CPXXgetlongannotationdefval

def CPXXgetdblannotationdefval(*args):
  return _pycplex_platform.CPXXgetdblannotationdefval(*args)
CPXXgetdblannotationdefval = _pycplex_platform.CPXXgetdblannotationdefval

def CPXXsetdblannotations(*args):
  return _pycplex_platform.CPXXsetdblannotations(*args)
CPXXsetdblannotations = _pycplex_platform.CPXXsetdblannotations

def CPXXsetlongannotations(*args):
  return _pycplex_platform.CPXXsetlongannotations(*args)
CPXXsetlongannotations = _pycplex_platform.CPXXsetlongannotations

def CPXXgetlongannotations(*args):
  return _pycplex_platform.CPXXgetlongannotations(*args)
CPXXgetlongannotations = _pycplex_platform.CPXXgetlongannotations

def CPXXgetdblannotations(*args):
  return _pycplex_platform.CPXXgetdblannotations(*args)
CPXXgetdblannotations = _pycplex_platform.CPXXgetdblannotations

def CPXXaddpwl(*args):
  return _pycplex_platform.CPXXaddpwl(*args)
CPXXaddpwl = _pycplex_platform.CPXXaddpwl

def CPXXdelpwl(*args):
  return _pycplex_platform.CPXXdelpwl(*args)
CPXXdelpwl = _pycplex_platform.CPXXdelpwl

def CPXXgetnumpwl(*args):
  return _pycplex_platform.CPXXgetnumpwl(*args)
CPXXgetnumpwl = _pycplex_platform.CPXXgetnumpwl

def CPXXgetpwl(*args):
  return _pycplex_platform.CPXXgetpwl(*args)
CPXXgetpwl = _pycplex_platform.CPXXgetpwl

def CPXXgetpwlindex(*args):
  return _pycplex_platform.CPXXgetpwlindex(*args)
CPXXgetpwlindex = _pycplex_platform.CPXXgetpwlindex

def CPXXgetpwlname(*args):
  return _pycplex_platform.CPXXgetpwlname(*args)
CPXXgetpwlname = _pycplex_platform.CPXXgetpwlname

def CPXXgetnumlazyconstraints(*args):
  return _pycplex_platform.CPXXgetnumlazyconstraints(*args)
CPXXgetnumlazyconstraints = _pycplex_platform.CPXXgetnumlazyconstraints

def CPXXgetnumusercuts(*args):
  return _pycplex_platform.CPXXgetnumusercuts(*args)
CPXXgetnumusercuts = _pycplex_platform.CPXXgetnumusercuts

def CPXEgetprobstats(*args):
  return _pycplex_platform.CPXEgetprobstats(*args)
CPXEgetprobstats = _pycplex_platform.CPXEgetprobstats

def CPXEgethist(*args):
  return _pycplex_platform.CPXEgethist(*args)
CPXEgethist = _pycplex_platform.CPXEgethist

def CPXEgetqualitymetrics(*args):
  return _pycplex_platform.CPXEgetqualitymetrics(*args)
CPXEgetqualitymetrics = _pycplex_platform.CPXEgetqualitymetrics

def CPXEshowquality(*args):
  return _pycplex_platform.CPXEshowquality(*args)
CPXEshowquality = _pycplex_platform.CPXEshowquality

def CPXXgetnumcores(*args):
  return _pycplex_platform.CPXXgetnumcores(*args)
CPXXgetnumcores = _pycplex_platform.CPXXgetnumcores

def CPXXcallbacksetfunc(*args):
  return _pycplex_platform.CPXXcallbacksetfunc(*args)
CPXXcallbacksetfunc = _pycplex_platform.CPXXcallbacksetfunc

def CPXXcallbackgetinfoint(*args):
  return _pycplex_platform.CPXXcallbackgetinfoint(*args)
CPXXcallbackgetinfoint = _pycplex_platform.CPXXcallbackgetinfoint

def CPXXcallbackgetinfolong(*args):
  return _pycplex_platform.CPXXcallbackgetinfolong(*args)
CPXXcallbackgetinfolong = _pycplex_platform.CPXXcallbackgetinfolong

def CPXXcallbackgetinfodbl(*args):
  return _pycplex_platform.CPXXcallbackgetinfodbl(*args)
CPXXcallbackgetinfodbl = _pycplex_platform.CPXXcallbackgetinfodbl

def CPXXcallbackabort(*args):
  return _pycplex_platform.CPXXcallbackabort(*args)
CPXXcallbackabort = _pycplex_platform.CPXXcallbackabort

def CPXXcallbackcandidateispoint(*args):
  return _pycplex_platform.CPXXcallbackcandidateispoint(*args)
CPXXcallbackcandidateispoint = _pycplex_platform.CPXXcallbackcandidateispoint

def CPXXcallbackcandidateisray(*args):
  return _pycplex_platform.CPXXcallbackcandidateisray(*args)
CPXXcallbackcandidateisray = _pycplex_platform.CPXXcallbackcandidateisray

def CPXXcallbackgetcandidatepoint(*args):
  return _pycplex_platform.CPXXcallbackgetcandidatepoint(*args)
CPXXcallbackgetcandidatepoint = _pycplex_platform.CPXXcallbackgetcandidatepoint

def CPXXcallbackgetcandidateray(*args):
  return _pycplex_platform.CPXXcallbackgetcandidateray(*args)
CPXXcallbackgetcandidateray = _pycplex_platform.CPXXcallbackgetcandidateray

def CPXXcallbackgetrelaxationpoint(*args):
  return _pycplex_platform.CPXXcallbackgetrelaxationpoint(*args)
CPXXcallbackgetrelaxationpoint = _pycplex_platform.CPXXcallbackgetrelaxationpoint

def CPXXcallbackgetincumbent(*args):
  return _pycplex_platform.CPXXcallbackgetincumbent(*args)
CPXXcallbackgetincumbent = _pycplex_platform.CPXXcallbackgetincumbent

def CPXXcallbackgetlocallb(*args):
  return _pycplex_platform.CPXXcallbackgetlocallb(*args)
CPXXcallbackgetlocallb = _pycplex_platform.CPXXcallbackgetlocallb

def CPXXcallbackgetlocalub(*args):
  return _pycplex_platform.CPXXcallbackgetlocalub(*args)
CPXXcallbackgetlocalub = _pycplex_platform.CPXXcallbackgetlocalub

def CPXXcallbackgetgloballb(*args):
  return _pycplex_platform.CPXXcallbackgetgloballb(*args)
CPXXcallbackgetgloballb = _pycplex_platform.CPXXcallbackgetgloballb

def CPXXcallbackgetglobalub(*args):
  return _pycplex_platform.CPXXcallbackgetglobalub(*args)
CPXXcallbackgetglobalub = _pycplex_platform.CPXXcallbackgetglobalub

def CPXXcallbackpostheursoln(*args):
  return _pycplex_platform.CPXXcallbackpostheursoln(*args)
CPXXcallbackpostheursoln = _pycplex_platform.CPXXcallbackpostheursoln

def CPXXcallbackaddusercuts(*args):
  return _pycplex_platform.CPXXcallbackaddusercuts(*args)
CPXXcallbackaddusercuts = _pycplex_platform.CPXXcallbackaddusercuts

def CPXXcallbackrejectcandidate(*args):
  return _pycplex_platform.CPXXcallbackrejectcandidate(*args)
CPXXcallbackrejectcandidate = _pycplex_platform.CPXXcallbackrejectcandidate

def CPXXmodelasstcallbacksetfunc(*args):
  return _pycplex_platform.CPXXmodelasstcallbacksetfunc(*args)
CPXXmodelasstcallbacksetfunc = _pycplex_platform.CPXXmodelasstcallbacksetfunc

def CPXXgetnumobjs(*args):
  return _pycplex_platform.CPXXgetnumobjs(*args)
CPXXgetnumobjs = _pycplex_platform.CPXXgetnumobjs

def CPXXmultiobjchgattribs(*args):
  return _pycplex_platform.CPXXmultiobjchgattribs(*args)
CPXXmultiobjchgattribs = _pycplex_platform.CPXXmultiobjchgattribs

def CPXXmultiobjgetindex(*args):
  return _pycplex_platform.CPXXmultiobjgetindex(*args)
CPXXmultiobjgetindex = _pycplex_platform.CPXXmultiobjgetindex

def CPXXmultiobjgetname(*args):
  return _pycplex_platform.CPXXmultiobjgetname(*args)
CPXXmultiobjgetname = _pycplex_platform.CPXXmultiobjgetname

def CPXXmultiobjgetobj(*args):
  return _pycplex_platform.CPXXmultiobjgetobj(*args)
CPXXmultiobjgetobj = _pycplex_platform.CPXXmultiobjgetobj

def CPXXmultiobjgetobjval(*args):
  return _pycplex_platform.CPXXmultiobjgetobjval(*args)
CPXXmultiobjgetobjval = _pycplex_platform.CPXXmultiobjgetobjval

def CPXXmultiobjgetobjvalbypriority(*args):
  return _pycplex_platform.CPXXmultiobjgetobjvalbypriority(*args)
CPXXmultiobjgetobjvalbypriority = _pycplex_platform.CPXXmultiobjgetobjvalbypriority

def CPXXmultiobjsetobj(*args):
  return _pycplex_platform.CPXXmultiobjsetobj(*args)
CPXXmultiobjsetobj = _pycplex_platform.CPXXmultiobjsetobj

def CPXXsetnumobjs(*args):
  return _pycplex_platform.CPXXsetnumobjs(*args)
CPXXsetnumobjs = _pycplex_platform.CPXXsetnumobjs

def CPXXmultiobjgetdblinfo(*args):
  return _pycplex_platform.CPXXmultiobjgetdblinfo(*args)
CPXXmultiobjgetdblinfo = _pycplex_platform.CPXXmultiobjgetdblinfo

def CPXXmultiobjgetintinfo(*args):
  return _pycplex_platform.CPXXmultiobjgetintinfo(*args)
CPXXmultiobjgetintinfo = _pycplex_platform.CPXXmultiobjgetintinfo

def CPXXmultiobjgetlonginfo(*args):
  return _pycplex_platform.CPXXmultiobjgetlonginfo(*args)
CPXXmultiobjgetlonginfo = _pycplex_platform.CPXXmultiobjgetlonginfo

def CPXXmultiobjgetnumsolves(*args):
  return _pycplex_platform.CPXXmultiobjgetnumsolves(*args)
CPXXmultiobjgetnumsolves = _pycplex_platform.CPXXmultiobjgetnumsolves

def CPXEgetnumprios(*args):
  return _pycplex_platform.CPXEgetnumprios(*args)
CPXEgetnumprios = _pycplex_platform.CPXEgetnumprios

def CPXEismultiobj(*args):
  return _pycplex_platform.CPXEismultiobj(*args)
CPXEismultiobj = _pycplex_platform.CPXEismultiobj

def CPXXparamsetadddbl(*args):
  return _pycplex_platform.CPXXparamsetadddbl(*args)
CPXXparamsetadddbl = _pycplex_platform.CPXXparamsetadddbl

def CPXXparamsetaddint(*args):
  return _pycplex_platform.CPXXparamsetaddint(*args)
CPXXparamsetaddint = _pycplex_platform.CPXXparamsetaddint

def CPXXparamsetaddlong(*args):
  return _pycplex_platform.CPXXparamsetaddlong(*args)
CPXXparamsetaddlong = _pycplex_platform.CPXXparamsetaddlong

def CPXXparamsetaddstr(*args):
  return _pycplex_platform.CPXXparamsetaddstr(*args)
CPXXparamsetaddstr = _pycplex_platform.CPXXparamsetaddstr

def CPXXparamsetapply(*args):
  return _pycplex_platform.CPXXparamsetapply(*args)
CPXXparamsetapply = _pycplex_platform.CPXXparamsetapply

def CPXXparamsetcopy(*args):
  return _pycplex_platform.CPXXparamsetcopy(*args)
CPXXparamsetcopy = _pycplex_platform.CPXXparamsetcopy

def CPXXparamsetcreate(*args):
  return _pycplex_platform.CPXXparamsetcreate(*args)
CPXXparamsetcreate = _pycplex_platform.CPXXparamsetcreate

def CPXXparamsetdel(*args):
  return _pycplex_platform.CPXXparamsetdel(*args)
CPXXparamsetdel = _pycplex_platform.CPXXparamsetdel

def CPXXparamsetfree(*args):
  return _pycplex_platform.CPXXparamsetfree(*args)
CPXXparamsetfree = _pycplex_platform.CPXXparamsetfree

def CPXXparamsetgetdbl(*args):
  return _pycplex_platform.CPXXparamsetgetdbl(*args)
CPXXparamsetgetdbl = _pycplex_platform.CPXXparamsetgetdbl

def CPXXparamsetgetids(*args):
  return _pycplex_platform.CPXXparamsetgetids(*args)
CPXXparamsetgetids = _pycplex_platform.CPXXparamsetgetids

def CPXXparamsetgetint(*args):
  return _pycplex_platform.CPXXparamsetgetint(*args)
CPXXparamsetgetint = _pycplex_platform.CPXXparamsetgetint

def CPXXparamsetgetlong(*args):
  return _pycplex_platform.CPXXparamsetgetlong(*args)
CPXXparamsetgetlong = _pycplex_platform.CPXXparamsetgetlong

def CPXXparamsetgetstr(*args):
  return _pycplex_platform.CPXXparamsetgetstr(*args)
CPXXparamsetgetstr = _pycplex_platform.CPXXparamsetgetstr

def CPXXparamsetreadcopy(*args):
  return _pycplex_platform.CPXXparamsetreadcopy(*args)
CPXXparamsetreadcopy = _pycplex_platform.CPXXparamsetreadcopy

def CPXXparamsetwrite(*args):
  return _pycplex_platform.CPXXparamsetwrite(*args)
CPXXparamsetwrite = _pycplex_platform.CPXXparamsetwrite

def CPXEwriteprobdev(*args):
  return _pycplex_platform.CPXEwriteprobdev(*args)
CPXEwriteprobdev = _pycplex_platform.CPXEwriteprobdev
class intPtr(_object):
    __swig_setmethods__ = {}
    __setattr__ = lambda self, name, value: _swig_setattr(self, intPtr, name, value)
    __swig_getmethods__ = {}
    __getattr__ = lambda self, name: _swig_getattr(self, intPtr, name)
    __repr__ = _swig_repr
    def __init__(self): 
        this = _pycplex_platform.new_intPtr()
        try: self.this.append(this)
        except: self.this = this
    __swig_destroy__ = _pycplex_platform.delete_intPtr
    __del__ = lambda self : None;
    def assign(self, *args): return _pycplex_platform.intPtr_assign(self, *args)
    def value(self): return _pycplex_platform.intPtr_value(self)
    def cast(self): return _pycplex_platform.intPtr_cast(self)
    __swig_getmethods__["frompointer"] = lambda x: _pycplex_platform.intPtr_frompointer
    if _newclass:frompointer = staticmethod(_pycplex_platform.intPtr_frompointer)
intPtr_swigregister = _pycplex_platform.intPtr_swigregister
intPtr_swigregister(intPtr)

def intPtr_frompointer(*args):
  return _pycplex_platform.intPtr_frompointer(*args)
intPtr_frompointer = _pycplex_platform.intPtr_frompointer

class cpxlongPtr(_object):
    __swig_setmethods__ = {}
    __setattr__ = lambda self, name, value: _swig_setattr(self, cpxlongPtr, name, value)
    __swig_getmethods__ = {}
    __getattr__ = lambda self, name: _swig_getattr(self, cpxlongPtr, name)
    __repr__ = _swig_repr
    def __init__(self): 
        this = _pycplex_platform.new_cpxlongPtr()
        try: self.this.append(this)
        except: self.this = this
    __swig_destroy__ = _pycplex_platform.delete_cpxlongPtr
    __del__ = lambda self : None;
    def assign(self, *args): return _pycplex_platform.cpxlongPtr_assign(self, *args)
    def value(self): return _pycplex_platform.cpxlongPtr_value(self)
    def cast(self): return _pycplex_platform.cpxlongPtr_cast(self)
    __swig_getmethods__["frompointer"] = lambda x: _pycplex_platform.cpxlongPtr_frompointer
    if _newclass:frompointer = staticmethod(_pycplex_platform.cpxlongPtr_frompointer)
cpxlongPtr_swigregister = _pycplex_platform.cpxlongPtr_swigregister
cpxlongPtr_swigregister(cpxlongPtr)

def cpxlongPtr_frompointer(*args):
  return _pycplex_platform.cpxlongPtr_frompointer(*args)
cpxlongPtr_frompointer = _pycplex_platform.cpxlongPtr_frompointer

class doublePtr(_object):
    __swig_setmethods__ = {}
    __setattr__ = lambda self, name, value: _swig_setattr(self, doublePtr, name, value)
    __swig_getmethods__ = {}
    __getattr__ = lambda self, name: _swig_getattr(self, doublePtr, name)
    __repr__ = _swig_repr
    def __init__(self): 
        this = _pycplex_platform.new_doublePtr()
        try: self.this.append(this)
        except: self.this = this
    __swig_destroy__ = _pycplex_platform.delete_doublePtr
    __del__ = lambda self : None;
    def assign(self, *args): return _pycplex_platform.doublePtr_assign(self, *args)
    def value(self): return _pycplex_platform.doublePtr_value(self)
    def cast(self): return _pycplex_platform.doublePtr_cast(self)
    __swig_getmethods__["frompointer"] = lambda x: _pycplex_platform.doublePtr_frompointer
    if _newclass:frompointer = staticmethod(_pycplex_platform.doublePtr_frompointer)
doublePtr_swigregister = _pycplex_platform.doublePtr_swigregister
doublePtr_swigregister(doublePtr)

def doublePtr_frompointer(*args):
  return _pycplex_platform.doublePtr_frompointer(*args)
doublePtr_frompointer = _pycplex_platform.doublePtr_frompointer

class CPXLPptrPtr(_object):
    __swig_setmethods__ = {}
    __setattr__ = lambda self, name, value: _swig_setattr(self, CPXLPptrPtr, name, value)
    __swig_getmethods__ = {}
    __getattr__ = lambda self, name: _swig_getattr(self, CPXLPptrPtr, name)
    __repr__ = _swig_repr
    def __init__(self): 
        this = _pycplex_platform.new_CPXLPptrPtr()
        try: self.this.append(this)
        except: self.this = this
    __swig_destroy__ = _pycplex_platform.delete_CPXLPptrPtr
    __del__ = lambda self : None;
    def assign(self, *args): return _pycplex_platform.CPXLPptrPtr_assign(self, *args)
    def value(self): return _pycplex_platform.CPXLPptrPtr_value(self)
    def cast(self): return _pycplex_platform.CPXLPptrPtr_cast(self)
    __swig_getmethods__["frompointer"] = lambda x: _pycplex_platform.CPXLPptrPtr_frompointer
    if _newclass:frompointer = staticmethod(_pycplex_platform.CPXLPptrPtr_frompointer)
CPXLPptrPtr_swigregister = _pycplex_platform.CPXLPptrPtr_swigregister
CPXLPptrPtr_swigregister(CPXLPptrPtr)

def CPXLPptrPtr_frompointer(*args):
  return _pycplex_platform.CPXLPptrPtr_frompointer(*args)
CPXLPptrPtr_frompointer = _pycplex_platform.CPXLPptrPtr_frompointer

class CPXENVptrPtr(_object):
    __swig_setmethods__ = {}
    __setattr__ = lambda self, name, value: _swig_setattr(self, CPXENVptrPtr, name, value)
    __swig_getmethods__ = {}
    __getattr__ = lambda self, name: _swig_getattr(self, CPXENVptrPtr, name)
    __repr__ = _swig_repr
    def __init__(self): 
        this = _pycplex_platform.new_CPXENVptrPtr()
        try: self.this.append(this)
        except: self.this = this
    __swig_destroy__ = _pycplex_platform.delete_CPXENVptrPtr
    __del__ = lambda self : None;
    def assign(self, *args): return _pycplex_platform.CPXENVptrPtr_assign(self, *args)
    def value(self): return _pycplex_platform.CPXENVptrPtr_value(self)
    def cast(self): return _pycplex_platform.CPXENVptrPtr_cast(self)
    __swig_getmethods__["frompointer"] = lambda x: _pycplex_platform.CPXENVptrPtr_frompointer
    if _newclass:frompointer = staticmethod(_pycplex_platform.CPXENVptrPtr_frompointer)
CPXENVptrPtr_swigregister = _pycplex_platform.CPXENVptrPtr_swigregister
CPXENVptrPtr_swigregister(CPXENVptrPtr)

def CPXENVptrPtr_frompointer(*args):
  return _pycplex_platform.CPXENVptrPtr_frompointer(*args)
CPXENVptrPtr_frompointer = _pycplex_platform.CPXENVptrPtr_frompointer

class CPXCHANNELptrPtr(_object):
    __swig_setmethods__ = {}
    __setattr__ = lambda self, name, value: _swig_setattr(self, CPXCHANNELptrPtr, name, value)
    __swig_getmethods__ = {}
    __getattr__ = lambda self, name: _swig_getattr(self, CPXCHANNELptrPtr, name)
    __repr__ = _swig_repr
    def __init__(self): 
        this = _pycplex_platform.new_CPXCHANNELptrPtr()
        try: self.this.append(this)
        except: self.this = this
    __swig_destroy__ = _pycplex_platform.delete_CPXCHANNELptrPtr
    __del__ = lambda self : None;
    def assign(self, *args): return _pycplex_platform.CPXCHANNELptrPtr_assign(self, *args)
    def value(self): return _pycplex_platform.CPXCHANNELptrPtr_value(self)
    def cast(self): return _pycplex_platform.CPXCHANNELptrPtr_cast(self)
    __swig_getmethods__["frompointer"] = lambda x: _pycplex_platform.CPXCHANNELptrPtr_frompointer
    if _newclass:frompointer = staticmethod(_pycplex_platform.CPXCHANNELptrPtr_frompointer)
CPXCHANNELptrPtr_swigregister = _pycplex_platform.CPXCHANNELptrPtr_swigregister
CPXCHANNELptrPtr_swigregister(CPXCHANNELptrPtr)

def CPXCHANNELptrPtr_frompointer(*args):
  return _pycplex_platform.CPXCHANNELptrPtr_frompointer(*args)
CPXCHANNELptrPtr_frompointer = _pycplex_platform.CPXCHANNELptrPtr_frompointer

class CPXPARAMSETptrPtr(_object):
    __swig_setmethods__ = {}
    __setattr__ = lambda self, name, value: _swig_setattr(self, CPXPARAMSETptrPtr, name, value)
    __swig_getmethods__ = {}
    __getattr__ = lambda self, name: _swig_getattr(self, CPXPARAMSETptrPtr, name)
    __repr__ = _swig_repr
    def __init__(self): 
        this = _pycplex_platform.new_CPXPARAMSETptrPtr()
        try: self.this.append(this)
        except: self.this = this
    __swig_destroy__ = _pycplex_platform.delete_CPXPARAMSETptrPtr
    __del__ = lambda self : None;
    def assign(self, *args): return _pycplex_platform.CPXPARAMSETptrPtr_assign(self, *args)
    def value(self): return _pycplex_platform.CPXPARAMSETptrPtr_value(self)
    def cast(self): return _pycplex_platform.CPXPARAMSETptrPtr_cast(self)
    __swig_getmethods__["frompointer"] = lambda x: _pycplex_platform.CPXPARAMSETptrPtr_frompointer
    if _newclass:frompointer = staticmethod(_pycplex_platform.CPXPARAMSETptrPtr_frompointer)
CPXPARAMSETptrPtr_swigregister = _pycplex_platform.CPXPARAMSETptrPtr_swigregister
CPXPARAMSETptrPtr_swigregister(CPXPARAMSETptrPtr)

def CPXPARAMSETptrPtr_frompointer(*args):
  return _pycplex_platform.CPXPARAMSETptrPtr_frompointer(*args)
CPXPARAMSETptrPtr_frompointer = _pycplex_platform.CPXPARAMSETptrPtr_frompointer

class intArray(_object):
    __swig_setmethods__ = {}
    __setattr__ = lambda self, name, value: _swig_setattr(self, intArray, name, value)
    __swig_getmethods__ = {}
    __getattr__ = lambda self, name: _swig_getattr(self, intArray, name)
    __repr__ = _swig_repr
    def __init__(self, *args): 
        this = _pycplex_platform.new_intArray(*args)
        try: self.this.append(this)
        except: self.this = this
    __swig_destroy__ = _pycplex_platform.delete_intArray
    __del__ = lambda self : None;
    def __getitem__(self, *args): return _pycplex_platform.intArray___getitem__(self, *args)
    def __setitem__(self, *args): return _pycplex_platform.intArray___setitem__(self, *args)
    def cast(self): return _pycplex_platform.intArray_cast(self)
    __swig_getmethods__["frompointer"] = lambda x: _pycplex_platform.intArray_frompointer
    if _newclass:frompointer = staticmethod(_pycplex_platform.intArray_frompointer)
intArray_swigregister = _pycplex_platform.intArray_swigregister
intArray_swigregister(intArray)

def intArray_frompointer(*args):
  return _pycplex_platform.intArray_frompointer(*args)
intArray_frompointer = _pycplex_platform.intArray_frompointer

class doubleArray(_object):
    __swig_setmethods__ = {}
    __setattr__ = lambda self, name, value: _swig_setattr(self, doubleArray, name, value)
    __swig_getmethods__ = {}
    __getattr__ = lambda self, name: _swig_getattr(self, doubleArray, name)
    __repr__ = _swig_repr
    def __init__(self, *args): 
        this = _pycplex_platform.new_doubleArray(*args)
        try: self.this.append(this)
        except: self.this = this
    __swig_destroy__ = _pycplex_platform.delete_doubleArray
    __del__ = lambda self : None;
    def __getitem__(self, *args): return _pycplex_platform.doubleArray___getitem__(self, *args)
    def __setitem__(self, *args): return _pycplex_platform.doubleArray___setitem__(self, *args)
    def cast(self): return _pycplex_platform.doubleArray_cast(self)
    __swig_getmethods__["frompointer"] = lambda x: _pycplex_platform.doubleArray_frompointer
    if _newclass:frompointer = staticmethod(_pycplex_platform.doubleArray_frompointer)
doubleArray_swigregister = _pycplex_platform.doubleArray_swigregister
doubleArray_swigregister(doubleArray)

def doubleArray_frompointer(*args):
  return _pycplex_platform.doubleArray_frompointer(*args)
doubleArray_frompointer = _pycplex_platform.doubleArray_frompointer

class longArray(_object):
    __swig_setmethods__ = {}
    __setattr__ = lambda self, name, value: _swig_setattr(self, longArray, name, value)
    __swig_getmethods__ = {}
    __getattr__ = lambda self, name: _swig_getattr(self, longArray, name)
    __repr__ = _swig_repr
    def __init__(self, *args): 
        this = _pycplex_platform.new_longArray(*args)
        try: self.this.append(this)
        except: self.this = this
    __swig_destroy__ = _pycplex_platform.delete_longArray
    __del__ = lambda self : None;
    def __getitem__(self, *args): return _pycplex_platform.longArray___getitem__(self, *args)
    def __setitem__(self, *args): return _pycplex_platform.longArray___setitem__(self, *args)
    def cast(self): return _pycplex_platform.longArray_cast(self)
    __swig_getmethods__["frompointer"] = lambda x: _pycplex_platform.longArray_frompointer
    if _newclass:frompointer = staticmethod(_pycplex_platform.longArray_frompointer)
longArray_swigregister = _pycplex_platform.longArray_swigregister
longArray_swigregister(longArray)

def longArray_frompointer(*args):
  return _pycplex_platform.longArray_frompointer(*args)
longArray_frompointer = _pycplex_platform.longArray_frompointer

# This file is compatible with both classic and new-style classes.


