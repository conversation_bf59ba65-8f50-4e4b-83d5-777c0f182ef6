# import gurobipy as gp
# from gurobipy import GRB
#
# try:
#     # 创建模型
#     model = gp.Model("simple_test")
#
#     # 添加变量
#     x = model.addVar(vtype=GRB.CONTINUOUS, name="x")
#     y = model.addVar(vtype=GRB.CONTINUOUS, name="y")
#
#     # 设置目标函数
#     model.setObjective(3 * x + 2 * y, GRB.MAXIMIZE)
#
#     # 添加约束
#     model.addConstr(x + y <= 10, "c1")
#     model.addConstr(2 * x + y <= 15, "c2")
#
#     # 优化
#     model.optimize()
#
#     # 输出结果
#     if model.status == GRB.OPTIMAL:
#         print("Optimal solution found:")
#         for v in model.getVars():
#             print(f"{v.varName}: {v.x}")
#         print(f"Objective value: {model.objVal}")
#     else:
#         print("No optimal solution found.")
# except gp.GurobiError as e:
#     print("Gurobi error:", e)

# import pulp as pl
# solver = pl.GUROBI_CMD()
# print(solver.available())

from pulp import GUROBI_CMD

# 检查是否可以使用 Gurobi 求解器
solver = GUROBI_CMD()
print("Gurobi available:", solver.available())