#!/usr/bin/env python3
"""
测试修复后的无人机喷洒农药代码
主要测试实验三的路径提取和可视化功能
"""

import sys
import os

# 添加UAV目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'UAV', 'Spray pesticide'))

def test_small_scale():
    """测试小规模场景"""
    print("=" * 60)
    print("开始测试小规模场景")
    print("=" * 60)
    
    try:
        # 导入修复后的模块
        from spray_v1_0_11 import (
            generate_unified_scenario_data,
            solve_moo_vrp_assignment,
            plot_assignment_grid,
            plot_flight_paths,
            save_results_to_excel
        )
        
        # 生成小规模测试数据
        params = generate_unified_scenario_data(num_drones=2, scale="small")
        print(f"生成参数完成: {params['num_points']} 个作业点, {params['num_drones']} 架无人机")
        
        # 运行实验三
        print("\n开始运行实验三...")
        solution3, status3, objective3, roles3, solution_x3 = solve_moo_vrp_assignment(params)
        
        if objective3 is not None:
            print(f"实验三求解成功! 目标值: {objective3}")
            print(f"求解状态: {status3}")
            print(f"角色数量: {len(roles3)}")
            print(f"路径变量数量: {len(solution_x3)}")
            
            # 测试路径提取
            print("\n测试路径提取...")
            path3 = {}
            for i in range(params['num_drones']):
                print(f"提取无人机 {i} 的路径...")
                path_map = {}
                for (drone_id, from_node, to_node), value in solution_x3.items():
                    if drone_id == i and value > 0.5:
                        path_map[from_node] = to_node
                        print(f"  路径段: {from_node} -> {to_node}")

                start_node = path_map.get('N')
                if start_node is not None:
                    drone_path = ['N', start_node]
                    current_node = start_node
                    max_iterations = len(roles3) + 2
                    iteration_count = 0
                    
                    while current_node != 'N' and iteration_count < max_iterations:
                        next_node = path_map.get(current_node)
                        if next_node is not None:
                            drone_path.append(next_node)
                            current_node = next_node
                            iteration_count += 1
                        else:
                            break
                    
                    path3[i] = drone_path
                    print(f"  完整路径: {drone_path}")
                else:
                    path3[i] = []
                    print(f"  无人机 {i} 没有分配任务")
            
            # 测试结果保存
            print("\n测试结果保存...")
            desktop_path = "."  # 保存到当前目录
            all_results = {
                'Exp3_MOO_VRP_Test': {
                    'solution_t': solution3,
                    'status': 'Optimal' if status3 == 1 else 'Suboptimal',
                    'objective': objective3,
                    'roles': roles3,
                    'path': path3,
                    'solution_x': solution_x3
                }
            }
            
            save_results_to_excel(params, all_results, desktop_path)
            print("结果保存完成!")
            
            return True
            
        else:
            print("实验三求解失败!")
            return False
            
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始测试修复后的无人机喷洒农药代码...")
    
    # 测试小规模场景
    success = test_small_scale()
    
    if success:
        print("\n" + "=" * 60)
        print("测试成功! 修复后的代码工作正常。")
        print("现在可以运行完整的实验了。")
        print("=" * 60)
    else:
        print("\n" + "=" * 60)
        print("测试失败! 请检查代码修复。")
        print("=" * 60)

if __name__ == "__main__":
    main()
