#!/usr/bin/env python3
"""
最终测试脚本：验证修复后的spray_v1.0.11.py
测试所有三个实验，重点验证实验三的路径提取和可视化
"""

import subprocess
import sys
import os
import time

def run_experiment(script_path, timeout=600):
    """运行实验脚本"""
    print(f"开始运行: {script_path}")
    print("=" * 60)
    
    start_time = time.time()
    
    try:
        # 运行脚本
        result = subprocess.run(
            [sys.executable, script_path],
            capture_output=True,
            text=True,
            timeout=timeout,
            cwd=os.path.dirname(os.path.abspath(__file__))
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"运行完成，耗时: {duration:.2f} 秒")
        print(f"返回码: {result.returncode}")
        
        if result.returncode == 0:
            print("✅ 运行成功!")
            
            # 分析输出
            output = result.stdout
            
            # 检查实验一
            if "实验一最优目标值:" in output:
                exp1_obj = output.split("实验一最优目标值:")[1].split("\n")[0].strip()
                print(f"  实验一目标值: {exp1_obj}")
            
            # 检查实验二
            if "实验二最优目标值:" in output:
                exp2_obj = output.split("实验二最优目标值:")[1].split("\n")[0].strip()
                print(f"  实验二目标值: {exp2_obj}")
            
            # 检查实验三
            if "实验三成功找到最优解" in output:
                print("  ✅ 实验三求解成功")
                if "最优目标值 (归一化):" in output:
                    exp3_obj = output.split("最优目标值 (归一化):")[1].split("\n")[0].strip()
                    print(f"  实验三目标值: {exp3_obj}")
            elif "实验三结果警告" in output:
                print("  ⚠️ 实验三找到可行解但非最优")
            elif "实验三求解失败" in output:
                print("  ❌ 实验三求解失败")
            
            # 检查路径提取
            if "路径提取完成" in output:
                print("  ✅ 路径提取完成")
            
            # 检查任务分配
            if "总分配任务数:" in output:
                task_count = output.split("总分配任务数:")[1].split("\n")[0].strip()
                print(f"  总分配任务数: {task_count}")
            
            # 检查图表生成
            chart_count = output.count("已保存至:")
            print(f"  生成图表数量: {chart_count}")
            
            # 检查Excel保存
            if "Excel文件保存成功" in output:
                print("  ✅ Excel文件保存成功")
            
            return True, output
            
        else:
            print("❌ 运行失败!")
            print("错误输出:")
            print(result.stderr)
            return False, result.stderr
            
    except subprocess.TimeoutExpired:
        print(f"❌ 运行超时 (>{timeout}秒)")
        return False, "Timeout"
    except Exception as e:
        print(f"❌ 运行异常: {e}")
        return False, str(e)

def check_output_files():
    """检查输出文件"""
    print("\n检查输出文件:")
    print("=" * 40)
    
    desktop_path = r"H:\OneDrive\Desktop"
    if not os.path.exists(desktop_path):
        desktop_path = "."
    
    expected_files = [
        "Crop_Distribution_Map.png",
        "Ideal_Spraying_Time_Map.png",
        "Experiment 1 Baseline Result.png",
        "Experiment 1 Heuristic Flight Paths.png",
        "Experiment 2 Agronomic Conflict Result.png", 
        "Experiment 2 Heuristic Flight Paths.png",
        "Experiment 3 MOO-VRP Result.png",
        "Experiment 3 Optimal Flight Paths.png"
    ]
    
    found_files = []
    missing_files = []
    
    for filename in expected_files:
        filepath = os.path.join(desktop_path, filename)
        if os.path.exists(filepath):
            found_files.append(filename)
            print(f"  ✅ {filename}")
        else:
            missing_files.append(filename)
            print(f"  ❌ {filename}")
    
    # 检查Excel文件
    excel_files = [f for f in os.listdir(desktop_path) if f.startswith("Drone_Assignment_Results_") and f.endswith(".xlsx")]
    if excel_files:
        print(f"  ✅ Excel结果文件: {excel_files[-1]}")
    else:
        print(f"  ❌ 未找到Excel结果文件")
    
    print(f"\n文件统计: {len(found_files)}/{len(expected_files)} 个图表文件生成成功")
    
    return len(found_files), len(expected_files)

def main():
    """主测试函数"""
    print("🚀 开始最终测试")
    print("测试修复后的无人机喷洒农药代码")
    print("=" * 60)
    
    # 测试修复后的代码
    script_path = os.path.join("UAV", "Spray pesticide", "spray_v1.0.11.py")
    
    if not os.path.exists(script_path):
        print(f"❌ 脚本文件不存在: {script_path}")
        return False
    
    # 运行实验
    success, output = run_experiment(script_path, timeout=900)  # 15分钟超时
    
    if success:
        # 检查输出文件
        found_count, total_count = check_output_files()
        
        print("\n" + "=" * 60)
        print("📊 测试结果总结")
        print("=" * 60)
        
        if "实验三成功找到最优解" in output or "实验三结果警告" in output:
            print("✅ 实验三求解: 成功")
        else:
            print("❌ 实验三求解: 失败")
        
        if "路径提取完成" in output:
            print("✅ 路径提取: 成功")
        else:
            print("❌ 路径提取: 失败")
        
        if "Excel文件保存成功" in output:
            print("✅ Excel保存: 成功")
        else:
            print("❌ Excel保存: 失败")
        
        print(f"✅ 图表生成: {found_count}/{total_count}")
        
        if found_count >= total_count * 0.8:  # 80%以上的文件生成成功
            print("\n🎉 测试总体成功!")
            print("修复后的代码可以正常运行，实验三的路径提取和可视化问题已解决。")
            return True
        else:
            print("\n⚠️ 测试部分成功")
            print("代码可以运行，但部分功能可能还有问题。")
            return False
    else:
        print("\n❌ 测试失败")
        print("代码运行出现问题，需要进一步调试。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
