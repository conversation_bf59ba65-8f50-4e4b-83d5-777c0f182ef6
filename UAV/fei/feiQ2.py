import math
import matplotlib.pyplot as plt
from matplotlib.ticker import MaxNLocator
from math import floor
import numpy as np
import xlrd
import pandas as pd
import pulp as pl
import pandas as pd
from math import cos, sin, acos, asin, pi, atan, fabs, ceil, floor
from collections import defaultdict
import random
import time

class Assignment:
	@classmethod
	# maxDrone: the amount of the drones
	def KM(cls, Q, L):
		row = len(Q)
		col = len(Q[0])

		# build a optimal problem
		pro = pl.LpProblem('Max(connection and coverage)', pl.LpMaximize)
		# build variables for the optimal problem
		lpvars = [[pl.LpVariable("x"+str(i)+"y"+str(j), lowBound = 0, upBound = 1, cat='Integer') for j in range(col)] for i in range(row)]

		# build optimal function
		all = pl.LpAffineExpression()
		for i in range(0,row):
			for j in range(0,col):
				all += Q[i][j]*lpvars[i][j]

		pro += all

		# build constraint for each role
		for j in range(0,col):
			pro += pl.LpConstraint(pl.LpAffineExpression([ (lpvars[i][j],1) for i in range(0,row)]) , 0,"L"+str(j),L[j])
		# build constraint for each agent
		for i in range(0,row):
			pro += pl.LpConstraint(pl.LpAffineExpression([ (lpvars[i][j],1) for j in range(0,col)]) , -1,"La"+str(i), La[i])

		# solve optimal problem
		status = pro.solve()
		# print("Assignment Status: ", pl.LpStatus[status])
		# print("Final Assignment Result", pl.value(pro.objective))

		# get the result of T matrix
		T = [[ lpvars[i][j].varValue for j in range(col) ] for i in range(row)]
		return T
		# return T, pl.value(pro.status), pl.value(pro.objective)

# 归一化Q
def getNormalizedQ(Q):
    max = min = Q[0][0]
    row = len(Q)
    column = len(Q[0])
    for i in range(row):
        for j in range(column):
            if (Q[i, j] > max):
                max = Q[i, j]
            if (Q[i, j] < min):
                min = Q[i, j]
    for i in range(row):
        for j in range(column):
            Q[i, j] = round((Q[i, j] - min) / (max - min), 4)
    return Q


if __name__ == '__main__':
    cluster = 21  # role:cluster
    base = 4  # agent:base
    L = [20, 31, 22, 27]  # total 114

    #第二次指派Q2
    data = xlrd.open_workbook('G:\Result\Q2.xls')
    tableUAV = data.sheets()[0]
    nrows = tableUAV.nrows  # 表行数
    for i in range(nrows):  # 逐行打印
    	# 跳过第一行
    	if i == 0:
    		continue
    	Q = tableUAV.row_values(i)
    	print(Q)
    Q = getNormalizedQ(Q)
    print(Q)
    temp = []
    T = Assignment.KM(Q, L)
    TMAT = np.array(T)
    print(TMAT)
