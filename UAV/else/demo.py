import math
import matplotlib.pyplot as plt
from matplotlib.ticker import MaxNLocator
from math import floor
import numpy as np
import xlrd
import pandas as pd
import pulp as pl
import pandas as pd
from math import cos, sin, acos, asin, pi, atan, fabs, ceil, floor
from collections import defaultdict
import random
import time

# 散点图
x = [1.61, 1.24, 0.98, 4.66, 6.75, 10.5, 13.81, 17.33, 12.41, 17.09, 2.73, 9.39, 12.44, 17.57, 12.54, 17.22, 8.39, 6.35, 2.28, 2.78, 7.99]
y = [19.53, 14.76, 12.94, 15.11, 15.87, 16.48, 18.55, 17.36, 14.76, 14.79, 11.4, 8.55, 10.19, 8.39, 6.3, 3.41, 3.73, 1.06, 2.8, 6.61, 10.16]
cluster_list = [(1.61, 19.53), (1.24, 14.76), (0.98, 12.94), (4.66, 15.11), (6.75, 15.87), (10.5, 16.48),(13.81, 18.55),(17.33, 17.36),
                (12.41, 14.76), (17.09, 14.79), (2.73, 11.4), (9.39, 8.55), (12.44, 10.19),(17.57, 8.39),(12.54, 6.3), (17.22, 3.41),
                (8.39, 3.73), (6.35, 1.06), (2.28, 2.8), (2.78, 6.61), (7.99, 10.16)]
plt.scatter(x, y, color = "g", s = 20, marker = 'x')
x1 = [18.95, 6.42, 11.52, 16.77]
y1 = [6.92, 9.6, 18.78, 15.33]
base_list = [(18.95, 6.92), (6.42, 9.6), (11.52, 18.78), (16.77, 15.33)]
plt.scatter(x1, y1, color = "red", s = 20, marker = 'x')
plt.xlim(0, 20, 1)
plt.ylim(0, 20, 2)
plt.gca().xaxis.set_major_locator(MaxNLocator(integer=True))   #限制坐标轴刻度为整数
plt.gca().yaxis.set_major_locator(MaxNLocator(integer=True))
plt.show()

# 指派v1.5
# 改成四维 改容量约束 增加批次约束

class Assignment:
    @classmethod
    # maxDrone: the amount of the drones
    def KM(cls, Q, La, L):
        row = len(Q)
        col = len(Q[0])
        k_num = len(Q[0][0])
        b_num = len(Q[0][0][0])
        

        # build a optimal problem
        pro = pl.LpProblem('MIN(connection and coverage )', pl.LpMinimize)
        # build variables for the optimal problem
        lpvars = [[[[pl.LpVariable("x" + str(i) + "y" + str(j) + "k" + str(k) + "b" + str(b), lowBound=0, upBound=1, cat='Integer')for b in range(b_num)]for k in range(k_num)] for j in range(col)]
                  for i in range(row)]

        # build optimal function
        all = pl.LpAffineExpression()
        for i in range(0, row):
            for j in range(0, col):
                for k in range(0,k_num):
                    for b in range(0,b_num):
                        all += Q[i][j][k][b] * lpvars[i][j][k][b]

        pro += all

        # build constraint 
        ## t[i][j]<l for each k,b
        for b in range(0,b_num):
            for k in range(0,k_num):
                tempSum=0
                for i in range(0, row):
                    for j in range(0, col):
                        tempSum +=lpvars[i][j][k][b]
                pro += tempSum ==L[k]
        
        ## when b==0, <la(i)
        for i in range(0,row):
            tempSum=0
            for j in range(0,col):
                for k in range(0,k_num):
                    tempSum +=lpvars[i][j][k][0]
            tempSum <= La[i]
        
        ## when b>0  lost
       
        for b in range(1,b_num):
            for i in range(row):
                tempSumi=0
                tempLost=La[i]
                for j in range(col):
                    for k in range(k_num):
                        tempSumi += lpvars[i][j][k][b]
                        for temp in range(1,b):
                            tempLost = tempLost - lpvars[i][j][k][temp-1] + lpvars[j][i][k][temp-1]
                pro += tempSumi <= tempLost                        
        

        # solve optimal problem
        status = pro.solve()
        # print("Assignment Status: ", pl.LpStatus[status])
        # print("Final Assignment Result", pl.value(pro.objective))

        # get the result of T matrix
        T = [[[[lpvars[i][j][k][b].varValue for b in range(b_num)]for k in range(k_num)] for j in range(col)] for i in range(row)]

        return T
    # return T, pl.value(pro.status), pl.value(pro.objective)


# 生成La矩阵
def genLaMatrix():
  
    return La

# 找到可行的La
def availableLa():
  
    return La


# 计算两点之间距离公式
def calDistance(x1, x2, y1, y2):
    result = math.sqrt((x2-x1)**2+(y2-y1)**2)
    return result

# 聚集点与基站组合距离（21*4的矩阵）
def genDistance(x,y,x1,y1):
    distance_BC = []
    for j in range(base):
        tempMatrix = []
        for i in range(cluster):
            tempMatrix.append(calDistance(x[i],x1[j],y[i],y1[j]))
        distance_BC.append(tempMatrix)
    return distance_BC

# 聚集点与基站组合距离算出Q(21*16的矩阵)
def genQMatrix(distance_BC):
    Q = []
    return Q

# 归一化后的Q
def getNormalizedQ(Q):
    max = min = Q[0][0]
    row = len(Q)
    column = len(Q[0])
    for i in range(row):
        for j in range(column):
            if (Q[i, j] > max):
                max = Q[i, j]
            if (Q[i, j] < min):
                min = Q[i, j]
    for i in range(row):
        for j in range(column):
            Q[i, j] = round((Q[i, j] - min) / (max - min), 4)
    return Q

# 计算基站派出/派回无人机数量
def countdrones(T):
    base_number = 4
    col = 21
    countbase_in = [0,0,0,0]
    countbase_out = [0,0,0,0]
    for i in range(base_number):
        for j in range(col):
            countbase_in[i] += T[i][j] + T[i + 4][j] + T[i + 8][j] + T[i + 12][j]  # eg:base1进基站为j=0,4,8,12
            countbase_out[i] += T[4*i][j] + T[4*i + 1][j] + T[4*i + 2][j] + T[4*i + 3][j]  # eg:base2出基站为j=0,1,2,3
    return countbase_in,countbase_out

# 指派路径
def drawpath(T,x,y,x1,y1):
    T_row = 16
    T_col = 21
    base1_out_x = []
    base1_out_y = []
    base1_in_x = []
    base1_in_y = []
    base2_out_x = []
    base2_out_y = []
    base2_in_x = []
    base2_in_y = []
    base3_out_x = []
    base3_out_y = []
    base3_in_x = []
    base3_in_y = []
    base4_out_x = []
    base4_out_y = []
    base4_in_x = []
    base4_in_y = []

    # out
    for i in range(T_row):
        outbase = math.floor(i/4) + 1
        for j in range(T_col):
            if T[i][j] == 1 and outbase == 1:
                base1_out_x.append((x1[0], x[j]))
                base1_out_y.append((y1[0], y[j]))
            elif T[i][j] == 1 and outbase == 2:
                base2_out_x.append((x1[1], x[j]))
                base2_out_y.append((y1[1], y[j]))
            elif T[i][j] == 1 and outbase == 3:
                base3_out_x.append((x1[2], x[j]))
                base3_out_y.append((y1[2], y[j]))
            elif T[i][j] == 1 and outbase == 4:
                base4_out_x.append((x1[3], x[j]))
                base4_out_y.append((y1[3], y[j]))

    # in
    for i in range(T_row):
        inbase = i - math.floor(i/4) * 4 + 1
        for j in range(T_col):
            if T[i][j] == 1 and inbase == 1:
                base1_in_x.append((x[j], x1[0]))
                base1_in_y.append((y[j], y1[0]))
            elif T[i][j] == 1 and inbase == 2:
                base2_in_x.append((x[j], x1[1]))
                base2_in_y.append((y[j], y1[1]))
            elif T[i][j] == 1 and inbase == 3:
                base3_in_x.append((x[j], x1[2]))
                base3_in_y.append((y[j], y1[2]))
            elif T[i][j] == 1 and inbase == 4:
                base4_in_x.append((x[j], x1[3]))
                base4_in_y.append((y[j], y1[3]))

    return base1_out_x,base1_out_y,base2_out_x,base2_out_y,base3_out_x,base3_out_y,base4_out_x,base4_out_y,\
           base1_in_x,base1_in_y,base2_in_x,base2_in_y,base3_in_x,base3_in_y,base4_in_x,base4_in_y


if __name__ == '__main__':
    base = 4
    outbase = 4
    inbase = 4
    cluster = 21
    ftcharge = 0.75 # 充电45分钟
    fv = 50  # 飞行速度50km/h
    Qmax = ftcharge*fv
    L = [1,2,1,3,3,1,2,2,2,2,1,2,1,2,2,3,2,2,2,3,1]   # total 40
    La = [16,16,16,16]
    capacity = [48, 48, 48, 48]   # 基站初始容量均为48, total 192
    Q=np.random.rand(4,4,16,3)
    
    T1 = Assignment.KM(Q, La, L)
    # T2 = Assignment.KM(QMatrix, La[1], L, outbase, inbase, batch)

    TMAT = np.array(T1)
    # TMAT2 = np.array(T2)
    print(TMAT)
    # print(TMAT2)





