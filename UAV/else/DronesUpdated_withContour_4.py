import matplotlib.pyplot as plt
from pandas import ExcelWriter
import pandas as pd
import numpy as np
from math import cos, sin, acos, asin, pi, atan, fabs, ceil
import csv
from collections import defaultdict
from heapq import *
import networkx as nx
import pulp as pl
from matplotlib.patches import Circle
import random
# add animation effect
from moviepy.video.io.bindings import mplfig_to_npimage
import moviepy.editor as mpy
from moviepy.editor import VideoClip
import time
from matplotlib import colors
from math import ceil
import xlwt

# add the figure 
from pandas import ExcelFile
from pandas import ExcelWriter

# add the jiuzhaigou data
# read data from excel

# 直飞 二维避障 GRA最核心部分

def readLocation(excel_Name, row, col, min_interval, setted_internal, lim_height):
		df = pd.read_excel(excel_Name, sheet_name='Sheet1', header = None)
		data = pd.DataFrame(df)
		step = setted_internal/min_interval
		# judge row len and the column len
		row_len = ceil(row/step)
		col_len = ceil(col/step)
		outputData = list()
		for i in range(row_len):
				for j in range(col_len):
						x_index = i*setted_internal/1000
						y_index = j*setted_internal/1000
						# judge if the point in seven central areas and judge if the height is less than 3000
						if data.loc[i*step][j*step] <= lim_height:
								outputData.append([x_index, y_index, data.loc[i*step][j*step]/1000])
		return outputData

#  save matrix data to excel
def save(data, path):
		# convert list to array
		data = np.array(data)
		f = xlwt.Workbook()  # create the excel 
		sheet1 = f.add_sheet(u'Sheet1', cell_overwrite_ok=True)  # create sheet
		[h, l] = data.shape  # h is the row number, l is the column number
		for i in range(h):
				for j in range(l):
						sheet1.write(i, j, round(data[i, j], 2))
		f.save(path)

# write excel
def writeExcel(excelName, Data):
		df = pd.DataFrame(Data)
		writer = ExcelWriter(excelName)
		df.to_excel(writer, 'Sheet1',index=False, header=['La'])
		writer.save()

#read data from excel that less than 5000 metres
def readData(excel_Name):
		df = pd.read_excel(excel_Name, sheet_name='Sheet1')
		data = pd.DataFrame(df)
		x_list = data['x']
		y_list = data['y']
		height_list = data['height']
		return(x_list, y_list, height_list)            


# sort the point list 
def sortPointbyType(A):
		return A.type

# define the point class
class point:
		x = 0
		y = 0
		radius = 0

		@classmethod
		def getLocation(cls):
				return x,y

# define the drones class
class drone(point):
		type = 0

# draw circle
def drawCircle(centralPoint):
		clr = Circle((centralPoint.x, centralPoint.y), radius = centralPoint.radius, edgecolor='magenta', linestyle= '--', fill = False)
		return clr

# define class function
# Apply KM algorithm to Assign the drones
class Assignment:
		@classmethod
		# maxDrone: the amount of the drones
		def KM(cls, Q, La):
						
						row = len(Q)
						col = len(Q[0])
						# build a optimal problem
						pro = pl.LpProblem('Max(connection and coverage)', pl.LpMinimize)
			
						# build variables for the optimal problem
						lpvars = [[pl.LpVariable("x"+str(i)+"y"+str(j), lowBound =0, upBound = 1, cat='Integer') for j in range(col)] for i in range(row)]
			
						# build optimal function 
						all = pl.LpAffineExpression()
						for i in range(0,row):
								for j in range(0,col):
										all += Q[i][j]*lpvars[i][j]
						pro += all
						
						# build constraint for each role
						for j in range(0,col):    
							pro += pl.LpConstraint(pl.LpAffineExpression([ (lpvars[i][j],1) for i in range(0,row)]) , 0,"L"+str(j),1)

						# build constraint for each agent
						for i in range(0,row):    
							pro += pl.LpConstraint(pl.LpAffineExpression([ (lpvars[i][j],1) for j in range(0,col)]) , -1,"La"+str(i), ceil(La[i]/2))

						# solve optimal problem
						pro.solve() 
						# get the result of T matrix
						T = [[ lpvars[i][j].varValue for j in range(col) ] for i in range(row)]
						return [T,pl.value(pro.objective)]





# generate terminal points from 30 to 120
def  genTerminalPoints(terminalAmount, minValue, maxValue, radius):
		terminalPoints = []
		xList = []
		yList = []
		while(terminalAmount >=1):
				# internal = random.randint(20,50)
				x = random.randrange(minValue, maxValue)
				y = random.randrange(minValue, maxValue)
				if x not in xList and y not in yList:
						if len(xList) == 0:
								p = point()
								xList.append(x)
								yList.append(y)
								p.x = x
								p.y = y
								p.radius = radius
								terminalPoints.append(p)
								terminalAmount-=1
						else: 
								p = point()
								for temp in terminalPoints:
										# times = random.randint(1,2)
										if calDistance(x,y, temp.x, temp.y) > 0:
												xList.append(x)
												yList.append(y)
												p.x = x
												p.y = y
												p.radius = radius
												terminalPoints.append(p)
												terminalAmount-=1
												break
		return xList, yList, terminalPoints

# calculate the Euclidean distance
def calDistance(x1, y1, x2, y2):
		result = np.sqrt(np.square(x1-x2) + np.square(y1-y2))
		return result

# generate the La matrix
def genLaMatrix(role_amount, agent_amount):
		res = []
		temp = agent_amount
		while(temp>=1):
				val = ceil(role_amount/agent_amount)
				val += random.randint(1, agent_amount) 
				if val not in res:
						res.append(val)
						temp-=1
		return res

				

# generate the drone factories point
def  genFactoriesPoints(factoriesAmount, minValue, maxValue):
		factoriesPoints = []
		xList = []
		yList = []
		while(factoriesAmount >=1):
				# internal = random.randint(20,50)
				x = random.randrange(minValue, maxValue)
				y = random.randrange(minValue, maxValue)
				if x not in xList and y not in yList:
						if len(xList) == 0:
								p = point()
								xList.append(x)
								yList.append(y)
								p.x = x
								p.y = y
								p.radius = radius
								factoriesPoints.append(p)
								factoriesAmount-=1
						else: 
								p = point()
								for temp in factoriesPoints:
										if calDistance(x,y, temp.x, temp.y) >= (maxValue-minValue)/2:
												xList.append(x)
												yList.append(y)
												p.x = x
												p.y = y
												p.radius = radius
												factoriesPoints.append(p)
												factoriesAmount-=1
												break
		return xList, yList, factoriesPoints

# Generate the vertexs
def genVertexes(vertexLength):
		vertex = list()
		for i in range(0, vertexLength):
				vertex.append(str(i))
		return vertex

# Generate the vertexs
def genVertexs(vertexLength):
		vertex = list()
		for i in range(0, vertexLength):
				vertex.append(i)
		return vertex

# Generate the adjacent matrix 
def genAdjMatrix(xList,  yList):
		adjMatrix = []
		for i in range(len(xList)):
				# create the Lower triangular matrix
				tempMatrix = [0]*i
				for j in range(i, len(xList)):
						tempMatrix.append(calDistance(xList[i], yList[i], xList[j], yList[j]))
				adjMatrix.append(tempMatrix)
		return adjMatrix


# Genereate the edges(from location to distance)
def genEdges(vertexs, adjMatrix):
		edgeLists = []
		for i in range(0, len(adjMatrix)):
				for j in range(i+1, len(adjMatrix[i])):
						edgeLists.append((vertexs[i], vertexs[j], adjMatrix[i][j]))
		return edgeLists


# transform label from text to int 
def transEdges(labelEdges):
		resEdges = []
		for i in range(0, len(labelEdges)):
				x = int(labelEdges[i][0])
				y = int(labelEdges[i][1])
				weight = labelEdges[i][2]
				resEdges.append((x, y, weight))
		return resEdges

# get location sets
def getLocation(xList, yList):
		locList = []
		for i in range(0, len(xList)):
				locList.append((xList[i], yList[i]))
		return locList


# prim algorithm 
# input: points and edges 
def prim(vertexs, edges):
		adjacent_vertex = defaultdict(list)      
		"""
		The input of defaultdict must be a list variable
		"""
		for v1,v2,length in edges:
				adjacent_vertex[v1].append((length, v1, v2))
				adjacent_vertex[v2].append((length, v2, v1))

		mst = []        #save the result of the Minimum spanning tree

		chosed = set(vertexs[0])    # chosed vertex set

		# the edges of the first selected adjacent vertexs 
		adjacent_vertexs_edges = adjacent_vertex[vertexs[0]]  
		
		# transform  a list into a heap 
		heapify(adjacent_vertexs_edges)

		while adjacent_vertexs_edges:
				# choose the smallest edge and pop it from the heap
				w, v1, v2 = heappop(adjacent_vertexs_edges)     
				if v2 not in chosed:
						chosed.add(v2)                          
						mst.append((v1,v2,w))   
						# insert the adjacent edges of v2 into the heap
						for next_vertex in adjacent_vertex[v2]:                    
								if next_vertex[2] not in chosed:
										heappush(adjacent_vertexs_edges, next_vertex)
		return mst

# get the location of the roles 
def evaluateDrones_updated(A, B, rDrone):
		resPoint = []   # the point sets of the drones
		centerPoint = point()
		dDrone = 2*rDrone   #the cover diameter of the drone
		rt = A.radius    # the cover radius of the terminal
		terminalDistance = calDistance(A.x, A.y, B.x, B.y)
		drones_Number  = ceil((terminalDistance+2*rt)/dDrone)
		# if two terminal can be connected by drones 
		if A.y-B.y == 0: # on one line
				cosA = 1
				sinA = 0
		elif A.x-B.x == 0:
				cosA = 0
				sinA = 1
		else:
				angleA = atan((A.y - B.y)/(A.x - B.x))
				cosA = cos(angleA)
				sinA = sin(angleA)        
		#even amount of the drones
		# centerPoint
		centerPoint.x = (A.x+B.x)/2
		centerPoint.y = (A.y+B.y)/2
		centerPoint.radius = rDrone
		if drones_Number%2 == 1:
				resPoint.append(centerPoint)
				stopFlag = (drones_Number-1)/2
				step = 0
				# get otherPoint, because of the symmertry
				while (stopFlag > 0):
						step += 1
						left = point()
						right = point()
						left.radius = right.radius = rDrone
						left.x = centerPoint.x - step*dDrone*cosA
						left.y = centerPoint.y - step*dDrone*sinA
						right.x = centerPoint.x + step*dDrone*cosA
						right.y = centerPoint.y + step*dDrone*sinA
						resPoint.append(left)
						resPoint.append(right)
						stopFlag -= 1
		else:
				stopFlag = (drones_Number-1)/2
				step = 0
				# get otherPoint, because of the symmertry
				while (stopFlag > 0):
						left = point()
						right = point()
						left.radius = right.radius = rDrone
						left.x = centerPoint.x - rDrone*cosA - step*dDrone*cosA
						left.y = centerPoint.y - rDrone*sinA - step*dDrone*sinA
						right.x = centerPoint.x + rDrone*cosA + step*dDrone*cosA
						right.y = centerPoint.y + rDrone*sinA + step*dDrone*sinA
						resPoint.append(left)
						resPoint.append(right)
						step += 1
						stopFlag -= 1
		return  resPoint


# evalue the max distance between the 

# Q[i][j]
#  a, b are the weight of connection and coverage
def evaluateQValue(factory, role, maxDistance, minDistance):
		QValue = abs(calDistance(factory.x, factory.y, role.x, role.y)-minDistance)/(maxDistance-minDistance)
		return QValue

def findMaximumDistance(factories, roles):
		maxDistance = 0 
		for factory in factories:
				for role in roles:
						maxDistance = max(maxDistance, calDistance(factory.x, factory.y, role.x, role.y))
		return maxDistance

def findMinimumDistance(factories, roles):
		maxDistance = 0 
		for factory in factories:
				for role in roles:
						maxDistance = min(maxDistance, calDistance(factory.x, factory.y, role.x, role.y))
		return maxDistance


# generate Q Matrix
def genQMatrix(factories, roles):
		QMatrix = []
		maxDistance = findMaximumDistance(factories, roles)
		minDistance = findMinimumDistance(factories, roles)
		for factory in factories:
				temp = []
				for role in roles :
						QValue = evaluateQValue(factory, role, maxDistance, minDistance)
						temp.append(QValue)
				QMatrix.append(temp)
		return QMatrix


if __name__ == "__main__":
		# add jiuzhaigou data 
		row = 2774
		col = 2913
		min_interval = 38.2
		step = 50
		setted_interval = min_interval*step
		lim_height = 5000
		# set up the drone shape from different Bases
		baseShape = ["p", "^", "D", "*", "P", "X", "d", "v"]
		# set up the terminal and the drone factories
		minValue = 6
		maxValue = 88
		radius = 3
		terminal_amount = 10
		factories_amount = 8
		# read and deal jiuzhaigou data
		x, y, heights = readData('Lessthan_points_5000.xlsx')
		# split heights into two weights
		col_len = ceil(col/step)
		Heights = []
		temp = 0
		h = []
		x_List = []
		y_List = []
		for i in range(len(heights)):
				h.append(heights[i])
				temp+=1
				if(temp==col_len):
						x_List.append(x[i])
						Heights.append(h)
						h = []
						temp = 0
		for j in range(len(Heights[0])):
				y_List.append(y[j])
		# generate grid data
		X, Y = np.meshgrid(y_List, x_List)
		terminalXList, terminalYList, points = genTerminalPoints(terminal_amount, minValue, maxValue, radius)
		factoriesXList, factoriesYList, factories = genFactoriesPoints(factories_amount, minValue, maxValue)

		# using process the contorl the display
		process = 0
		duration = 10 # the time of the gif
		t_former  = 0 #time stamp

		# initialize the figure
		fig, ax = plt.subplots(1, figsize=(13, 10), facecolor=(1,1,1))
		fig.subplots_adjust(left=0, right=1, bottom=0, top=1)

		# construct Minimum spanning tree algorithm    
		vertexs = genVertexes(terminal_amount)
		vertexs_Int = genVertexs(terminal_amount)
		adjMatrix = genAdjMatrix(terminalXList, terminalYList)
		edges = genEdges(vertexs, adjMatrix)
		mst = prim(vertexs, edges) 
		mst_IntLabel = transEdges(mst)
		locList = getLocation(terminalXList, terminalYList)


		roles = []
		for temp in mst_IntLabel:
				roles += evaluateDrones_updated(points[temp[0]], points[temp[1]], radius)
		



		# agent constains matrix 
		roles_amount = len(roles)
		La = genLaMatrix(roles_amount, factories_amount)

		# use E-CARGO model to solve the problems
		QMatrix = genQMatrix(factories, roles)
		TMatrix,result = Assignment.KM(QMatrix, La)
		La.append(result)
		# save Q, T and La
		writeExcel("/Users/<USER>/Downloads/La_3.xlsx", La)
		save(QMatrix, "/Users/<USER>/Downloads/QMatrix_3.xls")
		save(TMatrix, "/Users/<USER>/Downloads/TMatrix_3.xls") 


		# store the result 
		dronePoints = []
		for i in range(len(TMatrix)):
				for j in range(len(TMatrix[0])):
						temp = drone()
						if(TMatrix[i][j]==1):
								temp.x = roles[j].x
								temp.y = roles[j].y
								temp.radius = radius
								temp.type = i
								dronePoints.append(temp)
		
		# get the location of the drone sets
		dronesXList = [dronePoints[i].x for i in range(len(dronePoints))]
		dronesYList =  [dronePoints[i].y for i in range(len(dronePoints))]
		colorList = [dronePoints[i].type for i in range(len(dronePoints))]
		labelList = ['Base '+ str(dronePoints[i].type) for i in range(len(dronePoints))]
		display_X = []
		display_Y = []
		for i in range(factories_amount):
				display_X.append([dronePoints[p].x for p in range(len(dronePoints)) if dronePoints[p].type == i])
				display_Y.append([dronePoints[p].y for p in range(len(dronePoints)) if dronePoints[p].type == i])

		def make_frame(t):
				global t_former
				global process
				global duration
				global mst_IntLabel
				global vertexs_Int
				global dronePoints
				global dronesXList
				global dronesYList
				global display_X
				global display_Y
				global factoriesXList
				global factoriesYList
				global ax
				global fig
				global X
				global Y
				global Heights


				# use t_former stamp to control the picture last for 2 seconds
				if(t-t_former>=4):
						t_former =  t
						process+=2
				ax.clear()
				# draw contour color
				if process <4: 
						ax.contourf(X, Y, Heights, 6, alpha = 0.1, cmap = plt.cm.rainbow)           
						# draw contour
						C = ax.contour(X, Y, Heights, 6, colors = 'black')
						# draw contour data
						ax.clabel(C, inline = True, fontsize = 10)
						# plt.figure()
						G = nx.Graph()
						G.add_nodes_from(vertexs_Int)
						G.add_weighted_edges_from(mst_IntLabel)
						# generate the original communication mobile
						nx.draw_networkx_nodes(G, pos = locList,  node_color = 'green', node_size = 100, label = 'Communication vehicle')
				else:
						ax.contourf(X, Y, Heights, 6, alpha = 0., cmap = plt.cm.rainbow)           
						# draw contour
						C = ax.contour(X, Y, Heights, 6, alpha = 0,colors = 'black')
						# draw contour data
						ax.clabel(C, inline = True, fontsize = 10)
						# plt.figure()
						G = nx.Graph()
						G.add_nodes_from(vertexs_Int)
						G.add_weighted_edges_from(mst_IntLabel)
						ax.scatter(factoriesXList, factoriesYList,  s = 100, marker = 's',  c  = 'red', label = "Bases") 
						# generate the original communication mobile
						nx.draw_networkx_nodes(G, pos = locList,  node_color = 'green', node_size = 50, label = 'Communication vehicle')    
				if  process == 2:
						nx.draw_networkx_edges(G, pos = locList,  edge_color = 'darkturquoise', width = 5, label = 'Central distance')
				if process >= 4:
						# nx.draw(G, pos = locList, edge_color = 'turquoise',  node_color = 'b', node_size = 10, width = 2, label = 'communication vehicle') 
						nx.draw_networkx_edges(G, pos = locList,  edge_color = 'darkturquoise', width = 4, label = 'Central distance')
						for i in range(len(dronePoints)):
								clr = drawCircle(dronePoints[i])
								ax=plt.gca()
								ax.add_patch(clr)
						for i in range(len(display_X)):
								ax.scatter(display_X[i], display_Y[i],  s = 60, c = 'b', marker = baseShape[i],  label = 'Drones from Base '+ str(i+1))
				ax.scatter(factoriesXList, factoriesYList,  s = 150, marker = 's',  c  = 'red', label = "Bases") 
				ax.legend(fontsize='medium')
				return mplfig_to_npimage(fig)

		animation = VideoClip(make_frame, duration = 20)
		animation.write_gif("simpleDrones_final_3.gif", fps=5)   
