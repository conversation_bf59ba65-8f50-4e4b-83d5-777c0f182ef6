import math

import pulp as pl
import numpy as np


class GRA:
    def __init__(self, agent, role, qualification, role_range_vector, ability_limit):
        self.agent = agent
        self.role = role
        self.role_range_vector = role_range_vector
        self.qualification = qualification
        self.ability_limit = ability_limit

    def assignment(self, optimal_qualification_matrix, base_value):
        if optimal_qualification_matrix is None:
            optimal_qualification_matrix = self.qualification

        row, col = len(optimal_qualification_matrix), len(optimal_qualification_matrix[0])
        # build a optimal problem
        model = pl.LpProblem('HCEDP', pl.LpMinimize)
        var_t = [[pl.LpVariable("x" + str(i) + "y" + str(j), lowBound=0,
                                upBound=min(self.role_range_vector[j], self.ability_limit[i]), cat='Integer')
                  for j in range(col)]
                 for i in range(row)]

        # build optimal function
        object_function = pl.LpAffineExpression()
        if base_value is None:
            for i in range(0, row):
                for j in range(0, col):
                    object_function += optimal_qualification_matrix[i][j] * var_t[i][j]
        else:
            for i in range(0, row):
                for j in range(0, col):
                    object_function += ((optimal_qualification_matrix[i][j] - base_value) ** 2 / row) * var_t[i][j]

        model += object_function
        # build constraint for each role
        for j in range(0, col):
            model += pl.LpConstraint(pl.LpAffineExpression([(var_t[i][j], 1) for i in range(0, row)]), 0, "L" + str(j),
                                     self.role_range_vector[j])

        # build constraint for each agent
        for i in range(0, row):
            model += pl.LpConstraint(pl.LpAffineExpression([(var_t[i][j], 1) for j in range(0, col)]), -1,
                                     "La" + str(i), self.ability_limit[i])

        model.solve()
        res_t = [[var_t[i][j].varValue for j in range(col)] for i in range(row)]

        return [res_t, pl.value(model.status), pl.value(model.objective)]
