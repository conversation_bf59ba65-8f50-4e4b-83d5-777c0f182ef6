#!/usr/bin/python3

import pandas as pd
import numpy as np
from math import cos, sin, acos, asin, pi, atan, fabs, ceil, floor
from collections import defaultdict
import pulp as pl
from pulp import lpSum,LpVariable, pContinuous
import random
import time


# GRACCF的约束条件
class Assignment:
    @classmethod
    # maxDrone: the amount of the drones
    def KM(cls, Q, L, interval_time):
        row = len(Q)
        col = len(Q[0])

        # build a optimal problem
        pro = pl.LpProblem('Max(connection and coverage)', pl.LpMinimize)
        # build variables for the optimal problem
        lpvars = [[pl.LpVariable("x" + str(i) + "y" + str(j), lowBound=0, upBound=1, cat='Integer') for j in range(col)]
                  for i in range(row)]

        # build optimal function
        all = pl.LpAffineExpression()
        for i in range(0, row):
            for j in range(0, col):
                all += Q[i][j] * lpvars[i][j]

        pro += all
        # build constraint for each role

        for j in range(0, col):
            pro += pl.LpConstraint(pl.LpAffineExpression([(lpvars[i][j], 1) for i in range(0, row)]), 0, "L" + str(j),
                                   L[j])

        # build constraint for each agent
        for i in range(0, row):
            pro += pl.LpConstraint(pl.LpAffineExpression([(lpvars[i][j], 1) for j in range(0, col)]), -1, "La" + str(i),
                                   1)

        sum1 = 0
        for j in range(len(interval_time)):
            for i in range(interval_time[j][0] + 1):
                sum1 += lpvars[i][j]
            pro += sum1 >= 1
            sum1 = 0

        sum2=0
        for j in range(len(interval_time)):
            for i in range(row-interval_time[j][1]):
                for k in range(i,i+interval_time[j][1]):
                    sum2+=lpvars[k][j]
                pro+=sum2>=1
                sum2=0


        '''for j in range(len(interval_time)):
            for i in range(row - interval_time[j][1]):
                pro += lpSum([lpvars[k][j] for k in range(i, i + interval_time[j][1] + 1) if lpvars[k][j] == 1]) >= 2
              '''

        ''''
        A=[]
        # solve optimal problem
        for i in range(row):
            for j in range(col):
                if lpvars[i][j]==1:
                    A.append([i,j])
                    print('11111111111111111111111111')
        #A=LpVariable.dicts('r',[[i,j] for j in range(col) for i in range(row) if lpvars[i][j]==1])]

        print(A)
        sum2=0
        for j in range(col):
            for i in range(sum2,sum2+L[j]):
                if i<sum2+L[j]-1:
                  pro+=(A[i+1][0]-A[i][0])<=interval_time[j][1]
                  print(pro)
                if i==sum2+L[j]-1:
                    pro+=(row-A[i][0])<Interval_time[j][1]
                    print(pro)
            print('222222222222222222222222222222')
            sum2+=L[j]

        '''
        status = pro.solve()
        print("Assignment Status: ", pl.LpStatus[status])
        print("Final Assignment Result", pl.value(pro.objective))

        # get the result of T matrix
        T = [[lpvars[i][j].varValue for j in range(col)] for i in range(row)]
        return [T, pl.value(pro.status), pl.value(pro.objective)]


# 生成Q矩阵
def getQMat(agentNum, roleNum, agentType, electricityPrice, power):
    Q = np.zeros((agentNum, roleNum))
    for i in range(agentNum):
        for j in range(roleNum):
            if agentType[i] == 0:
                Q[i, j] = electricityPrice[0] * power[j] * 0.5
            if agentType[i] == 1:
                Q[i, j] = electricityPrice[1] * power[j] * 0.5
            if agentType[i] == 2:
                Q[i, j] = electricityPrice[2] * power[j] * 0.5
    return Q


# 归一化后的Q
def getNormalizedQ(Q):
    max = min = Q[0][0]
    row = len(Q)
    column = len(Q[0])
    for i in range(row):
        for j in range(column):
            if (Q[i, j] > max):
                max = Q[i, j]
            if (Q[i, j] < min):
                min = Q[i, j]
    for i in range(row):
        for j in range(column):
            Q[i, j] = round((Q[i, j] - min) / (max - min), 2)
    return Q


if __name__ == '__main__':
    condenserNum = 6
    timeSlice_num = 48
    power = [3500, 2500, 4000, 3800, 4200, 3200]
    L = [3, 2, 4, 3, 4, 2]

    Lmax = [5, 4, 5, 5, 5, 4]
    electricityPrice = [0.425, 0.725, 1.025]
    timeSlice = [[14, 0], [4, 1], [28, 2], [2, 1]]  # [14,0]means the first time is 低锋
    #Interval_time = [[8, 20], [15, 35], [6, 14], [14, 20], [7, 15], [13, 30]]
    Interval_time = [[8, 16], [15, 24], [6, 12], [14, 16], [7, 12], [13, 24]]
    agentType = np.zeros(timeSlice_num)
    sum = 0
    for i in range(len(timeSlice)):
        for j in range(timeSlice[i][0]):
            agentType[sum + j] = timeSlice[i][1]
        sum += timeSlice[i][0]

    Q = getQMat(timeSlice_num, condenserNum, agentType, electricityPrice, power)
    print(Q)
    Q = getNormalizedQ(Q)
    print(Q)
    T, status, p = Assignment.KM(Q, L, Interval_time)
    A = []
    for i in range(timeSlice_num):
        for j in range(condenserNum):
            if T[i][j] == 1:
                A.append([i, j])
    print(len(A))
    print(A)
    P = np.array(T)
    data1 = pd.DataFrame(P)

    writer = pd.ExcelWriter('t.xlsx')  # 写入Excel文件
    data1.to_excel(writer, 'T', float_format='%.5f')  # ‘page_1’是写入excel的sheet名

    writer.save()

    writer.close()


