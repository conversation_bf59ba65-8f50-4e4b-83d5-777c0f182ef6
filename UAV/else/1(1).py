# encoding:utf-8

import random


class Flow:
    def __init__(self):
        self.linkID = 0
        self.width = 0
        self.flow = [[[0.0 for x in range(5)] for x in range(4)] for x in range(3)]


f = [Flow() for x in range(3)]  # 构建3个具有Flow类型

# 初始化赋值
for i in range(3):
    f[i].linkID = random.randint(0, 16)
    f[i].width = random.randint(0, 8)
    for j in range(5):
        for k in range(4):
            for n in range(3):
                f[i].flow[n][k][j] = random.randint(0, 12)


for i in range(3):
    print('当前ID:%d,当前with:%d,当前flow:%d' % (f[i].linkID, f[i].width, f[i].flow[0][1][2]))
    if i > 0:
        print('上一个ID:%d,上一个with:%d,上一个flow:%d' % (f[i - 1].linkID, f[i - 1].width, f[i - 1].flow[0][1][2]))
