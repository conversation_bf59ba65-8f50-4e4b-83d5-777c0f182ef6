# import numpy as np
#
# def recurGRACCF_Mat(matLength):
# 	resMat = np.zeros((matLength, matLength))
# 	resMat[1][5]=-0.3;resMat[1][6]=0.35;resMat[1][7]=0.35;resMat[1][20]=-0.4;resMat[1][50]=0.8;resMat[1][51]=0.9;
#
# 	resMat[2][5]=-0.2;resMat[2][6]=-0.2;resMat[2][7]=0.2;resMat[2][20]=-0.5;resMat[2][50]=0.5;resMat[2][51]=0.6;
#
# 	resMat[5][1]=-0.2;resMat[5][2]=0.2;resMat[5][17]=0.2;resMat[5][18]=0.2;resMat[5][19]=0.3;resMat[5][44]=-0.3;resMat[5][46]=0.35;resMat[5][50]=0.7;resMat[5][51]=0.6;
#
# 	resMat[6][1]=-0.35;resMat[6][2]=-0.2;resMat[6][17]=0.2;resMat[6][18]=0.2;resMat[6][19]=0.3;resMat[6][44]=-0.2;resMat[6][46]=-0.2;
#
# 	resMat[7][1]=0.35;resMat[7][2]=0.4;resMat[7][17]=0.2;resMat[7][18]=0.2;resMat[7][19]=0.2;resMat[7][44]=-0.3;resMat[6][46]=0.35;
#
# 	resMat[17][5]=0.2;resMat[17][6]=0.2;resMat[17][7]=0.3;resMat[17][20]=-0.5;resMat[17][21]=-0.4;resMat[17][23]=-0.3;resMat[17][50]=0.6;resMat[17][51]=0.7;
#
# 	resMat[18][5]=0.2;resMat[18][6]=0.2;resMat[18][7]=0.3;resMat[18][20]=-0.4;resMat[18][21]=-0.45;resMat[18][23]=-0.3;
#
# 	resMat[19][5]=0.2;resMat[19][6]=0.2;resMat[19][7]=0.2;resMat[19][20]=-0.2;resMat[19][21]=-0.2;resMat[19][23]=-0.3;
#
# 	resMat[20][17]=0.3;resMat[20][18]=0.2;resMat[20][19]=0.3;resMat[20][50]=0.8;resMat[20][51]=0.7;
#
#
# 	resMat[21][17]=-0.4;resMat[21][18]=-0.45;resMat[21][19]=-0.2;resMat[21][50]=0.6;resMat[21][51]=0.5;
#
# 	resMat[23][17]=-0.2;resMat[23][18]=-0.2;resMat[23][19]=-0.3;
#
# 	resMat[44][1]=0.3;resMat[44][2]=0.2;resMat[44][5]=-0.3;resMat[44][6]=0.35;resMat[44][7]=0.35;resMat[44][17]=0.3;resMat[44][18]=0.2;resMat[44][19]=0.1;resMat[44][20]=-0.5;resMat[44][21]=-0.4;
# 	resMat[44][23]=-0.3;resMat[44][50]=0.7;resMat[44][51]=0.7;
#
# 	resMat[46][5]=-0.2;resMat[46][6]=-0.2;resMat[46][7]=0.2;resMat[46][50]=0.6;resMat[46][51]=0.6;
#
# 	resMat[50][20]=-0.4;resMat[50][44]=0.8;resMat[50][46]=0.6;
#
# 	resMat[51][20]=-0.3;resMat[51][44]=0.8;resMat[51][46]=0.6;
#
# 	return resMat
#
# if __name__ == '__main__':
#     positionType = 4
#     empolyeeNum = 13
#     matLength = positionType * empolyeeNum
#     resMat = recurGRACCF_Mat(matLength)


# def deco(taskcode):
# 	UandT = [[] for k in range(numU)]  # 存储每架无人机需要执行的任务
# 	# 为无人机分配任务
# 	for i in range(numT):
# 		for j in range(numU):
# 			if int(taskcode[i]) == j:
# 				# a = 0  # 设置标志位判断当前值是否为列表中第一个元素
# 				for l in range(len(UandT[j])):
# 					if taskcode[i] < taskcode[l]:
# 						UandT[j].insert(l, i)  # 为任务进行排序
# 						# a = 1
# 						break
# 	return UandT
#
#
# if __name__ == '__main__':
numU = 7
numT = 5
taskcode = [0, 1, 2, 3, 4]
# UandT = deco(taskcode)
# print(UandT)

UandT = [[] for k in range(numU)]  # 存储每架无人机需要执行的任务
for i in range(numT):
	for j in range(numU):
		if int(taskcode[i]) == j:
			UandT[j].append(i)
			break
