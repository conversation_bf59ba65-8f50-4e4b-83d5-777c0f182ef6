import matplotlib.pyplot as plt
from matplotlib.ticker import MaxNLocator
import numpy as np
import xlrd
import pulp as pl
import pandas as pd
from math import cos, sin, acos, asin, pi, atan, fabs, ceil, floor
from collections import defaultdict
import random
import time
import math

def tu_diagram(time_mat, fitness_mat):
	plt.plot(time_mat, fitness_mat_30, c='b', lw=2, label='Pop-Size=30')
	plt.legend()
	plt.title('fitness-time', fontsize=20)
	plt.xlabel('time', fontsize=14)
	plt.ylabel('fitness', fontsize=14)
	plt.tick_params(axis='both', labelsize=13)
	# plt.savefig("tu_diagram.png", dpi=800)
	plt.show()