# -*- coding: utf-8 -*-
"""
文件目的：所有全局变量，初始变量

@author: liu
"""
import numpy as np
from math import ceil
import matplotlib.pyplot as plt
from matplotlib.ticker import MaxNLocator
import numpy as np
import xlrd
import pandas as pd
import pulp as pl
import pandas as pd
from math import cos, sin, acos, asin, pi, atan, fabs, ceil, floor
# from collections import defaultdict
import random
import time
from gurobipy import *


#无人机/目标点坐标（随机分布）
'''
#情景一：
UAV_start = np.array([[684,536],[709,548],[737,548],[761,532]])

target_pos = np.array([[691,243],[868,300],[785,413],[569,324],[766,167],[938,213]])
'''

#情景二：
# UAV_start = np.random.uniform(100, 200, (8, 2))
#
# target_pos = np.random.uniform(300, 700, (20, 2))


#情景三：4基站、22聚集点
# 无人机相关参数设置
#随机生成La
def genLaMatrix(role_amount, agent_amount):
		La = []
		temp = agent_amount
		while(temp>=1):
				val = ceil(role_amount/agent_amount)
				val += random.randint(1, agent_amount)
				if val not in La:
						La.append(val)
						temp-=1
		return La

base = 4
cluster = 22
combination = 16
L = [1, 2, 1, 3, 3, 1, 2, 2, 2, 2, 1, 2, 1, 2, 2, 3, 2, 2, 2, 3, 1, 3]  # 聚集点需求total：43
L_sum = sum(L)
La = genLaMatrix(L_sum, base)
La_sum = sum(La)
capacity = [i * 3 for i in La]
ftcharge = 0.75  # 充电45分钟
fv = 50  # 飞行速度50km/h
Qmax = ftcharge * fv

# 先随机生成基站位置和算出La，再算出无人机位置
def genUAVpos():
    base_start = np.random.uniform(0,20,(base,2))
    temp = 0
    tempsum = []
    UAV_start = []
    for i in range(0,len(La)):
        temp = La[i]
        tempsum = np.tile(base_start[i], (La[i], 1))
        tempsum = np.array(tempsum)
        UAV_start.append(tempsum)
    return UAV_start

# 目标点坐标
def genclpos():
    target_repos = np.random.uniform(0, 20, (cluster, 2))
    temp = 0
    tempsum = []
    target = []
    for i in range(0,len(L)):
        temp = L[i]
        tempsum = np.tile(target_repos[i], (L[i], 1))
        tempsum = np.array(tempsum)
        target.append(tempsum)
    return target

UAV_start = genUAVpos()
UAV_start_pos = np.concatenate((UAV_start[0], UAV_start[1], UAV_start[2], UAV_start[3]))  # 合并成数组
target = genclpos()
target_pos = np.concatenate((target[0], target[1], target[2], target[3], target[4], target[5], target[6],
                             target[7], target[8], target[9], target[10], target[11], target[12], target[13],
                             target[14], target[15], target[16], target[17], target[18], target[19], target[20],
                             target[21]))  # 合并成数组

#无人机/目标点数目
La = genLaMatrix(L_sum,4)
numU = sum(La)
numT = sum(L)

#无人机任务执行时间
# timeu = np.random.uniform(50, 80, (numU, numT))
timeu = 0

#无人机充电时间，单位h
ftcharge = 0.75 * 3600

#无人机飞行速度，单位km/h
flyv = 50

#最大飞行半径,单位m
max_flight = ftcharge * flyv * 1000
# max_flight = 1

#种群数量
population_size = 50

#最大迭代时间，单位s
max_time = 0.1

#迭代次数
max_steps = 1000