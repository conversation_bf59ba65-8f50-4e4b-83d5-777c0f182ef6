import numpy as np

# b_num = 3
# numU = 3
# TMatrix = [[0 for i in range(numU)]for b in range(b_num)]
# for b in range(0,b_num):
#     TMatrix[b] = [0,1,1]
#
# temp = []
# T_sum = [[0 for i in range(numU)]for b in range(b_num)]
# for b in range(0, b_num):
#     temp[b] = np.sum(TMatrix[b], axis=0)
#     # temp = np.array(temp)
#     T_sum[b].append(temp[b])
#
# temp_cont = []
# for b in range(0, b_num):
#     for i in range(numU):
#         if T_sum[b][i] != 0:
#             temp_cont[b] += 1


# cost_sum = 0
# UandT = deco(taskcode[k])  # 每架无人机分配了哪些任务
La = [1,2,1,2]
numT = 5
numU = 6
base = 4
UandT = [[0],[],[1],[],[2],[3]]

outUAV = [0 for i in range(base)]
for p in range(numU):  # 计算每个基站当前派出无人机数
    if p in range(0, La[0]):
        if UandT[p] != []:
            outUAV[0] += 1
    elif p in range(La[0], La[0]+La[1]):
        if UandT[p] != []:
            outUAV[1] += 1
    elif p in range(La[0]+La[1], La[0]+La[1]+La[2]):
        if UandT[p] != []:
            outUAV[2] += 1
    elif p in range(La[0]+La[1]+La[2], La[0]+La[1]+La[2]+La[3]):
        if UandT[p] != []:
            outUAV[3] += 1

print(outUAV)

empty_UAV = outUAV  # 每个基站派出的无人机数 = 基站当前剩余可充电空位

distance_BC = [[1,1,1,2],
               [2,3,2,1],
               [2,3,2,1],
               [1,2,3,1]]
distance_BC_min = np.min(distance_BC, axis = 0)
distance_BC_min2 = np.partition(distance_BC, kth = 1, axis = 0)[1]
distance_BC_max2 = np.partition(distance_BC, kth = -2, axis = 0)[-2]
distance_BC_max = np.partition(distance_BC, kth = -1, axis = 0)[-1]

inUAV= [0 for i in range(base)]
for i in range(numU):
    UAV = UandT[i]  # 第i架无人机需要执行的任务
    print(UAV)
#     cost = 0
#     if len(UAV) == 0:  # 无人机没有任务
#         inUAV[i] = 0
#     pass
#
#     elif len(UAV) == 1:  # 无人机有一个任务
#     for t in range(base):
#         print(distance_BC[t][UAV[0]])
#         break

        # if distance_BC[t][UAV[0]] == distance_BC_min[UAV[0]] and empty_UAV[t] > 0:
        #     inUAV[t] = t + 1
        #     empty_UAV[t] = empty_UAV[t] - 1
        # elif distance_BC[t][UAV[0]] == distance_BC_min2[UAV[0]] and empty_UAV[t] > 0:
        #     inUAV[t] = t + 1
        #     empty_UAV[t] = empty_UAV[t] - 1
        # elif distance_BC[t][UAV[0]] == distance_BC_max2[UAV[0]] and empty_UAV[t] > 0:
        #     inUAV[t] = t + 1
        #     empty_UAV[t] = empty_UAV[t] - 1
        # elif distance_BC[t][UAV[0]] == distance_BC_max[UAV[0]] and empty_UAV[t] > 0:
        #     inUAV[t] = t + 1
        #     empty_UAV[t] = empty_UAV[t] - 1


    # else:  # 无人机有超过一个任务
    #     for t in range(base):
    #         if distance_BC[t][UAV[len(UAV) - 1]] == distance_BC_min[UAV[len(UAV) - 1]] and \
    #                 empty_UAV[t] > 0:  # 返回其他基站(比较是否是最短距离且判断基站是否有空位)
    #             inUAV[i] = t + 1
    #             empty_UAV[t] = empty_UAV[t] - 1
    #         elif distance_BC[t][UAV[len(UAV) - 1]] == distance_BC_min2[UAV[len(UAV) - 1]] and \
    #                 empty_UAV[t] > 0:
    #             inUAV[i] = t + 1
    #             empty_UAV[t] = empty_UAV[t] - 1
    #         elif distance_BC[t][UAV[len(UAV) - 1]] == distance_BC_max2[UAV[len(UAV) - 1]] and \
    #                 empty_UAV[t] > 0:
    #             inUAV[i] = t + 1
    #             empty_UAV[t] = empty_UAV[t] - 1
    #         elif distance_BC[t][UAV[len(UAV) - 1]] == distance_BC_max[UAV[len(UAV) - 1]] and \
    #                 self.empty_UAV[t] > 0:
    #             inUAV[i] = t + 1
    #             empty_UAV[t] = empty_UAV[t] - 1


# print(empty_UAV)

