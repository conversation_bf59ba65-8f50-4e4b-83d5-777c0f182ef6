import matplotlib.pyplot as plt
from matplotlib.ticker import MaxNLocator
import numpy as np
import xlrd
import pulp as pl
import pandas as pd
from math import cos, sin, acos, asin, pi, atan, fabs, ceil, floor
from collections import defaultdict
import random
import time
import math

#合并PSO 2022.9.22
#2022.9.23 未返回充电（从基站到一个或多个任务点）(已指派 未计算)/ 可计算航迹距离
#2022.9.24 可返回充电 未计算返回基站(出现问题：不能算多批次)
#2022.9.26 记录结果 多批次
#2022.9.27 修改飞出总数各批次计数 适应度计算+返回基站类合并
#2022.9.28 未算返回基站
#2022.9.29 一个UAV只执行一个任务

#PSO_UAVv3.5一个无人机飞多个任务点

#随机生成La
def genLaMatrix(role_amount, agent_amount):
		La = []
		temp = agent_amount
		while(temp>=1):
				val = ceil(role_amount/agent_amount)
				val += random.randint(1, agent_amount)
				if val not in La:
						La.append(val)
						temp-=1
		return La

# 无人机到各目标点距离(54*43的矩阵)
def genUCDisatnce():
    distanceUC = []
    temp = 0
    for j in range(La_sum):
        tempMatrix = []
        for i in range(L_sum):
            d_x = UAV_start_pos[j][0] - target_pos[i][0]
            d_y = UAV_start_pos[j][1] - target_pos[i][1]
            temp = np.sqrt(d_x ** 2 + d_y ** 2)
            tempMatrix.append(temp)
        distanceUC.append(tempMatrix)
    distanceUC = np.array(distanceUC)
    return distanceUC

# 基站到各目标点距离（4*43的矩阵）
def dis_BC():
    distance_BC = []
    temp = 0
    for j in range(base):
        tempMatrix = []
        for i in range(L_sum):
            d_x = base_start[j][0] - target_pos[i][0]
            d_y = base_start[j][1] - target_pos[i][1]
            temp = np.sqrt(d_x ** 2 + d_y ** 2)
            tempMatrix.append(temp)
        distance_BC.append(tempMatrix)
    distance_BC = np.array(distance_BC)
    return distance_BC


#各目标点之间的距离（43*43的矩阵）
def dis_cc():
    distanceCC = []
    temp = 0
    for i in range(0, L_sum):
        tempMatrix = []
        for j in range(0, L_sum):
            d_xx = target_pos[i][0] - target_pos[j][0]
            d_yy = target_pos[i][1] - target_pos[j][1]
            temp = np.sqrt(d_xx ** 2 + d_yy ** 2)
            tempMatrix.append(temp)
        distanceCC.append(tempMatrix)
    distanceCC = np.array(distanceCC)
    return distanceCC

# 距离标准化
def normal():
    UtoT = genUCDisatnce()
    TtoT = dis_cc()
    max_dis = max(UtoT.max(), TtoT.max())
    UtoT = UtoT / max_dis
    TtoT = TtoT / max_dis
    return UtoT, TtoT, max_dis

# # 先随机生成基站位置和算出La，再算出无人机位置
# def genUAVpos():
#     base_start = np.random.uniform(0,20,(base,2))
#     temp = 0
#     tempsum = []
#     UAV_start = []
#     for i in range(0,len(La)):
#         temp = La[i]
#         tempsum = np.tile(base_start[i], (La[i], 1))
#         tempsum = np.array(tempsum)
#         UAV_start.append(tempsum)
#     return UAV_start, base_start

# 先随机生成基站位置和算出La，再算出无人机位置
def genUAVpos():
    base_start = [(18.95, 6.92), (6.42, 9.6), (11.52, 18.78), (16.77, 15.33)]
    temp = 0
    tempsum = []
    UAV_start = []
    for i in range(0,len(La)):
        temp = La[i]
        tempsum = np.tile(base_start[i], (La[i], 1))
        tempsum = np.array(tempsum)
        UAV_start.append(tempsum)
    return UAV_start, base_start


# 目标点坐标
# def genclpos():
#     target_repos = np.random.uniform(0, 20, (cluster, 2))
#     temp = 0
#     tempsum = []
#     target = []
#     for i in range(0,len(L)):
#         temp = L[i]
#         tempsum = np.tile(target_repos[i], (L[i], 1))
#         tempsum = np.array(tempsum)
#         target.append(tempsum)
#     return target, target_repos

# 目标点坐标
def genclpos():
    target_repos = [(1.61, 19.53), (1.24, 14.76), (0.98, 12.94), (4.66, 15.11), (6.75, 15.87), (10.5, 16.48),(13.81, 18.55),(17.33, 17.36),
                (12.41, 14.76), (17.09, 13.79), (2.73, 11.4), (9.39, 8.55), (12.44, 10.19),(17.57, 8.39),(12.54, 6.3), (17.22, 3.41),
                (8.39, 3.73), (6.35, 1.06), (2.28, 2.8), (2.78, 6.61), (7.99, 10.16), (15.48,8.88)]
    temp = 0
    tempsum = []
    target = []
    for i in range(0,len(L)):
        temp = L[i]
        tempsum = np.tile(target_repos[i], (L[i], 1))
        tempsum = np.array(tempsum)
        target.append(tempsum)
    return target, target_repos

# 最大飞行距离约束
def cont(dis_U):  # 形参为无人机飞行距离
	if dis_U < max_flight:
		cost = 0
	else:
		cost = float('inf')  # 正无穷
	return cost

# 浮点型编码的解码方案
# def deco(taskcode):
# 	UandT = [[] for k in range(numU)]  # 存储每架无人机需要执行的任务
# 	# 为无人机分配任务
# 	for i in range(numT):
# 		for j in range(numU):
# 			if int(taskcode[i]) == j:
# 				a = 0  # 设置标志位判断当前值是否为列表中第一个元素
# 				for l in range(len(UandT[j])):
# 					if taskcode[i] < taskcode[l]:
# 						UandT[j].insert(l, i)  # 为任务进行排序
# 						a = 1
# 						break
# 				if a != 1:
# 					UandT[j].append(i)
# 	return UandT

# 浮点型编码的解码方案（一个无人机只执行一个任务）
def deco(taskcode):
	UandT = [[] for k in range(numU)]  # 存储每架无人机需要执行的任务
	# 为无人机分配任务
	for j in range(numU):
		for i in range(numT):
			if int(taskcode[i]) == j:
					a = 0  # 设置标志位判断当前值是否为列表中第一个元素
					for l in range(len(UandT[j])):
						if taskcode[i] < taskcode[l]:
							UandT[j].insert(l,i)  # 为任务进行排序
							a = 1
							break
					if a != 1:
						UandT[j].append(i)
	return UandT

# 分配矩阵
def T(taskcode):
	T = np.zeros((numT, numU))
	UandT = [[] for k in range(numU)]  # 存储每架无人机需要执行的任务
	# 为无人机分配任务
	for j in range(numU):
		for i in range(numT):
			if int(taskcode[i]) == j:
				T[i][j] = 1
				# a = 0  # 设置标志位判断当前值是否为列表中第一个元素
				# for l in range(len(UandT[j])):
				# 	if taskcode[i] < taskcode[l]:
				# 		UandT[j].insert(l, i)  # 为任务进行排序
				# 		# a = 1
				# 		break
				# if a != 1:
				# 	# UandT[j].append(i)
				# 	T[i][j] = 1
	return T

# 航迹总距离
def condis(taskcode):
	#UandT
	UandT = [[] for k in range(numU)]  # 存储每架无人机需要执行的任务
	# 为无人机分配任务
	for j in range(numU):
		for i in range(numT):
			if int(taskcode[i]) == j:
				UandT[j].append(i)
				# a = 0  # 设置标志位判断当前值是否为列表中第一个元素
				# for l in range(len(UandT[j])):
				# 	if taskcode[i] < taskcode[l]:
				# 		UandT[j].insert(l, i)  # 为任务进行排序
				# 		a = 1
				# 		break
				# if a != 1:
				# 	UandT[j].append(i)

	#distanceUC
	distanceUC = []
	temp = 0
	for j in range(La_sum):
		tempMatrix = []
		for i in range(L_sum):
			d_x = UAV_start_pos[j][0] - target_pos[i][0]
			d_y = UAV_start_pos[j][1] - target_pos[i][1]
			temp = np.sqrt(d_x ** 2 + d_y ** 2)
			tempMatrix.append(temp)
		distanceUC.append(tempMatrix)
	distanceUC = np.array(distanceUC)

	#distanceCC
	distanceCC = []
	temp = 0
	for i in range(0, L_sum):
		tempMatrix = []
		for j in range(0, L_sum):
			d_xx = target_pos[i][0] - target_pos[j][0]
			d_yy = target_pos[i][1] - target_pos[j][1]
			temp = np.sqrt(d_xx ** 2 + d_yy ** 2)
			tempMatrix.append(temp)
		distanceCC.append(tempMatrix)
	distanceCC = np.array(distanceCC)

	condis = [[] for k in range(numU)]
	temp = 0
	temp2 = 0
	temp3 = 0
	temp4 = 0
	for i in range(numU):
		if len(UandT[i]) == 0:
			condis[i] = 0
			pass
		else:
			if len(UandT[i]) == 1:
				temp = UandT[i][0]
				condis[i] = distanceUC[i][temp] * 2
			else:
				temp = UandT[i][0]
				condis[i] = distanceUC[i][temp]
				if len(UandT[i]) == 2:
					temp2 = UandT[i][1]
					retemp = distanceCC[temp][temp2] + distanceUC[i][temp2]
					condis[i] += retemp
					retemp = 0
				if len(UandT[i]) == 3:
					temp3 = UandT[i][2]
					retemp = distanceCC[temp][temp2]+distanceCC[temp2][temp3]+distanceUC[i][temp3]
					condis[i] += retemp
					retemp = 0
				if len(UandT[i]) == 4:
					temp4 = UandT[i][3]
					retemp = distanceCC[temp][temp2]+distanceCC[temp2][temp3]+distanceCC[temp3][temp4]+distanceUC[i][temp4]
					condis[i] += retemp
					retemp = 0
				if len(UandT[i]) == 5:
					temp5 = UandT[i][4]
					retemp = distanceCC[temp][temp2]+distanceCC[temp2][temp3]+distanceCC[temp3][temp4]+distanceCC[temp4][temp5]+distanceUC[i][temp5]
					condis[i] += retemp
					retemp = 0
	con_dis = 0
	for i in range(numU):
		temp_sum = condis[i]
		con_dis += temp_sum
		temp_sum = 0
	return con_dis


# 计算飞出基站无人机数量
def conout(La, T):
	T_sum = np.sum(T, axis = 0)
	T_sum = np.array(T_sum)
	temp = 0
	temp2 = 0
	temp3 = 0
	retemp = 0
	conout_UAV = [0 for k in range(base)]
	for i in range(base):
		if i == 0:
			temp = La[i]
			for j in range(0, temp):
				if np.array(T_sum)[j] != 0:
					retemp += 1
			conout_UAV[i] = retemp
			retemp = 0
		if i == 1:
			temp2 = La[i] + La[i-1]
			temp3 = La[i-1]
			for j in range(temp3, temp2):
				if np.array(T_sum)[j] != 0:
					retemp += 1
			conout_UAV[i] = retemp
			temp2 = 0
			temp3 = 0
			retemp = 0
		if i == 2:
			temp2 = La[i] + La[i-1] + La[i-2]
			temp3 = La[i-1] + La[i-2]
			for j in range(temp3, temp2):
				if np.array(T_sum)[j] != 0:
					retemp += 1
			conout_UAV[i] = retemp
			temp2 = 0
			temp3 = 0
			retemp = 0
		if i == 3:
			temp2 = La[i] + La[i-1] + La[i-2] + La[i-3]
			temp3 = La[i-3] + La[i-1] + La[i-2]
			for j in range(temp3, temp2):
				if np.array(T_sum)[j] != 0:
					retemp += 1
			conout_UAV[i] = retemp
			temp2 = 0
			temp3 = 0
			retemp = 0
	return conout_UAV


# 计时函数
def timer():  # 单位为秒
	global a, time_start, time_end
	if a == 1:
		time_end = time.time()
	elif a == 0:
		time_start = time.time()
		a = 1
	return time_end - time_start  # 返回当前运行时间

# 计算无人机各段飞行所需时间，虽然同计算fit_dis，但是程序最后执行不占用时间，所以没必要合并到fit_dis
def time_ufly(UandT):
	UtoT, TtoT, max_dis = normal()
	UtoT = UtoT * max_dis
	TtoT = TtoT * max_dis  # 还原到归一化之前的距离
	timeU = [[] for k in range(numU)]
	for i in range(numU):
		UAV = UandT[i]
		if len(UAV) == 0:  # 无人机没有任务
			pass
		elif len(UAV) == 1:  # 无人机有一个任务
			time0 = UtoT[i][UAV[0]] / 50  # 距离除以速度等于飞行时间
			timeU[i].extend([time0, time0])
		else:  # 无人机有超过一个任务
			time1 = UtoT[i][UAV[0]] / 50
			timeU[i].append(time1)
			for j in range(len(UAV) - 1):
				time2 = TtoT[UAV[j]][UAV[j + 1]] / 50
				timeU[i].append(time2)
			time3 = UtoT[i][UAV[len(UAV) - 1]] / 50
			timeU[i].append(time3)
	return timeU


# 无人机/任务坐标初始图
def tu_scatter(UandT):
	# 散点图绘制
	plt.scatter(UAV_start_pos[:, 0], UAV_start_pos[:, 1], marker='<', c='b', s=100, label='UAV')
	plt.scatter(target_pos[:, 0], target_pos[:, 1], marker='o', c='r', s=100, label='target')
	# 添加图例
	plt.legend()
	# 设置标题，坐标轴标签
	plt.title('UAV/target position', fontsize=20)
	plt.xlabel('Y', fontsize=14)
	plt.ylabel('X', fontsize=14)
	# 坐标轴刻度大小
	plt.tick_params(axis='both', labelsize=13)
	# 标记无人机序号
	n = np.arange(1, numU + 1)
	for i, num in enumerate(n):
		xynum = (UAV_start_pos[i][0] + 5, UAV_start_pos[i][1])  # 标记点坐标
		plt.annotate(num, xynum)
	# 标记任务序号
	n = np.arange(1, numT + 1)
	for i, num in enumerate(n):
		xynum = (target_pos[i][0] + 5, target_pos[i][1])
		plt.annotate(num, xynum)
	# 保存散点图
	plt.savefig("tu_scatter.png", dpi=800)
	fly(UandT)
	plt.show()

# 程序运行曲线图
def tu_diagram(time_mat, fitness_mat):
	plt.plot(time_mat, fitness_mat, c='y', lw=2, label='PSO')
	plt.legend()
	plt.title('fitness-time', fontsize=20)
	plt.xlabel('time', fontsize=14)
	plt.ylabel('fitness', fontsize=14)
	plt.tick_params(axis='both', labelsize=13)
	plt.savefig("tu_diagram.png", dpi=800)
	plt.show()

# # 绘制飞行路线图
def fly(UandT):
	for i in range(numU):
		listut = np.array([UAV_start_pos[i]])  # 每个无人机飞过的任务点坐标
		if len(UandT[i]) == 0:
			pass
		else:
			listut = np.row_stack((listut, target_pos[UandT[i]]))
		if len(UandT[i]) != 1:
			listut = np.row_stack((listut, UAV_start_pos[i]))
		plt.plot(listut[:, 0], listut[:, 1], lw=1, linestyle='-.')
	plt.savefig("tu_fly.png", dpi=800)

# 计算每批次派出
def outB(numU, UandT):
	outUAV = [0 for o in range(base)]
	empty_UAV = [0 for o in range(base)]
	for p in range(numU):  # 计算每个基站当前派出无人机数
		if p in range(0, La[0]):
			if UandT[p] != []:
				outUAV[0] += 1
		elif p in range(La[0], La[0] + La[1]):
			if UandT[p] != []:
				outUAV[1] += 1
		elif p in range(La[0] + La[1], La[0] + La[1] + La[2]):
			if UandT[p] != []:
				outUAV[2] += 1
		elif p in range(La[0] + La[1] + La[2], La[0] + La[1] + La[2] + La[3]):
			if UandT[p] != []:
				outUAV[3] += 1
		empty_UAV = outUAV
	return outUAV, empty_UAV
	# self.empty_UAV = self.outUAV  # 每个基站派出的无人机数 = 基站当前剩余可充电空位


# 适应度计算fit_dis
class calculate_tcost(object):
	def __init__(self):
		# 初始化参数
		self.fitness = np.array([])
		self.UtoT, self.TtoT, self.max_dis = normal()
		self.disU = [[] for k in range(numU)]  # 存储每个无人机飞过的每段距离
		self.distanceUC = genUCDisatnce()
		self.distance_BC = dis_BC()
		self.distanceUC_min = np.min(self.distanceUC, axis = 0)
		self.distanceUC_min2 = np.partition(self.distanceUC, kth = 1, axis = 0)[1]
		self.distanceUC_max2 = np.partition(self.distanceUC, kth = -2, axis = 0)[-2]
		self.distanceUC_max = np.partition(self.distanceUC, kth = -1, axis = 0)[-1]
		self.inUAV = [0 for i in range(numU)]  # 记录各无人机的返回基站（54个UAV）


	# 计算航迹代价
	def trackcost(self, taskcode):
		for k in range(population_size):
			cost_sum = 0
			UandT = deco(taskcode[k])  # 每架无人机分配了哪些任务

			self.outUAV, self.empty_UAV = outB(numU, UandT)

			for i in range(numU):
				UAV = UandT[i]  # 第i架无人机需要执行的任务
				cost = 0
				if len(UAV) == 0:  # 无人机没有任务
					self.inUAV[i] = 0
					# pass

				elif len(UAV) == 1:  # 无人机有一个任务
					dis0 = self.UtoT[i][UAV[0]]
					self.disU[i].append(dis0)
					for t in range(base):
						if self.distance_BC[t][UAV[0]] == self.distanceUC_min[UAV[0]] and self.empty_UAV[t] > 0:
							dis0_in = self.distance_BC[t][UAV[0]]
							self.disU[i].append(dis0_in)
							self.inUAV[t] = t + 1
							self.empty_UAV[t] = self.empty_UAV[t] - 1
						elif self.distance_BC[t][UAV[0]] == self.distanceUC_min2[UAV[0]] and self.empty_UAV[t] > 0:
							dis0_in = self.distance_BC[t][UAV[0]]
							self.disU[i].append(dis0_in)
							self.inUAV[t] = t + 1
							self.empty_UAV[t] = self.empty_UAV[t] - 1
						elif self.distance_BC[t][UAV[0]] == self.distanceUC_max2[UAV[0]] and self.empty_UAV[t] > 0:
							dis0_in = self.distance_BC[t][UAV[0]]
							self.disU[i].append(dis0_in)
							self.inUAV[t] = t + 1
							self.empty_UAV[t] = self.empty_UAV[t] - 1
						elif self.distance_BC[t][UAV[0]] == self.distanceUC_max[UAV[0]] and self.empty_UAV[t] > 0:
							dis0_in = self.distance_BC[t][UAV[0]]
							self.disU[i].append(dis0_in)
							self.inUAV[t] = t + 1
							self.empty_UAV[t] = self.empty_UAV[t] - 1

				# else:  # 无人机有超过一个任务
				# 	dis1 = self.UtoT[i][UAV[0]]
				# 	self.disU[i].append(dis1)
				# 	for j in range(len(UAV) - 1):
				# 		dis2 = self.TtoT[UAV[j]][UAV[j + 1]]  # 访问多目标点，目标点之间距离
				# 		self.disU[i].append(dis2)
				#
				# 	# dis3 = self.UtoT[i][UAV[len(UAV) - 1]] #返回原基站
				# 	for t in range(base):
				# 		if self.distance_BC[t][UAV[len(UAV) - 1]] == self.distanceUC_min[UAV[len(UAV) - 1]] and \
				# 				self.empty_UAV[t] > 0:  # 返回其他基站(比较是否是最短距离且判断基站是否有空位)
				# 			dis3 = self.distance_BC[t][UAV[len(UAV) - 1]]
				# 			self.disU[i].append(dis3)
				# 			self.inUAV[t] = t + 1
				# 			self.empty_UAV[t] = self.empty_UAV[t] - 1
				# 		elif self.distance_BC[t][UAV[len(UAV) - 1]] == self.distanceUC_min2[UAV[len(UAV) - 1]] and \
				# 				self.empty_UAV[t] > 0:
				# 			dis3 = self.distance_BC[t][UAV[len(UAV) - 1]]
				# 			self.disU[i].append(dis3)
				# 			self.inUAV[t] = t + 1
				# 			self.empty_UAV[t] = self.empty_UAV[t] - 1
				# 		elif self.distance_BC[t][UAV[len(UAV) - 1]] == self.distanceUC_max2[UAV[len(UAV) - 1]] and \
				# 				self.empty_UAV[t] > 0:
				# 			dis3 = self.distance_BC[t][UAV[len(UAV) - 1]]
				# 			self.disU[i].append(dis3)
				# 			self.inUAV[t] = t + 1
				# 			self.empty_UAV[t] = self.empty_UAV[t] - 1
				# 		elif self.distance_BC[t][UAV[len(UAV) - 1]] == self.distanceUC_max[UAV[len(UAV) - 1]] and \
				# 				self.empty_UAV[t] > 0:
				# 			dis3 = self.distance_BC[t][UAV[len(UAV) - 1]]
				# 			self.disU[i].append(dis3)
				# 			self.inUAV[t] = t + 1
				# 			self.empty_UAV[t] = self.empty_UAV[t] - 1

				cost = sum(self.disU[i])
				# cost = cost + cont(cost * self.max_dis)
				cost = cost + cont(cost)
				cost_sum = cost_sum + cost
			self.fitness = np.append(self.fitness, cost_sum)
		return self.fitness, self.inUAV


#PSO
class PSO(object):
	def __init__(self):
		# 后期加速因子和惯性权重采用微分递减修改
		self.w = 0.6
		self.c1 = self.c2 = 2
		print(type(self.c2))
		self.population_size = population_size  # 粒子群数量
		self.dim = numT  # 搜索空间的维度
		self.max_steps = max_steps  # 迭代次数
		self.x_bound = [0, numU]  # 解空间范围
		self.x = np.random.uniform(self.x_bound[0], self.x_bound[1],
								   (self.population_size, self.dim))  # 初始化粒子群位置
		self.v = np.random.rand(self.population_size, self.dim)  # 初始化粒子群速度
		fitness, inUAV = self.calculate_fitness(self.x)
		self.p = self.x  # 个体的最佳位置
		self.pg = self.x[np.argmin(fitness)]  # 全局最佳位置
		self.individual_best_fitness = fitness  # 个体的最优适应度
		self.global_best_fitness = np.min(fitness)  # 全局最佳适应度

		# 存储时间与与之对应的适应值
		self.time_mat = []
		self.fitness_mat = []

	# 适应值计算
	def calculate_fitness(self, x):
		ctc = calculate_tcost()
		fitness, inUAV = ctc.trackcost(x)
		return fitness, inUAV

	# 进化过程
	def evolve(self):
		time_pso = 0  # 存储程序当前运行时间
		while (time_pso < max_time):
			time_pso = timer()
			r1 = np.random.rand(self.population_size, self.dim)
			r2 = np.random.rand(self.population_size, self.dim)

			# 更新速度和权重
			self.v = self.w * self.v + self.c1 * r1 * (self.p - self.x) + self.c2 * r2 * (self.pg - self.x)
			self.v = np.clip(self.v, -1, 1)  # 限定速度范围
			self.x = self.v + self.x
			self.x = np.clip(self.x, 0, numU)
			fitness, inUAV = self.calculate_fitness(self.x)

			# 需要更新的个体
			update_id = np.greater(self.individual_best_fitness, fitness)
			self.p[update_id] = self.x[update_id]
			self.individual_best_fitness[update_id] = fitness[update_id]


			# 新一代出现了更小的fitness，所以更新全局最优fitness和位置
			if np.min(fitness) < self.global_best_fitness:
				self.pg = self.x[np.argmin(fitness)]
				self.global_best_fitness = np.min(fitness)
			self.time_mat.append(time_pso)
			self.fitness_mat.append(self.global_best_fitness)

			# print('当前运行时间：%.5f' % (time_pso))
			# print('当前最优值: %.5f' % (self.global_best_fitness))
			print('分配方案：', deco(self.x[np.argmin(fitness)]))  # 显示任务分配结果
			np.set_printoptions(threshold=np.inf) #完全显示分配矩阵
			print('分配矩阵：', T(self.x[np.argmin(fitness)]))
			print('当前最优值: %.5f' % (self.global_best_fitness))

			# self.inUAV = con_inUAV(self.x[np.argmin(fitness)])
			TMatrix = T(self.x[np.argmin(fitness)])
			distance_total = self.global_best_fitness
			# condistance = condis(self.x[np.argmin(fitness)])

		# tu_diagram(self.time_mat, self.fitness_mat)  # 绘制fitness曲线图
		scheme = deco(self.x[np.argmin(fitness)])


		# return scheme, TMatrix, condistance
		return scheme, TMatrix, inUAV, distance_total


#记录各基站返回无人机数
def conin(inUAV):
	conin_UAV = [0 for k in range(base)]
	temp_in_0 = 0
	temp_in_1 = 0
	temp_in_2 = 0
	temp_in_3 = 0
	for i in range(numU):
		if inUAV[i] == 1:
			temp_in_0 += 1
		elif inUAV[i] == 2:
			temp_in_1 += 1
		elif inUAV[i] == 3:
			temp_in_2 += 1
		elif inUAV[i] == 4:
			temp_in_3 += 1
	conin_UAV[0] = temp_in_0
	conin_UAV[1] = temp_in_1
	conin_UAV[2] = temp_in_2
	conin_UAV[3] = temp_in_3
	return conin_UAV


if __name__ == '__main__':

	run_start = time.perf_counter()

	# 无人机相关参数设置
	base = 4
	cluster = 22
	combination = 16
	L = [1, 2, 1, 3, 3, 1, 2, 2, 2, 2, 1, 2, 1, 2, 2, 3, 2, 2, 2, 3, 1, 3]  # 聚集点需求total：43
	L_sum = sum(L)
	La = genLaMatrix(L_sum, base)
	La_sum = sum(La)
	capacity = [i * 3 for i in La]
	ftcharge = 0.75  # 充电45分钟
	fv = 50  # 飞行速度50km/h
	Qmax = ftcharge * fv
	b_num = 33

	# 无人机和目标点初始坐标
	UAV_start, base_start = genUAVpos()
	UAV_start_pos = np.concatenate((UAV_start[0], UAV_start[1], UAV_start[2], UAV_start[3]))  # 合并成数组
	target, target_repos = genclpos()
	target_pos = np.concatenate((target[0], target[1], target[2], target[3], target[4], target[5], target[6],
								 target[7], target[8], target[9], target[10], target[11], target[12], target[13],
								 target[14], target[15], target[16], target[17], target[18], target[19], target[20],
								 target[21]))  # 合并成数组

	distanceUC = genUCDisatnce()
	distanceCC = dis_cc()
	distance_BC = dis_BC()

	# 无人机/目标点数目
	La = genLaMatrix(L_sum, 4)
	numU = sum(La)
	numT = sum(L)

	row = numU
	column = numT
	UtoT, TtoT, max_dis = normal()

	# 单位统一改成m/s
	# 无人机任务执行时间
	timeu = 0 #假设无人机无需在任务点停留

	# 无人机充电时间，单位h（改成s）
	ftcharge = 0.75 * 3600

	# 无人机飞行速度，单位km/h
	flyv = 50  / 3600

	# 最大飞行半径,单位m
	# 距离单位km
	max_flight = ftcharge * flyv
	# max_flight = 1

	# 种群数量
	population_size = 100

	# 最大迭代时间，单位s
	max_time = 0.01

	# 迭代次数
	max_steps = 1000

	#time_s赋值
	a = 0 #标志位，timestart只在第一次赋值
	time_start = time_end = 0 #初始化

	timer() # timestart进行赋值

	# run_start = time.perf_counter()

    # 批次
	scheme_b = []
	TMatrix_b = []
	distance_total_b = [0 for b in range(b_num)]
	inUAV_b = []
	conout_UAV_b = []
	conin_UAV_b = []
	resi_base_b = []
	outtotal = [0 for b in range(b_num)]
	intotal = [0 for b in range(b_num)]
	for b in range(b_num):
		pso = PSO()
		scheme, TMatrix, inUAV, distance_total= pso.evolve()
		scheme_b.append(scheme)
		TMatrix_b.append(TMatrix)
		distance_total_b[b] = distance_total

		#派出
		conout_UAV = conout(La, TMatrix)  # 各基站派出无人机数
		conout_UAV_b.append(conout_UAV) # 不同批次各基站派出无人机数
		resi_base = conout_UAV #各基站剩余空位
		resi_base_b.append(resi_base)
		outtotal[b] = sum(conout_UAV) #派出总数

		#派回
		inUAV_b.append(inUAV)  # 不同批次无人机返回基站序号（1-4）
		conin_UAV = conin(inUAV)  # 统计各基站派回数量
		conin_UAV_b.append(conin_UAV)  # 不同批次各基站派回数量
		intotal[b] = sum(conin_UAV)  # 派回总数

	scheme_b = np.array(scheme_b)
	TMatrix_b = np.array(TMatrix_b)
	distance_total_b = np.array(distance_total_b)
	inUAV_b = np.array(inUAV_b)
	conin_UAV_b = np.array(conin_UAV_b)
	conout_UAV_b = np.array(conout_UAV_b)
	resi_base_b = np.array(resi_base_b)

	distance_total = sum(distance_total_b)

	run_total = time.perf_counter() - run_start

	# tu_scatter(scheme)  # 绘制散点图(飞行结果图)

	print('最佳方案：', scheme)  # 最终方案
	print('最佳分配矩阵', TMatrix)
	# print('最佳方案总航迹', condistance)
	print('最佳方案总航迹', distance_total)
	# print('运行时间', timer())
	print('运行时间', run_total)

#待解决
#P1.e-cargo 协作
#P2.参数设置
#P3.e-cargo+粒子群
#P4.返程 √
#P5.时间 √
#P6.返回基站计算
#P7.最佳方案→最短路径？ √（适应度：目标函数的值最小）
#P8.分批次后UAV位置变+充电空位变
#P9.170航迹距离 返程的？→返程基站位置未知？