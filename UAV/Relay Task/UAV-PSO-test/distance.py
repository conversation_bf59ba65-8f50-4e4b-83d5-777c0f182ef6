# -*- coding: utf-8 -*-
"""
文件目的：所有与距离有关的函数


@author: liu
"""

import math
import globalv
import numpy as np
import random


UAV_start_pos = globalv.UAV_start_pos

target_pos = globalv.target_pos

row = globalv.numU

column = globalv.numT

# 生成La
def genLaMatrix(role_amount, agent_amount):
		La = []
		temp = agent_amount
		while(temp>=1):
				val = math.ceil(role_amount/agent_amount)
				val += random.randint(1, agent_amount)
				if val not in La:
						La.append(val)
						temp-=1
		return La

# 无人机相关参数设置
base = 4
cluster = 22
combination = 16
L = [1, 2, 1, 3, 3, 1, 2, 2, 2, 2, 1, 2, 1, 2, 2, 3, 2, 2, 2, 3, 1, 3]
L_sum = sum(L)  # 聚集点需求total：43
La = genLaMatrix(L_sum, base)
La_sum = sum(La)
capacity = [i * 3 for i in La]
ftcharge = 0.75  # 充电45分钟
fv = 50  # 飞行速度50km/h
Qmax = ftcharge * fv

# 计算两点之间距离公式
# def calDistance(x1, x2, y1, y2):
#     result = math.sqrt((x2-x1)**2+(y2-y1)**2)
#     return result

# 聚集点与基站组合距离（21*4的矩阵）
# def genDistance(x,y,x1,y1):
#     distance_BC = []
#     for j in range(base):
#         tempMatrix = []
#         for i in range(cluster):
#             tempMatrix.append(calDistance(x[i],x1[j],y[i],y1[j]))
#         distance_BC.append(tempMatrix)
#     return distance_BC

# 无人机到各目标点距离(43*22的矩阵)
# def genUCDisatnce():
#     distanceUC = []
#     for j in range(L_sum):
#         tempMatrix = []
#         for i in range(cluster):
#             tempMatrix.append(calDistance(UAV_start[j][0], target_pos[i][0], UAV_start[j][1], target_pos[i][1]))
#         distanceUC.append(tempMatrix)
#     return distanceUC

# 无人机到各目标点距离(43*22的矩阵)
def genUCDisatnce():
    distanceUC = []
    temp = 0
    for j in range(La_sum):
        tempMatrix = []
        for i in range(L_sum):
            d_x = UAV_start_pos[j][0] - target_pos[i][0]
            d_y = UAV_start_pos[j][1] - target_pos[i][1]
            temp = np.sqrt(d_x ** 2 + d_y ** 2)
            tempMatrix.append(temp)
        distanceUC.append(tempMatrix)
    distanceUC = np.array(distanceUC)
    return distanceUC

# #各目标点之间的距离
# def dis_cc():
#     distanceCC = []
#     for i in range(cluster):
#         for j in range(cluster):
#             distanceCC.append(calDistance(target_pos[i][0], target_pos[j][0], target_pos[i][1], target_pos[j][1]))
#     return distanceCC

#各目标点之间的距离
def dis_cc():
    distanceCC = []
    temp = 0
    for i in range(0, L_sum):
        tempMatrix = []
        for j in range(0, L_sum):
            d_xx = target_pos[i][0] - target_pos[j][0]
            d_yy = target_pos[i][1] - target_pos[j][1]
            temp = np.sqrt(d_xx ** 2 + d_yy ** 2)
            tempMatrix.append(temp)
        distanceCC.append(tempMatrix)
    distanceCC = np.array(distanceCC)
    return distanceCC

# 聚集点与基站组合距离算出Q(21*16的矩阵)
# def genQMatrix(distance_BC):
#     Q = []
#     for k in range(base):
#         for j in range(base):
#             temp = []
#             for i in range(cluster):
#                 total_distance = []
#                 total_distance = distance_BC_array[k,i]+distance_BC_array[j,i]
#                 temp.append(total_distance)
#             Q.append(temp)
#     return Q


# #无人机到各目标点距离
# def dis_UT():
#
#     UtoT = np.zeros((row,column))#初始化矩阵信息
#
#     for i in range(row):
#
#         for j in range(column):
#
#             d_x = UAV_start[i][0] - target_pos[j][0]
#
#             d_y = UAV_start[i][1] - target_pos[j][1]
#
#             UtoT[i][j] = np.sqrt(d_x**2 + d_y**2)
#
#     return UtoT#函数返回距离矩阵
    
# #各目标点之间的距离
# def dis_TT():
#
#     TtoT = np.zeros((column,column))
#
#     for i in range(column):
#
#         for j in range(column):
#
#             d_x = target_pos[i][0] - target_pos[j][0]
#
#             d_y = target_pos[i][1] - target_pos[j][1]
#
#             TtoT[i][j] = np.sqrt(d_x**2 + d_y**2)
#
#     return TtoT

# #距离标准化
# def normal():
#
#     UtoT = dis_UT()
#
#     TtoT = dis_TT()
#
#     max_dis = max(UtoT.max(), TtoT.max())
#
#     UtoT = UtoT / max_dis
#
#     TtoT = TtoT / max_dis
#
#     return UtoT, TtoT, max_dis


# 距离标准化
def normal():
    UtoT = genUCDisatnce()

    TtoT = dis_cc()

    max_dis = max(UtoT.max(), TtoT.max())

    UtoT = UtoT / max_dis

    TtoT = TtoT / max_dis

    return UtoT, TtoT, max_dis

UtoT, TtoT, max_dis = normal()

