import matplotlib.pyplot as plt
from matplotlib.ticker import MaxNLocator
import numpy as np
import xlrd
import pulp as pl
import pandas as pd
from math import cos, sin, acos, asin, pi, atan, fabs, ceil, floor
from collections import defaultdict
import random
import time
import matplotlib.pyplot as plt
import math

# 2022.9.25 基站组合+目标点(不可行：还是需要对无人机进行编码规划路径)

#随机生成La
def genLaMatrix(role_amount, agent_amount):
		La = []
		temp = agent_amount
		while(temp>=1):
				val = ceil(role_amount/agent_amount)
				val += random.randint(1, agent_amount)
				if val not in La:
						La.append(val)
						temp-=1
		return La

# 计算两点之间距离公式
def calDistance(x1, x2, y1, y2):
    result = math.sqrt((x2-x1)**2+(y2-y1)**2)
    return result

# 聚集点之间距离（22*22的矩阵）
def gendisCC(x,y):
    distance_CC = []
    for j in range(cluster):
        tempMatrix = []
        for i in range(cluster):
            tempMatrix.append(calDistance(x[i], x[j], y[i], y[j]))
        distance_CC.append(tempMatrix)
    return distance_CC

# 聚集点与基站组合距离（22*4的矩阵）
def gendisBC(x,y,x1,y1):
    distance_BC = []
    for j in range(base):
        tempMatrix = []
        for i in range(cluster):
            tempMatrix.append(calDistance(x[i],x1[j],y[i],y1[j]))
        distance_BC.append(tempMatrix)
    return distance_BC

# 聚集点与基站组合距离算出Q(22*16的矩阵)
def genQMatrix(distance_BC):
    Q = []
    for k in range(base):
        for j in range(base):
            temp = []
            for i in range(cluster):
                total_distance = []
                total_distance = distance_BC_array[k,i]+distance_BC_array[j,i]
                temp.append(total_distance)
            Q.append(temp)
    return Q

# 距离标准化
def normal():
    distance_BC = gendisBC(base_pos_x, base_pos_y, target_pos_x, target_pos_y)
    distance_CC = gendisCC(target_pos_x, target_pos_y)
    max_dis = max(UtoT.max(), TtoT.max())
    distance_BC = distance_BC / max_dis
    distance_CC = distance_CC / max_dis
    return distance_BC, distance_CC, max_dis

# 最大飞行距离约束
def cont(dis_U):  # 形参为无人机飞行距离
	if dis_U < max_flight:
		cost = 0
	else:
		cost = float('inf')  # 正无穷
	return cost

# 浮点型编码的解码方案
def deco(taskcode):
	UandT = [[] for k in range(numU)]  # 存储每架无人机需要执行的任务
	# 为无人机分配任务
	for i in range(numT):
		for j in range(numU):
			if int(taskcode[i]) == j:
				a = 0  # 设置标志位判断当前值是否为列表中第一个元素
				for l in range(len(UandT[j])):
					if taskcode[i] < taskcode[l]:
						UandT[j].insert(l, i)  # 为任务进行排序
						a = 1
						break
				if a != 1:
					UandT[j].append(i)
	return UandT

if __name__ == '__main__':

	# 无人机相关参数设置
    base = 4
    cluster = 22
    combination = 16
    L = [1, 2, 1, 3, 3, 1, 2, 2, 2, 2, 1, 2, 1, 2, 2, 3, 2, 2, 2, 3, 1, 3]  # 聚集点需求total：43
    L_sum = sum(L)
    La = genLaMatrix(L_sum, base)
    La_sum = sum(La)
    capacity = [i * 3 for i in La]
    ftcharge = 0.75  # 充电45分钟
    fv = 50  # 飞行速度50km/h
    Qmax = ftcharge * fv

    # 随机生成基站和目标点位置
    base_pos = np.random.uniform(0, 20, (base,2))
    target_pos = np.random.uniform(0, 20, (cluster, 2))
    base_pos_x = [i[0] for i in base_pos]
    base_pos_y = [i[1] for i in base_pos]
    target_pos_x = [i[0] for i in target_pos]
    target_pos_y = [i[1] for i in target_pos]

    distance_CC = gendisCC(target_pos_x, target_pos_y)
    distance_BC = gendisBC(base_pos_x, base_pos_y, target_pos_x, target_pos_y)
    Q = genQMatrix(distance_BC)