# 计算派回基站
class con_inUAV(object):
	def __init__(self):
		# 初始化参数
		self.fitness = np.array([])
		self.UtoT, self.TtoT, self.max_dis = normal()
		self.disU = [[] for k in range(numU)]  # 存储每个无人机飞过的每段距离
		self.distance_BC = dis_BC()
		self.distance_BC_min = np.min(self.distance_BC, axis = 0)
		self.distance_BC_min2 = np.partition(self.distance_BC, 1)[1]
		self.distance_BC_max2 = np.partition(self.distance_BC, -2)[-2]
		self.distance_BC_max = np.partition(self.distance_BC, -1)[-1]
		self.outUAV = [0 for o in range(base)] #计算每个基站当前派出无人机数
		self.inUAV = [0 for t in range(numU)] #计算各无人机的返回基站（54个UAV）

	# 计算航迹代价
	def trackcost1(self, taskcode):
		for k in range(population_size):
			cost_sum = 0
			UandT = deco(taskcode[k])  # 每架无人机分配了哪些任务

			retemp = 0
			for p in range(numU):  # 计算每个基站当前派出无人机数
				if p in range(0,La[0]):
					if UandT[p] != 0:
						retemp += 1
					self.outUAV[0] = retemp
				if p in range(La[0], La[1]):
					if UandT[p] != 0:
						retemp += 1
					self.outUAV[1] = retemp
				if p in range(La[1], La[2]):
					if UandT[p] != 0:
						retemp += 1
					self.outUAV[2] = retemp
				if p in range(La[2], La[3]):
					if UandT[p] != 0:
						retemp +=1
					self.outUAV[3] = retemp

			empty_UAV = self.outUAV # 每个基站派出的无人机数 = 基站当前剩余可充电空位

			for i in range(numU):
				UAV = UandT[i]  # 第i架无人机需要执行的任务
				cost = 0
				if len(UAV) == 0:  # 无人机没有任务
					self.inUAV[i] = 0
					pass

				elif len(UAV) == 1:  # 无人机有一个任务
					dis0 = self.UtoT[i][UAV[0]]
					self.disU[i].append(dis0)
					if self.distance_BC[0][UAV[0]] == self.distance_BC_min[UAV[0]] and empty_UAV[0] > 0: #返回其他基站
						dis0_in =self.distance_BC[0][UAV[0]]
						self.disU[i].append(dis0_in)
						self.inUAV[i] = 1
						empty_UAV[0] = empty_UAV[0] - 1
					elif self.distance_BC[1][UAV[0]] == self.distance_BC_min2[UAV[0]] and empty_UAV[1] > 0:
						dis0_in =self.distance_BC[1][UAV[0]]
						self.disU[i].append(dis0_in)
						self.inUAV[i] = 2
						empty_UAV[1] = empty_UAV[1] - 1
					elif self.distance_BC[2][UAV[0]] == self.distance_BC_max2[UAV[0]] and empty_UAV[2] > 0:
						dis0_in =self.distance_BC[2][UAV[0]]
						self.disU[i].append(dis0_in)
						self.inUAV[i] = 3
						empty_UAV[2] = empty_UAV[2] - 1
					elif self.distance_BC[3][UAV[0]] == self.distance_BC_max[UAV[0]] and empty_UAV[3] > 0:
						dis0_in = self.distance_BC[3][UAV[0]]
						self.disU[i].append(dis0_in)
						self.inUAV[i] = 4
						empty_UAV[3] = empty_UAV[3] - 1

				else:  # 无人机有超过一个任务
					dis1 = self.UtoT[i][UAV[0]]
					self.disU[i].append(dis1)
					for j in range(len(UAV) - 1):
						dis2 = self.TtoT[UAV[j]][UAV[j + 1]]
						self.disU[i].append(dis2)
					# dis3 = self.UtoT[i][UAV[len(UAV) - 1]] #返回原基站
					if self.distance_BC[0][UAV[len(UAV)-1]] == self.distance_BC_min[UAV[len(UAV)-1]] and empty_UAV[0] > 0: #返回其他基站(比较是否是最短距离且判断基站是否有空位)
						dis3 = self.distance_BC[0][UAV[len(UAV)-1]]
						self.disU[i].append(dis3)
						empty_UAV[0] = empty_UAV[0] - 1
						self.inUAV[i] = 1
					elif self.distance_BC[1][UAV[len(UAV)-1]] == self.distance_BC_min2[UAV[len(UAV)-1]] and empty_UAV[1] > 0:
						dis3 = self.distance_BC[1][UAV[len(UAV) - 1]]
						self.disU[i].append(dis3)
						empty_UAV[1] = empty_UAV[1] - 1
						self.inUAV[i] = 2
					elif self.distance_BC[2][UAV[len(UAV)-1]] == self.distance_BC_max2[UAV[len(UAV)-1]] and empty_UAV[2] > 0:
						dis3 = self.distance_BC[2][UAV[len(UAV) - 1]]
						self.disU[i].append(dis3)
						empty_UAV[2] = empty_UAV[2] - 1
						self.inUAV[i] = 3
					elif self.distance_BC[3][UAV[len(UAV)-1]] == self.distance_BC_max[UAV[len(UAV)-1]] and empty_UAV[3] > 0:
						dis3 = self.distance_BC[3][UAV[len(UAV) - 1]]
						self.disU[i].append(dis3)
						empty_UAV[3] = empty_UAV[3] - 1
						self.inUAV[i] = 4

				cost = sum(self.disU[i])
				# cost = cost + cont(cost * self.max_dis)
				cost = cost + cont(cost)
				cost_sum = cost_sum + cost
			self.fitness = np.append(self.fitness, cost_sum)
		return self.inUAV

#PSO
class PSO(object):
	def __init__(self):
		# 后期加速因子和惯性权重采用微分递减修改
		self.w = 1
		self.c1 = self.c2 = 2
		print(type(self.c2))
		self.population_size = population_size  # 粒子群数量
		self.dim = numT  # 搜索空间的维度
		self.max_steps = max_steps  # 迭代次数
		self.x_bound = [0, numU]  # 解空间范围
		self.x = np.random.uniform(self.x_bound[0], self.x_bound[1],
								   (self.population_size, self.dim))  # 初始化粒子群位置
		self.v = np.random.rand(self.population_size, self.dim)  # 初始化粒子群速度
		fitness = self.calculate_fitness(self.x)
		self.p = self.x  # 个体的最佳位置
		self.pg = self.x[np.argmin(fitness)]  # 全局最佳位置
		self.individual_best_fitness = fitness  # 个体的最优适应度
		self.global_best_fitness = np.min(fitness)  # 全局最佳适应度

		# 存储时间与与之对应的适应值
		self.time_mat = []
		self.fitness_mat = []

	# 适应值计算
	def calculate_fitness(self, x):
		ctc = calculate_tcost()
		fitness = ctc.trackcost(x)
		return fitness

	#返回基站计算
	def calculate_inUAV(self, x):
		ctc1 = con_inUAV()
		inUAV = ctc1.trackcost1(x)
		return inUAV

	# 进化过程
	def evolve(self):
		time_pso = 0  # 存储程序当前运行时间
		while (time_pso < max_time):
			time_pso = timer()
			r1 = np.random.rand(self.population_size, self.dim)
			r2 = np.random.rand(self.population_size, self.dim)

			# 更新速度和权重
			self.v = self.w * self.v + self.c1 * r1 * (self.p - self.x) + self.c2 * r2 * (self.pg - self.x)
			self.v = np.clip(self.v, -1, 1)  # 限定速度范围
			self.x = self.v + self.x
			self.x = np.clip(self.x, 0, numU)
			fitness = self.calculate_fitness(self.x)
			# con_dis = self.calculate_dis(self.x)
			inUAV = self.calculate_inUAV(self.x)

			# 需要更新的个体
			update_id = np.greater(self.individual_best_fitness, fitness)
			self.p[update_id] = self.x[update_id]
			self.individual_best_fitness[update_id] = fitness[update_id]


			# 新一代出现了更小的fitness，所以更新全局最优fitness和位置
			if np.min(fitness) < self.global_best_fitness:
				self.pg = self.x[np.argmin(fitness)]
				self.global_best_fitness = np.min(fitness)
			self.time_mat.append(time_pso)
			self.fitness_mat.append(self.global_best_fitness)

			print('当前运行时间：%.5f' % (time_pso))
			print('当前最优值: %.5f' % (self.global_best_fitness))
			print('分配方案：', deco(self.x[np.argmin(fitness)]))  # 显示任务分配结果
			np.set_printoptions(threshold=np.inf) #完全显示分配矩阵
			print('分配矩阵：', T(self.x[np.argmin(fitness)]))
			print('航迹距离', condis(self.x[np.argmin(fitness)]))

			self.inUAV = con_inUAV(self.x[np.argmin(fitness)])
			TMatrix = T(self.x[np.argmin(fitness)])
			condistance = condis(self.x[np.argmin(fitness)])

		tu_diagram(self.time_mat, self.fitness_mat)  # 绘制fitness曲线图
		scheme = deco(self.x[np.argmin(fitness)])

		return scheme, TMatrix, condistance, inUAV