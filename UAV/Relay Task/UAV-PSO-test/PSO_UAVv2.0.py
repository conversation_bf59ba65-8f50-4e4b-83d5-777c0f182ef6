import matplotlib.pyplot as plt
from matplotlib.ticker import MaxNLocator
import numpy as np
import xlrd
import pulp as pl
import pandas as pd
from math import cos, sin, acos, asin, pi, atan, fabs, ceil, floor
from collections import defaultdict
import random
import time
import matplotlib.pyplot as plt

#合并PSO 2022.9.22
#2022.9.23 未返回充电（从基站到一个或多个任务点）/ 可计算航迹距离

#随机生成La
def genLaMatrix(role_amount, agent_amount):
		La = []
		temp = agent_amount
		while(temp>=1):
				val = ceil(role_amount/agent_amount)
				val += random.randint(1, agent_amount)
				if val not in La:
						La.append(val)
						temp-=1
		return La

# 无人机到各目标点距离(54*43的矩阵)
def genUCDisatnce():
    distanceUC = []
    temp = 0
    for j in range(La_sum):
        tempMatrix = []
        for i in range(L_sum):
            d_x = UAV_start_pos[j][0] - target_pos[i][0]
            d_y = UAV_start_pos[j][1] - target_pos[i][1]
            temp = np.sqrt(d_x ** 2 + d_y ** 2)
            tempMatrix.append(temp)
        distanceUC.append(tempMatrix)
    distanceUC = np.array(distanceUC)
    return distanceUC

#各目标点之间的距离（43*43的矩阵）
def dis_cc():
    distanceCC = []
    temp = 0
    for i in range(0, L_sum):
        tempMatrix = []
        for j in range(0, L_sum):
            d_xx = target_pos[i][0] - target_pos[j][0]
            d_yy = target_pos[i][1] - target_pos[j][1]
            temp = np.sqrt(d_xx ** 2 + d_yy ** 2)
            tempMatrix.append(temp)
        distanceCC.append(tempMatrix)
    distanceCC = np.array(distanceCC)
    return distanceCC

# 距离标准化
def normal():
    UtoT = genUCDisatnce()
    TtoT = dis_cc()
    max_dis = max(UtoT.max(), TtoT.max())
    UtoT = UtoT / max_dis
    TtoT = TtoT / max_dis
    return UtoT, TtoT, max_dis

# 先随机生成基站位置和算出La，再算出无人机位置
def genUAVpos():
    base_start = np.random.uniform(0,20,(base,2))
    temp = 0
    tempsum = []
    UAV_start = []
    for i in range(0,len(La)):
        temp = La[i]
        tempsum = np.tile(base_start[i], (La[i], 1))
        tempsum = np.array(tempsum)
        UAV_start.append(tempsum)
    return UAV_start

# 目标点坐标
def genclpos():
    target_repos = np.random.uniform(0, 20, (cluster, 2))
    temp = 0
    tempsum = []
    target = []
    for i in range(0,len(L)):
        temp = L[i]
        tempsum = np.tile(target_repos[i], (L[i], 1))
        tempsum = np.array(tempsum)
        target.append(tempsum)
    return target

# 最大飞行距离约束
def cont(dis_U):  # 形参为无人机飞行距离
	if dis_U < max_flight:
		cost = 0
	else:
		cost = float('inf')  # 正无穷
	return cost

# 浮点型编码的解码方案
def deco(taskcode):
	UandT = [[] for k in range(numU)]  # 存储每架无人机需要执行的任务
	# 为无人机分配任务
	for i in range(numT):
		for j in range(numU):
			if int(taskcode[i]) == j:
				a = 0  # 设置标志位判断当前值是否为列表中第一个元素
				for l in range(len(UandT[j])):
					if taskcode[i] < taskcode[l]:
						UandT[j].insert(l, i)  # 为任务进行排序
						a = 1
						break
				if a != 1:
					UandT[j].append(i)
	return UandT

# 分配矩阵
def T(taskcode):
	T = np.zeros((numT, numU))
	UandT = [[] for k in range(numU)]  # 存储每架无人机需要执行的任务
	# 为无人机分配任务
	for i in range(numT):
		for j in range(numU):
			if int(taskcode[i]) == j:
				a = 0  # 设置标志位判断当前值是否为列表中第一个元素
				for l in range(len(UandT[j])):
					if taskcode[i] < taskcode[l]:
						UandT[j].insert(l, i)  # 为任务进行排序
						a = 1
						break
				if a != 1:
					# UandT[j].append(i)
					T[i][j] = 1
	return T

# 航迹总距离
def condis(taskcode):
	#UandT
	UandT = [[] for k in range(numU)]  # 存储每架无人机需要执行的任务
	# 为无人机分配任务
	for i in range(numT):
		for j in range(numU):
			if int(taskcode[i]) == j:
				a = 0  # 设置标志位判断当前值是否为列表中第一个元素
				for l in range(len(UandT[j])):
					if taskcode[i] < taskcode[l]:
						UandT[j].insert(l, i)  # 为任务进行排序
						a = 1
						break
				if a != 1:
					UandT[j].append(i)

	#distanceUC
	distanceUC = []
	temp = 0
	for j in range(La_sum):
		tempMatrix = []
		for i in range(L_sum):
			d_x = UAV_start_pos[j][0] - target_pos[i][0]
			d_y = UAV_start_pos[j][1] - target_pos[i][1]
			temp = np.sqrt(d_x ** 2 + d_y ** 2)
			tempMatrix.append(temp)
		distanceUC.append(tempMatrix)
	distanceUC = np.array(distanceUC)

	#distanceCC
	distanceCC = []
	temp = 0
	for i in range(0, L_sum):
		tempMatrix = []
		for j in range(0, L_sum):
			d_xx = target_pos[i][0] - target_pos[j][0]
			d_yy = target_pos[i][1] - target_pos[j][1]
			temp = np.sqrt(d_xx ** 2 + d_yy ** 2)
			tempMatrix.append(temp)
		distanceCC.append(tempMatrix)
	distanceCC = np.array(distanceCC)

	condis = [[] for k in range(numU)]
	temp = 0
	temp2 = 0
	temp3 = 0
	temp4 = 0
	for i in range(numU):
		if len(UandT[i]) == 0:
			condis[i] = 0
			pass
		else:
			if len(UandT[i]) == 1:
				temp = UandT[i][0]
				condis[i] = distanceUC[i][temp]
			else:
				temp = UandT[i][0]
				condis[i] = distanceUC[i][temp]
				if len(UandT[i]) == 2:
					temp2 = UandT[i][1]
					condis[i] += distanceCC[temp][temp2]
					if len(UandT[i]) == 3:
						temp3 = UandT[i][2]
						condis[i] += distanceCC[temp2][temp3]
						if len(UandT[i]) == 4:
							temp4 = UandT[i][3]
							condis[i] += distanceCC[temp3][temp4]
					# for j in range(numT):
					# for k in range(numT):
					# 	temp2 = UandT[i][j]
					# 	temp3 = UandT[i][k]
					# 	condis[i] += distanceCC[temp2][temp3]
	con_dis = 0
	for i in range(numU):
		con_dis += condis[i]
	return con_dis

# # 航迹距离
# def condistance(taskcode):
# 	condis = np.zeros((1, numU))
# 	temp = 0
# 	T = np.array(T)
# 	T_rowsum = np.sum(T, axis=0)
# 	UtoT_dis = genUCDisatnce()
# 	for j in range(numU):
# 		if T_rowsum[j] == 0:
# 			pass
# 		elif T_rowsum[j] == 1:
# 			for i in range(numT):
# 				if T[i][j] == 1:
# 					temp = UtoT_dis[i][j]
# 				condis[j] = temp
# 		else:
# 			scheme[j][]


# 计时函数
def timer():  # 单位为秒
	global a, time_start, time_end
	if a == 1:
		time_end = time.time()
	elif a == 0:
		time_start = time.time()
		a = 1
	return time_end - time_start  # 返回当前运行时间

# 计算无人机各段飞行所需时间，虽然同计算fit_dis，但是程序最后执行不占用时间，所以没必要合并到fit_dis
def time_ufly(UandT):
	UtoT, TtoT, max_dis = normal()
	UtoT = UtoT * max_dis
	TtoT = TtoT * max_dis  # 还原到归一化之前的距离
	timeU = [[] for k in range(numU)]
	for i in range(numU):
		UAV = UandT[i]
		if len(UAV) == 0:  # 无人机没有任务
			pass
		elif len(UAV) == 1:  # 无人机有一个任务
			time0 = UtoT[i][UAV[0]] / 50  # 距离除以速度等于飞行时间
			timeU[i].extend([time0, time0])
		else:  # 无人机有超过一个任务
			time1 = UtoT[i][UAV[0]] / 50
			timeU[i].append(time1)
			for j in range(len(UAV) - 1):
				time2 = TtoT[UAV[j]][UAV[j + 1]] / 50
				timeU[i].append(time2)
			time3 = UtoT[i][UAV[len(UAV) - 1]] / 50
			timeU[i].append(time3)
	return timeU


# 无人机/任务坐标初始图
def tu_scatter(UandT):
	# 散点图绘制
	plt.scatter(UAV_start_pos[:, 0], UAV_start_pos[:, 1], marker='<', c='b', s=100, label='UAV')
	plt.scatter(target_pos[:, 0], target_pos[:, 1], marker='o', c='r', s=100, label='target')
	# 添加图例
	plt.legend()
	# 设置标题，坐标轴标签
	plt.title('UAV/target position', fontsize=20)
	plt.xlabel('Y', fontsize=14)
	plt.ylabel('X', fontsize=14)
	# 坐标轴刻度大小
	plt.tick_params(axis='both', labelsize=13)
	# 标记无人机序号
	n = np.arange(1, numU + 1)
	for i, num in enumerate(n):
		xynum = (UAV_start_pos[i][0] + 5, UAV_start_pos[i][1])  # 标记点坐标
		plt.annotate(num, xynum)
	# 标记任务序号
	n = np.arange(1, numT + 1)
	for i, num in enumerate(n):
		xynum = (target_pos[i][0] + 5, target_pos[i][1])
		plt.annotate(num, xynum)
	# 保存散点图
	plt.savefig("tu_scatter.png", dpi=800)
	fly(UandT)
	plt.show()

# 程序运行曲线图
def tu_diagram(time_mat, fitness_mat):
	plt.plot(time_mat, fitness_mat, c='y', lw=2, label='PSO')
	plt.legend()
	plt.title('fitness-time', fontsize=20)
	plt.xlabel('time', fontsize=14)
	plt.ylabel('fitness', fontsize=14)
	plt.tick_params(axis='both', labelsize=13)
	plt.savefig("tu_diagram.png", dpi=800)
	plt.show()

# 绘制甘特图
def gante(timeufly, UandT, timeu):
	# 形参为飞行时间列表,分配结果列表,执行时间
	plt.plot(0)
	plt.xlabel("time/min", fontsize=14)
	plt.ylabel("UAV", fontsize=14)
	y = range(0, numU, 1)
	# 纵轴刻度表示
	uavlist = []
	for i in range(numU):
		uavlist.append(r'$UAV%d$' % (i + 1))
	plt.yticks(y, uavlist)
	plt.title('UAV/target gan', fontsize=20)
	for j in range(numU):  # 无人机个数
		if len(UandT[j]) == 0:  # 没任务的无人机画白色表示空
			plt.barh(j, 5, height=0.5, color="w")
		else:
			for i in range(len(UandT[j])):
				if i == 0:  # 如果任务是第一个任务
					time = timeufly[j][i]
					plt.barh(j, timeu[j][i], left=time, height=0.35)
					plt.text(time + 10, j, 'task%d' % (UandT[j][i] + 1), verticalalignment="center", color="white",
							 size=5)
				else:  # 如果任务不是第一个任务
					time = time + timeufly[j][i] + timeu[j][i - 1]
					plt.barh(j, timeu[j][i], left=time, height=0.35)
					plt.text(time + 5, j, 'task%d' % (UandT[j][i] + 1), verticalalignment="center", color="white",
							 size=5)
	plt.savefig("tu_gante.png", dpi=800)
	plt.show()


# 绘制飞行路线图
def fly(UandT):
	for i in range(numU):
		listut = np.array([UAV_start_pos[i]])  # 每个无人机飞过的任务点坐标
		if len(UandT[i]) == 0:
			pass
		else:
			listut = np.row_stack((listut, target_pos[UandT[i]]))
		if len(UandT[i]) != 1:
			listut = np.row_stack((listut, UAV_start_pos[i]))
		plt.plot(listut[:, 0], listut[:, 1], lw=1, linestyle='-.')
	plt.savefig("tu_fly.png", dpi=800)

# 适应度计算fit_dis
class calculate_tcost(object):
	def __init__(self):
		# 初始化参数
		self.fitness = np.array([])
		self.UtoT, self.TtoT, self.max_dis = normal()
		self.disU = [[] for k in range(numU)]  # 存储每个无人机飞过的每段距离

	# 计算航迹代价
	def trackcost(self, taskcode):
		for k in range(population_size):
			cost_sum = 0
			UandT = deco(taskcode[k])  # 每架无人机分配了哪些任务
			for i in range(numU):
				UAV = UandT[i]  # 第i架无人机需要执行的任务
				cost = 0
				if len(UAV) == 0:  # 无人机没有任务
					pass
				elif len(UAV) == 1:  # 无人机有一个任务
					dis0 = self.UtoT[i][UAV[0]]
					self.disU[i].extend([dis0, dis0])
				else:  # 无人机有超过一个任务
					dis1 = self.UtoT[i][UAV[0]]
					self.disU[i].append(dis1)
					for j in range(len(UAV) - 1):
						dis2 = self.TtoT[UAV[j]][UAV[j + 1]]
						self.disU[i].append(dis2)
					dis3 = self.UtoT[i][UAV[len(UAV) - 1]]
					self.disU[i].append(dis3)
				cost = sum(self.disU[i])
				cost = cost + cont(cost * self.max_dis)
				cost_sum = cost_sum + cost
			self.fitness = np.append(self.fitness, cost_sum)
		return self.fitness

# 计算航迹距离
# class calculate_tcost_dis(object):
# 	def __init__(self):
# 		# 初始化参数
# 		self.fitness = np.array([])
# 		self.UtoT, self.TtoT, self.max_dis = normal()
# 		self.disU = [[] for k in range(numU)]  # 存储每个无人机飞过的每段距离
# 		# self.con_dis = 0
#
# 	# 计算航迹代价
# 	def trackcost(self, taskcode):
# 		for k in range(population_size):
# 			cost_sum = 0
# 			UandT = deco(taskcode[k])  # 每架无人机分配了哪些任务
# 			for i in range(numU):
# 				UAV = UandT[i]  # 第i架无人机需要执行的任务
# 				cost = 0
# 				if len(UAV) == 0:  # 无人机没有任务
# 					pass
# 				elif len(UAV) == 1:  # 无人机有一个任务
# 					dis0 = self.UtoT[i][UAV[0]]
# 					self.disU[i].extend([dis0, dis0])
# 				else:  # 无人机有超过一个任务
# 					dis1 = self.UtoT[i][UAV[0]]
# 					self.disU[i].append(dis1)
# 					for j in range(len(UAV) - 1):
# 						dis2 = self.TtoT[UAV[j]][UAV[j + 1]]
# 						self.disU[i].append(dis2)
# 					dis3 = self.UtoT[i][UAV[len(UAV) - 1]]
# 					self.disU[i].append(dis3)
# 				cost_1 = sum(self.disU[i])
# 				# self.con_dis += cost_1
# 				# self.con_dis = sum(self.disU[i])
# 				cost = cost_1 + cont(cost * self.max_dis)
# 				cost_sum = cost_sum + cost
# 			self.fitness = np.append(self.fitness, cost_sum)
# 			self.con_dis = np.sum(self.disU)
# 		return self.con_dis

#PSO
class PSO(object):
	def __init__(self):
		# 后期加速因子和惯性权重采用微分递减修改
		self.w = 1
		self.c1 = self.c2 = 2
		print(type(self.c2))
		self.population_size = population_size  # 粒子群数量
		self.dim = numT  # 搜索空间的维度
		self.max_steps = max_steps  # 迭代次数
		self.x_bound = [0, numU]  # 解空间范围
		self.x = np.random.uniform(self.x_bound[0], self.x_bound[1],
								   (self.population_size, self.dim))  # 初始化粒子群位置
		self.v = np.random.rand(self.population_size, self.dim)  # 初始化粒子群速度
		fitness = self.calculate_fitness(self.x)
		self.p = self.x  # 个体的最佳位置
		self.pg = self.x[np.argmin(fitness)]  # 全局最佳位置
		self.individual_best_fitness = fitness  # 个体的最优适应度
		self.global_best_fitness = np.min(fitness)  # 全局最佳适应度

		# 存储时间与与之对应的适应值
		self.time_mat = []
		self.fitness_mat = []

	# 适应值计算
	def calculate_fitness(self, x):
		ctc = calculate_tcost()
		fitness = ctc.trackcost(x)
		return fitness

	# 距离计算
	# def calculate_dis(self, x):
	# 	ctc_dis = calculate_tcost_dis()
	# 	con_dis = ctc_dis.trackcost(x)
	# 	return con_dis

	# 进化过程
	def evolve(self):
		time_pso = 0  # 存储程序当前运行时间
		while (time_pso < max_time):
			time_pso = timer()
			r1 = np.random.rand(self.population_size, self.dim)
			r2 = np.random.rand(self.population_size, self.dim)

			# 更新速度和权重
			self.v = self.w * self.v + self.c1 * r1 * (self.p - self.x) + self.c2 * r2 * (self.pg - self.x)
			self.v = np.clip(self.v, -1, 1)  # 限定速度范围
			self.x = self.v + self.x
			self.x = np.clip(self.x, 0, numU)
			fitness = self.calculate_fitness(self.x)
			# con_dis = self.calculate_dis(self.x)



			# 需要更新的个体
			update_id = np.greater(self.individual_best_fitness, fitness)
			self.p[update_id] = self.x[update_id]
			self.individual_best_fitness[update_id] = fitness[update_id]

			# 新一代出现了更小的fitness，所以更新全局最优fitness和位置
			if np.min(fitness) < self.global_best_fitness:
				self.pg = self.x[np.argmin(fitness)]
				self.global_best_fitness = np.min(fitness)
			self.time_mat.append(time_pso)
			self.fitness_mat.append(self.global_best_fitness)


			print('当前运行时间：%.5f' % (time_pso))
			print('当前最优值: %.5f' % (self.global_best_fitness))
			print('分配方案：', deco(self.x[np.argmin(fitness)]))  # 显示任务分配结果
			np.set_printoptions(threshold=np.inf) #完全显示分配矩阵
			print('分配矩阵：', T(self.x[np.argmin(fitness)]))
			print('航迹距离', condis(self.x[np.argmin(fitness)]))

			TMatrix = T(self.x[np.argmin(fitness)])
			condistance = condis(self.x[np.argmin(fitness)])

		tu_diagram(self.time_mat, self.fitness_mat)  # 绘制fitness曲线图
		scheme = deco(self.x[np.argmin(fitness)])

		# return scheme, TMatrix, condistance
		return scheme, TMatrix, condistance

if __name__ == '__main__':
	# 无人机相关参数设置
	base = 4
	cluster = 22
	combination = 16
	L = [1, 2, 1, 3, 3, 1, 2, 2, 2, 2, 1, 2, 1, 2, 2, 3, 2, 2, 2, 3, 1, 3]  # 聚集点需求total：43
	L_sum = sum(L)
	La = genLaMatrix(L_sum, base)
	La_sum = sum(La)
	capacity = [i * 3 for i in La]
	ftcharge = 0.75  # 充电45分钟
	fv = 50  # 飞行速度50km/h
	Qmax = ftcharge * fv

	# 无人机和目标点初始坐标
	UAV_start = genUAVpos()
	UAV_start_pos = np.concatenate((UAV_start[0], UAV_start[1], UAV_start[2], UAV_start[3]))  # 合并成数组
	target = genclpos()
	target_pos = np.concatenate((target[0], target[1], target[2], target[3], target[4], target[5], target[6],
								 target[7], target[8], target[9], target[10], target[11], target[12], target[13],
								 target[14], target[15], target[16], target[17], target[18], target[19], target[20],
								 target[21]))  # 合并成数组

	# 无人机/目标点数目
	La = genLaMatrix(L_sum, 4)
	numU = sum(La)
	numT = sum(L)

	row = numU
	column = numT
	UtoT, TtoT, max_dis = normal()

	# 无人机任务执行时间
	# timeu = np.random.uniform(50, 80, (numU, numT))
	timeu = 0

	# 无人机充电时间，单位h
	ftcharge = 0.75 * 3600

	# 无人机飞行速度，单位km/h
	flyv = 50

	# 最大飞行半径,单位m
	max_flight = ftcharge * flyv * 1000
	# max_flight = 1

	# 种群数量
	population_size = 50

	# 最大迭代时间，单位s
	max_time = 0.1

	# 迭代次数
	max_steps = 1000

	#time_s赋值
	a = 0 #标志位，timestart只在第一次赋值
	time_start = time_end = 0 #初始化

	timer() # timestart进行赋值
	pso = PSO()
	scheme, TMatrix, condistance = pso.evolve()
	timefly = time_ufly(scheme)
	timefly = timefly * 3600
	tu_scatter(scheme)  # 绘制散点图
	timefly = time_ufly(scheme)
	# gante(timefly, scheme, timeu)#绘制甘特图
	print('最佳方案：', scheme)  # 最终方案
	print('最佳分配矩阵', TMatrix)
	print('最佳方案总航迹', condistance)


#待解决
#P1.e-cargo 协作
#P2.参数设置
#P3.飞行速度时间单位
#P4.e-cargo+粒子群
