import matplotlib.pyplot as plt
from matplotlib.ticker import MaxNLocator
import numpy as np
import xlrd
import pulp as pl
import pandas as pd
from math import cos, sin, acos, asin, pi, atan, fabs, ceil, floor
from collections import defaultdict
import random
import time
import matplotlib.pyplot as plt

# 计算飞出基站无人机数量
def conout(La, T_sum):
	# T_sum = np.sum(T, axis = 0)
	# T_sum = np.array(T_sum)
	temp = 0
	temp2 = 0
	temp3 = 0
	retemp = 0
	conout_UAV = [[] for k in range(base)]
	for i in range(base):
		if i == 0:
			temp = La[i]
			for j in range(0, temp):
				if np.array(T_sum)[j] != 0:
					retemp += 1
			conout_UAV[i] = retemp
			retemp = 0
		if i == 1:
			temp2 = La[i] + La[i-1]
			temp3 = La[i-1]
			for j in range(temp3, temp2):
				if np.array(T_sum)[j] != 0:
					retemp += 1
			conout_UAV[i] = retemp
			temp2 = 0
			temp3 = 0
			retemp = 0
		if i == 2:
			temp2 = La[i] + La[i-1] + La[i-2]
			temp3 = La[i] + La[i-1]
			for j in range(temp3, temp2):
				if np.array(T_sum)[j] != 0:
					retemp += 1
			conout_UAV[i] = retemp
			temp2 = 0
			temp3 = 0
			retemp = 0
		if i == 3:
			temp2 = La[i] + La[i-1] + La[i-2] + La[i-3]
			temp3 = La[i] + La[i-1] + La[i-2]
			for j in range(temp3, temp2):
				if np.array(T_sum)[j] != 0:
					retemp += 1
			conout_UAV[i] = retemp
			temp2 = 0
			temp3 = 0
			retemp = 0
	return conout_UAV


if __name__ == '__main__':
	base = 4
	La = [13, 15, 14, 12]
	T_sum = [0.00000, 2.00000, 0.00000, 1.00000, 1.00000, 2.00000, 0.00000, 1.00000, 0.00000, 1.00000, 1.00000, 2.00000,
			 3.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 1.00000, 0.00000, 3.00000, 0.00000, 1.00000, 0.00000,
			 3.00000, 3.00000, 1.00000, 3.00000, 1.00000, 0.00000, 0.00000, 1.00000, 0.00000, 2.00000, 0.00000, 2.00000,
			 1.00000, 0.00000, 0.00000, 1.00000, 0.00000, 0.00000, 1.00000, 1.00000, 1.00000, 0.00000, 1.00000,
			 0.00000, 0.00000, 1.00000, 0.00000, 1.00000, 0.00000, 0.00000]

	# 记录总飞出无人机数
	# T_sum = np.sum(TMatrix, axis=0)
	T_sum = np.array(T_sum)
	temp_cont = 0
	for i in range(len(T_sum)):
		if np.array(T_sum)[i] != 0:
			temp_cont +=1

	conout_UAV = conout(La, T_sum)  # 各基站飞出无人机数
	print(conout_UAV)
	print(temp_cont)
