# numU = 2
# numT = 3
# UandT = [[0],[1,2]]
# distanceUC = [[1,2,3],
#               [2,2,3]]
# distanceCC = [[1,1,1],
#               [2,2,2],
#               [3,3,3]]
#
#
# condis = [[] for k in range(numU)]
# temp = 0
# temp2 = 0
# temp3 = 0
# temp4 = 0
# for i in range(numU):
#     if len(UandT[i]) == 0:
#         condis[i] = 0
#         pass
#     else:
#         if len(UandT[i]) == 1:
#             temp = UandT[i][0]
#             condis[i] = distanceUC[i][temp] * 2
#         else:
#             temp = UandT[i][0]
#             condis[i] = distanceUC[i][temp]
#             if len(UandT[i]) == 2:
#                 temp2 = UandT[i][1]
#                 retemp = distanceCC[temp][temp2] + distanceUC[i][temp2]
#                 condis[i] += retemp
#             if len(UandT[i]) == 3:
#                 temp3 = UandT[i][2]
#                 retemp = distanceCC[temp][temp2] + distanceCC[temp2][temp3] + distanceUC[i][temp3]
#                 condis[i] += retemp
#             if len(UandT[i]) == 4:
#                 temp4 = UandT[i][3]
#                 retemp = distanceCC[temp][temp2] + distanceCC[temp2][temp3] + distanceCC[temp3][temp4] + distanceUC[i][
#                     temp4]
#                 condis[i] += retemp
#             if len(UandT[i]) == 5:
#                 temp5 = UandT[i][4]
#                 retemp = distanceCC[temp][temp2] + distanceCC[temp2][temp3] + distanceCC[temp3][temp4] + \
#                          distanceCC[temp4][temp5] + distanceUC[i][temp5]
#                 condis[i] += retemp


# def trackcost(self, taskcode):
#     for k in range(population_size):
#         cost_sum = 0
#         UandT = deco(taskcode[k])  # 每架无人机分配了哪些任务

        # self.outUAV, self.empty_UAV = outB(numU, UandT)
import numpy as np

outUAV = [1,1,0,0]
empty_UAV = [1,1,0,0]
numU = 2
numT = 3
base = 4
UandT = [[0],[1,2]]
distanceUC = [[1,2,3],
              [2,2,3]]
distanceCC = [[1,1,1],
              [2,2,2],
              [3,3,3]]
distance_BC = [[1,2,3],
               [2,2,4],
               [3,3,3],
               [4,4,4]]
distanceUC_min = np.min(distanceUC, axis=0)
distanceUC_min2 = np.partition(distanceUC, kth=1, axis=0)[1]
distanceUC_max2 = np.partition(distanceUC, kth=-2, axis=0)[-2]
distanceUC_max = np.partition(distanceUC, kth=-1, axis=0)[-1]
inUAV = [0 for i in range(base)]

for i in range(numU):
    UAV = UandT[i]  # 第i架无人机需要执行的任务
    cost = 0
    if len(UAV) == 0:  # 无人机没有任务
        inUAV[i] = 0
    # pass

    elif len(UAV) == 1:  # 无人机有一个任务
        # dis0 = UtoT[i][UAV[0]]
        # disU[i].append(dis0)
        for t in range(base):
            if distance_BC[i][UAV[0]] == distanceUC_min[UAV[0]] and empty_UAV[t] > 0:
                # dis0_in = distance_BC[t][UAV[0]]
                # disU[i].append(dis0_in)
                inUAV[t] = t + 1
                empty_UAV[t] = empty_UAV[t] - 1
            elif distance_BC[t][UAV[0]] == distanceUC_min2[UAV[0]] and empty_UAV[t] > 0:
                # dis0_in = distance_BC[t][UAV[0]]
                # disU[i].append(dis0_in)
                inUAV[t] = t + 1
                empty_UAV[t] = empty_UAV[t] - 1
            elif distance_BC[t][UAV[0]] == distanceUC_max2[UAV[0]] and empty_UAV[t] > 0:
                # dis0_in = distance_BC[t][UAV[0]]
                # disU[i].append(dis0_in)
                inUAV[t] = t + 1
                empty_UAV[t] = empty_UAV[t] - 1
            elif distance_BC[t][UAV[0]] == distanceUC_max[UAV[0]] and empty_UAV[t] > 0:
                # dis0_in = distance_BC[t][UAV[0]]
                # disU[i].append(dis0_in)
                inUAV[t] = t + 1
                empty_UAV[t] = empty_UAV[t] - 1

    else:  # 无人机有超过一个任务
        # dis1 = UtoT[i][UAV[0]]
        # disU[i].append(dis1)
        for j in range(len(UAV) - 1):
            # dis2 = TtoT[UAV[j]][UAV[j + 1]]  # 访问多目标点，目标点之间距离
            # disU[i].append(dis2)

        # dis3 = self.UtoT[i][UAV[len(UAV) - 1]] #返回原基站
            for t in range(base):
                if distance_BC[t][UAV[len(UAV) - 1]] == distanceUC_min[UAV[len(UAV) - 1]] and \
                        empty_UAV[t] > 0:  # 返回其他基站(比较是否是最短距离且判断基站是否有空位)
                    # dis3 = self.distance_BC[t][UAV[len(UAV) - 1]]
                    # disU[i].append(dis3)
                    inUAV[t] = t + 1
                    empty_UAV[t] = self.empty_UAV[t] - 1
                elif distance_BC[t][UAV[len(UAV) - 1]] == distanceUC_min2[UAV[len(UAV) - 1]] and \
                        empty_UAV[t] > 0:
                    # dis3 = self.distance_BC[t][UAV[len(UAV) - 1]]
                    # disU[i].append(dis3)
                    inUAV[t] = t + 1
                    empty_UAV[t] = empty_UAV[t] - 1
                elif distance_BC[t][UAV[len(UAV) - 1]] == distanceUC_max2[UAV[len(UAV) - 1]] and \
                        empty_UAV[t] > 0:
                    # dis3 = distance_BC[t][UAV[len(UAV) - 1]]
                    # disU[i].append(dis3)
                    inUAV[t] = t + 1
                    empty_UAV[t] = empty_UAV[t] - 1
                elif distance_BC[t][UAV[len(UAV) - 1]] == distanceUC_max[UAV[len(UAV) - 1]] and \
                        empty_UAV[t] > 0:
                    # dis3 = self.distance_BC[t][UAV[len(UAV) - 1]]
                    # disU[i].append(dis3)
                    inUAV[t] = t + 1
                    empty_UAV[t] = empty_UAV[t] - 1


#     cost = sum(self.disU[i])
#     # cost = cost + cont(cost * self.max_dis)
#     cost = cost + cont(cost)
#     cost_sum = cost_sum + cost
# self.fitness = np.append(self.fitness, cost_sum)
# return self.fitness, self.inUAV
