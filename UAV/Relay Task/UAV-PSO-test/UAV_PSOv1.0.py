from pso import PSO
import matplotlib.pyplot as plt
from matplotlib.ticker import MaxNLocator
import numpy as np
import xlrd
import pandas as pd
from math import cos, sin, acos, asin, pi, atan, fabs, ceil, floor
# from collections import defaultdict
import random
import time
from gurobipy import *

# 粒子群

# 先随机生成基站位置和算出La，再算出无人机位置
def genUAVpos(base):
    base_start = np.random.uniform(0,20,(base,2))
    temp = 0
    tempsum = []
    UAV_start = []
    for i in range(0,len(La)):
        temp = La[i]
        tempsum = np.tile(base_start[i], (La[i], 1))
        tempsum = np.array(tempsum)
        UAV_start.append(tempsum)
    return UAV_start

# 随机生成La
def genLaMatrix(role_amount, agent_amount):
		La = []
		temp = agent_amount
		while(temp>=1):
				val = math.ceil(role_amount/agent_amount)
				val += random.randint(1, agent_amount)
				if val not in La:
						La.append(val)
						temp-=1
		return La

# 计算两点之间距离公式
def calDistance(x1, x2, y1, y2):
    result = math.sqrt((x2-x1)**2+(y2-y1)**2)
    return result

# 聚集点与基站组合距离（21*4的矩阵）
def genDistance(x,y,x1,y1):
    distance_BC = []
    for j in range(base):
        tempMatrix = []
        for i in range(cluster):
            tempMatrix.append(calDistance(x[i],x1[j],y[i],y1[j]))
        distance_BC.append(tempMatrix)
    return distance_BC

# 聚集点与基站组合距离算出Q(21*16的矩阵)
def genQMatrix(distance_BC):
    Q = []
    for k in range(base):
        for j in range(base):
            temp = []
            for i in range(cluster):
                total_distance = []
                total_distance = distance_BC_array[k,i]+distance_BC_array[j,i]
                temp.append(total_distance)
            Q.append(temp)
    return Q

# 归一化后的Q
def getNormalizedQ(Q):
    max = min = Q[0][0]
    row = len(Q)
    column = len(Q[0])
    for i in range(row):
        for j in range(column):
            if (Q[i, j] > max):
                max = Q[i, j]
            if (Q[i, j] < min):
                min = Q[i, j]
    for i in range(row):
        for j in range(column):
            Q[i, j] = round((Q[i, j] - min) / (max - min), 4)
    return Q

# PSO
class PSO(object):
    def __init__(self):
        # 后期加速因子和惯性权重采用微分递减修改
        self.w = 1
        self.c1 = self.c2 = 2
        print(type(self.c2))
        self.population_size = globalv.population_size  # 粒子群数量
        self.dim = globalv.numT  # 搜索空间的维度

        # self.max_steps = globalv.max_steps   # 迭代次数
        self.x_bound = [0, globalv.numU]  # 解空间范围
        self.x = np.random.uniform(self.x_bound[0], self.x_bound[1],
                                   (self.population_size, self.dim))  # 初始化粒子群位置
        self.v = np.random.rand(self.population_size, self.dim)  # 初始化粒子群速度
        fitness = self.calculate_fitness(self.x)
        self.p = self.x  # 个体的最佳位置
        self.pg = self.x[np.argmin(fitness)]  # 全局最佳位置
        self.individual_best_fitness = fitness  # 个体的最优适应度
        self.global_best_fitness = np.min(fitness)  # 全局最佳适应度

        # 存储时间与与之对应的适应值
        self.time_mat = []
        self.fitness_mat = []

    # 适应值计算
    def calculate_fitness(self, x):
        ctc = calculate_tcost()
        fitness = ctc.trackcost(x)
        return fitness

    # 进化过程
    def evolve(self):
        time_pso = 0  # 存储程序当前运行时间
        while (time_pso < globalv.max_time):
            time_pso = timer()
            r1 = np.random.rand(self.population_size, self.dim)
            r2 = np.random.rand(self.population_size, self.dim)

            # 更新速度和权重
            self.v = self.w * self.v + self.c1 * r1 * (self.p - self.x) + self.c2 * r2 * (self.pg - self.x)
            self.v = np.clip(self.v, -1, 1)  # 限定速度范围
            self.x = self.v + self.x
            self.x = np.clip(self.x, 0, globalv.numU)
            fitness = self.calculate_fitness(self.x)

            # 需要更新的个体
            update_id = np.greater(self.individual_best_fitness, fitness)
            self.p[update_id] = self.x[update_id]
            self.individual_best_fitness[update_id] = fitness[update_id]

            # 新一代出现了更小的fitness，所以更新全局最优fitness和位置
            if np.min(fitness) < self.global_best_fitness:
                self.pg = self.x[np.argmin(fitness)]
                self.global_best_fitness = np.min(fitness)
            self.time_mat.append(time_pso)
            self.fitness_mat.append(self.global_best_fitness)

            print('当前运行时间：%.5f' % (time_pso))
            print('当前最优值: %.5f' % (self.global_best_fitness))
            print('分配方案：', decode.deco(self.x[np.argmin(fitness)]))  # 显示任务分配结果

        plots.tu_diagram(self.time_mat, self.fitness_mat)  # 绘制fitness曲线图
        scheme = decode.deco(self.x[np.argmin(fitness)])
        return scheme






if __name__ == '__main__':

    #无人机相关参数设置
    base = 4
    cluster = 22
    combination = 16
    L = [1,2,1,3,3,1,2,2,2,2,1,2,1,2,2,3,2,2,2,3,1,3]
    L_sum = sum(L) #聚集点需求total：43
    La = genLaMatrix(L_sum, base)
    capacity = [i * 3 for i in La]
    ftcharge = 0.75 # 充电45分钟
    fv = 50  # 飞行速度50km/h
    Qmax = ftcharge*fv

    # 生成所有无人机坐标和聚集点坐标
    UAV_start = genUAVpos(base)
    UAV_start = np.concatenate((UAV_start[0], UAV_start[1], UAV_start[2], UAV_start[3]))  # 合并成数组
    target_pos = np.random.uniform(0, 20, (cluster, 2))

    # 粒子群求解
    # pso = PSO()
    # scheme = pso.evolve()

