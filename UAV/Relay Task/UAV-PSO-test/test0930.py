import math
import matplotlib.pyplot as plt
from matplotlib.ticker import MaxNLocator
from math import floor
import numpy as np
import xlrd
import pandas as pd
import pulp as pl
import pandas as pd
from math import cos, sin, acos, asin, pi, atan, fabs, ceil, floor
from collections import defaultdict
import random
import time
from gurobipy import *
import copy as cp

# 计算传递闭包, type == 1 表示正类， type == -1 表示负类
def calTransitiveClosure(relationMat, typ = 1):
	resMat = cp.deepcopy(relationMat)
	MatLength = len(relationMat)
	for k in range(MatLength): # 经过中间节点k中转，能更新多少个传递关系
		for i in range(MatLength):
			for j in range(MatLength):
				if resMat[i][j] != 0:continue
				resMat[i][j] = typ * resMat[i][k]*resMat[k][j]
	return resMat

# 计算欧性闭包, type == 1 表示正类， type == -1 表示负类
def calEuclideanClosure(relationMat, typ = 1):
	resMat = cp.deepcopy(relationMat)
	MatLength = len(relationMat)
	for k in range(MatLength): # 经过中间节点k中转，能更新多少个欧性关系
		for i in range(MatLength):
			for j in range(MatLength):
				if resMat[i][j] != 0 or i == j:continue
				resMat[i][j] = typ * resMat[k][i]*resMat[k][j]
	return resMat


# 生成随机的ACC矩阵
def genRandRelationMat(matLength, times = 3): # times表示ACC矩阵的冲突数
	resMat = np.zeros((matLength, matLength))
	while times:
		i = random.randint(0, matLength-1)
		j = random.randint(0, matLength-1)
		if i != j and resMat[i][j] == 0:
			resMat[i][j] = -1 + 2*np.random.random()
			times -= 1
	return resMat

# 获取处理后的ACC矩阵 (type = 1 正关系 type = -1 负关系)
def genRelationMat(relationMat, type = 1): # times表示ACC矩阵的冲突数
	resMat = cp.deepcopy(relationMat)
	if type == 1:
		resMat[resMat < 0] = 0
	else:
		resMat[resMat > 0] = 0
	return resMat

# 利用tao值去更新ACC矩阵, type = 1 高合作，低冲突（理想团队）， type = 0 考虑合作的情况下，筛选低冲突， type = 1 考虑冲突的情况下，筛选合作
def genRelationMat_withTao(relationMat, tao_positive, type = 1):
	positive_relationMat = genRelationMat(relationMat, 1)
	negative_relationMat = genRelationMat(relationMat, -1)
	if type == 0:
		positive_relationMat[positive_relationMat < 0] = 0
		negative_relationMat[negative_relationMat > tao_positive-1] = 0
	elif type == 1:
		positive_relationMat[positive_relationMat < tao_positive] = 0
		negative_relationMat[negative_relationMat > tao_positive-1] = 0
	else:
		positive_relationMat[positive_relationMat < tao_positive] = 0
		negative_relationMat[negative_relationMat > 0] = 0
	return positive_relationMat+negative_relationMat;

# 对关系矩阵降维处理，输出
def dimensionalityReduction(relationMat, correspondingList):
	resMat = []
	for i in range(len(relationMat)):
		for j in range(len(relationMat[0])):
			if relationMat[i][j] != 0:
				resMat.append([int(correspondingList[i][0]), int(correspondingList[i][1]), int(correspondingList[j][0]), int(correspondingList[j][1]), relationMat[i][j]])
	return resMat

# 计算两点之间距离公式
def calDistance(x1, x2, y1, y2):
    result = math.sqrt((x2-x1)**2+(y2-y1)**2)
    return result

# 聚集点与聚集点组合距离算出Q(22*2的矩阵)
def genCCMatrix(x,y):
    Qc = []
    for i in range(cluster):
        temp = []
        for j in range(cluster):
            temp.append(calDistance(x[i],x[j],y[i],y[j]))
        Qc.append(temp)
    return Qc

def getNormalizedQ(Q):
    max = min = Q[0][0]
    row = len(Q)
    column = len(Q[0])
    for i in range(row):
        for j in range(column):
            if (Q[i, j] > max):
                max = Q[i, j]
            if (Q[i, j] < min):
                min = Q[i, j]
    for i in range(row):
        for j in range(column):
            Q[i, j] = round((Q[i, j] - min) / (max - min), 4)
    return Q

x = [1.61, 1.24, 0.98, 4.66, 6.75, 10.5, 13.81, 17.33, 12.41, 17.09, 2.73, 9.39, 12.44, 17.57, 12.54, 17.22, 8.39, 6.35, 2.28, 2.78, 7.99, 15.08]
y = [19.53, 14.76, 12.94, 15.11, 15.87, 16.48, 18.55, 17.36, 14.76, 13.79, 11.4, 8.55, 10.19, 8.39, 6.3, 3.41, 3.73, 1.06, 2.8, 6.61, 10.16, 8.88]
x1 = [18.95, 6.42, 11.52, 16.77]
y1 = [6.92, 9.6, 18.78, 15.33]

if __name__ == '__main__':
	b_num = 1
	base = 4
	outbase = 4
	inbase = 4
	cluster = 22
	combination = 16
	ftcharge = 0.75  # 充电45分钟
	fv = 50  # 飞行速度50km/h
	Qmax = ftcharge * fv
	L = [1, 2, 1, 3, 3, 1, 2, 2, 2, 2, 1, 2, 1, 2, 2, 3, 2, 2, 2, 3, 1, 3]  # total 43
	capacity = [33, 21, 39, 54]  # 基站初始容量均为48, total 192

	La = [15, 13, 14, 12]
	UAV_num = sum(La)

	Qc = genCCMatrix(x, y)
	Qc = np.array(Qc)
	QcMatrix = getNormalizedQ(Qc)

	correspondingList = []
	for i in range(UAV_num):
		for j in range(cluster):
			correspondingList.append((i, j))

	matLength = UAV_num * cluster
	# interval = 0.05
	# tao_positive = 0
	# x = np.arange(0, 1, interval)
	# y_orig = []

	# while 1 - tao_positive >= 0:
	print("------------begin")
	relationMat_orig = Qc
	relationMat = cp.deepcopy(relationMat_orig)
	# relationMat = genRelationMat_withTao(relationMat, tao_positive, 2)
	dimension_relationMat = dimensionalityReduction(relationMat, correspondingList)
	# print(len(dimension_relationMat))
	correspondingList = np.array(correspondingList)
	dimension_relationMat = np.array(dimension_relationMat)
	# relationMat = cp.deepcopy(relationMat_orig)
	# res = genRelationMat_withTao(relationMat, tao_positive, 2)
	# dimension_relationMat_res = dimensionalityReduction(res, correspondingList)
	# dimension_relationMat_res = dimensionalityReduction(relationMat, correspondingList)
	# print(len(dimension_relationMat_res))



