# # 容量约束 批次之间 ( k 批次飞入 <= k+1 批次飞出)
# for b in range(0, b_num - 2):
#     for i in range(base):
#         sumoutd = 0
#         sumind = 0
#         for temp in (0, b + 1):
#             for j in range(col):
#                 sumind += lpvars[temp][i][j] + lpvars[temp][i + 4][j] + lpvars[temp][i + 8][j] + lpvars[temp][i + 12][
#                     j]  # k批次飞入总和
#         for temp_2 in (0, b + 2):
#             for j in range(col):
#                 sumoutd += lpvars[temp_2][4 * i][j] + lpvars[temp_2][4 * i + 1][j] + lpvars[temp_2][4 * i + 2][j] + \
#                            lpvars[temp_2][4 * i + 3][j]  # k+1批次飞出总和
#         pro += sumind <= sumoutd

for temp in (0,1):
    print('值', temp)