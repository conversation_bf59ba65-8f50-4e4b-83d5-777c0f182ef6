import math
import matplotlib.pyplot as plt
from matplotlib.ticker import MaxNLocator
from math import floor
import numpy as np
import xlrd
import pandas as pd
import pulp as pl
import pandas as pd
from math import cos, sin, acos, asin, pi, atan, fabs, ceil, floor
from collections import defaultdict
import random
import time

class Assignment:
	@classmethod
	# maxDrone: the amount of the drones
	def KM2(cls, Q, La, L):
		row = len(Q)
		col = len(Q[0])

		# build a optimal problem
		pro = pl.LpProblem('Max(connection and coverage)', pl.LpMinimize)
		# build variables for the optimal problem
		lpvars = [[pl.LpVariable("x"+str(i)+"y"+str(j), lowBound = 0, upBound = 1, cat='Integer') for j in range(col)] for i in range(row)]

		# build optimal function
		all = pl.LpAffineExpression()
		for i in range(0,row):
			for j in range(0, col):
				all += Q[i][j]*lpvars[i][j]

		pro += all

		# build constraint for each role
		RSum = 0
		for j in range(col):
			for i in range(row):
				RSum += lpvars[i][j]
			pro += RSum <= L[j] # 小于基站容量
			RSum = 0

		ASum = 0
		for i in range(row):
			for j in range(col):
				ASum += lpvars[i][j]
			pro += ASum == La[i] # UAV全飞出
			ASum = 0

		# solve optimal problem
		status = pro.solve()
		# print("Assignment Status: ", pl.LpStatus[status])
		# print("Final Assignment Result", pl.value(pro.objective))

		# get the result of T matrix
		T = [[ lpvars[i][j].varValue for j in range(col) ] for i in range(row)]
		return T
		# return T, pl.value(pro.status), pl.value(pro.objective)


# 归一化后的Q
def getNormalizedQ(Q):
    max = min = Q[0][0]
    row = len(Q)
    column = len(Q[0])
    for i in range(row):
        for j in range(column):
            if (Q[i, j] > max):
                max = Q[i, j]
            if (Q[i, j] < min):
                min = Q[i, j]
    for i in range(row):
        for j in range(column):
            Q[i, j] = round((Q[i, j] - min) / (max - min), 4)
    return Q

if __name__ == '__main__':
	cluster = 21  # agent:cluster
	base = 4  # role:base

	row = 21
	base = 4
	# 第二次指派
	L2 = [7, 24, 12, 17] # 基站剩余可容纳量，第一次指派基站派出 [13, 7, 10, 10]
	La2 = [1, 2, 1, 3, 3, 1, 2, 2, 2, 2, 1, 2, 1, 2, 2, 3, 2, 2, 2, 3, 1] # 21个点可派出的最大无人机数
	Q2 =[
		[21.44032882, 11.03363041, 9.938339902, 15.73103938],
		[19.36774897, 7.311497795, 11.03806142, 15.54045688],
		[18.95155139, 6.383510006, 12.04978008, 15.96985285],
		[16.4705859, 5.784263134, 7.780006427, 12.11199818],
		[15.13084598, 6.278678205, 5.587575503, 10.03454035],
		[12.7591575, 7.99879991, 2.516028617, 6.374590183],
		[12.71520743, 11.60666188, 2.301521236, 4.373785546],
		[10.56494203, 13.38826725, 5.98101162, 2.105825254],
		[10.20966209, 7.906054642, 4.117341375, 4.397101318],
		[8.086810249, 11.8652855, 6.851642139, 0.627694193],
		[16.82732302, 4.105618102, 11.47730369, 14.57966049],
		[9.697963704, 3.150142854, 10.44939233, 10.02161664],
		[7.285121825, 6.048842865, 8.639126113, 6.720751446],
		[2.016258912, 11.21546254, 12.02308613, 6.985957343],
		[6.439914596, 6.953013735, 12.52161331, 9.971649813],
		[3.913182848, 12.44813641, 16.39289175, 11.9284911],
		[11.03130545, 6.191752579, 15.37203305, 14.31029],
		[13.89602821, 8.54028688, 18.45880007, 17.66944538],
		[17.17158409, 7.961130573, 18.4590899, 19.15622614],
		[16.17297128, 4.710594442, 14.98320727, 16.48509933],
		[11.42887571, 1.666883319, 9.314789316, 10.18907749]
	]
	print(Q2)
	Q2 = np.array(Q2)
	Q2 = getNormalizedQ(Q2)
	print(Q2)
	T2 = Assignment.KM2(Q2, La2, L2)
	TMAT2 = np.array(T2)
	print(TMAT2)