import math
import matplotlib.pyplot as plt
from matplotlib.ticker import MaxNLocator
from math import floor
import numpy as np
import xlrd
import pandas as pd
import pulp as pl
import pandas as pd
from math import cos, sin, acos, asin, pi, atan, fabs, ceil, floor
from collections import defaultdict
import random
import time

# 指派v1.0：分两次指派，基站飞出到聚集点；聚集点飞回不同基站

# 散点图
# x = [1.61, 1.24, 0.98, 4.66, 6.75, 10.5, 13.81, 17.33, 12.41, 17.09, 2.73, 9.39, 12.44, 17.57, 12.54, 17.22, 8.39, 6.35, 2.28, 2.78, 7.99]
# y = [19.53, 14.76, 12.94, 15.11, 15.87, 16.48, 18.55, 17.36, 14.76, 14.79, 11.4, 8.55, 10.19, 8.39, 6.3, 3.41, 3.73, 1.06, 2.8, 6.61, 10.16]
x = [1.61, 1.24, 0.98, 4.66, 6.75, 10.5, 13.81, 17.33, 12.41, 17.09, 2.73, 9.39, 12.44, 17.57, 12.54, 17.22, 8.39, 6.35, 2.28, 2.78, 7.99, 15.08]
y = [19.53, 14.76, 12.94, 15.11, 15.87, 16.48, 18.55, 17.36, 14.76, 13.79, 11.4, 8.55, 10.19, 8.39, 6.3, 3.41, 3.73, 1.06, 2.8, 6.61, 10.16, 8.88]
plt.scatter(x, y, color = "green", s = 20, marker = 'x')
x1 = [18.95, 6.42, 11.52, 16.77]
y1 = [6.92, 9.6, 18.78, 15.33]
plt.scatter(x1, y1, color = "red", s = 20, marker = 'x')
plt.xlim(0, 20, 1)
plt.ylim(0, 20, 2)
plt.gca().xaxis.set_major_locator(MaxNLocator(integer=True))   #限制坐标轴刻度为整数
plt.gca().yaxis.set_major_locator(MaxNLocator(integer=True))
plt.show()


class Assignment:
    @classmethod
    # maxDrone: the amount of the drones
    def KM(cls, Q, La, L):
        row = len(Q)
        col = len(Q[0])

        # build a optimal problem
        pro = pl.LpProblem('Max(connection and coverage)', pl.LpMinimize)
        # build variables for the optimal problem
        lpvars = [[pl.LpVariable("x"+str(i)+"y"+str(j), lowBound = 0, upBound = 1, cat='Integer') for j in range(col)] for i in range(row)]

        # build optimal function
        all = pl.LpAffineExpression()
        for i in range(0,row):
            for j in range(0,col):
                all += Q[i][j]*lpvars[i][j]

        pro += all

        # build constraint for each role
        for j in range(0,col):
            pro += pl.LpConstraint(pl.LpAffineExpression([ (lpvars[i][j],1) for i in range(0,row)]) , 0,"L"+str(j),L[j])
        # build constraint for each agent
        for i in range(0,row):
            pro += pl.LpConstraint(pl.LpAffineExpression([ (lpvars[i][j],1) for j in range(0,col)]) , -1,"La"+str(i), La[i])

        # solve optimal problem
        status = pro.solve()
        # print("Assignment Status: ", pl.LpStatus[status])
        # print("Final Assignment Result", pl.value(pro.objective))

        # get the result of T matrix
        T = [[ lpvars[i][j].varValue for j in range(col) ] for i in range(row)]
        return T
    # return T, pl.value(pro.status), pl.value(pro.objective)

class Assignment2:
    @classmethod
    # maxDrone: the amount of the drones
    def KM2(cls, Q, La, L):
        row = len(Q)
        col = len(Q[0])

        # build a optimal problem
        pro = pl.LpProblem('Max(connection and coverage)', pl.LpMinimize)
        # build variables for the optimal problem
        lpvars = [[pl.LpVariable("x"+str(i)+"y"+str(j), lowBound = 0, upBound = 1, cat='Integer') for j in range(col)] for i in range(row)]

        # build optimal function
        all = pl.LpAffineExpression()
        for i in range(0,row):
            for j in range(0, col):
                all += Q[i][j]*lpvars[i][j]

        pro += all

        # build constraint for each role
        RSum = 0
        for j in range(col):
            for i in range(row):
                RSum += lpvars[i][j]
            pro += RSum <= L[j] # 小于基站容量
            RSum = 0

        ASum = 0
        for i in range(row):
            for j in range(col):
                ASum += lpvars[i][j]
            pro += ASum == La[i] # UAV全飞出
            ASum = 0

        # solve optimal problem
        status = pro.solve()
        # print("Assignment Status: ", pl.LpStatus[status])
        # print("Final Assignment Result", pl.value(pro.objective))

        # get the result of T matrix
        T = [[ lpvars[i][j].varValue for j in range(col) ] for i in range(row)]
        return T
    # return T, pl.value(pro.status), pl.value(pro.objective)


# 归一化后的Q
def getNormalizedQ(Q):
    max = min = Q[0][0]
    row = len(Q)
    column = len(Q[0])
    for i in range(row):
        for j in range(column):
            if (Q[i, j] > max):
                max = Q[i, j]
            if (Q[i, j] < min):
                min = Q[i, j]
    for i in range(row):
        for j in range(column):
            Q[i, j] = round((Q[i, j] - min) / (max - min), 4)
    return Q


# 第一次指派图
def genpath(TMAT,x,y,x1,y1):
    TMAT_row = 4
    TMAT_col = 21
    path1_x = []
    path2_x = []
    path3_x = []
    path4_x = []
    path1_y = []
    path2_y = []
    path3_y = []
    path4_y = []
    for i in range(TMAT_row):
        for j in range(TMAT_col):
            if T[i][j] == 1 and i == 0:
                path1_x.append((x1[i], x[j]))
                path1_y.append((y1[i], y[j]))
            elif T[i][j] == 1 and i == 1:
                path2_x.append((x1[i], x[j]))
                path2_y.append((y1[i], y[j]))
            elif T[i][j] == 1 and i == 2:
                path3_x.append((x1[i], x[j]))
                path3_y.append((y1[i], y[j]))
            elif T[i][j] == 1 and i == 3:
                path4_x.append((x1[i], x[j]))
                path4_y.append((y1[i], y[j]))
    return path1_x, path1_y, path2_x, path2_y, path3_x, path3_y, path4_x, path4_y

# 第二次指派图
def genpath2(TMAT2,x,y,x1,y1):
    TMAT2_row = 21  # i 聚集点
    TMAT2_col = 4  # j 基站
    path11_x = []
    path22_x = []
    path33_x = []
    path44_x = []
    path11_y = []
    path22_y = []
    path33_y = []
    path44_y = []
    for i in range(TMAT2_row):
        for j in range(TMAT2_col):
            if T2[i][j] == 1 and j == 0:
                path11_x.append((x[i], x1[j]))
                path11_y.append((y[i], y1[j]))
            elif T2[i][j] == 1 and j == 1:
                path22_x.append((x[i], x1[j]))
                path22_y.append((y[i], y1[j]))
            elif T2[i][j] == 1 and j == 2:
                path33_x.append((x[i], x1[j]))
                path33_y.append((y[i], y1[j]))
            elif T2[i][j] == 1 and j == 3:
                path44_x.append((x[i], x1[j]))
                path44_y.append((y[i], y1[j]))
    return path11_x, path11_y, path22_x, path22_y, path33_x, path33_y, path44_x, path44_y


if __name__ == '__main__':
    # 第一次指派# cluster = 21  # role:cluster
    base = 4  # agent:base
    L = [1, 2, 1, 3, 3, 1, 2, 2, 2, 2, 1, 2, 1, 2, 2, 3, 2, 2, 2, 3, 1] # total 40
    La = [20, 31, 22, 27] # total 100
    # La1 = np.array(La)
    # La1 = La1/2
    # La1 = np.floor(La1)
    # print(La1)
    Q = [
        [21.44032882, 19.36774897, 18.95155139, 16.4705859, 15.13084598, 12.7591575, 12.71520743, 10.56494203,
         10.20966209, 8.086810249, 16.82732302, 9.697963704, 7.285121825, 2.016258912, 6.439914596, 3.913182848,
         11.03130545, 13.89602821, 17.17158409, 16.17297128, 11.42887571],
        [11.03363041, 7.311497795, 6.383510006, 5.784263134, 6.278678205, 7.99879991, 11.60666188, 13.38826725,
         7.906054642, 11.8652855, 4.105618102, 3.150142854, 6.048842865, 11.21546254, 6.953013735, 12.44813641,
         6.191752579, 8.54028688, 7.961130573, 4.710594442, 1.666883319],
        [9.938339902, 11.03806142, 12.04978008, 7.780006427, 5.587575503, 2.516028617, 2.301521236, 5.98101162,
         4.117341375, 6.851642139, 11.47730369, 10.44939233, 8.639126113, 12.02308613, 12.52161331, 6.39289175,
         15.37203305, 18.45880007, 18.4590899, 14.98320727, 9.314789316],
        [15.73103938, 15.54045688, 15.96985285, 12.11199818, 10.03454035, 6.374590183, 4.373785546, 2.105825254,
         4.397101318, 0.627694193, 14.57966049, 10.02161664, 6.720751446, 6.985957343, 9.971649813, 11.9284911,
         14.31029, 17.66944538, 19.15622614, 16.48509933, 10.18907749]
    ]
    Q = np.array(Q)
    Q = getNormalizedQ(Q)
    print(Q)
    T = Assignment.KM(Q, La, L)
    TMAT = np.array(T)
    print(TMAT)


    # 点坐标
    cluster_list = [(1.61, 19.53), (1.24, 14.76), (0.98, 12.94), (4.66, 15.11), (6.75, 15.87), (10.5, 16.48), (13.81, 18.55),
                    (17.33, 17.36), (12.41, 14.76), (17.09, 14.79), (2.73, 11.4), (9.39, 8.55), (12.44, 10.19), (17.57, 8.39),
                    (12.54, 6.3), (17.22, 3.41), (8.39, 3.73), (6.35, 1.06), (2.28, 2.8), (2.78, 6.61), (7.99, 10.16)]
    x = [1.61, 1.24, 0.98, 4.66, 6.75, 10.5, 13.81, 17.33, 12.41, 17.09, 2.73, 9.39, 12.44, 17.57, 12.54, 17.22, 8.39,
         6.35, 2.28, 2.78, 7.99]
    y = [19.53, 14.76, 12.94, 15.11, 15.87, 16.48, 18.55, 17.36, 14.76, 14.79, 11.4, 8.55, 10.19, 8.39, 6.3, 3.41, 3.73,
         1.06, 2.8, 6.61, 10.16]

    base_list = [(18.95, 6.92), (6.42, 9.6), (11.52, 18.78), (16.77, 15.33)]
    x1 = [18.95, 6.42, 11.52, 16.77]
    y1 = [6.92, 9.6, 18.78, 15.33]

    # 第一次指派路径图
    path1_x,path1_y,path2_x,path2_y,path3_x,path3_y,path4_x,path4_y = genpath(TMAT,x,y,x1,y1)
    print(path1_x)
    print(path1_y)
    print(path2_x)
    print(path2_y)
    print(path3_x)
    print(path3_y)
    print(path4_x)
    print(path4_y)

    plt.figure()
    for i in range(len(path1_x)):
        plt.plot(path1_x[i], path1_y[i], linewidth='1', color='g')
        plt.scatter(path1_x[i], path1_y[i], color='b', marker='*')
        plt.scatter([18.95, ], [6.92, ], s=150, color='r', marker='*')
    for i in range(len(path2_x)):
        plt.plot(path2_x[i], path2_y[i], linewidth='1', color='b')
        plt.scatter(path2_x[i], path2_y[i], color='b', marker='*')
        plt.scatter([6.42, ], [9.6, ], s=150, color='r', marker='*')
    for i in range(len(path3_x)):
        plt.plot(path3_x[i], path3_y[i], linewidth='1', color='c')
        plt.scatter(path3_x[i], path3_y[i], color='b', marker='*')
        plt.scatter([11.52, ], [18.78, ], s=150, color='r', marker='*')
    for i in range(len(path4_x)):
        plt.plot(path4_x[i], path4_y[i], linewidth='1', color='y')
        plt.scatter(path4_x[i], path4_y[i], color='b', marker='*')
        plt.scatter([16.77, ], [15.33, ], s=150, color='r', marker='*')
    plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
    plt.title('第一次指派')
    plt.xlim(0, 20, 1)
    plt.ylim(0, 20, 2)
    plt.gca().xaxis.set_major_locator(MaxNLocator(integer=True))  # 限制坐标轴刻度为整数
    plt.gca().yaxis.set_major_locator(MaxNLocator(integer=True))
    plt.show()


    # 第二次指派
    Assign2_role = 4   # role:base
    Assign2_agent = 21   # agent:cluster
    L2 = [7, 24, 12, 17] # 基站剩余可容纳量，第一次指派基站派出 [13, 7, 10, 10]
    La2 = [1, 2, 1, 3, 3, 1, 2, 2, 2, 2, 1, 2, 1, 2, 2, 3, 2, 2, 2, 3, 1] # 21个点可派出的最大无人机数
    Q2 =[
    [21.44032882, 11.03363041, 9.938339902, 15.73103938],
    [19.36774897, 7.311497795, 11.03806142, 15.54045688],
    [18.95155139, 6.383510006, 12.04978008, 15.96985285],
    [16.4705859, 5.784263134, 7.780006427, 12.11199818],
    [15.13084598, 6.278678205, 5.587575503, 10.03454035],
    [12.7591575, 7.99879991, 2.516028617, 6.374590183],
    [12.71520743, 11.60666188, 2.301521236, 4.373785546],
    [10.56494203, 13.38826725, 5.98101162, 2.105825254],
    [10.20966209, 7.906054642, 4.117341375, 4.397101318],
    [8.086810249, 11.8652855, 6.851642139, 0.627694193],
    [16.82732302, 4.105618102, 11.47730369, 14.57966049],
    [9.697963704, 3.150142854, 10.44939233, 10.02161664],
    [7.285121825, 6.048842865, 8.639126113, 6.720751446],
    [2.016258912, 11.21546254, 12.02308613, 6.985957343],
    [6.439914596, 6.953013735, 12.52161331, 9.971649813],
    [3.913182848, 12.44813641, 16.39289175, 11.9284911],
    [11.03130545, 6.191752579, 15.37203305, 14.31029],
    [13.89602821, 8.54028688, 18.45880007, 17.66944538],
    [17.17158409, 7.961130573, 18.4590899, 19.15622614],
    [16.17297128, 4.710594442, 14.98320727, 16.48509933],
    [11.42887571, 1.666883319, 9.314789316, 10.18907749]
]
    print(Q2)
    Q2 = np.array(Q2)
    Q2 = getNormalizedQ(Q2)
    print(Q2)
    T2 = Assignment2.KM2(Q2, La2, L2)
    TMAT2 = np.array(T2)
    print(TMAT2)

    # 第二次指派路径图
    path11_x, path11_y, path22_x, path22_y, path33_x, path33_y, path44_x, path44_y = genpath2(TMAT2, x, y, x1, y1)
    print(path11_x)
    print(path11_y)
    print(path22_x)
    print(path22_y)
    print(path33_x)
    print(path33_y)
    print(path44_x)
    print(path44_y)

    plt.figure()
    for i in range(len(path11_x)):
        plt.plot(path11_x[i], path11_y[i], linewidth='1', color='g')
        plt.scatter(path11_x[i], path11_y[i], color='b', marker='*')
        plt.scatter([18.95, ], [6.92, ], s=150, color='r', marker='*')
    for i in range(len(path22_x)):
        plt.plot(path22_x[i], path22_y[i], linewidth='1', color='b')
        plt.scatter(path22_x[i], path22_y[i], color='b', marker='*')
        plt.scatter([6.42, ], [9.6, ], s=150, color='r', marker='*')
    for i in range(len(path33_x)):
        plt.plot(path33_x[i], path33_y[i], linewidth='1', color='c')
        plt.scatter(path33_x[i], path33_y[i], color='b', marker='*')
        plt.scatter([11.52, ], [18.78, ], s=150, color='r', marker='*')
    for i in range(len(path44_x)):
        plt.plot(path44_x[i], path44_y[i], linewidth='1', color='y')
        plt.scatter(path44_x[i], path44_y[i], color='b', marker='*')
        plt.scatter([16.77, ], [15.33, ], s=150, color='r', marker='*')
    plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
    plt.title('第二次指派')
    plt.xlim(0, 20, 1)
    plt.ylim(0, 20, 2)
    plt.gca().xaxis.set_major_locator(MaxNLocator(integer=True))  # 限制坐标轴刻度为整数
    plt.gca().yaxis.set_major_locator(MaxNLocator(integer=True))
    plt.show()
















