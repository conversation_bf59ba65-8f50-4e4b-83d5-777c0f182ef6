import networkx as nx
import matplotlib.pyplot as plt
import random

# 生成一个具有22个节点的随机树结构
G = nx.random_tree(22)

# 创建一个字典来存储节点的层级
node_levels = nx.shortest_path_length(G, source=0)

# 创建一个叶子节点列表（没有子节点的节点）
leaf_nodes = [node for node in G.nodes() if G.out_degree(node) == 0]

# 创建邻接矩阵
adj_matrix = nx.adjacency_matrix(G).toarray()

# 创建同级矩阵
sibling_matrix = [[1 if G.has_edge(i, j) else 0 for j in range(22)] for i in range(22)]

# 创建叶子节点向量
leaf_vector = [1 if node in leaf_nodes else 0 for node in range(22)]

# 绘制树形结构图
pos = nx.spring_layout(G)
nx.draw(G, pos, with_labels=True, node_size=500, node_color='lightblue')
plt.title("随机生成的树形结构图")
plt.show()

# 打印结果
print("邻接矩阵:")
for row in adj_matrix:
    print(row)

print("\n同级矩阵:")
for row in sibling_matrix:
    print(row)

print("\n叶子节点向量:")
print(leaf_vector)