import numpy as np


def generate_and_complete_matrix(n):
    # Generate a random upper triangular matrix
    upper_triangular = np.triu(np.random.randint(2, size=(n, n)), k=1)

    # Complete the lower triangular part by taking the transpose of the upper triangular matrix
    lower_triangular = upper_triangular.T

    # Combine upper and lower triangular matrices to form the complete matrix
    complete_matrix = upper_triangular + lower_triangular

    # Set diagonal elements to zero
    np.fill_diagonal(complete_matrix, 0)

    return complete_matrix


def print_matrix(matrix):
    for row in matrix:
        print(row)


# Generate and print the complete matrix
n = 10
complete_matrix = generate_and_complete_matrix(n)

print("Generated Matrix:")
print_matrix(complete_matrix)