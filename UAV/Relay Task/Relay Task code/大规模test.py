import math
import matplotlib.pyplot as plt
from matplotlib.ticker import MaxNLocator
from math import floor
import numpy as np
import xlrd
import pandas as pd
import pulp as pl
import pandas as pd
from math import cos, sin, acos, asin, pi, atan, fabs, ceil, floor
from collections import defaultdict
import random
import time

#  随机生成role
A = 0
B = 21
cluster = 50
x = np.random.choice(range(A, B), cluster)
y = np.random.choice(range(A, B), cluster)
print(x)
print(y)
dense = np.random.choice(range(1, 4), cluster)
print(dense)

# 随机生成agent
