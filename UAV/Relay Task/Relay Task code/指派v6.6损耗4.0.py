import math
import matplotlib.pyplot as plt
from matplotlib.ticker import MaxNLocator
from math import floor
import numpy as np
import xlrd
import pandas as pd
import pulp as pl
import pandas as pd
from math import cos, sin, acos, asin, pi, atan, fabs, ceil, floor
from collections import defaultdict
import random
import time
from gurobipy import *
import openpyxl

#   2022.11.9   故障（正常指派&故障后指派）记录空位
#   故障 + 修复批次 > b_num
#   故障 + 修复批次 <= b_num

class Assignment4:   # 故障后指派 故障 + 修复批次 <= b_num
    @classmethod
    def KM4(cls, Q, La, L, b_num):
        row = len(Q)
        col = len(Q[0])

        # build a optimal problem
        pro = pl.LpProblem('MIN(connection and coverage )', pl.LpMinimize)
        solver = pl.getSolver('GUROBI_CMD')

        # build variables for the optimal problem
        lpvars = [[[pl.LpVariable("x" + str(i) + "y" + str(j) + "l" + str(b), lowBound=0, upBound=1, cat='Integer') for
                    j in range(col)] for i in range(row)]for b in range(faub_num)]


        # build optimal function
        all = pl.LpAffineExpression()
        for b in range(0, faub_num):
            for i in range(0, row):
                for j in range(0,col):
                    all += Q[i][j] * lpvars[b][i][j]
        pro += all

        # build constraint
        ## 满足聚集点需求
        for b in range(0, faub_num):
            for j in range(0, col):
                tempSum = 0
                for i in range(0, row):
                    tempSum += lpvars[b][i][j]
                pro += tempSum == L[j]

        # la约束
        for b in range(0, faub_num):
            for i in range(0, outbase):
                tempSum = 0
                for j in range(0, col):
                    tempSum += lpvars[b][4 * i][j] + lpvars[b][4 * i + 1][j] + lpvars[b][4 * i + 2][j] + lpvars[b][4 * i + 3][j]
                pro += tempSum <= La[i]

        #   记录故障第一批飞出的空位
        for i in range(base):
            firstout = 0
            for j in range(col):
                firstout += lpvars[0][4 * i][j] + lpvars[0][4 * i + 1][j] + lpvars[0][4 * i + 2][j] + lpvars[0][4 * i + 3][j]
            empty[i] += firstout

        #   故障时的第一批（算空位）
        for b in range(0, 1):
            for i in range(base):
                sumin = 0
                sumout = 0
                faupla = fpn[i]  # 故障数
                for j in range(col):
                    sumin += lpvars[b][i][j] + lpvars[b][i + 4][j] + lpvars[b][i + 8][j] + lpvars[b][i + 12][j] #这一批飞出
                    sumout += lpvars[b + 1][4 * i][j] + lpvars[b + 1][4 * i + 1][j] + lpvars[b + 1][4 * i + 2][j] + lpvars[b + 1][4 * i + 3][j] #下一批飞出
                empty[i] += empty[i] + sumout - faupla
                pro += sumin <= empty[i]

        #   故障修复时的剩下批（算空位）
        for b in range(1, fauc - 1):
            for i in range(base):
                sumin = 0
                sumout = 0
                for j in range(col):
                    sumin += lpvars[b][i][j] + lpvars[b][i + 4][j] + lpvars[b][i + 8][j] + lpvars[b][i + 12][j] #这一批飞出
                    sumout += lpvars[b + 1][4 * i][j] + lpvars[b + 1][4 * i + 1][j] + lpvars[b + 1][4 * i + 2][j] + lpvars[b + 1][4 * i + 3][j] #下一批飞出
                empty[i] += empty[i] + sumout
                pro += sumin <= empty[i]

        #   故障后恢复正常
        for b in range(fauc - 1, fauc):
            for i in range(base):
                sumin = 0
                faupla = fpn[i]  # 故障数
                for j in range(col):
                    sumin += lpvars[b][i][j] + lpvars[b][i + 4][j] + lpvars[b][i + 8][j] + lpvars[b][i + 12][j]  # 这一批飞出
                pro += sumin <= empty[i] + faupla

        #   总容量约束
        for i in range(base):
            sumout = 0
            sumin = 0
            fin = beforef_in[i]
            fout = beforef_out[i]
            for b in range(0, faub_num):
                for j in range(col):
                    sumout += lpvars[b][4*i][j] + lpvars[b][4*i+1][j] + lpvars[b][4*i+2][j] + lpvars[b][4*i+3][j]
                    sumin += lpvars[b][i][j] + lpvars[b][i+4][j] + lpvars[b][i+8][j] + lpvars[b][i+12][j]
            pro += sumout == sumin + fin - fout

        # solve optimal problem
        status = pro.solve(solver)

        # get the result of T matrix
        T = [[[lpvars[b][i][j].varValue for j in range(col)] for i in range(row)] for b in range(faub_num)]

        return T

class Assignment3:   # 故障后指派 故障 + 修复批次 > b_num
    @classmethod
    def KM3(cls, Q, La, L, b_num):
        row = len(Q)
        col = len(Q[0])

        # build a optimal problem
        pro = pl.LpProblem('MIN(connection and coverage )', pl.LpMinimize)
        solver = pl.getSolver('GUROBI_CMD')

        # build variables for the optimal problem
        lpvars = [[[pl.LpVariable("x" + str(i) + "y" + str(j) + "l" + str(b), lowBound=0, upBound=1, cat='Integer') for
                    j in range(col)] for i in range(row)]for b in range(faub_num)]


        # build optimal function
        all = pl.LpAffineExpression()
        for b in range(0, faub_num):
            for i in range(0, row):
                for j in range(0,col):
                    all += Q[i][j] * lpvars[b][i][j]
        pro += all

        # build constraint
        ## 满足聚集点需求
        for b in range(0, faub_num):
            for j in range(0, col):
                tempSum = 0
                for i in range(0, row):
                    tempSum += lpvars[b][i][j]
                pro += tempSum == L[j]

        # la约束
        for b in range(0, faub_num):
            for i in range(0, outbase):
                tempSum = 0
                for j in range(0, col):
                    tempSum += lpvars[b][4 * i][j] + lpvars[b][4 * i + 1][j] + lpvars[b][4 * i + 2][j] + lpvars[b][4 * i + 3][j]
                pro += tempSum <= La[i]

        #   记录故障第一批飞出的空位
        for i in range(base):
            firstout = 0
            for j in range(col):
                firstout += lpvars[0][4 * i][j] + lpvars[0][4 * i + 1][j] + lpvars[0][4 * i + 2][j] + lpvars[0][4 * i + 3][j]
            empty[i] += firstout

        #   故障时的第一批（算空位）
        for b in range(0, 1):
            for i in range(base):
                sumin = 0
                sumout = 0
                faupla = fpn[i]  # 故障数
                for j in range(col):
                    sumin += lpvars[b][i][j] + lpvars[b][i + 4][j] + lpvars[b][i + 8][j] + lpvars[b][i + 12][j] #这一批飞出
                    sumout += lpvars[b + 1][4 * i][j] + lpvars[b + 1][4 * i + 1][j] + lpvars[b + 1][4 * i + 2][j] + lpvars[b + 1][4 * i + 3][j] #下一批飞出
                empty[i] += empty[i] + sumout - faupla
                pro += sumin <= empty[i]

        #   故障修复时的剩下批（算空位）
        for b in range(1, faub_num - 2):
            for i in range(base):
                sumin = 0
                sumout = 0
                for j in range(col):
                    sumin += lpvars[b][i][j] + lpvars[b][i + 4][j] + lpvars[b][i + 8][j] + lpvars[b][i + 12][j] #这一批飞进
                    sumout += lpvars[b + 1][4 * i][j] + lpvars[b + 1][4 * i + 1][j] + lpvars[b + 1][4 * i + 2][j] + lpvars[b + 1][4 * i + 3][j] #下一批飞出
                empty[i] += empty[i] + sumout
                pro += sumin <= empty[i]

        #   最后一批
        for b in range(faub_num - 1, faub_num):
            for i in range(base):
                sumin = 0
                faupla = fpn[i]  # 故障数
                for j in range(col):
                    sumin += lpvars[b][i][j] + lpvars[b][i + 4][j] + lpvars[b][i + 8][j] + lpvars[b][i + 12][j]  # 这一批飞出
                pro += sumin <= empty[i] + faupla   # 最后一批时仍未修复好，只能返回发生故障的基站充电


        #   总容量约束
        for i in range(base):
            sumout = 0
            sumin = 0
            fin = beforef_in[i]
            fout = beforef_out[i]
            for b in range(0, faub_num):
                for j in range(col):
                    sumout += lpvars[b][4*i][j] + lpvars[b][4*i+1][j] + lpvars[b][4*i+2][j] + lpvars[b][4*i+3][j]
                    sumin += lpvars[b][i][j] + lpvars[b][i+4][j] + lpvars[b][i+8][j] + lpvars[b][i+12][j]
            pro += sumout == sumin + fin - fout

        # solve optimal problem
        status = pro.solve(solver)

        # get the result of T matrix
        T = [[[lpvars[b][i][j].varValue for j in range(col)] for i in range(row)] for b in range(faub_num)]

        return T

class Assignment2:   # 故障后指派 故障 + 修复批次 <= b_num
    @classmethod
    def KM2(cls, Q, La, L, b_num):
        row = len(Q)
        col = len(Q[0])

        # build a optimal problem
        pro = pl.LpProblem('MIN(connection and coverage )', pl.LpMinimize)
        solver = pl.getSolver('GUROBI_CMD')

        # build variables for the optimal problem
        lpvars = [[[pl.LpVariable("x" + str(i) + "y" + str(j) + "l" + str(b), lowBound=0, upBound=1, cat='Integer') for
                    j in range(col)] for i in range(row)]for b in range(faub_num)]


        # build optimal function
        all = pl.LpAffineExpression()
        for b in range(0, faub_num):
            for i in range(0, row):
                for j in range(0,col):
                    all += Q[i][j] * lpvars[b][i][j]
        pro += all

        # build constraint
        ## 满足聚集点需求
        for b in range(0, faub_num):
            for j in range(0, col):
                tempSum = 0
                for i in range(0, row):
                    tempSum += lpvars[b][i][j]
                pro += tempSum == L[j]

        # la约束
        for b in range(0, faub_num):
            for i in range(0, outbase):
                tempSum = 0
                for j in range(0, col):
                    tempSum += lpvars[b][4 * i][j] + lpvars[b][4 * i + 1][j] + lpvars[b][4 * i + 2][j] + lpvars[b][4 * i + 3][j]
                pro += tempSum <= La[i]

        #   记录故障第一批飞出的空位
        for i in range(base):
            firstout = 0
            for j in range(col):
                firstout += lpvars[0][4 * i][j] + lpvars[0][4 * i + 1][j] + lpvars[0][4 * i + 2][j] + lpvars[0][4 * i + 3][j]
            empty[i] += firstout

        #   故障时的第一批（算空位）
        for b in range(0, 1):
            for i in range(base):
                sumin = 0
                sumout = 0
                faupla = fpn[i]  # 故障数
                for j in range(col):
                    sumin += lpvars[b][i][j] + lpvars[b][i + 4][j] + lpvars[b][i + 8][j] + lpvars[b][i + 12][j] #这一批飞出
                    sumout += lpvars[b + 1][4 * i][j] + lpvars[b + 1][4 * i + 1][j] + lpvars[b + 1][4 * i + 2][j] + lpvars[b + 1][4 * i + 3][j] #下一批飞出
                empty[i] += empty[i] + sumout - faupla
                pro += sumin <= empty[i]

        #   故障修复时的剩下批（算空位）
        for b in range(1, fauc):
            for i in range(base):
                sumin = 0
                sumout = 0
                for j in range(col):
                    sumin += lpvars[b][i][j] + lpvars[b][i + 4][j] + lpvars[b][i + 8][j] + lpvars[b][i + 12][j] #这一批飞出
                    sumout += lpvars[b + 1][4 * i][j] + lpvars[b + 1][4 * i + 1][j] + lpvars[b + 1][4 * i + 2][j] + lpvars[b + 1][4 * i + 3][j] #下一批飞出
                empty[i] += empty[i] + sumout
                pro += sumin <= empty[i]

        #   故障修复完成后的那一批次
        for b in range(fauc, fauc + 1):
            for i in range(base):
                sumin = 0
                sumout = 0
                faupla = fpn[i]  # 故障数
                for j in range(col):
                    sumin += lpvars[b][i][j] + lpvars[b][i + 4][j] + lpvars[b][i + 8][j] + lpvars[b][i + 12][j] #这一批飞出
                    sumout += lpvars[b + 1][4 * i][j] + lpvars[b + 1][4 * i + 1][j] + lpvars[b + 1][4 * i + 2][j] + lpvars[b + 1][4 * i + 3][j] #下一批飞出
                empty[i] += empty[i] + sumout + faupla
                pro += sumin <= empty[i]

        #   故障后恢复正常
        for b in range(fauc + 1, faub_num - 2):
            for i in range(base):
                sumin = 0
                sumout = 0
                for j in range(col):
                    sumin += lpvars[b][i][j] + lpvars[b][i + 4][j] + lpvars[b][i + 8][j] + lpvars[b][i + 12][j] #这一批飞出
                    sumout += lpvars[b + 1][4 * i][j] + lpvars[b + 1][4 * i + 1][j] + lpvars[b + 1][4 * i + 2][j] + lpvars[b + 1][4 * i + 3][j] #下一批飞出
                empty[i] += empty[i] + sumout
                pro += sumin <= empty[i]

        #   总容量约束
        for i in range(base):
            sumout = 0
            sumin = 0
            fin = beforef_in[i]
            fout = beforef_out[i]
            for b in range(0, faub_num):
                for j in range(col):
                    sumout += lpvars[b][4*i][j] + lpvars[b][4*i+1][j] + lpvars[b][4*i+2][j] + lpvars[b][4*i+3][j]
                    sumin += lpvars[b][i][j] + lpvars[b][i+4][j] + lpvars[b][i+8][j] + lpvars[b][i+12][j]
            pro += sumout == sumin + fin - fout

        # solve optimal problem
        status = pro.solve(solver)

        # get the result of T matrix
        T = [[[lpvars[b][i][j].varValue for j in range(col)] for i in range(row)] for b in range(faub_num)]

        return T

class Assignment:  # 正常指派
    @classmethod
    def KM(cls, Q, La, L, b_num):
        row = len(Q)
        col = len(Q[0])

        # build a optimal problem
        pro = pl.LpProblem('MIN(connection and coverage )', pl.LpMinimize)
        solver = pl.getSolver('GUROBI_CMD')

        # build variables for the optimal problem
        lpvars = [[[pl.LpVariable("x" + str(i) + "y" + str(j) + "l" + str(b), lowBound=0, upBound=1, cat='Integer') for
                    j in range(col)] for i in range(row)]for b in range(b_num)]


        # build optimal function
        all = pl.LpAffineExpression()
        for b in range(0,b_num):
            for i in range(0,row):
                for j in range(0,col):
                    all += Q[i][j] * lpvars[b][i][j]
        pro += all


        # build constraint
        ## 满足聚集点需求
        for b in range(b_num):
            for j in range(0, col):
                tempSum = 0
                for i in range(0, row):
                    tempSum += lpvars[b][i][j]
                pro += tempSum == L[j]


        # la约束
        for b in range(0, b_num):
            for i in range(0, outbase):
                tempSum = 0
                for j in range(0, col):
                    tempSum += lpvars[b][4 * i][j] + lpvars[b][4 * i + 1][j] + lpvars[b][4 * i + 2][j] + lpvars[b][4 * i + 3][j]
                pro += tempSum <= La[i]


        # 容量约束 批次之间 ( k 批次飞入 <= k+1 批次飞出)
        for b in range(0, b_num - 2):
            for i in range(base):
                sumoutd = 0
                sumind = 0
                for temp in (0, b + 1):
                    for j in range(col):
                        sumind += lpvars[temp][i][j] + lpvars[temp][i + 4][j] + lpvars[temp][i + 8][j] + lpvars[temp][i + 12][j] #k批次飞入总和
                for temp_2 in (0, b + 2):
                    for j in range(col):
                        sumoutd += lpvars[temp_2][4 * i][j] + lpvars[temp_2][4 * i + 1][j] + lpvars[temp_2][4 * i + 2][j] + lpvars[temp_2][4 * i + 3][j] #k+1批次飞出总和
                pro += sumind <= sumoutd


        # 总容量约束
        for i in range(base):
            sumout = 0
            sumin = 0
            for b in range(0, b_num):
                for j in range(col):
                    sumout += lpvars[b][4*i][j] + lpvars[b][4*i+1][j] + lpvars[b][4*i+2][j] + lpvars[b][4*i+3][j]
                    sumin += lpvars[b][i][j] + lpvars[b][i+4][j] + lpvars[b][i+8][j] + lpvars[b][i+12][j]
            pro += sumout == sumin


        # solve optimal problem
        status = pro.solve(solver)
        print("Assignment Status: ", pl.LpStatus[status])
        print("Final Assignment Result", pl.value(pro.objective))

        # get the result of T matrix
        T = [[[lpvars[b][i][j].varValue for j in range(col)] for i in range(row)] for b in range(b_num)]

        # return T
        # return [T, pl.value(pro.status), pl.value(pro.objective)]

        return T



# 计算两点之间距离公式
def calDistance(x1, x2, y1, y2):
    result = math.sqrt((x2-x1)**2+(y2-y1)**2)
    return result

# 聚集点与基站组合距离（21*4的矩阵）
def genDistance(x,y,x1,y1):
    distance_BC = []
    for j in range(base):
        tempMatrix = []
        for i in range(cluster):
            tempMatrix.append(calDistance(x[i],x1[j],y[i],y1[j]))
        distance_BC.append(tempMatrix)
    return distance_BC

# 聚集点与基站组合距离算出Q(21*16的矩阵)
def genQMatrix(distance_BC):
    Q = []
    for k in range(base):
        for j in range(base):
            temp = []
            for i in range(cluster):
                total_distance = []
                total_distance = distance_BC[k,i]+distance_BC[j,i]
                temp.append(total_distance)
            Q.append(temp)
    return Q

# 归一化后的Q
def getNormalizedQ(Q):
    max = min = Q[0][0]
    row = len(Q)
    column = len(Q[0])
    for i in range(row):
        for j in range(column):
            if (Q[i, j] > max):
                max = Q[i, j]
            if (Q[i, j] < min):
                min = Q[i, j]
    for i in range(row):
        for j in range(column):
            Q[i, j] = round((Q[i, j] - min) / (max - min), 4)
    return Q

# 随机生成La
# def genLaMatrix():
#     La = []
#     for i in range(0,4):
#         a = random.randint(1,20)
#         La.append(a)
#     return La

# 随机生成La
def genLaMatrix(role_amount, agent_amount):
		La = []
		temp = agent_amount
		while(temp>=1):
				val = ceil(role_amount/agent_amount)
				val += random.randint(1, agent_amount)
				if val not in La:
						La.append(val)
						temp-=1
		return La

# 找到可行的La
# def availableLa():
#     TMAT_row = 16
#     TMAT_col = 22
#     flag = 1
#     cont = 0
#     La = []
#     if cont<1000000000000:
#         LaMatrix = genLaMatrix(43,4)
#         T = Assignment.KM(QMatrix, LaMatrix, L)
#         TMAT= np.array(T)
#         for b in range(b_num):
#             for i in range(TMAT_row):
#                 for j in range(TMAT_col):
#                     if TMAT[b][i][j] > 1 or TMAT[b][i][j] < 0:
#                         flag = 0
#                     cont += 1
#             if flag == 1:
#                 break
#         La = LaMatrix
#     return La

# 计算基站派出/派回无人机数量
def countdrones(T):
    countbase_in = [[0,0,0,0] for i in range(b_num)]
    countbase_out = [[0,0,0,0] for i in range(b_num)]
    for b in range(0, b_num):
        countin = 0
        countout = 0
        for i in range(0, base):
            for j in range(0, cluster):
                countin += T[b][i][j] + T[b][i + 4][j] + T[b][i + 8][j] + T[b][i + 12][j]  # eg:base1进基站为j=0,4,8,12
                countout += T[b][4 * i][j] + T[b][4 * i + 1][j] + T[b][4 * i + 2][j] + T[b][4 * i + 3][j]  # eg:base2出基站为j=0,1,2,3
            countbase_in[b][i] = countin
            countbase_out[b][i] = countout
            countin = 0
            countout = 0
    countbase_in = np.array(countbase_in)
    countbase_out = np.array(countbase_out)
    return countbase_in, countbase_out


# 指派路径
def drawpath(T,x,y,x1,y1):
    T_row = 16
    T_col = 22
    base1_out_x = [[] for i in range(b_num)]
    base1_out_y = [[] for i in range(b_num)]
    base1_in_x = [[] for i in range(b_num)]
    base1_in_y = [[] for i in range(b_num)]
    base2_out_x = [[] for i in range(b_num)]
    base2_out_y = [[] for i in range(b_num)]
    base2_in_x = [[] for i in range(b_num)]
    base2_in_y = [[] for i in range(b_num)]
    base3_out_x = [[] for i in range(b_num)]
    base3_out_y = [[] for i in range(b_num)]
    base3_in_x = [[] for i in range(b_num)]
    base3_in_y = [[] for i in range(b_num)]
    base4_out_x = [[] for i in range(b_num)]
    base4_out_y = [[] for i in range(b_num)]
    base4_in_x = [[] for i in range(b_num)]
    base4_in_y = [[] for i in range(b_num)]

    # out
    for b in range(b_num):
        for i in range(T_row):
            outbase = math.floor(i / 4) + 1
            for j in range(T_col):
                if T[b][i][j] == 1 and outbase == 1:
                    base1_out_x[b].append((x1[0], x[j]))
                    base1_out_y[b].append((y1[0], y[j]))
                elif T[b][i][j] == 1 and outbase == 2:
                    base2_out_x[b].append((x1[1], x[j]))
                    base2_out_y[b].append((y1[1], y[j]))
                elif T[b][i][j] == 1 and outbase == 3:
                    base3_out_x[b].append((x1[2], x[j]))
                    base3_out_y[b].append((y1[2], y[j]))
                elif T[b][i][j] == 1 and outbase == 4:
                    base4_out_x[b].append((x1[3], x[j]))
                    base4_out_y[b].append((y1[3], y[j]))

    # in
    for b in range(b_num):
        for i in range(T_row):
            inbase = i - math.floor(i / 4) * 4 + 1
            for j in range(T_col):
                if T[b][i][j] == 1 and inbase == 1:
                    base1_in_x[b].append((x[j], x1[0]))
                    base1_in_y[b].append((y[j], y1[0]))
                elif T[b][i][j] == 1 and inbase == 2:
                    base2_in_x[b].append((x[j], x1[1]))
                    base2_in_y[b].append((y[j], y1[1]))
                elif T[b][i][j] == 1 and inbase == 3:
                    base3_in_x[b].append((x[j], x1[2]))
                    base3_in_y[b].append((y[j], y1[2]))
                elif T[b][i][j] == 1 and inbase == 4:
                    base4_in_x[b].append((x[j], x1[3]))
                    base4_in_y[b].append((y[j], y1[3]))

    base1_out_x = np.array(base1_out_x,dtype=object)
    base1_out_y = np.array(base1_out_y,dtype=object)
    base1_in_x = np.array(base1_in_x,dtype=object)
    base1_in_y = np.array(base1_in_y,dtype=object)
    base2_out_x = np.array(base2_out_x,dtype=object)
    base2_out_y = np.array(base2_out_y,dtype=object)
    base2_in_x = np.array(base2_in_x,dtype=object)
    base2_in_y = np.array(base2_in_y,dtype=object)
    base3_out_x = np.array(base3_out_x,dtype=object)
    base3_out_y = np.array(base3_out_y,dtype=object)
    base3_in_x = np.array(base3_in_x,dtype=object)
    base3_in_y = np.array(base3_in_y,dtype=object)
    base4_out_x = np.array(base4_out_x,dtype=object)
    base4_out_y = np.array(base4_out_y,dtype=object)
    base4_in_x = np.array(base4_in_x,dtype=object)
    base4_in_y = np.array(base4_in_y,dtype=object)

    return base1_out_x,base1_out_y,base2_out_x,base2_out_y,base3_out_x,base3_out_y,base4_out_x,base4_out_y,\
           base1_in_x,base1_in_y,base2_in_x,base2_in_y,base3_in_x,base3_in_y,base4_in_x,base4_in_y

#   Q矩阵汇总
def gennewQmatrix(x,y,x1,y1):
    distance_BC = genDistance(x, y, x1, y1)
    distance_BC_array = np.array(distance_BC)

    Q = genQMatrix(distance_BC_array)
    Q = np.array(Q)
    QMatrix = np.array(Q)

    # 对超过续航时间的Q[i][j]赋为255
    for i in range(0,len(QMatrix)):
        for j in range(0,len(QMatrix[0])):
            if QMatrix[i][j] >= Qmax:
                QMatrix[i][j] = 255

    QMatrix = getNormalizedQ(QMatrix)
    return Q

#   总航迹
def contnewpath(T, b_num):
    condistance = []
    for b in range(b_num):
        condis = 0
        for i in range(base):
            for j in range(cluster):
                if T[b][i][j] == 1:
                    condis += Q[i][j]
        condistance.append(condis)
    condistance_total = sum(condistance)
    return condistance_total, condistance



if __name__ == '__main__':

    x = [1.61, 1.24, 0.98, 4.66, 6.75, 10.5, 13.81, 17.33, 12.41, 17.09, 2.73, 9.39, 12.44, 17.57, 12.54, 17.22, 8.39,
         6.35, 2.28, 2.78, 7.99, 15.08]
    y = [19.53, 14.76, 12.94, 15.11, 15.87, 16.48, 18.55, 17.36, 14.76, 13.79, 11.4, 8.55, 10.19, 8.39, 6.3, 3.41, 3.73,
         1.06, 2.8, 6.61, 10.16, 8.88]
    x1 = [15.95, 16.42, 11.52, 16.77]
    y1 = [6.92, 9.6, 18.78, 15.33]

    b_num = 100
    base = 4
    outbase = 4
    inbase = 4
    cluster = 22
    combination = 16
    ftcharge = 0.75 # 充电45分钟
    fv = 50  # 飞行速度50km/h
    Qmax = ftcharge*fv
    L = [1,2,1,3,3,1,2,2,2,2,1,2,1,2,2,3,2,2,2,3,1,3]   # total 43
    La = [15, 13, 14, 12]
    capacity = [45, 39, 42, 36]

    distance_BC = genDistance(x, y, x1, y1)
    distance_BC_array = np.array(distance_BC)
    print(distance_BC)
    Q = genQMatrix(distance_BC_array)
    Q = np.array(Q)
    QMatrix = np.array(Q)

    # 对超过续航时间的Q[i][j]赋为255
    for i in range(0,len(QMatrix)):
        for j in range(0,len(QMatrix[0])):
            if QMatrix[i][j] >= Qmax:
                QMatrix[i][j] = 255

    QMatrix = getNormalizedQ(QMatrix)
    print(QMatrix)

    #损耗
    # fau_b1 = random.randint(0, 100)  # 随机故障批次
    # fau_b1 = 1
    fauc = 5
    fpro = 0.1 # 故障比率
    capacity = np.array(capacity)
    fpn = fpro * capacity   # 故障充电平台数量
    fpn = np.ceil(fpn)  # 向上取整




    start_sum = time.perf_counter()
    # 记录总指派结果（故障前+故障后）
    T_total = [[[[]for j in range(cluster)]for i in range(combination)]for b in range(b_num)]


    start_1 = time.perf_counter()
    T0 = Assignment.KM(QMatrix, La, L, b_num)    # 正常指派
    T0 = np.array(T0)
    distance_T0, condistance_T0 = contnewpath(T0, b_num)
    print("正常运行情况下的总航迹距离：", distance_T0)
    condistance_T0 = np.array(condistance_T0)
    total_1 = time.perf_counter() - start_1
    print("正常运行情况下运行时间：", total_1)


    # 故障 循环每批次
    increasetime = []
    increasedistance = []
    for fau_b1 in range(b_num):
        faub_num = b_num - fau_b1 + 1  # 故障后批次（用于第二次指派）
        start_2 = time.perf_counter()
        # 存放故障批次前的指派结果
        tempT = [[[[] for j in range(cluster)] for i in range(combination)] for b in range(fau_b1 - 1)]
        for b in range(0, fau_b1 - 1):  # 34批结果记录
            for i in range(combination):
                for j in range(cluster):
                    tempT[b][i][j] = T0[b][i][j]
                    T_total[b][i][j] = tempT[b][i][j]

        # 记录故障批次前派出派进情况
        beforef_in = [[] for i in range(base)]
        beforef_out = [[] for i in range(base)]
        beforef_empty = [[] for i in range(base)]
        for i in range(base):
            tempbeforef_in = 0
            tempbeforef_out = 0
            for b in range(0, fau_b1 - 1):
                for j in range(cluster):
                    tempbeforef_in += tempT[b][i][j] + tempT[b][i + 4][j] + tempT[b][i + 8][j] + tempT[b][i + 12][j]
                    tempbeforef_out += tempT[b][4 * i][j] + tempT[b][4 * i + 1][j] + tempT[b][4 * i + 2][j] + \
                                       tempT[b][4 * i + 3][j]
            tempempty = tempbeforef_out - tempbeforef_in
            beforef_in[i] = tempbeforef_in
            beforef_out[i] = tempbeforef_out
            beforef_empty[i] = tempempty

        empty = [0 for i in range(base)]
        for i in range(base):
            empty[i] = beforef_empty[i]

        # 合并两种情况
        if fau_b1 + fauc < b_num:
            T1 = Assignment2.KM2(QMatrix, La, L, faub_num)
            T_total = tempT + T1
        elif fau_b1 + fauc == b_num:
            T3 = Assignment4.KM4(QMatrix, La, L, faub_num)
            T_total = tempT + T3
        else:
            T2 = Assignment3.KM3(QMatrix, La, L, faub_num)
            T_total = tempT + T2

        distance_total, condistance_total = contnewpath(T_total, b_num)
        print("故障情况的总航迹距离：", distance_total)
        condistance_total = np.array(condistance_total)
        T_total = np.array(T_total)
        total_2 = time.perf_counter() - start_2
        print("故障情况下需要多运行的时间：", total_2)

        total_sum = time.perf_counter() - start_sum
        print("故障情况下运行总时间：", total_sum)

        distance_increase = distance_total - distance_T0
        print("故障后多出的总航迹距离：", distance_increase)

        increasetime.append(total_2)
        increasedistance.append(distance_increase)
    increasetime = np.array(increasetime)
    increasedistance = np.array(increasedistance)

    # 1000次随机实验：坐标变,记录每次时间和航迹距离
    # scale = 1000
    # large_time = [[]for t in range(scale)]
    # large_path = [[]for t in range(scale)]
    # for t in range(0, scale):
    #     start = time.perf_counter()
    #     x = np.random.choice(range(0, 21), 22)  # 聚集点坐标
    #     y = np.random.choice(range(0, 21), 22)
    #     x1 = np.random.choice(range(0, 21), 4)  # 基站坐标
    #     y1 = np.random.choice(range(0, 21), 4)
    #     Q = gennewQmatrix(x, y, x1, y1)
    #     T = Assignment.KM(Q, La, L)
    #     total = time.perf_counter() - start
    #     large_time[t] = total
    #     large_path[t] = contnewpath(T)
    #
    # large_time = np.array(large_time)
    # large_path = np.array(large_path)



    # 记录多次运行结果
    # fre = [[]for t in range(b_num)] #重复运行次数
    # divsum = [[[]for j in range(2)]for i in range(len(fre))] #记录多次运行的结果
    # temp = 0
    # tempb_num = b_num
    # for b in range(0, tempb_num):
    #     start_2 = 0
    #     total_2 = 0
    #     if tempb_num % (b+1) != 0:
    #         divsum[b][0] = 0
    #         divsum[b][1] = 0
    #     if tempb_num % (b+1) == 0:
    #         fre[b] = int(tempb_num / (b + 1))
    #         divsum[b][0] = fre[b]
    #         temp = fre[b]
    #         start_2 = time.perf_counter()
    #         for t in range(0, temp):
    #             b_num = b + 1
    #             T, status, p = Assignment.KM(QMatrix, La, L)
    #         total_2 = time.perf_counter() - start_2
    #         divsum[b][1] = total_2
    #
    # divsum = np.array(divsum)
    # divsum = divsum[divsum.sum(axis=1) != 0, :] #去除全0行
    #
    # total_3 = time.perf_counter() - start_3

    #
    # countbase_in,countbase_out = countdrones(T_total)
    # print(countbase_in)
    # print(countbase_out)


    # 计算路径总距离
    # condistance = []
    # for b in range(b_num):
    #     condis = 0
    #     for i in range(base):
    #         for j in range(cluster):
    #             if T_total[b][i][j] == 1:
    #                 condis += Q[i][j]
    #     condistance.append(condis)
    # print(condistance)
    # condistance_total = sum(condistance)
    # print('总航迹', condistance_total)
    #
    # # print('运行时间', total)
    # print('一次性运行时间:', total_1)
    # # print('分次运行时间：', total_2)
    # # print('总运行时间:', total_3)
    #
    # # 计算总批次的总派出和派入
    # totalin = [0 for i in range(base)]
    # totalout = [0 for i in range(base)]
    # for i in range(base):
    #     tempcontin = 0
    #     tempcontout = 0
    #     for b in range(b_num):
    #         tempcontin += countbase_in[b][i]
    #         tempcontout += countbase_out[b][i]
    #     totalin[i] = tempcontin
    #     totalout[i] = tempcontout
    # print('各基站总派出无人机数:', totalout)
    # print('各基站总派回无人机数:', totalin)
    #
    # sumtotalin = 0
    # sumtotalout = 0
    # for i in range(base):
    #     sumtotalin += totalin[i]
    #     sumtotalout += totalout[i]
    # print('批次总返回数', sumtotalin)
    # print('批次总派出数', sumtotalout)
















