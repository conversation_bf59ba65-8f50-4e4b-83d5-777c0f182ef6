import numpy as np
import networkx as nx
import matplotlib.pyplot as plt


# 生成树形图
def generate_tree_graph(superior_matrix):
    num_tasks = superior_matrix.shape[0]
    G = nx.DiGraph()

    # 添加节点
    for i in range(num_tasks):
        G.add_node(i, label=f'节点_{i}')

    # 添加边
    for i in range(num_tasks):
        for j in range(num_tasks):
            if superior_matrix[i, j] == 1:
                G.add_edge(j, i)  # 上下级关系中，j是i的上级

    return G


# 生成22个任务点
num_tasks = 22

# 随机选择上下级关系
num_parents = np.random.randint(5, 10)  # 上级个数随机取5-10个
parent_indices = np.random.choice(range(1, num_tasks), min(num_parents, num_tasks - 1), replace=False)  # 选择不包括第一个点的上级点

# 生成上下级矩阵
superior_matrix = np.zeros((num_tasks, num_tasks), dtype=int)
for parent_idx in parent_indices:
    superior_matrix[parent_idx, 0] = 1  # 第一个点为根节点
    superior_matrix[0, parent_idx] = 1
    children_count = np.random.randint(2, 6)  # 子节点个数随机取2-5个
    children_indices = np.random.choice(range(parent_idx + 1, num_tasks),
                                        min(children_count, num_tasks - parent_idx - 1), replace=False)  # 子节点随机选择
    for child_idx in children_indices:
        superior_matrix[child_idx, parent_idx] = 1
        superior_matrix[parent_idx, child_idx] = 1

# 生成同级矩阵
sibling_matrix = np.zeros((num_tasks, num_tasks), dtype=int)
for i in range(num_tasks):
    for j in range(i + 1, num_tasks):
        if i != j and np.sum(superior_matrix[i]) == np.sum(superior_matrix[j]):  # 同级节点的父节点相同
            sibling_matrix[i, j] = 1
            sibling_matrix[j, i] = 1

# 根据上下级矩阵和同级矩阵推导叶子节点向量
def infer_leaf_vector(superior_matrix, sibling_matrix):
    num_tasks = superior_matrix.shape[0]
    leaf_vector = np.ones(num_tasks, dtype=int)  # 初始化叶子节点向量，假设所有节点都是叶子节点

    # 遍历每个节点
    for i in range(num_tasks):
        # 检查是否存在下级节点
        if np.sum(superior_matrix[:, i]) > 0:
            leaf_vector[i] = 0  # 如果存在下级节点，则当前节点不是叶子节点

        # 检查是否存在同级节点
        for j in range(i + 1, num_tasks):
            if sibling_matrix[i, j] == 1:
                leaf_vector[i] = 0  # 如果存在同级节点，则当前节点不是叶子节点

    return leaf_vector

leaf_vector = infer_leaf_vector(superior_matrix, sibling_matrix)

# 生成树形图
G = generate_tree_graph(superior_matrix)

# 检查图是否为空
if len(G) == 0:
    print("图为空，无法生成树形图。")
else:
    # 绘制图形
    plt.figure(figsize=(12, 8))
    pos = nx.layout.shell_layout(G)  # 使用水平布局算法进行布局

    # 绘制父节点
    nx.draw_networkx_nodes(G, pos, nodelist=[i for i in range(num_tasks) if np.sum(superior_matrix[i]) == 1],
                           node_size=1000, node_color='lightblue')
    # 绘制子节点
    nx.draw_networkx_nodes(G, pos, nodelist=[i for i in range(num_tasks) if np.sum(superior_matrix[i]) > 0],
                           node_size=1000, node_color='orange')
    # 绘制叶子节点
    nx.draw_networkx_nodes(G, pos, nodelist=[i for i in range(num_tasks) if leaf_vector[i] == 1],
                           node_size=1000, node_color='lightgreen')

    nx.draw_networkx_labels(G, pos, font_size=10, font_color='black')  # 添加节点标签
    nx.draw_networkx_edges(G, pos, arrows=False)  # 绘制边

    plt.title('从左到右的任务树形图')
    plt.axis('off')  # 不显示坐标轴
    plt.show()

# 打印结果
print("上下级矩阵：")
print(superior_matrix)
print("\n同级矩阵：")
print(sibling_matrix)
print("\n叶子节点")
print(leaf_vector)
