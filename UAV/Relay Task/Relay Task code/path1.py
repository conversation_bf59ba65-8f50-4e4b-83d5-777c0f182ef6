import matplotlib.pyplot as plt
import numpy as np

# x = [[1, 3], [2, 5]] #两点之间连线
# y = [[4, 7], [6, 3]]
#
# for i in range(len(x)):
#     plt.plot(x[i], y[i], color='r')
#     plt.scatter(x[i], y[i], color='b')
# plt.show()

# 第一次指派后基站与UAV路径
base1_x = [[18.95, 9.39], [18.95, 17.57], [18.95, 12.54], [18.95, 17.22], [18.95, 8.39], [18.95, 6.35], [18.95, 2.28],
           [18.95, 2.78], [18.95, 7.99]]
base1_y = [[6.92, 8.55], [6.92, 8.39], [6.92, 6.3], [6.92, 3.41], [6.92, 3.73], [6.92, 1.06], [6.92, 2.8],
           [6.92, 6.61], [6.92, 10.16]]

for i in range(len(base1_x)):
    plt.plot(base1_x[i], base1_y[i], linewidth='1', color='g')
    plt.scatter(base1_x[i], base1_y[i], color='b', marker='*')
    # ax.arrow(1, 1, 2, 2, head_width=0.05, head_length=0.1, fc='k', ec='k')    # 加箭头
    plt.scatter([18.95, ], [6.92, ], s=150, color='r', marker='*')


base2_x = [[6.42, 1.24], [6.42, 0.98], [6.42, 4.66], [6.42, 6.75], [6.42, 2.73], [6.42, 9.39], [6.42, 12.44], [6.42, 12.54], [6.42, 8.39], [6.42, 6.35], [6.42, 2.28], [6.42, 2.78], [6.42, 7.99]]
base2_y = [[9.6, 14.76], [9.6, 12.94], [9.6, 15.11], [9.6, 15.87], [9.6, 11.4], [9.6, 8.55], [9.6, 10.19], [9.6, 6.3], [9.6, 3.73], [9.6, 1.06], [9.6, 2.8], [9.6, 6.61], [9.6, 10.16]]

for i in range(len(base2_x)):
    plt.plot(base2_x[i], base2_y[i], linewidth='1', color='b')
    plt.scatter(base2_x[i], base2_y[i], color='b', marker='*')
    plt.scatter([6.42, ], [9.6, ], s=150, color='r', marker='*')

base3_x = [[11.52, 1.61], [11.52, 1.24], [11.52, 4.66], [11.52, 6.75], [11.52, 10.5], [11.52, 13.81], [11.52, 17.33],
           [11.52, 12.41], [11.52, 17.09], [11.52, 17.22], [11.52, 2.78]]
base3_y = [[18.78, 19.53], [18.78, 14.76], [18.78, 15.11], [18.78, 15.87], [18.78, 16.48], [18.78, 18.55], [18.78, 17.36],
           [18.78, 14.76], [18.78, 14.79], [18.78, 3.41], [18.78, 6.61]]

for i in range(len(base3_x)):
    plt.plot(base3_x[i], base3_y[i], linewidth='1', color='c')
    plt.scatter(base3_x[i], base3_y[i], color='b', marker='*')
    plt.scatter([11.52, ], [18.78, ], s=150, color='r', marker='*')

base4_x = [[16.77, 4.66], [16.77, 6.75], [16.77, 13.81], [16.77, 17.33], [16.77, 12.41], [16.77, 17.09],
           [16.77, 17.57], [16.77, 17.22]]
base4_y = [[15.33, 15.11], [15.33, 15.87], [15.33, 18.55], [15.33, 17.36], [15.33, 14.76], [15.33, 14.79],
           [15.33, 8.39], [15.33, 3.41]]

for i in range(len(base4_x)):
    plt.plot(base4_x[i], base4_y[i], linewidth='1', color='y')
    plt.scatter(base4_x[i], base4_y[i], color='b', marker='*')
    plt.scatter([16.77, ], [15.33, ], s=150, color='r', marker='*')

plt.rcParams['font.sans-serif']=['SimHei'] #用来正常显示中文标签
plt.title('第一次指派')
plt.show()



