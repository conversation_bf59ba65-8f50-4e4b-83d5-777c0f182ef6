# ver1.0(点太小）
import matplotlib.pyplot as plt
import numpy as np
import math
import matplotlib.pyplot as plt
from matplotlib.ticker import MaxNLocator
from math import floor
import numpy as np
import xlrd
import pandas as pd
import pulp as pl
import pandas as pd
from math import cos, sin, acos, asin, pi, atan, fabs, ceil, floor
from collections import defaultdict
import random
import time
from gurobipy import *
import openpyxl
import sys
import os
import networkx as nx

#
# # 任务点坐标
# coordinates = [(5, 10), (10, 15), (15, 5), (20, 20), (25, 10), (30, 25), (35, 15), (40, 5)]
#
# # 人口密集程度（对应new_L的值）
# population_density = [20, 30, 10, 40, 25, 35, 15, 30]
#
# # 将人口密集程度转换为二维数组
# density_matrix = np.zeros((50, 50))  # 假设场景大小为50x50
# for coord, density in zip(coordinates, population_density):
#     density_matrix[coord[0], coord[1]] = density
#
# # 绘制热力图
# plt.imshow(density_matrix, cmap='hot', interpolation='nearest')
# plt.colorbar()  # 添加颜色条
# plt.title('Population Density Heatmap')
# plt.xlabel('X')
# plt.ylabel('Y')
# plt.show()

# ver2.0（方块点+圆点）
# import matplotlib.pyplot as plt
# import numpy as np
#
# # 任务点坐标
# coordinates = [(5, 10), (10, 15), (15, 5), (20, 20), (25, 10), (30, 25), (35, 15), (40, 5)]
#
# # 人口密集程度（对应new_L的值）
# population_density = [20, 30, 10, 40, 25, 35, 15, 30]
#
# # 将人口密集程度转换为二维数组
# density_matrix = np.zeros((50, 50))  # 假设场景大小为50x50
# for coord, density in zip(coordinates, population_density):
#     density_matrix[coord[0], coord[1]] = density
#
# # 绘制热力图
# plt.imshow(density_matrix, cmap='hot', interpolation='nearest')
# plt.colorbar()  # 添加颜色条
# plt.title('Population Density Heatmap')
# plt.xlabel('X')
# plt.ylabel('Y')
#
# # 根据new_L绘制点并调整点的大小
# x, y = zip(*coordinates)
# new_L = population_density  # 假设new_L就是人口密集程度
# plt.scatter(y, x, s=np.array(new_L) * 10, c='blue', alpha=0.5)  # 根据new_L调整点的大小
#
# plt.show()

# # ver3.0
# b_num = 5
# L = [5, 6, 3, 3, 3, 2, 8, 6]
# new_L = []
# new_L.append(L)
# # for b in range(1, b_num):
# #     # 将前一个批次的L中的每个元素与对应的随机向量元素相乘，并向上取整，存储到新的L中
# #     random_vector = [random.random() for _ in range(8)]
# #     new_L.append([math.ceil(L[i] * random_vector[i]) for i in range(len(L))])
# # new_L = np.array(new_L)
#
# for b in range(b_num):
#     plt.figure()
#     x = [1.61, 0.98, 4.66, 6.75, 10.5, 13.81, 17.33, 12.41]
#     y = [19.53, 4.76, 12.94, 15.87,8.55, 17.36, 14.76, 13.79]
#     random_vector = [random.random() for _ in range(8)]
#     new_L.append([math.ceil(L[i] * random_vector[i]) for i in range(len(L))])
#
#     # colors = [1, 2, 1, 3, 3, 1, 2, 2]
#     colors = new_L[b]  # 将new_L中每批次的L作为颜色数据
#     # sizes = [150, 300, 150, 450, 450, 150, 300, 300]
#     sizes = [color * 150 for color in colors]  # 根据new_L的值设置点的大小
#     cluster_list = [(1.61, 19.53), (0.98,4.76), (4.66,12.94), (6.75,15.87),(10.5,8.55),(13.81,17.36),(17.33,14.76),(12.41,13.79)]
#
#     plt.scatter(x, y, c=colors, s=sizes, alpha=0.5, edgecolors='w', label = 'Crowded Areas', cmap='copper_r')
#     plt.legend(loc='lower right', fontsize=9)
#
#     x1 = [18.95, 6.42, 11.52, 16.77]
#     y1 = [6.92, 9.6, 18.78, 15.33]
#     base_list = [(18.95, 6.92), (6.42, 9.6), (11.52, 18.78), (16.77, 15.33)]
#     plt.scatter(x1, y1, color="darkred", s=400, alpha=0.7, label = 'Base Station', marker='*')
#
#     plt.legend(loc='lower right', fontsize=9)
#     plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
#     c = b + 1
#     plt.title('Roadmap for the return of the %sth batch'%c,fontdict={'weight':'normal','size': 15})
#     plt.xlim(0, 20, 1)
#     plt.ylim(0, 20, 2)
#     plt.gca().xaxis.set_major_locator(MaxNLocator(integer=True))  # 限制坐标轴刻度为整数
#     plt.gca().yaxis.set_major_locator(MaxNLocator(integer=True))
#
#     plt.show()
#     c = 0

# ver4.0（终版）
import matplotlib.pyplot as plt
import random
import math
import numpy as np
from matplotlib.ticker import MaxNLocator, ScalarFormatter
from matplotlib.cm import ScalarMappable
import matplotlib.colors as mcolors


b_num = 5
L = [5, 6, 3, 3, 3, 2, 8, 6]
new_L = []
new_L.append(L)


for b in range(b_num):
    plt.figure()
    camp_reversed = mcolors.ListedColormap(list(reversed(plt.cm.autumn(np.linspace(0,1,256))))) #热力图色带反转

    x = [4.61, 3.98, 3.66, 6.75, 10.5, 13.81, 17.33, 12.41]
    y = [9.53, 4.76, 12.94, 15.87, 8.55, 17.36, 10.76, 13.79]

    random_vector = [random.random() for _ in range(8)]
    new_L.append([math.ceil(L[i] * random_vector[i]) for i in range(len(L))])

    colors = new_L[b]  # 将new_L中每批次的L作为颜色数据
    sizes = [color * 250 for color in colors]  # 根据new_L的值设置点的大小

    # 创建一个标量映射对象，并设置其数据范围和颜色映射
    norm = plt.Normalize(min(colors), max(colors))
    cmap = plt.get_cmap('Accent')
    sm = ScalarMappable(norm=norm, cmap= camp_reversed)

    plt.scatter(x, y, c=colors, s=sizes, alpha=0.5, edgecolors='w', label='人口密集区域', cmap = camp_reversed)

    x1 = [18.95, 6.42, 11.52, 16.77]
    y1 = [6.92, 9.6, 18.78, 15.33]
    plt.scatter(x1, y1, color="green", s=200, alpha=0.7, label='无人机基站', marker='*')



    plt.legend(loc='lower right', fontsize=12)
    cbar = plt.colorbar(sm)
    cbar.set_label('人口密集程度')
    # 设置颜色条格式，避免使用偏移
    cbar.formatter = ScalarFormatter(useOffset=False)
    cbar.update_ticks()

    plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
    c = b + 1
    plt.title('景区X内第%s批次接力任务的人口热力图' % c, fontdict={'weight': 'normal', 'size': 15})
    plt.xlim(0, 20, 1)
    plt.ylim(0, 20, 2)
    plt.gca().xaxis.set_major_locator(MaxNLocator(integer=True))  # 限制坐标轴刻度为整数
    plt.gca().yaxis.set_major_locator(MaxNLocator(integer=True))

    plt.show()

