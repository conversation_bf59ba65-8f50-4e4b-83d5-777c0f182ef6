import math
import matplotlib.pyplot as plt
from matplotlib.ticker import MaxNLocator
from math import floor
import numpy as np
import xlrd
import pandas as pd
import pulp as pl
import pandas as pd
from math import cos, sin, acos, asin, pi, atan, fabs, ceil, floor
from collections import defaultdict
import random
import time
from gurobipy import *
import openpyxl
import sys
import os
import networkx as nx
import matplotlib.pyplot as plt
import random
import math
import numpy as np
from matplotlib.ticker import MaxNLocator, ScalarFormatter
from matplotlib.cm import ScalarMappable
import matplotlib.colors as mcolors

# 为了求解一次

class Assignment:
    @classmethod
    def relaytask(cls, Q, La, L, b_num):
        row = len(Q)
        col = len(Q[0])

        # build a optimal problem
        pro = pl.LpProblem('MIN(connection and coverage )', pl.LpMinimize)
        solver = pl.getSolver('GUROBI_CMD')


        # build variables for the optimal problem
        lpvars = [[[pl.LpVariable("x" + str(i) + "y" + str(j) + "l" + str(b), lowBound=0, upBound=1, cat='Integer') for
                    j in range(col)] for i in range(row)]for b in range(b_num)]

        # build optimal function
        all = pl.LpAffineExpression()
        for b in range(0,b_num):
            for i in range(0,row):
                for j in range(0,col):
                    all += Q[i][j] * lpvars[b][i][j]
        pro += all

        # build constraint
        ## 满足聚集点需求
        for b in range(b_num):
            for j in range(0, col):
                tempSum = 0
                for i in range(0, row):
                    tempSum += lpvars[b][i][j]
                pro += tempSum == new_L[b][j]

        # la约束 (第一批飞出)
        for i in range(0, base):
            tempSum = 0
            for j in range(0, col):
                tempSum += lpvars[0][4 * i][j] + lpvars[0][4 * i + 1][j] + lpvars[0][4 * i + 2][j] + lpvars[0][4 * i + 3][j]
            pro += tempSum <= La[i]

        #   容量约束 (考虑基站本身有的无人机数量)
        for b in range(0, b_num - 1):
            for i in range(base):
                sumout = 0
                sumin = 0
                suminnow = lpvars[b][i][j] + lpvars[b][i + 4][j] + lpvars[b][i + 8][j] + lpvars[b][i + 12][j]   # 这一次飞入
                sumoutnow = lpvars[b][4 * i][j] + lpvars[b][4 * i + 1][j] + lpvars[b][4 * i + 2][j] + lpvars[b][4 * i + 3][j]   # 这一次飞出
                sumoutnext = lpvars[b + 1][4 * i][j] + lpvars[b + 1][4 * i + 1][j] + lpvars[b + 1][4 * i + 2][j] + lpvars[b + 1][4 * i + 3][j]  # 下一次飞出
                tempsum = 0
                for temp in range(0, b + 1):
                    for j in range(col):
                        sumin += lpvars[temp][i][j] + lpvars[temp][i + 4][j] + lpvars[temp][i + 8][j] + lpvars[temp][i + 12][j]    #k次飞进总和
                        sumout += lpvars[temp][4 * i][j] + lpvars[temp][4 * i + 1][j] + lpvars[temp][4 * i + 2][j] + lpvars[temp][4 * i + 3][j]    #k次飞出总和
                pro += sumoutnext <= eUAV[i] + sumin - sumout
                pro += sumin - sumout >= -1 * eUAV[i]
                # pro += suminnow - sumoutnow - sumoutnext <= eUAV[i]
                pro += sumout + sumoutnext >= sumin

        # 总容量约束
        for i in range(base):
            sumout = 0
            sumin = 0
            for b in range(0, b_num):
                for j in range(col):
                    sumout += lpvars[b][4*i][j] + lpvars[b][4*i+1][j] + lpvars[b][4*i+2][j] + lpvars[b][4*i+3][j]
                    sumin += lpvars[b][i][j] + lpvars[b][i+4][j] + lpvars[b][i+8][j] + lpvars[b][i+12][j]
            pro += sumout == sumin

        # # 树形约束1
        # for b in range(0,b_num):
        #     for i in range(row):
        #         for j in range(col):
        #             for jj in range(col):
        #                 for jjj in range(col):
        #                     pro += superior_matrix[jj][j] * lpvars[b][i][jj] <= lpvars[b][i][j] + \
        #                                sibling_matrix[jjj][j] * lpvars[b][i][jjj]
        #
        # # 树形约束2
        # for b in range(0,b_num):
        #     for i in range(row):
        #         temp = 0
        #         for j in range(col):
        #             temp += lpvars[b][i][j] * leaf_vector[j]
        #         pro += temp <= 1

        # 相邻上下级
        for b in range(0,b_num):
            for i in range(base):
                for j in range(col):
                    for jj in range(col):
                        # pro += Rc[j, jj] * (lpvars[b][i][j] + lpvars[b][i][jj]) <= 1
                        pro += superior_matrix[j, jj] * (lpvars[b][i][j] + lpvars[b][i][jj]) <= 1
                        # pro += lpvars[b][i][j] + lpvars[b][i][jj] >= superior_matrix[jj, j] * 2

                        # pro += lpvars[b][4 * i][j] + lpvars[b][4 * i + 1][j] + lpvars[b][4 * i + 2][j] + lpvars[b][4 * i + 3][j] >= superior_matrix[jj, j]
                        # pro += lpvars[b][4 * i][jj] + lpvars[b][4 * i + 1][jj] + lpvars[b][4 * i + 2][jj] + lpvars[b][4 * i + 3][jj] >= superior_matrix[jj, j]

                        # pro += lpvars[b][i][j] >= Rc[jj, j]
                        # pro += lpvars[b][i][jj] >= Rc[jj, j]


        # solve optimal problem
        status = pro.solve(solver)
        print("Assignment Status: ", pl.LpStatus[status])
        print("Final Assignment Result", pl.value(pro.objective))

        # get the result of T matrix
        T = [[[lpvars[b][i][j].varValue for j in range(col)] for i in range(row)] for b in range(b_num)]
        return T

# 计算两点之间距离公式
def calDistance(x1, x2, y1, y2):
    result = math.sqrt((x2-x1)**2+(y2-y1)**2)
    return result

# 聚集点与基站组合距离（21*4的矩阵）
def genDistance(x,y,x1,y1):
    distance_BC = []
    for j in range(base):
        tempMatrix = []
        for i in range(cluster):
            tempMatrix.append(calDistance(x[i],x1[j],y[i],y1[j]))
        distance_BC.append(tempMatrix)
    return distance_BC

# 聚集点之间的距离（21*21的矩阵）
def gentaskdistance(x,y):
    taskdistance = []
    for i in range(cluster):
        tempMatrix = []
        for i in range(cluster):
            tempMatrix.append(calDistance(x[i],y[i],x[i],y[i]))
        taskdistance.append(tempMatrix)
    return taskdistance

# 聚集点与基站组合距离算出Q(21*16的矩阵)
def genQMatrix(distance_BC):
    Q = []
    for k in range(base):
        for j in range(base):
            temp = []
            for i in range(cluster):
                total_distance = []
                total_distance = distance_BC[k,i]+distance_BC[j,i]
                temp.append(total_distance)
            Q.append(temp)
    return Q

# 归一化后的Q
def getNormalizedQ(Q):
    max = min = Q[0][0]
    row = len(Q)
    column = len(Q[0])
    for i in range(row):
        for j in range(column):
            if (Q[i, j] > max):
                max = Q[i, j]
            if (Q[i, j] < min):
                min = Q[i, j]
    for i in range(row):
        for j in range(column):
            Q[i, j] = round((Q[i, j] - min) / (max - min), 4)
    return Q

# 随机生成La
def genLaMatrix(role_amount, agent_amount):
		La = []
		temp = agent_amount
		while(temp>=1):
				val = ceil(role_amount/agent_amount)
				val += random.randint(1, agent_amount)
				if val not in La:
						La.append(val)
						temp-=1
		return La

#   Q矩阵汇总
def gennewQmatrix(x,y,x1,y1):
    distance_BC = genDistance(x, y, x1, y1)
    distance_BC_array = np.array(distance_BC)

    Q = genQMatrix(distance_BC_array)
    Q = np.array(Q)
    QMatrix = np.array(Q)

    # 对超过续航时间的Q[i][j]赋为255
    for i in range(0,len(QMatrix)):
        for j in range(0,len(QMatrix[0])):
            if QMatrix[i][j] >= Qmax:
                QMatrix[i][j] = 255

    QMatrix = getNormalizedQ(QMatrix)
    return Q

#   总航迹
def contnewpath(T, b_num):
    condistance = []
    for b in range(b_num):
        condis = 0
        for i in range(combination):
            for j in range(cluster):
                if T[b][i][j] == 1:
                    condis += Q[i][j]
        condistance.append(condis)
    condistance_total = sum(condistance)
    return condistance_total

# 计算基站派出/派回无人机数量
def countdrones(T):
    countbase_in = [[0,0,0,0] for i in range(b_num)]
    countbase_out = [[0,0,0,0] for i in range(b_num)]
    for b in range(0, b_num):
        countin = 0
        countout = 0
        for i in range(0, base):
            for j in range(0, cluster):
                countin += T[b][i][j] + T[b][i + 4][j] + T[b][i + 8][j] + T[b][i + 12][j]  # eg:base1进基站为j=0,4,8,12
                countout += T[b][4 * i][j] + T[b][4 * i + 1][j] + T[b][4 * i + 2][j] + T[b][4 * i + 3][j]  # eg:base2出基站为j=0,1,2,3
            countbase_in[b][i] = countin
            countbase_out[b][i] = countout
            countin = 0
            countout = 0
    countbase_in = np.array(countbase_in)
    countbase_out = np.array(countbase_out)
    return countbase_in, countbase_out

# 根据上下级矩阵和同级矩阵推导叶子节点向量
def infer_leaf_vector(superior_matrix, sibling_matrix):
    num_tasks = superior_matrix.shape[0]
    leaf_vector = np.ones(num_tasks, dtype=int)  # 初始化叶子节点向量，假设所有节点都是叶子节点

    # 遍历每个节点
    for i in range(num_tasks):
        # 检查是否存在下级节点
        if np.sum(superior_matrix[:, i]) > 0:
            leaf_vector[i] = 0  # 如果存在下级节点，则当前节点不是叶子节点

        # 检查是否存在同级节点
        for j in range(i + 1, num_tasks):
            if sibling_matrix[i, j] == 1:
                leaf_vector[i] = 0  # 如果存在同级节点，则当前节点不是叶子节点

    return leaf_vector


# 生成树形图
def generate_tree_graph(superior_matrix):
    num_tasks = superior_matrix.shape[0]
    G = nx.DiGraph()

    # 添加节点
    for i in range(num_tasks):
        G.add_node(i, label=f'Point_{i}')

    # 添加边
    for i in range(num_tasks):
        for j in range(num_tasks):
            if superior_matrix[i, j] == 1:
                G.add_edge(j, i)  # 上下级关系中，j是i的上级

    return G

# 指派路径
def drawpath(T,x,y,x1,y1,combination,cluster):
    T_row = combination
    T_col = cluster
    base1_out_x = [[] for i in range(b_num)]
    base1_out_y = [[] for i in range(b_num)]
    base1_in_x = [[] for i in range(b_num)]
    base1_in_y = [[] for i in range(b_num)]
    base2_out_x = [[] for i in range(b_num)]
    base2_out_y = [[] for i in range(b_num)]
    base2_in_x = [[] for i in range(b_num)]
    base2_in_y = [[] for i in range(b_num)]
    base3_out_x = [[] for i in range(b_num)]
    base3_out_y = [[] for i in range(b_num)]
    base3_in_x = [[] for i in range(b_num)]
    base3_in_y = [[] for i in range(b_num)]
    base4_out_x = [[] for i in range(b_num)]
    base4_out_y = [[] for i in range(b_num)]
    base4_in_x = [[] for i in range(b_num)]
    base4_in_y = [[] for i in range(b_num)]

    # out
    for b in range(b_num):
        for i in range(T_row):
            outbase = math.floor(i / 4) + 1
            for j in range(T_col):
                if T[b][i][j] == 1 and outbase == 1:
                    base1_out_x[b].append((x1[0], x[j]))
                    base1_out_y[b].append((y1[0], y[j]))
                elif T[b][i][j] == 1 and outbase == 2:
                    base2_out_x[b].append((x1[1], x[j]))
                    base2_out_y[b].append((y1[1], y[j]))
                elif T[b][i][j] == 1 and outbase == 3:
                    base3_out_x[b].append((x1[2], x[j]))
                    base3_out_y[b].append((y1[2], y[j]))
                elif T[b][i][j] == 1 and outbase == 4:
                    base4_out_x[b].append((x1[3], x[j]))
                    base4_out_y[b].append((y1[3], y[j]))

    # in
    for b in range(b_num):
        for i in range(T_row):
            inbase = i - math.floor(i / 4) * 4 + 1
            for j in range(T_col):
                if T[b][i][j] == 1 and inbase == 1:
                    base1_in_x[b].append((x[j], x1[0]))
                    base1_in_y[b].append((y[j], y1[0]))
                elif T[b][i][j] == 1 and inbase == 2:
                    base2_in_x[b].append((x[j], x1[1]))
                    base2_in_y[b].append((y[j], y1[1]))
                elif T[b][i][j] == 1 and inbase == 3:
                    base3_in_x[b].append((x[j], x1[2]))
                    base3_in_y[b].append((y[j], y1[2]))
                elif T[b][i][j] == 1 and inbase == 4:
                    base4_in_x[b].append((x[j], x1[3]))
                    base4_in_y[b].append((y[j], y1[3]))

    base1_out_x = np.array(base1_out_x,dtype=object)
    base1_out_y = np.array(base1_out_y,dtype=object)
    base1_in_x = np.array(base1_in_x,dtype=object)
    base1_in_y = np.array(base1_in_y,dtype=object)
    base2_out_x = np.array(base2_out_x,dtype=object)
    base2_out_y = np.array(base2_out_y,dtype=object)
    base2_in_x = np.array(base2_in_x,dtype=object)
    base2_in_y = np.array(base2_in_y,dtype=object)
    base3_out_x = np.array(base3_out_x,dtype=object)
    base3_out_y = np.array(base3_out_y,dtype=object)
    base3_in_x = np.array(base3_in_x,dtype=object)
    base3_in_y = np.array(base3_in_y,dtype=object)
    base4_out_x = np.array(base4_out_x,dtype=object)
    base4_out_y = np.array(base4_out_y,dtype=object)
    base4_in_x = np.array(base4_in_x,dtype=object)
    base4_in_y = np.array(base4_in_y,dtype=object)

    return base1_out_x,base1_out_y,base2_out_x,base2_out_y,base3_out_x,base3_out_y,base4_out_x,base4_out_y,\
           base1_in_x,base1_in_y,base2_in_x,base2_in_y,base3_in_x,base3_in_y,base4_in_x,base4_in_y

#   Q矩阵汇总
def gennewQmatrix(x,y,x1,y1):
    distance_BC = genDistance(x, y, x1, y1)
    distance_BC_array = np.array(distance_BC)

    Q = genQMatrix(distance_BC_array)
    Q = np.array(Q)
    QMatrix = np.array(Q)

    # 对超过续航时间的Q[i][j]赋为255
    for i in range(0,len(QMatrix)):
        for j in range(0,len(QMatrix[0])):
            if QMatrix[i][j] >= Qmax:
                QMatrix[i][j] = 255

    QMatrix = getNormalizedQ(QMatrix)
    return Q

# 随机生成层级相邻矩阵
def generate_and_complete_matrix(n):
    # Generate a random upper triangular matrix
    upper_triangular = np.triu(np.random.randint(2, size=(n, n)), k=1)

    # Complete the lower triangular part by taking the transpose of the upper triangular matrix
    lower_triangular = upper_triangular.T

    # Combine upper and lower triangular matrices to form the complete matrix
    complete_matrix = upper_triangular + lower_triangular

    # Set diagonal elements to zero
    np.fill_diagonal(complete_matrix, 0)

    return complete_matrix


def print_matrix(matrix):
    for row in matrix:
        print(row)


if __name__ == '__main__':


    # x = [4.61, 3.98, 3.66, 6.75, 10.5, 13.81, 17.33, 12.41, 7.21, 15.21, 11.89, 17.21]
    # y = [9.53, 4.76, 12.94, 15.87, 8.55, 17.36, 10.76, 13.79, 2.51, 4.23, 3.55, 5.86]
    # x1 = [18.95, 6.42, 11.52, 16.77]
    # y1 = [6.92, 9.6, 18.78, 15.33]



    # x = [4.61, 3.98, 3.66, 6.75, 10.5, 13.81, 17.33, 12.41]
    # y = [9.53, 4.76, 12.94, 15.87, 8.55, 17.36, 10.76, 13.79]
    # x1 = [18.95, 6.42, 11.52, 16.77]
    # y1 = [6.92, 9.6, 18.78, 15.33]

    b_num = 50
    base = 4
    outbase = 4
    inbase = 4
    cluster = 60
    combination = 16
    ftcharge = 0.75 # 充电45分钟
    fv = 50  # 飞行速度50km/h
    Qmax = ftcharge*fv
    # L = [5, 6, 3, 3, 3, 2, 8, 6, 2, 3, 1, 3]
    L = np.random.randint(0, 6, size=cluster)
    # L = [2, 1, 2, 1, 1, 2, 1, 1]   # total 47

    x = np.random.choice(range(0, 21), cluster)  # 聚集点坐标
    y = np.random.choice(range(0, 21), cluster)
    x1 = np.random.choice(range(0, 21), base)  # 基站坐标
    y1 = np.random.choice(range(0, 21), base)
    Q = gennewQmatrix(x, y, x1, y1)

    # 层级相邻关系矩阵
    superior_matrix = generate_and_complete_matrix(cluster)

    # 动态需求
    new_L = []
    new_L.append(L)
    random_vector = np.zeros(cluster)
    saverandom_vector = []
    saverandom_vector.append(random_vector)

    # # 读取随机向量
    # file_path_saverandom_vector = 'G:\\毕业论文\\树形\\saverandom_vector.csv'
    # saverandom_vector = pd.read_csv(file_path_saverandom_vector, header=None)
    # for b in range(1, b_num):
    #     # 将前一个批次的L中的每个元素与对应的随机向量元素相乘，并向上取整，存储到新的L中
    #     # random_vector = [random.random() for _ in range(cluster)]
    #     new_L.append([math.ceil(L[i] * saverandom_vector[b][i]) for i in range(len(L))])
    #     # saverandom_vector.append(random_vector)
    # # new_L = np.array(new_L)

    for b in range(1, b_num):
        # 将前一个批次的L中的每个元素与对应的随机向量元素相乘，并向上取整，存储到新的L中
        random_vector = [random.random() for _ in range(cluster)]
        new_L.append([math.ceil(L[i] * random_vector[i]) for i in range(len(L))])
        saverandom_vector.append(random_vector)
    # new_L = np.array(new_L)

    capacity = np.array([45, 39, 42, 36])
    eUAV = capacity/3
    eUAV = np.array(eUAV)

    La = [100, 100, 120, 150]    # total 54
    # La = [1000,1000,1000,1000]

    # 聚集点之间距离
    # 将x和y坐标列表合并成一个数组，每一行表示一个点的坐标
    coordinates = np.column_stack((x, y))
    # 计算点之间的距离矩阵
    num_points = coordinates.shape[0]
    distancesRc = np.zeros((num_points, num_points))
    for i in range(num_points):
        for j in range(num_points):
            distancesRc[i, j] = np.linalg.norm(coordinates[i] - coordinates[j])
    distancesRc = np.array(distancesRc)
    Rc = getNormalizedQ(distancesRc)
    Rc = np.array(Rc)

    # # 树形
    # # 读取同级矩阵
    # file_path_sibling_matrix = 'G:\\毕业论文\\树形\\分层同级矩阵.csv'
    # sibling_matrix = pd.read_csv(file_path_sibling_matrix, header=None)
    # sibling_matrix = np.array(sibling_matrix)
    # # print("同级矩阵维度：", sibling_matrix.shape)
    # print("同级矩阵：", sibling_matrix)
    #
    # # 读取上下级矩阵
    # file_path_superior_matrix = 'G:\\毕业论文\\树形\\分层上下级矩阵.csv'
    # superior_matrix = pd.read_csv(file_path_superior_matrix, header=None)
    # superior_matrix = np.array(superior_matrix)
    # # print("上下级矩阵维度：", superior_matrix.shape)
    # print("上下级矩阵：", superior_matrix)
    #
    # # 读取Rc
    # file_path_Rc = 'G:\\毕业论文\\树形\\Rc.csv'
    # superior_matrix = pd.read_csv(file_path_Rc, header=None)
    # superior_matrix = np.array(Rc)
    # # print("Rc矩阵维度：", superior_matrix.shape)
    # print("Rc矩阵：", superior_matrix)

    # # 生成叶子节点向量
    # leaf_vector = np.zeros(num_tasks, dtype=int)  # 先将所有节点设为非叶子节点
    # for i in range(num_tasks):
    #     if np.sum(superior_matrix[:, i]) == 0:  # 没有下级节点的节点为叶子节点
    #         leaf_vector[i] = 1

    leaf_vector = [1,1,1,1,0,0,0,0,0,0,1,0]
    print("叶子节点：", leaf_vector)

    #一次运行结果
    distance_BC = genDistance(x, y, x1, y1)
    distance_BC_array = np.array(distance_BC)
    print(distance_BC)
    Q = genQMatrix(distance_BC_array)
    Q = np.array(Q)
    QMatrix = np.array(Q)

    # 对超过续航时间的Q[i][j]赋为255
    for i in range(0,len(QMatrix)):
        for j in range(0,len(QMatrix[0])):
            if QMatrix[i][j] >= Qmax:
                QMatrix[i][j] = 255

    QMatrix = getNormalizedQ(QMatrix)
    print(QMatrix)

    start_1 = time.perf_counter()
    T1 = Assignment.relaytask(QMatrix, La, new_L, b_num)
    total_1 = time.perf_counter() - start_1
    TMAT = np.array(T1)
    print(TMAT)

    countbase_in, countbase_out = countdrones(T1)

    # 计算路径总距离
    condistance = []
    for b in range(b_num):
        condis = 0
        for i in range(combination):
            for j in range(cluster):
                if T1[b][i][j] == 1:
                    condis += Q[i][j]
        condistance.append(condis)
    print(condistance)
    condistance_total = sum(condistance)
    print('总航迹', condistance_total)

    # print('运行时间', total)
    # print('一次性运行时间:', total_1)
    # # print('分次运行时间：', total_2)
    # print('总运行时间:', total_3)

    print('总批次数：', b_num)

    # 计算总批次的总派出和派入
    totalin = [0 for i in range(base)]
    totalout = [0 for i in range(base)]
    for i in range(base):
        tempcontin = 0
        tempcontout = 0
        for b in range(b_num):
            tempcontin += countbase_in[b][i]
            tempcontout += countbase_out[b][i]
        totalin[i] = tempcontin
        totalout[i] = tempcontout
    print('各基站总派出无人机数:', totalout)
    print('各基站总派回无人机数:', totalin)

    sumtotalin = 0
    sumtotalout = 0
    for i in range(base):
        sumtotalin += totalin[i]
        sumtotalout += totalout[i]
    print('批次总返回数', sumtotalin)
    print('批次总派出数', sumtotalout)

    # 指派路径图
    base1_out_x, base1_out_y, base2_out_x, base2_out_y, base3_out_x, base3_out_y, base4_out_x, base4_out_y, \
    base1_in_x, base1_in_y, base2_in_x, base2_in_y, base3_in_x, base3_in_y, base4_in_x, base4_in_y = drawpath(TMAT,x,y,x1,y1,combination,cluster)

    # 派出路径图
    for b in range(b_num):
        plt.figure()
        camp_reversed = mcolors.ListedColormap(list(reversed(plt.cm.autumn(np.linspace(0, 1, 256)))))  # 热力图色带反转

        # x = [4.61, 3.98, 3.66, 6.75, 10.5, 13.81, 17.33, 12.41, 7.21, 15.21, 11.89, 17.21]
        # y = [9.53, 4.76, 12.94, 15.87, 8.55, 17.36, 10.76, 13.79, 2.51, 4.23, 3.55, 5.86]

        # random_vector = [random.random() for _ in range(cluster)]
        # saverandom_vector.append(random_vector)
        new_L.append([math.ceil(L[i] * saverandom_vector[b][i]) for i in range(len(L))])

        colors = new_L[b]  # 将new_L中每批次的L作为颜色数据
        sizes = [color * 250 for color in colors]  # 根据new_L的值设置点的大小

        # 创建一个标量映射对象，并设置其数据范围和颜色映射
        norm = plt.Normalize(min(colors), max(colors))
        cmap = plt.get_cmap('Accent')
        sm = ScalarMappable(norm=norm, cmap=camp_reversed)

        plt.scatter(x, y, c=colors, s=sizes, alpha=0.5, edgecolors='w', label='人口密集区域', cmap=camp_reversed)

        # x1 = [18.95, 6.42, 11.52, 16.77]
        # y1 = [6.92, 9.6, 18.78, 15.33]
        plt.scatter(x1, y1, color="green", s=200, alpha=0.7, label='无人机基站', marker='*')

        plt.legend(loc='lower right', fontsize=10, markerscale=0.5)
        cbar = plt.colorbar(sm)
        cbar.set_label('人口密集程度')

        # 设置颜色条格式，避免使用偏移
        cbar.formatter = ScalarFormatter(useOffset=False)
        cbar.update_ticks()

        # 派出图
        for i in range(len(base1_out_x[b])):
            plt.plot(base1_out_x[b][i], base1_out_y[b][i], linewidth='1', color='steelblue')
        for i in range(len(base2_out_x[b])):
            plt.plot(base2_out_x[b][i], base2_out_y[b][i], linewidth='1', color='steelblue')
        for i in range(len(base3_out_x[b])):
            plt.plot(base3_out_x[b][i], base3_out_y[b][i], linewidth='1', color='steelblue')
        for i in range(len(base4_out_x[b])):
            plt.plot(base4_out_x[b][i], base4_out_y[b][i], linewidth='1', color='steelblue')

        plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
        c = b + 1
        plt.title('第%s批次无人机派出路径图' % c, fontdict={'weight': 'normal', 'size': 15})
        plt.xlim(0, 20, 1)
        plt.ylim(0, 20, 2)
        plt.gca().xaxis.set_major_locator(MaxNLocator(integer=True))  # 限制坐标轴刻度为整数
        plt.gca().yaxis.set_major_locator(MaxNLocator(integer=True))

        # plt.show()
        c = 0

    # 派回路径图
    for b in range(b_num):
        plt.figure()
        camp_reversed = mcolors.ListedColormap(list(reversed(plt.cm.autumn(np.linspace(0, 1, 256)))))  # 热力图色带反转

        # x = [4.61, 3.98, 3.66, 6.75, 10.5, 13.81, 17.33, 12.41, 7.21, 15.21, 11.89, 17.21]
        # y = [9.53, 4.76, 12.94, 15.87, 8.55, 17.36, 10.76, 13.79, 2.51, 4.23, 3.55, 5.86]

        # random_vector = [random.random() for _ in range(cluster)]
        # saverandom_vector.append(random_vector)
        new_L.append([math.ceil(L[i] * saverandom_vector[b][i]) for i in range(len(L))])

        colors = new_L[b]  # 将new_L中每批次的L作为颜色数据
        sizes = [color * 250 for color in colors]  # 根据new_L的值设置点的大小

        # 创建一个标量映射对象，并设置其数据范围和颜色映射
        norm = plt.Normalize(min(colors), max(colors))
        cmap = plt.get_cmap('Accent')
        sm = ScalarMappable(norm=norm, cmap=camp_reversed)

        plt.scatter(x, y, c=colors, s=sizes, alpha=0.5, edgecolors='w', label='人口密集区域', cmap=camp_reversed)

        # x1 = [18.95, 6.42, 11.52, 16.77]
        # y1 = [6.92, 9.6, 18.78, 15.33]
        plt.scatter(x1, y1, color="green", s=200, alpha=0.7, label='无人机基站', marker='*')

        plt.legend(loc='lower right', fontsize=10, markerscale=0.5)
        cbar = plt.colorbar(sm)
        cbar.set_label('人口密集程度')

        # 设置颜色条格式，避免使用偏移
        cbar.formatter = ScalarFormatter(useOffset=False)
        cbar.update_ticks()

        # 派回图
        for i in range(len(base1_in_x[b])):
            plt.plot(base1_in_x[b][i], base1_in_y[b][i], linewidth='1', color='steelblue', linestyle = "-.")
        for i in range(len(base2_in_x[b])):
            plt.plot(base2_in_x[b][i], base2_in_y[b][i], linewidth='1', color='steelblue', linestyle = "-.")
        for i in range(len(base3_in_x[b])):
            plt.plot(base3_in_x[b][i], base3_in_y[b][i], linewidth='1', color='steelblue', linestyle = "-.")
        for i in range(len(base4_in_x[b])):
            plt.plot(base4_in_x[b][i], base4_in_y[b][i], linewidth='1', color='steelblue', linestyle = "-.")


        plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
        c = b + 1
        plt.title('第%s批次无人机派回路径图' % c, fontdict={'weight': 'normal', 'size': 15})
        plt.xlim(0, 20, 1)
        plt.ylim(0, 20, 2)
        plt.gca().xaxis.set_major_locator(MaxNLocator(integer=True))  # 限制坐标轴刻度为整数
        plt.gca().yaxis.set_major_locator(MaxNLocator(integer=True))

        # plt.show()
        c = 0

    saverandom_vector = np.array(saverandom_vector)

    print('运行时间',total_1)