import math
import matplotlib.pyplot as plt
from matplotlib.ticker import MaxNLocator
from math import floor
import numpy as np
import xlrd
import pandas as pd
import pulp as pl
import pandas as pd
from math import cos, sin, acos, asin, pi, atan, fabs, ceil, floor
from collections import defaultdict
import random
import time

base = 4
outbase = 4
b_num = 2
col = 3
Finalout = 0
Finalin = 0
sumconout = 0
sumconin = 0
sum_intotal = []
sum_outtotal = []
T =[[[1, 1, 0],
    [1, 0, 0],
    [1, 1, 0],
    [1, 1, 1],
    [1, 1, 0],
    [1, 0, 0],
    [1, 1, 0],
    [1, 1, 1],
    [1, 1, 0],
    [1, 1, 1],
    [1, 0, 0],
    [1, 1, 0],
    [1, 1, 1],
    [1, 1, 0],
    [1, 0, 0],
    [1, 1, 0]],
    [[1, 1, 0],
    [1, 0, 0],
    [1, 1, 0],
    [1, 1, 1],
    [1, 1, 0],
    [1, 0, 0],
    [1, 1, 0],
    [1, 1, 1],
    [1, 1, 0],
    [1, 1, 1],
    [1, 0, 0],
    [1, 1, 0],
    [1, 1, 1],
    [1, 1, 0],
    [1, 0, 0],
    [1, 1, 0]]]
# for i in range(outbase):
#     sumconin = 0
#     sumconout = 0
#     for b in range(0, b_num):
#         for j in range(col):
#             tempconin = T[b][i][j] + T[b][i + 4][j] + T[b][i + 8][j] + T[b][i + 12][j]
#             sumconin += tempconin
#             tempconout = T[b][4 * i][j] + T[b][4 * i + 1][j] + T[b][4 * i + 2][j] + T[b][4 * i + 3][j]
#             sumconout += tempconout
#         print(sumconin)
#     sum_intotal.append(sumconin)
#     sum_outtotal.append(sumconout)
#     for b in range(b_num - 1, b_num):
#         teout = sum_outtotal[i]
#         tein = sum_intotal[i]  # b-1批次飞进飞出总和
#         for j in range(col):
#             Finalout += T[b][4 * i][j] + T[b][4 * i + 1][j] + T[b][4 * i + 2][j] + T[b][4 * i + 3][j]
#             Finalin += T[b][i][j] + T[b][i + 4][j] + T[b][i + 8][j] + T[b][i + 12][j]
#     lastin = teout + Finalout - tein
#
# T = np.array(T)

tempconin = 0
tempconout = 0
contempty = 0
for i in range(base):
    tempSumi = 0
    for b in range(0, b_num):
        for j in range(col):
            tempSumi += T[b][i][j] + T[b][i + 4][j] + T[b][i + 8][j] + T[b][i + 12][j] #当前批次派进数量
            tempconin += T[b][i][j] + T[b][i + 4][j] + T[b][i + 8][j] + T[b][i + 12][j]
            tempconout += T[b][4 * i][j] + T[b][4 * i + 1][j] + T[b][4 * i + 2][j] + T[b][4 * i + 3][j]
        tempempty = tempconout - tempconin
        contempty += tempempty
    # pro += tempSumi <= contempty
    # tempconin = 0
    # tempconout = 0

T = np.array(T)