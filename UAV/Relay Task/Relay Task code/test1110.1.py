for t in range(0, scale):
    x = np.random.choice(range(0, 21), 22)  # 聚集点坐标
    y = np.random.choice(range(0, 21), 22)
    x1 = np.random.choice(range(0, 21), 4)  # 基站坐标
    y1 = np.random.choice(range(0, 21), 4)
    Q = gennewQmatrix(x, y, x1, y1)

    start_sum = time.perf_counter()
    # 记录总指派结果（故障前+故障后）
    T_total = [[[[] for j in range(cluster)] for i in range(combination)] for b in range(b_num)]

    start_1 = time.perf_counter()
    T0 = Assignment.KM(Q, La, L, b_num)  # 正常指派
    T0 = np.array(T0)
    distance_T0, condistance_T0 = contnewpath(T0, b_num)
    condistance_T0 = np.array(condistance_T0)
    total_1 = time.perf_counter() - start_1
    large_time_1[t] = total_1
    large_path_1[t] = distance_T0

    fau_b1 = random.randint(0, 100)
    faub_num = b_num - fau_b1 + 1  # 故障后批次（用于第二次指派）

    start_2 = time.perf_counter()
    # 存放故障批次前的指派结果
    tempT = [[[[] for j in range(cluster)] for i in range(combination)] for b in range(fau_b1 - 1)]
    for b in range(0, fau_b1 - 1):  # 34批结果记录
        for i in range(combination):
            for j in range(cluster):
                tempT[b][i][j] = T0[b][i][j]
                T_total[b][i][j] = tempT[b][i][j]

    # 记录故障批次前派出派进情况
    beforef_in = [[] for i in range(base)]
    beforef_out = [[] for i in range(base)]
    beforef_empty = [[] for i in range(base)]
    for i in range(base):
        tempbeforef_in = 0
        tempbeforef_out = 0
        for b in range(0, fau_b1 - 1):
            for j in range(cluster):
                tempbeforef_in += tempT[b][i][j] + tempT[b][i + 4][j] + tempT[b][i + 8][j] + tempT[b][i + 12][j]
                tempbeforef_out += tempT[b][4 * i][j] + tempT[b][4 * i + 1][j] + tempT[b][4 * i + 2][j] + \
                                   tempT[b][4 * i + 3][j]
        tempempty = tempbeforef_out - tempbeforef_in
        beforef_in[i] = tempbeforef_in
        beforef_out[i] = tempbeforef_out
        beforef_empty[i] = tempempty

    empty = [0 for i in range(base)]
    for i in range(base):
        empty[i] = beforef_empty[i]

    # 合并两种情况
    if fau_b1 + fauc < b_num:
        T1 = Assignment2.KM2(Q, La, L, faub_num)
        T_total = tempT + T1
    elif fau_b1 + fauc == b_num:
        T3 = Assignment4.KM4(Q, La, L, faub_num)
        T_total = tempT + T3
    else:
        T2 = Assignment3.KM3(Q, La, L, faub_num)
        T_total = tempT + T2

    # 判断是否有空
    for b in range(b_num):
        for i in range(combination):
            for j in range(cluster):
                if T_total[b][i][j] is None:
                    cont_false += 1
                    break

    distance_total, condistance_total = contnewpath(T_total, b_num)
    condistance_total = np.array(condistance_total)
    T_total = np.array(T_total)
    total_2 = time.perf_counter() - start_2

    total_sum = time.perf_counter() - start_sum
    large_time_f[t] = total_sum
    large_path_f[t] = distance_total