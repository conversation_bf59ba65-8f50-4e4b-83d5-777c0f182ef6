import math
import matplotlib.pyplot as plt
from matplotlib.ticker import MaxNLocator
from math import floor
import numpy as np
import xlrd
import pandas as pd
import pulp as pl
import pandas as pd
from math import cos, sin, acos, asin, pi, atan, fabs, ceil, floor
from collections import defaultdict
import random
import time

# 读取xls文件数据
# data = xlrd.open_workbook('G:\Result\Q2.xls')  # 打开xls文件
# table = data.sheets()[0]
# nrows = table.nrows  # 获取表的行数
# for i in range(nrows):  # 循环逐行打印
#     # 跳过第一行
#     if i == 0:
#         continue
#     print(table.row_values(i))

# 列表逐个元素相减
# capacity = [60, 60, 60, 60] # 基站初始容量均为60
# La = [20, 31, 22, 27] # total 100
# remainder = list(map(lambda x:x[0]-x[1], zip(capacity,La)))
# print(remainder)

# 续航时间约束
# Q = [
#         [21.44032882, 19.36774897, 18.95155139, 16.4705859, 15.13084598, 12.7591575, 12.71520743, 10.56494203,
#          10.20966209, 8.086810249, 16.82732302, 9.697963704, 7.285121825, 2.016258912, 6.439914596, 3.913182848,
#          11.03130545, 13.89602821, 17.17158409, 16.17297128, 11.42887571],
#         [11.03363041, 7.311497795, 6.383510006, 5.784263134, 6.278678205, 7.99879991, 11.60666188, 13.38826725,
#          7.906054642, 11.8652855, 4.105618102, 3.150142854, 6.048842865, 11.21546254, 6.953013735, 12.44813641,
#          6.191752579, 8.54028688, 7.961130573, 4.710594442, 1.666883319],
#         [9.938339902, 11.03806142, 12.04978008, 7.780006427, 5.587575503, 2.516028617, 2.301521236, 5.98101162,
#          4.117341375, 6.851642139, 11.47730369, 10.44939233, 8.639126113, 12.02308613, 12.52161331, 6.39289175,
#          15.37203305, 18.45880007, 18.4590899, 14.98320727, 9.314789316],
#         [15.73103938, 15.54045688, 15.96985285, 12.11199818, 10.03454035, 6.374590183, 4.373785546, 2.105825254,
#          4.397101318, 0.627694193, 14.57966049, 10.02161664, 6.720751446, 6.985957343, 9.971649813, 11.9284911,
#          14.31029, 17.66944538, 19.15622614, 16.48509933, 10.18907749]
#     ]
# fspeed  = 50
# Ftcharge = 0.75
# Q = np.array(Q)
# print(Q/fspeed)

# 生成基站与聚集点距离矩阵
# x = [1.61, 1.24, 0.98, 4.66, 6.75, 10.5, 13.81, 17.33, 12.41, 17.09, 2.73, 9.39, 12.44, 17.57, 12.54, 17.22, 8.39, 6.35, 2.28, 2.78, 7.99]
# y = [19.53, 14.76, 12.94, 15.11, 15.87, 16.48, 18.55, 17.36, 14.76, 14.79, 11.4, 8.55, 10.19, 8.39, 6.3, 3.41, 3.73, 1.06, 2.8, 6.61, 10.16]
# x1 = [18.95, 6.42, 11.52, 16.77]
# y1 = [6.92, 9.6, 18.78, 15.33]
#
# # 计算两点之间距离公式
# def calDistance(x1, y1, x2, y2):
# 		result = np.sqrt(np.square(x1-x2) + np.square(y1-y2))
# 		return result
#
# # 聚集点与基站组合距离（21*4的矩阵）
# def genDistance(x,y,x1,y1):
#     base = 4       # 4个基站
#     cluster = 21   # 21个聚集点
#     distance_BC = []
#     for j in range(base):
#         tempMatrix = []
#         for i in range(cluster):
#             tempMatrix.append(calDistance(x[i],x1[j],y[i],y1[j]))
#         distance_BC.append(tempMatrix)
#     return distance_BC
#
# # 聚集点与基站组合距离算出Q(21*16的矩阵)
# def genQMatrix(distance_BC):
#     base = 4
#     cluster = 21
#     Q = []
#     for k in range(base):
#         for j in range(base):
#             temp = []
#             for i in range(cluster):
#                 total_distance = []
#                 total_distance = distance_BC_array[k,i]+distance_BC_array[j,i]
#                 temp.append(total_distance)
#             Q.append(temp)
#     return Q
#
# if __name__ == '__main__':
#     distance_BC = genDistance(x,y,x1,y1)
#     distance_BC_array = np.array(distance_BC)
#     print(distance_BC)
#     Q = genQMatrix(distance_BC_array)
#     Q_array = np.array(Q)
#     print(Q)

# 加箭头
# def drawArrow1(A, B):
#     fig = plt.figure(figsize=(20, 20))
#
#     # fc: filling color
#     # ec: edge color
#     plt.figure()
#     plt.annotate("", xy=(B[0], B[1]), xytext=(A[0], A[1]), arrowprops=dict(arrowstyle="->"))
#     plt.xlim(0, 20)
#     plt.ylim(0, 20)
#     plt.grid() #网格
#     plt.show()
#
# if __name__ == '__main__':
#     a = np.array([1, 2])
#     b = np.array([3, 4])
#     drawArrow1(a, b)

# 生成La矩阵
# def genLaMatrix():
#     numberCout = 4
#     La = []
#     for i in range(0,numberCout):
#         if i == 0:
#             a = np.random.randint(16, size=4)
#             ratio = 16/sum(a)
#             a1 = a*ratio
#             La.append(a1)
#         elif i == 1:
#             a = np.random.randint(12, size=4)
#             ratio = 12 / sum(a)
#             a1 = a * ratio
#             La.append(a1)
#         elif i == 2:
#             a = np.random.randint(6, size=4)
#             ratio = 6 / sum(a)
#             a1 = a * ratio
#             La.append(a1)
#         elif i == 3:
#             a = np.random.randint(8, size=4)
#             ratio = 8 / sum(a)
#             a1 = a * ratio
#             La.append(a1)
#     return La
#
# if __name__ == '__main__':
#     La = genLaMatrix()
#     print(La)

#
def genLaMatrix(role_amount, agent_amount):
		res = []
		temp = agent_amount
		while(temp>=1):
				val = ceil(role_amount/agent_amount)
				val += random.randint(1, agent_amount)
				if val not in res:
						res.append(val)
						temp-=1
		return res

if __name__ == '__main__':
    role_amount = 43
    agent_amount = 4
    res = genLaMatrix(43,4)
    print(res)
