import math
import matplotlib.pyplot as plt
from matplotlib.ticker import MaxNLocator
from math import floor
import numpy as np
import xlrd
import pandas as pd
import pulp as pl
import pandas as pd
from math import cos, sin, acos, asin, pi, atan, fabs, ceil, floor
from collections import defaultdict
import random
import time
from gurobipy import *

#   2022.11.3
#   容量约束：本批飞入<=本批飞出+下一批飞出
#   总飞入=总飞出
#   更换基站、聚集点坐标


# 散点图
x = [1.61, 1.24, 0.98, 4.66, 6.75, 10.5, 13.81, 17.33, 12.41, 17.09, 2.73, 9.39, 12.44, 17.57, 12.54, 17.22, 8.39, 6.35, 2.28, 2.78, 7.99, 15.08]
y = [19.53, 14.76, 12.94, 15.11, 15.87, 16.48, 18.55, 17.36, 14.76, 13.79, 11.4, 8.55, 10.19, 8.39, 6.3, 3.41, 3.73, 1.06, 2.8, 6.61, 10.16, 8.88]
# x = np.random.choice(range(0, 21), 22)
# y = np.random.choice(range(0, 21), 22)
# x = [7,2,12,10,9,6,5,1,17,9,0,4,5,0,2,7,15,3,18,15,6,8]
# y = [4,7,0,9,3,15,15,1,0,10,20,12,3,2,13,16,7,11,5,2,18,15]
colors = [1,2,1,3,3,1,2,2,2,2,1,2,1,2,2,3,2,2,2,3,1,3]
sizes = [150,300,150,450,450,150,300,300,300,300,150,300,150,300,300,450,300,300,300,450,150,450]
# cluster_list = [(1.61, 19.53), (1.24, 14.76), (0.98, 12.94), (4.66, 15.11), (6.75, 15.87), (10.5, 16.48),(13.81, 18.55),(17.33, 17.36),
#                 (12.41, 14.76), (17.09, 13.79), (2.73, 11.4), (9.39, 8.55), (12.44, 10.19),(17.57, 8.39),(12.54, 6.3), (17.22, 3.41),
#                 (8.39, 3.73), (6.35, 1.06), (2.28, 2.8), (2.78, 6.61), (7.99, 10.16), (15.48,8.88)]
plt.scatter(x, y, c=colors , s = sizes, alpha = 0.5, edgecolors = 'w', label = 'Crowded Areas',cmap='copper_r')
x1 = [15.95, 16.42, 11.52, 16.77]
y1 = [6.92, 9.6, 18.78, 15.33] #10-50批次用
# base_list = [(15.95, 6.92), (16.42, 9.6), (11.52, 18.78), (16.77, 15.33)]
# x1 = [18.95, 6.42, 11.52, 16.77]
# y1 = [6.92, 9.6, 18.78, 15.33]
# base_list = [(18.95, 6.92), (6.42, 9.6), (11.52, 18.78), (16.77, 15.33)]
# x1 = np.random.choice(range(0, 21), 4)
# y1 = np.random.choice(range(0, 21), 4)
# x1 = [10,1,2,13]
# y1 = [10,16,10,10]
plt.scatter(x1, y1, color = "darkred", s = 400, alpha = 0.7, marker = '*', label = 'Base Station')
plt.xlim(0, 20, 1)
plt.ylim(0, 20, 2)
plt.gca().xaxis.set_major_locator(MaxNLocator(integer=True))   #限制坐标轴刻度为整数
plt.gca().yaxis.set_major_locator(MaxNLocator(integer=True))
plt.legend(loc='lower right', fontsize=9)
plt.title("Coordinate diagram of position distribution",fontdict={'weight':'normal','size': 15})
# plt.show()


class Assignment:
    @classmethod
    # maxDrone: the amount of the drones
    def KM(cls, Q, La, L):
        row = len(Q)
        col = len(Q[0])

        # build a optimal problem
        pro = pl.LpProblem('MIN(connection and coverage )', pl.LpMinimize)
        solver = pl.getSolver('GUROBI_CMD')

        # build variables for the optimal problem
        lpvars = [[[pl.LpVariable("x" + str(i) + "y" + str(j) + "l" + str(b), lowBound=0, upBound=1, cat='Integer') for
                    j in range(col)] for i in range(row)]for b in range(b_num)]


        # build optimal function
        all = pl.LpAffineExpression()
        for b in range(0,b_num):
            for i in range(0,row):
                for j in range(0,col):
                    all += Q[i][j] * lpvars[b][i][j]
        pro += all


        # build constraint
        ## 满足聚集点需求
        for b in range(b_num):
            for j in range(0, col):
                tempSum = 0
                for i in range(0, row):
                    tempSum += lpvars[b][i][j]
                pro += tempSum == L[j]


        # la约束
        for b in range(0, b_num):
            for i in range(0, outbase):
                tempSum = 0
                for j in range(0, col):
                    tempSum += lpvars[b][4 * i][j] + lpvars[b][4 * i + 1][j] + lpvars[b][4 * i + 2][j] + lpvars[b][4 * i + 3][j]
                pro += tempSum <= La[i]

        # 容量约束：本批飞入 <= 本批飞出 + 下批飞出
        # 除最后一批次
        for b in range(0, b_num - 1):
            for i in range(base):
                tempSumiin = 0
                tempSumiout = 0
                nextout = 0
                for j in range(col):
                    tempSumiin += lpvars[b][i][j] + lpvars[b][i + 4][j] + lpvars[b][i + 8][j] + lpvars[b][i + 12][j]  # 本批飞入
                    tempSumiout += lpvars[b][4 * i][j] + lpvars[b][4 * i + 1][j] + lpvars[b][4 * i + 2][j] + lpvars[b][4 * i + 3][j]  # 本批飞出
                    nextout += lpvars[b + 1][4 * i][j] + lpvars[b + 1][4 * i + 1][j] + lpvars[b + 1][4 * i + 2][j] + lpvars[b + 1][4 * i + 3][j]  # 下批飞出
                # for temp in range(b, b + 1):  # 计算下批
                #     for j in range(col):
                #         nextout += lpvars[b + 1][4 * i][j] + lpvars[b + 1][4 * i + 1][j] + lpvars[b + 1][4 * i + 2][j] + lpvars[b + 1][4 * i + 3][j]  # 下批飞出
                pro += tempSumiin <= tempSumiout + nextout

        # 最后一批次
        for b in range(b_num - 1, b_num):
            for i in range(base):
                lastin = 0
                lastout = 0
                for j in range(col):
                    lastin += lpvars[b][i][j] + lpvars[b][i + 4][j] + lpvars[b][i + 8][j] + lpvars[b][i + 12][j]  # 最后一批飞入
                    lastout += lpvars[b][4 * i][j] + lpvars[b][4 * i + 1][j] + lpvars[b][4 * i + 2][j] + lpvars[b][4 * i + 3][j]  # 最后一批飞出
                pro += lastin <= lastout


        # 总容量约束
        for i in range(base):
            sumout = 0
            sumin = 0
            for b in range(0, b_num):
                for j in range(col):
                    sumout += lpvars[b][4*i][j] + lpvars[b][4*i+1][j] + lpvars[b][4*i+2][j] + lpvars[b][4*i+3][j]
                    sumin += lpvars[b][i][j] + lpvars[b][i+4][j] + lpvars[b][i+8][j] + lpvars[b][i+12][j]
            pro += sumout == sumin


        # solve optimal problem
        status = pro.solve(solver)
        print("Assignment Status: ", pl.LpStatus[status])
        print("Final Assignment Result", pl.value(pro.objective))

        # get the result of T matrix
        T = [[[lpvars[b][i][j].varValue for j in range(col)] for i in range(row)] for b in range(b_num)]

        # return T
        return [T, pl.value(pro.status), pl.value(pro.objective)]

# 计算两点之间距离公式
def calDistance(x1, x2, y1, y2):
    result = math.sqrt((x2-x1)**2+(y2-y1)**2)
    return result

# 聚集点与基站组合距离（21*4的矩阵）
def genDistance(x,y,x1,y1):
    distance_BC = []
    for j in range(base):
        tempMatrix = []
        for i in range(cluster):
            tempMatrix.append(calDistance(x[i],x1[j],y[i],y1[j]))
        distance_BC.append(tempMatrix)
    return distance_BC

# 聚集点与基站组合距离算出Q(21*16的矩阵)
def genQMatrix(distance_BC):
    Q = []
    for k in range(base):
        for j in range(base):
            temp = []
            for i in range(cluster):
                total_distance = []
                total_distance = distance_BC_array[k,i]+distance_BC_array[j,i]
                temp.append(total_distance)
            Q.append(temp)
    return Q

# 归一化后的Q
def getNormalizedQ(Q):
    max = min = Q[0][0]
    row = len(Q)
    column = len(Q[0])
    for i in range(row):
        for j in range(column):
            if (Q[i, j] > max):
                max = Q[i, j]
            if (Q[i, j] < min):
                min = Q[i, j]
    for i in range(row):
        for j in range(column):
            Q[i, j] = round((Q[i, j] - min) / (max - min), 4)
    return Q

# 随机生成La
# def genLaMatrix():
#     La = []
#     for i in range(0,4):
#         a = random.randint(1,20)
#         La.append(a)
#     return La

# 随机生成La
def genLaMatrix(role_amount, agent_amount):
		La = []
		temp = agent_amount
		while(temp>=1):
				val = ceil(role_amount/agent_amount)
				val += random.randint(1, agent_amount)
				if val not in La:
						La.append(val)
						temp-=1
		return La

# 找到可行的La
# def availableLa():
#     TMAT_row = 16
#     TMAT_col = 22
#     flag = 1
#     cont = 0
#     La = []
#     if cont<1000000000000:
#         LaMatrix = genLaMatrix(43,4)
#         T = Assignment.KM(QMatrix, LaMatrix, L)
#         TMAT= np.array(T)
#         for b in range(b_num):
#             for i in range(TMAT_row):
#                 for j in range(TMAT_col):
#                     if TMAT[b][i][j] > 1 or TMAT[b][i][j] < 0:
#                         flag = 0
#                     cont += 1
#             if flag == 1:
#                 break
#         La = LaMatrix
#     return La

# 计算基站派出/派回无人机数量
def countdrones(T):
    countbase_in = [[0,0,0,0] for i in range(b_num)]
    countbase_out = [[0,0,0,0] for i in range(b_num)]
    for b in range(0, b_num):
        countin = 0
        countout = 0
        for i in range(0, base):
            for j in range(0, cluster):
                countin += T[b][i][j] + T[b][i + 4][j] + T[b][i + 8][j] + T[b][i + 12][j]  # eg:base1进基站为j=0,4,8,12
                countout += T[b][4 * i][j] + T[b][4 * i + 1][j] + T[b][4 * i + 2][j] + T[b][4 * i + 3][j]  # eg:base2出基站为j=0,1,2,3
            countbase_in[b][i] = countin
            countbase_out[b][i] = countout
            countin = 0
            countout = 0
    countbase_in = np.array(countbase_in)
    countbase_out = np.array(countbase_out)
    return countbase_in, countbase_out


# 指派路径
def drawpath(T,x,y,x1,y1):
    T_row = 16
    T_col = 22
    base1_out_x = [[] for i in range(b_num)]
    base1_out_y = [[] for i in range(b_num)]
    base1_in_x = [[] for i in range(b_num)]
    base1_in_y = [[] for i in range(b_num)]
    base2_out_x = [[] for i in range(b_num)]
    base2_out_y = [[] for i in range(b_num)]
    base2_in_x = [[] for i in range(b_num)]
    base2_in_y = [[] for i in range(b_num)]
    base3_out_x = [[] for i in range(b_num)]
    base3_out_y = [[] for i in range(b_num)]
    base3_in_x = [[] for i in range(b_num)]
    base3_in_y = [[] for i in range(b_num)]
    base4_out_x = [[] for i in range(b_num)]
    base4_out_y = [[] for i in range(b_num)]
    base4_in_x = [[] for i in range(b_num)]
    base4_in_y = [[] for i in range(b_num)]

    # out
    for b in range(b_num):
        for i in range(T_row):
            outbase = math.floor(i / 4) + 1
            for j in range(T_col):
                if T[b][i][j] == 1 and outbase == 1:
                    base1_out_x[b].append((x1[0], x[j]))
                    base1_out_y[b].append((y1[0], y[j]))
                elif T[b][i][j] == 1 and outbase == 2:
                    base2_out_x[b].append((x1[1], x[j]))
                    base2_out_y[b].append((y1[1], y[j]))
                elif T[b][i][j] == 1 and outbase == 3:
                    base3_out_x[b].append((x1[2], x[j]))
                    base3_out_y[b].append((y1[2], y[j]))
                elif T[b][i][j] == 1 and outbase == 4:
                    base4_out_x[b].append((x1[3], x[j]))
                    base4_out_y[b].append((y1[3], y[j]))

    # in
    for b in range(b_num):
        for i in range(T_row):
            inbase = i - math.floor(i / 4) * 4 + 1
            for j in range(T_col):
                if T[b][i][j] == 1 and inbase == 1:
                    base1_in_x[b].append((x[j], x1[0]))
                    base1_in_y[b].append((y[j], y1[0]))
                elif T[b][i][j] == 1 and inbase == 2:
                    base2_in_x[b].append((x[j], x1[1]))
                    base2_in_y[b].append((y[j], y1[1]))
                elif T[b][i][j] == 1 and inbase == 3:
                    base3_in_x[b].append((x[j], x1[2]))
                    base3_in_y[b].append((y[j], y1[2]))
                elif T[b][i][j] == 1 and inbase == 4:
                    base4_in_x[b].append((x[j], x1[3]))
                    base4_in_y[b].append((y[j], y1[3]))

    base1_out_x = np.array(base1_out_x,dtype=object)
    base1_out_y = np.array(base1_out_y,dtype=object)
    base1_in_x = np.array(base1_in_x,dtype=object)
    base1_in_y = np.array(base1_in_y,dtype=object)
    base2_out_x = np.array(base2_out_x,dtype=object)
    base2_out_y = np.array(base2_out_y,dtype=object)
    base2_in_x = np.array(base2_in_x,dtype=object)
    base2_in_y = np.array(base2_in_y,dtype=object)
    base3_out_x = np.array(base3_out_x,dtype=object)
    base3_out_y = np.array(base3_out_y,dtype=object)
    base3_in_x = np.array(base3_in_x,dtype=object)
    base3_in_y = np.array(base3_in_y,dtype=object)
    base4_out_x = np.array(base4_out_x,dtype=object)
    base4_out_y = np.array(base4_out_y,dtype=object)
    base4_in_x = np.array(base4_in_x,dtype=object)
    base4_in_y = np.array(base4_in_y,dtype=object)

    return base1_out_x,base1_out_y,base2_out_x,base2_out_y,base3_out_x,base3_out_y,base4_out_x,base4_out_y,\
           base1_in_x,base1_in_y,base2_in_x,base2_in_y,base3_in_x,base3_in_y,base4_in_x,base4_in_y


if __name__ == '__main__':

    b_num = 2
    base = 4
    outbase = 4
    inbase = 4
    cluster = 22
    combination = 16
    ftcharge = 0.75 # 充电45分钟
    fv = 50  # 飞行速度50km/h
    Qmax = ftcharge*fv
    L = [1,2,1,3,3,1,2,2,2,2,1,2,1,2,2,3,2,2,2,3,1,3]   # total 43
    capacity = [33, 21, 39, 54]   # 基站初始容量均为48, total 192
    # Latotal = [16,16,16,16]
    # La = [11,7,13,18]
    # La = [17, 12, 10, 14]

    # La = [4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4]  # 每批最多16,共16种基站组合

    # La = genLaMatrix(43,4) # 个数
    La = [15, 13, 14, 12]

    distance_BC = genDistance(x, y, x1, y1)
    distance_BC_array = np.array(distance_BC)
    print(distance_BC)
    Q = genQMatrix(distance_BC_array)
    Q = np.array(Q)
    QMatrix = np.array(Q)

    # 对超过续航时间的Q[i][j]赋为255
    for i in range(0,len(QMatrix)):
        for j in range(0,len(QMatrix[0])):
            if QMatrix[i][j] >= Qmax:
                QMatrix[i][j] = 255

    QMatrix = getNormalizedQ(QMatrix)
    print(QMatrix)

    # role_amount = 43
    # agent_amount = 4
    # La = availableLa()

    #   总运行时间
    start_3 = time.perf_counter()

    #   一次运行结果
    start_1 = time.perf_counter()
    T1, status, p = Assignment.KM(QMatrix, La, L)
    total_1 = time.perf_counter() - start_1
    TMAT = np.array(T1)
    print(TMAT)


    # 记录多次运行结果
    fre = [[]for t in range(b_num)] #重复运行次数
    divsum = [[[]for j in range(2)]for i in range(len(fre))] #记录多次运行的结果
    temp = 0
    tempb_num = b_num
    for b in range(0, tempb_num):
        start_2 = 0
        total_2 = 0
        if tempb_num % (b+1) != 0:
            divsum[b][0] = 0
            divsum[b][1] = 0
        if tempb_num % (b+1) == 0:
            fre[b] = int(tempb_num / (b + 1))
            divsum[b][0] = fre[b]
            temp = fre[b]
            start_2 = time.perf_counter()
            for t in range(0, temp):
                b_num = b + 1
                T, status, p = Assignment.KM(QMatrix, La, L)
            total_2 = time.perf_counter() - start_2
            divsum[b][1] = total_2

    divsum = np.array(divsum)
    divsum = divsum[divsum.sum(axis=1) != 0, :] #去除全0行

    total_3 = time.perf_counter() - start_3




    countbase_in,countbase_out = countdrones(TMAT)
    print(countbase_in)
    print(countbase_out)


    # 指派路径图
    # base1_out_x, base1_out_y, base2_out_x, base2_out_y, base3_out_x, base3_out_y, base4_out_x, base4_out_y, \
    # base1_in_x, base1_in_y, base2_in_x, base2_in_y, base3_in_x, base3_in_y, base4_in_x, base4_in_y = drawpath(TMAT,x,y,x1,y1)
    # print(base1_out_x)
    # print(base1_out_y)
    # print(base1_in_x)
    # print(base1_in_y)
    # print(base2_out_x)
    # print(base2_out_y)
    # print(base2_in_x)
    # print(base2_in_y)
    # print(base3_out_x)
    # print(base3_out_y)
    # print(base3_in_x)
    # print(base3_in_y)
    # print(base4_out_x)
    # print(base4_out_y)
    # print(base4_in_x)
    # print(base4_in_y)
    #
    # sizes = [100, 200, 100, 300, 300, 100, 200, 200, 200, 200, 100, 200, 100, 200, 200, 300, 200, 200, 200, 300, 100,
    #          300]
    # # out
    # for b in range(b_num):
    #     plt.figure()
    #     x = [1.61, 1.24, 0.98, 4.66, 6.75, 10.5, 13.81, 17.33, 12.41, 17.09, 2.73, 9.39, 12.44, 17.57, 12.54, 17.22,
    #          8.39,
    #          6.35, 2.28, 2.78, 7.99, 15.08]
    #     y = [19.53, 14.76, 12.94, 15.11, 15.87, 16.48, 18.55, 17.36, 14.76, 13.79, 11.4, 8.55, 10.19, 8.39, 6.3, 3.41,
    #          3.73,
    #          1.06, 2.8, 6.61, 10.16, 8.88]
    #     colors = [1, 2, 1, 3, 3, 1, 2, 2, 2, 2, 1, 2, 1, 2, 2, 3, 2, 2, 2, 3, 1, 3]
    #     sizes = [150, 300, 150, 450, 450, 150, 300, 300, 300, 300, 150, 300, 150, 300, 300, 450, 300, 300, 300, 450,
    #              150, 450]
    #     cluster_list = [(1.61, 19.53), (1.24, 14.76), (0.98, 12.94), (4.66, 15.11), (6.75, 15.87), (10.5, 16.48),
    #                     (13.81, 18.55), (17.33, 17.36),
    #                     (12.41, 14.76), (17.09, 13.79), (2.73, 11.4), (9.39, 8.55), (12.44, 10.19), (17.57, 8.39),
    #                     (12.54, 6.3), (17.22, 3.41),
    #                     (8.39, 3.73), (6.35, 1.06), (2.28, 2.8), (2.78, 6.61), (7.99, 10.16), (15.48, 8.88)]
    #     plt.scatter(x, y, c=colors, s=sizes, alpha=0.5, edgecolors='w', label = 'Crowded Areas', cmap='copper_r')
    #     plt.legend(loc='lower right', fontsize=9)
    #     x1 = [18.95, 6.42, 11.52, 16.77]
    #     y1 = [6.92, 9.6, 18.78, 15.33]
    #     base_list = [(18.95, 6.92), (6.42, 9.6), (11.52, 18.78), (16.77, 15.33)]
    #     plt.scatter(x1, y1, color = "darkred", s = 400, alpha=0.7, label = 'Base Station', marker='*')
    #     plt.legend(loc='lower right', fontsize=9)
    #     for i in range(len(base1_out_x[b])):
    #         plt.plot(base1_out_x[b][i], base1_out_y[b][i], linewidth='1', color='cadetblue', label = 'dispatch roadmap')
    #     for i in range(len(base2_out_x[b])):
    #         plt.plot(base2_out_x[b][i], base2_out_y[b][i], linewidth='1', color='cadetblue', label = 'dispatch roadmap')
    #     for i in range(len(base3_out_x[b])):
    #         plt.plot(base3_out_x[b][i], base3_out_y[b][i], linewidth='1', color='cadetblue', label = 'dispatch roadmap')
    #     for i in range(len(base4_out_x[b])):
    #         plt.plot(base4_out_x[b][i], base4_out_y[b][i], linewidth='1', color='cadetblue', label = 'dispatch roadmap')
    #     plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
    #     c = b + 1
    #     plt.title('Roadmap for the dispatch of the %sth batch'%c,fontdict={'weight':'normal','size': 15})
    #     plt.xlim(0, 20, 1)
    #     plt.ylim(0, 20, 2)
    #     plt.gca().xaxis.set_major_locator(MaxNLocator(integer=True))  # 限制坐标轴刻度为整数
    #     plt.gca().yaxis.set_major_locator(MaxNLocator(integer=True))
    #     plt.show()
    #     c = 0
    #
    #
    # # in
    # for b in range(b_num):
    #     plt.figure()
    #     x = [1.61, 1.24, 0.98, 4.66, 6.75, 10.5, 13.81, 17.33, 12.41, 17.09, 2.73, 9.39, 12.44, 17.57, 12.54, 17.22,
    #          8.39,
    #          6.35, 2.28, 2.78, 7.99, 15.08]
    #     y = [19.53, 14.76, 12.94, 15.11, 15.87, 16.48, 18.55, 17.36, 14.76, 13.79, 11.4, 8.55, 10.19, 8.39, 6.3, 3.41,
    #          3.73,
    #          1.06, 2.8, 6.61, 10.16, 8.88]
    #     colors = [1, 2, 1, 3, 3, 1, 2, 2, 2, 2, 1, 2, 1, 2, 2, 3, 2, 2, 2, 3, 1, 3]
    #     sizes = [150, 300, 150, 450, 450, 150, 300, 300, 300, 300, 150, 300, 150, 300, 300, 450, 300, 300, 300, 450,
    #              150, 450]
    #     cluster_list = [(1.61, 19.53), (1.24, 14.76), (0.98, 12.94), (4.66, 15.11), (6.75, 15.87), (10.5, 16.48),
    #                     (13.81, 18.55), (17.33, 17.36),
    #                     (12.41, 14.76), (17.09, 13.79), (2.73, 11.4), (9.39, 8.55), (12.44, 10.19), (17.57, 8.39),
    #                     (12.54, 6.3), (17.22, 3.41),
    #                     (8.39, 3.73), (6.35, 1.06), (2.28, 2.8), (2.78, 6.61), (7.99, 10.16), (15.48, 8.88)]
    #     plt.scatter(x, y, c=colors, s=sizes, alpha=0.5, edgecolors='w', label = 'Crowded Areas', cmap='copper_r')
    #     plt.legend(loc='lower right', fontsize=9)
    #     x1 = [18.95, 6.42, 11.52, 16.77]
    #     y1 = [6.92, 9.6, 18.78, 15.33]
    #     base_list = [(18.95, 6.92), (6.42, 9.6), (11.52, 18.78), (16.77, 15.33)]
    #     plt.scatter(x1, y1, color="darkred", s=400, alpha=0.7, label = 'Base Station', marker='*')
    #     plt.legend(loc='lower right', fontsize=9)
    #     for i in range(len(base1_in_x[b])):
    #         plt.plot(base1_in_x[b][i], base1_in_y[b][i], linewidth='1', color='cadetblue', linestyle = "-.")
    #     for i in range(len(base2_in_x[b])):
    #         plt.plot(base2_in_x[b][i], base2_in_y[b][i], linewidth='1', color='cadetblue', linestyle = "-.")
    #     for i in range(len(base3_in_x[b])):
    #         plt.plot(base3_in_x[b][i], base3_in_y[b][i], linewidth='1', color='cadetblue', linestyle = "-.")
    #     for i in range(len(base4_in_x[b])):
    #         plt.plot(base4_in_x[b][i], base4_in_y[b][i], linewidth='1', color='cadetblue', linestyle = "-.")
    #     plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
    #     c = b + 1
    #     plt.title('Roadmap for the return of the %sth batch'%c,fontdict={'weight':'normal','size': 15})
    #     plt.xlim(0, 20, 1)
    #     plt.ylim(0, 20, 2)
    #     plt.gca().xaxis.set_major_locator(MaxNLocator(integer=True))  # 限制坐标轴刻度为整数
    #     plt.gca().yaxis.set_major_locator(MaxNLocator(integer=True))
    #
    #     plt.show()
    #     c = 0

    # 计算路径总距离
    condistance = []
    for b in range(b_num):
        condis = 0
        for i in range(base):
            for j in range(cluster):
                if T1[b][i][j] == 1:
                    condis += Q[i][j]
        condistance.append(condis)
    print(condistance)
    condistance_total = sum(condistance)
    print('总航迹', condistance_total)

    # print('运行时间', total)
    print('一次性运行时间:', total_1)
    # # print('分次运行时间：', total_2)
    print('总运行时间:', total_3)

    # 计算总批次的总派出和派入
    totalin = [0 for i in range(base)]
    totalout = [0 for i in range(base)]
    for i in range(base):
        tempcontin = 0
        tempcontout = 0
        for b in range(b_num):
            tempcontin += countbase_in[b][i]
            tempcontout += countbase_out[b][i]
        totalin[i] = tempcontin
        totalout[i] = tempcontout
    print('各基站总派出无人机数:', totalout)
    print('各基站总派回无人机数:', totalin)

    sumtotalin = 0
    sumtotalout = 0
    for i in range(base):
        sumtotalin += totalin[i]
        sumtotalout += totalout[i]
    print('批次总返回数', sumtotalin)
    print('批次总派出数', sumtotalout)















