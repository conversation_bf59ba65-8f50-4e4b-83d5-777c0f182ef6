import math
import matplotlib.pyplot as plt
from matplotlib.ticker import MaxNLocator
from math import floor
import numpy as np
import xlrd
import pandas as pd
import pulp as pl
import pandas as pd
from math import cos, sin, acos, asin, pi, atan, fabs, ceil, floor
from collections import defaultdict
import random
import time
from gurobipy import *
import openpyxl

#   2022.11.19 增大规模role/agent

class Assignment:
    @classmethod
    def relaytask(cls, Q, La, L, b_num):
        row = len(Q)
        col = len(Q[0])

        # build a optimal problem
        pro = pl.LpProblem('MIN(connection and coverage )', pl.LpMinimize)
        solver = pl.getSolver('GUROBI_CMD')

        # build variables for the optimal problem
        lpvars = [[[pl.LpVariable("x" + str(i) + "y" + str(j) + "l" + str(b), lowBound=0, upBound=1, cat='Integer') for
                    j in range(col)] for i in range(row)]for b in range(b_num)]

        # build optimal function
        all = pl.LpAffineExpression()
        for b in range(0,b_num):
            for i in range(0,row):
                for j in range(0,col):
                    all += Q[i][j] * lpvars[b][i][j]
        pro += all

        # build constraint
        ## 满足聚集点需求
        for b in range(b_num):
            for j in range(0, col):
                tempSum = 0
                for i in range(0, row):
                    tempSum += lpvars[b][i][j]
                pro += tempSum == L[j]

        # la约束
        for b in range(0, b_num):
            for i in range(0, base):
                tempSum = 0
                for j in range(0, col):
                    tempSum += lpvars[b][4 * i][j] + lpvars[b][4 * i + 1][j] + lpvars[b][4 * i + 2][j] + lpvars[b][4 * i + 3][j]
                pro += tempSum <= La[i]

            #   容量约束 (考虑基站本身有的无人机数量)
            for b in range(0, b_num - 1):
                for i in range(base):
                    sumout = 0
                    sumin = 0
                    sumoutnext = lpvars[b + 1][4 * i][j] + lpvars[b + 1][4 * i + 1][j] + lpvars[b + 1][4 * i + 2][j] + \
                                 lpvars[b + 1][4 * i + 3][j]  # 下一次飞出
                    tempsum = 0
                    for temp in range(0, b + 1):
                        for j in range(col):
                            sumin += lpvars[temp][i][j] + lpvars[temp][i + 4][j] + lpvars[temp][i + 8][j] + \
                                     lpvars[temp][i + 12][j]  # k次飞进
                            sumout += lpvars[temp][4 * i][j] + lpvars[temp][4 * i + 1][j] + lpvars[temp][4 * i + 2][j] + \
                                      lpvars[temp][4 * i + 3][j]  # k次飞出
                    tempsum = eUAV[i] + sumin - sumout
                    pro += sumoutnext <= tempsum

            # 总容量约束
            for i in range(base):
                sumout = 0
                sumin = 0
                for b in range(0, b_num):
                    for j in range(col):
                        sumout += lpvars[b][4 * i][j] + lpvars[b][4 * i + 1][j] + lpvars[b][4 * i + 2][j] + \
                                  lpvars[b][4 * i + 3][j]
                        sumin += lpvars[b][i][j] + lpvars[b][i + 4][j] + lpvars[b][i + 8][j] + lpvars[b][i + 12][j]
                pro += sumout == sumin

        # solve optimal problem
        status = pro.solve(solver)
        print("Assignment Status: ", pl.LpStatus[status])
        print("Final Assignment Result", pl.value(pro.objective))

        # get the result of T matrix
        T = [[[lpvars[b][i][j].varValue for j in range(col)] for i in range(row)] for b in range(b_num)]
        return T

# 计算两点之间距离公式
def calDistance(x1, x2, y1, y2):
    result = math.sqrt((x2-x1)**2+(y2-y1)**2)
    return result

# 聚集点与基站组合距离（21*4的矩阵）
def genDistance(x,y,x1,y1):
    distance_BC = []
    for j in range(base):
        tempMatrix = []
        for i in range(cluster):
            tempMatrix.append(calDistance(x[i],x1[j],y[i],y1[j]))
        distance_BC.append(tempMatrix)
    return distance_BC

# 聚集点与基站组合距离算出Q(21*16的矩阵)
def genQMatrix(distance_BC):
    Q = []
    for k in range(base):
        for j in range(base):
            temp = []
            for i in range(cluster):
                total_distance = []
                total_distance = distance_BC[k,i]+distance_BC[j,i]
                temp.append(total_distance)
            Q.append(temp)
    return Q

# 归一化后的Q
def getNormalizedQ(Q):
    max = min = Q[0][0]
    row = len(Q)
    column = len(Q[0])
    for i in range(row):
        for j in range(column):
            if (Q[i, j] > max):
                max = Q[i, j]
            if (Q[i, j] < min):
                min = Q[i, j]
    for i in range(row):
        for j in range(column):
            Q[i, j] = round((Q[i, j] - min) / (max - min), 4)
    return Q

# 随机生成La
def genLaMatrix(role_amount, agent_amount):
		La = []
		temp = agent_amount
		while(temp>=1):
				val = ceil(role_amount/agent_amount)
				val += random.randint(1, agent_amount)
				if val not in La:
						La.append(val)
						temp-=1
		return La

# 计算基站派出/派回无人机数量
def countdrones(T):
    countbase_in = [[0,0,0,0] for i in range(b_num)]
    countbase_out = [[0,0,0,0] for i in range(b_num)]
    for b in range(0, b_num):
        countin = 0
        countout = 0
        for i in range(0, base):
            for j in range(0, cluster):
                countin += T[b][i][j] + T[b][i + 4][j] + T[b][i + 8][j] + T[b][i + 12][j]  # eg:base1进基站为j=0,4,8,12
                countout += T[b][4 * i][j] + T[b][4 * i + 1][j] + T[b][4 * i + 2][j] + T[b][4 * i + 3][j]  # eg:base2出基站为j=0,1,2,3
            countbase_in[b][i] = countin
            countbase_out[b][i] = countout
            countin = 0
            countout = 0
    countbase_in = np.array(countbase_in)
    countbase_out = np.array(countbase_out)
    return countbase_in, countbase_out

#   Q矩阵汇总
def gennewQmatrix(x,y,x1,y1):
    distance_BC = genDistance(x, y, x1, y1)
    distance_BC_array = np.array(distance_BC)

    Q = genQMatrix(distance_BC_array)
    Q = np.array(Q)
    QMatrix = np.array(Q)

    # 对超过续航时间的Q[i][j]赋为255
    for i in range(0,len(QMatrix)):
        for j in range(0,len(QMatrix[0])):
            if QMatrix[i][j] >= Qmax:
                QMatrix[i][j] = 255

    QMatrix = getNormalizedQ(QMatrix)
    return Q

#   总航迹
def contnewpath(T, b_num):
    condistance = []
    for b in range(b_num):
        condis = 0
        for i in range(combination):
            for j in range(cluster):
                if T[b][i][j] == 1:
                    condis += Q[i][j]
        condistance.append(condis)
    condistance_total = sum(condistance)
    return condistance_total

#  随机生成cluster
def gencluster(cluster):
    A = 0
    B = 21
    # cluster = 50
    x = np.random.choice(range(A, B), cluster)
    y = np.random.choice(range(A, B), cluster)
    # print(x)
    # print(y)
    dense = np.random.choice(range(1, 4), cluster)
    # print(dense)
    return x,y,dense


if __name__ == '__main__':

    base = 4
    cluster = 10
    combination = 16
    ftcharge = 0.75 # 充电45分钟
    fv = 50  # 飞行速度50km/h
    Qmax = ftcharge*fv

    dense = np.random.choice(range(1, 4), cluster)
    L = dense
    sumL = sum(L)

    La = genLaMatrix(sumL,base) # 个数
    La = np.array(La)
    capacity = La * 3
    eUAV = capacity / 3

    #   一次运行+多次运行
    scale = 1
    large_time_total = [[] for t in range(scale)]
    large_path_total = [[] for t in range(scale)]
    large_time_div_100 = [[] for t in range(scale)]
    large_path_div_100 = [[] for t in range(scale)]

    for t in range(scale):
        x = np.random.choice(range(0, 21), cluster)  # 聚集点坐标
        y = np.random.choice(range(0, 21), cluster)
        x1 = np.random.choice(range(0, 21), base)  # 基站坐标
        y1 = np.random.choice(range(0, 21), base)
        Q = gennewQmatrix(x, y, x1, y1)

        start_t = time.perf_counter()  # 一次运行
        b_num = 100
        T = Assignment.relaytask(Q, La, L, b_num)
        total_t = time.perf_counter() - start_t
        large_time_total[t] = total_t
        large_path_total[t] = contnewpath(T, b_num)

        start_d = time.perf_counter()  # 多次运行
        temp = 0
        fre7 = 10
        temp_b7 = 10
        for i in range(fre7):
            T7 = Assignment.relaytask(Q, La, L, temp_b7)
            temp += contnewpath(T7, temp_b7)
        total_d = time.perf_counter() - start_d
        large_time_div_100[t] = total_d
        large_path_div_100[t] = temp
        print('执行次数:', t)


    large_time_total = np.array(large_time_total)
    large_path_total = np.array(large_path_total)
    large_time_div_100 = np.array(large_time_div_100)
    large_path_div_100 = np.array(large_path_div_100)

















