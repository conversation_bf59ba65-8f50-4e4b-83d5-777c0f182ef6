import matplotlib.pyplot as plt

# 第二次指派后聚集点与基站路径

base11_x = [[9.39, 18.95], [2.28, 18.95], [17.57, 18.95], [12.54, 18.95], [17.22, 18.95], [8.39, 18.95], [6.35, 18.95]]
base11_y = [[8.55, 6.92], [2.8, 6.92], [8.39, 6.92], [6.3, 6.92], [3.41, 6.92], [3.73, 6.92], [1.06, 6.92]]

for i in range(len(base11_x)):
    plt.plot(base11_x[i], base11_y[i], linewidth='1', label='base1', color='g')
    plt.scatter(base11_x[i], base11_y[i], color='b', marker='*')
    plt.scatter([18.95, ], [6.92, ], s=150, color='r', marker='*')


base22_x = [[1.24, 6.42], [0.98, 6.42], [4.66, 6.42], [6.75, 6.42], [2.73, 6.42], [9.39, 6.42], [12.44, 6.42], [12.54, 6.42], [17.22, 6.42], [8.39, 6.42], [6.35, 6.42], [2.28, 6.42], [2.78, 6.42], [7.99, 6.42]]
base22_y = [[14.76, 9.6], [12.94, 9.6], [15.11, 9.6], [15.87, 9.6], [11.4, 9.6], [8.55, 9.6], [10.19, 9.6], [6.3, 9.6], [3.41, 9.6], [3.73, 9.6], [1.06, 9.6], [2.8, 9.6], [6.61, 9.6], [10.16, 9.6]]

for i in range(len(base22_x)):
    plt.plot(base22_x[i], base22_y[i], linewidth='1', label='base2', color='b')
    plt.scatter(base22_x[i], base22_y[i], color='b', marker='*')
    plt.scatter([6.42, ], [9.6, ], s=150, color='r', marker='*')

base33_x = [[1.61, 11.52], [1.24, 11.52], [4.66, 11.52], [6.75, 11.52], [10.5, 11.52], [13.81, 11.52], [17.33, 11.52], [12.41, 11.52], [17.09, 11.52], [2.78, 11.52]]
base33_y = [[19.53,18.78], [14.76, 18.78], [15.11, 18.78], [15.87, 18.78], [16.48, 18.78], [18.55, 18.78], [17.36, 18.78], [14.76, 18.78], [14.79, 18.78], [6.61, 18.78]]

for i in range(len(base33_x)):
    plt.plot(base33_x[i], base33_y[i], linewidth='1', label='base3', color='c')
    plt.scatter(base33_x[i], base33_y[i], color='b', marker='*')
    plt.scatter([11.52, ], [18.78, ], s=150, color='r', marker='*')


base44_x = [[4.66, 16.77], [6.75, 16.77], [13.81, 16.77], [17.33, 16.77], [12.41, 16.77], [17.09, 16.77], [17.57, 16.77], [17.22, 16.77], [2.78, 16.77]]
base44_y = [[15.11, 15.33], [15.87, 15.33], [18.55, 15.33], [17.36, 15.33], [14.76, 15.33], [14.79, 15.33], [8.39, 15.33], [3.41, 15.33], [6.61, 15.33]]

for i in range(len(base44_x)):
    plt.plot(base44_x[i], base44_y[i], linewidth='1', label='base4', color='y')
    plt.scatter(base44_x[i], base44_y[i], color='b', marker='*')
    plt.scatter([16.77, ], [15.33, ], s=150, color='r', marker='*')

plt.rcParams['font.sans-serif']=['SimHei'] #用来正常显示中文标签
plt.title('第二次指派')
plt.show()