import math
import matplotlib.pyplot as plt
from matplotlib.ticker import MaxNLocator
from math import floor
import numpy as np
import xlrd
import pandas as pd
import pulp as pl
import pandas as pd
from math import cos, sin, acos, asin, pi, atan, fabs, ceil, floor
from collections import defaultdict
import random
import time

# 散点图
x = [1.61, 1.24, 0.98, 4.66, 6.75, 10.5, 13.81, 17.33, 12.41, 17.09, 2.73, 9.39, 12.44, 17.57, 12.54, 17.22, 8.39, 6.35, 2.28, 2.78, 7.99]
y = [19.53, 14.76, 12.94, 15.11, 15.87, 16.48, 18.55, 17.36, 14.76, 14.79, 11.4, 8.55, 10.19, 8.39, 6.3, 3.41, 3.73, 1.06, 2.8, 6.61, 10.16]
cluster_list = [(1.61, 19.53), (1.24, 14.76), (0.98, 12.94), (4.66, 15.11), (6.75, 15.87), (10.5, 16.48),(13.81, 18.55),(17.33, 17.36),
                (12.41, 14.76), (17.09, 14.79), (2.73, 11.4), (9.39, 8.55), (12.44, 10.19),(17.57, 8.39),(12.54, 6.3), (17.22, 3.41),
                (8.39, 3.73), (6.35, 1.06), (2.28, 2.8), (2.78, 6.61), (7.99, 10.16)]
plt.scatter(x, y, color = "g", s = 20, marker = 'x')
x1 = [18.95, 6.42, 11.52, 16.77]
y1 = [6.92, 9.6, 18.78, 15.33]
base_list = [(18.95, 6.92), (6.42, 9.6), (11.52, 18.78), (16.77, 15.33)]
plt.scatter(x1, y1, color = "red", s = 20, marker = 'x')
plt.xlim(0, 20, 1)
plt.ylim(0, 20, 2)
plt.gca().xaxis.set_major_locator(MaxNLocator(integer=True))   #限制坐标轴刻度为整数
plt.gca().yaxis.set_major_locator(MaxNLocator(integer=True))
plt.show()

# 指派v1.5 (fei)
# 改成四维 增加批次约束

class Assignment:
    @classmethod
    # maxDrone: the amount of the drones
    def KM(cls, Q, La, L, outbase, inbase, batch):
        row = len(Q)
        col = len(Q[0])
        batch = 2

        # ij：基站  k：聚集点  b:批次
        # build a optimal problem
        pro = pl.LpProblem('Max(connection and coverage )', pl.LpMinimize)
        # build variables for the optimal problem
        lpvars = [[[pl.LpVariable("x" + str(i) + "y" + str(j) + "l" + str(k), lowBound=0, upBound=1, cat='Integer') for
                    k in range(batch)] for j in range(col)]for i in range(row)]
        # x_vars = [[pl.LpVariable("a" + str(a) + "b" + str(b), 0, 1, pl.LpBinary) for b in range(inbase)] for a in range(len(outbase))]

        # build optimal function v1.0
        # all = pl.LpAffineExpression()
        # for i in range(0, row):
        #     for j in range(0, col):
        #         for k in range(0,batch):
        #             all += Q[i][j] * lpvars[i][j][k]
        #
        # pro += all

        # build optimal function v1.1
        all = pl.LpAffineExpression()
        for k in range(0,batch):
            for i in range(0, row):
                for j in range(0, col):
                    all += Q[i][j] * lpvars[i][j][k]
        pro += all

        # build constraint for each role
        # La约束 v1.0
        # ASum = 0
        # for k in range(batch):
        #     for i in range(outbase):
        #         for j in range(col):
        #             ASum += lpvars[4*i][j][k] + lpvars[4*i+1][j][k] + lpvars[4*i+2][j][k] + lpvars[4*i+3][j][k]  # 基站该批次派出总和
        #         # pro += ASum <= La[k][4*i] + La[k][4*i+1] + La[k][4*i+2] + La[k][4*i+3]  # 小于基站该批次可派出的最大数量
        #         pro += ASum <= La[4*i] + La[4*i+1] + La[4*i+2] + La[4*i + 3]
        #         ASum = 0

        # La约束  v1.1
        ASum = 0
        for i in range(outbase):
            for j in range(col):
                for k in range(batch):
                    ASum += lpvars[4*i][j][k] + lpvars[4*i + 1][j][k] + lpvars[4*i + 2][j][k] + lpvars[4*i + 3][j][k]  # 基站该批次派出总和
                # pro += ASum <= La[k][4*i] + La[k][4*i+1] + La[k][4*i+2] + La[k][4*i+3]  # 小于基站该批次可派出的最大数量
                pro += ASum <= 16
                ASum = 0

        # L约束  v1.0
        RSum = 0
        for k in range(batch):
            for j in range(col):
                for i in range(row):
                    RSum += lpvars[i][j][k]
                pro += RSum == L[j]  # 满足聚集点需求
                RSum = 0

        # L约束  v1.1
        # Rsum = 0
        # for j in range(col):
        #     for i in range(row):
        #         for k in range(batch):
        #             RSum += lpvars[i][j][k]
        #         pro += RSum == L[j]  # 满足聚集点需求
        #         RSum = 0


        # 基站容量约束v1.2 算指派矩阵行和
        count_in = 0
        count_out = 0
        count_outpre = 0
        base_number = 4
        for k in range(batch):
            for i in range(outbase):
                for j in range(col):
                    count_in += lpvars[i][j][k] + lpvars[i+4][j][k] + lpvars[i+8][j][k] + lpvars[i+12][j][k]  # eg:base1进基站为j=0,4,8,12
                    count_out += lpvars[4*i][j][k] + lpvars[4*i+1][j][k] + lpvars[4*i+2][j][k] + lpvars[4*i+3][j][k]  # eg:base2出基站为j=0,1,2,3
                    count_outpre += lpvars[4*i][j][k-1] + lpvars[4*i+1][j][k-1] + lpvars[4*i+2][j][k-1] + lpvars[4*i+3][j][k-1]
                pro += count_in == count_out + count_outpre
                count_in = 0
                count_out = 0
                count_outpre = 0

        # solve optimal problem
        status = pro.solve()
        # print("Assignment Status: ", pl.LpStatus[status])
        # print("Final Assignment Result", pl.value(pro.objective))

        # get the result of T matrix
        T = [[[lpvars[i][j][k].varValue for k in range(batch)] for j in range(col)] for i in range(row)]

        return T
    # return T, pl.value(pro.status), pl.value(pro.objective)


# 生成La矩阵
def genLaMatrix():
    numberCout = 4
    La = []
    for i in range(0,numberCout):
        if i == 0:     #16
            a = np.random.randint(4)
            La.append(a+3)
            b = np.random.randint(4-a)
            La.append(b+3)
            c = np.random.randint(4 - a - b)
            La.append(c+3)
            d = 4 - a - b - c + 3
            La.append(d)
        elif i == 1:    #16
            a = np.random.randint(4)
            La.append(a+3)
            b = np.random.randint(4 - a)
            La.append(b+3)
            c = np.random.randint(4 - a - b)
            La.append(c+3)
            d = 4 - a - b - c + 3
            La.append(d)
        elif i == 2:    #16
            a = np.random.randint(4)
            La.append(a+3)
            b = np.random.randint(4 - a)
            La.append(b+3)
            c = np.random.randint(4 - a - b)
            La.append(c+3)
            d = 4 - a - b - c + 3
            La.append(d)
        elif i == 3:   #22
            a = np.random.randint(4)
            La.append(a+3)
            b = np.random.randint(4 - a)
            La.append(b+3)
            c = np.random.randint(4 - a - b)
            La.append(c+3)
            d = 4 - a - b - c + 3
            La.append(d)
    return La

# 计算两点之间距离公式
def calDistance(x1, x2, y1, y2):
    result = math.sqrt((x2-x1)**2+(y2-y1)**2)
    return result

# 聚集点与基站组合距离（21*4的矩阵）
def genDistance(x,y,x1,y1):
    distance_BC = []
    for j in range(base):
        tempMatrix = []
        for i in range(cluster):
            tempMatrix.append(calDistance(x[i],x1[j],y[i],y1[j]))
        distance_BC.append(tempMatrix)
    return distance_BC

# 聚集点与基站组合距离算出Q(21*16的矩阵)
def genQMatrix(distance_BC):
    Q = []
    for k in range(base):
        for j in range(base):
            temp = []
            for i in range(cluster):
                total_distance = []
                total_distance = distance_BC_array[k,i]+distance_BC_array[j,i]
                temp.append(total_distance)
            Q.append(temp)
    return Q

# 归一化后的Q
def getNormalizedQ(Q):
    max = min = Q[0][0]
    row = len(Q)
    column = len(Q[0])
    for i in range(row):
        for j in range(column):
            if (Q[i, j] > max):
                max = Q[i, j]
            if (Q[i, j] < min):
                min = Q[i, j]
    for i in range(row):
        for j in range(column):
            Q[i, j] = round((Q[i, j] - min) / (max - min), 4)
    return Q

# 计算基站派出/派回无人机数量
def countdrones(T):
    base_number = 4
    col = 21
    countbase_in = [0,0,0,0]
    countbase_out = [0,0,0,0]
    for i in range(base_number):
        for j in range(col):
            countbase_in[i] += T[i][j] + T[i + 4][j] + T[i + 8][j] + T[i + 12][j]  # eg:base1进基站为j=0,4,8,12
            countbase_out[i] += T[4*i][j] + T[4*i + 1][j] + T[4*i + 2][j] + T[4*i + 3][j]  # eg:base2出基站为j=0,1,2,3
    return countbase_in,countbase_out

# 指派路径
def drawpath(T,x,y,x1,y1):
    T_row = 16
    T_col = 21
    base1_out_x = []
    base1_out_y = []
    base1_in_x = []
    base1_in_y = []
    base2_out_x = []
    base2_out_y = []
    base2_in_x = []
    base2_in_y = []
    base3_out_x = []
    base3_out_y = []
    base3_in_x = []
    base3_in_y = []
    base4_out_x = []
    base4_out_y = []
    base4_in_x = []
    base4_in_y = []

    # out
    for i in range(T_row):
        outbase = math.floor(i/4) + 1
        for j in range(T_col):
            if T[i][j] == 1 and outbase == 1:
                base1_out_x.append((x1[0], x[j]))
                base1_out_y.append((y1[0], y[j]))
            elif T[i][j] == 1 and outbase == 2:
                base2_out_x.append((x1[1], x[j]))
                base2_out_y.append((y1[1], y[j]))
            elif T[i][j] == 1 and outbase == 3:
                base3_out_x.append((x1[2], x[j]))
                base3_out_y.append((y1[2], y[j]))
            elif T[i][j] == 1 and outbase == 4:
                base4_out_x.append((x1[3], x[j]))
                base4_out_y.append((y1[3], y[j]))

    # in
    for i in range(T_row):
        inbase = i - math.floor(i/4) * 4 + 1
        for j in range(T_col):
            if T[i][j] == 1 and inbase == 1:
                base1_in_x.append((x[j], x1[0]))
                base1_in_y.append((y[j], y1[0]))
            elif T[i][j] == 1 and inbase == 2:
                base2_in_x.append((x[j], x1[1]))
                base2_in_y.append((y[j], y1[1]))
            elif T[i][j] == 1 and inbase == 3:
                base3_in_x.append((x[j], x1[2]))
                base3_in_y.append((y[j], y1[2]))
            elif T[i][j] == 1 and inbase == 4:
                base4_in_x.append((x[j], x1[3]))
                base4_in_y.append((y[j], y1[3]))

    return base1_out_x,base1_out_y,base2_out_x,base2_out_y,base3_out_x,base3_out_y,base4_out_x,base4_out_y,\
           base1_in_x,base1_in_y,base2_in_x,base2_in_y,base3_in_x,base3_in_y,base4_in_x,base4_in_y


if __name__ == '__main__':
    base = 4
    outbase = 4
    inbase = 4
    cluster = 21
    ftcharge = 0.75 # 充电45分钟
    fv = 50  # 飞行速度50km/h
    Qmax = ftcharge*fv
    L = [1,2,1,3,3,1,2,2,2,2,1,2,1,2,2,3,2,2,2,3,1]   # total 40
    capacity = [48, 48, 48, 48]   # 基站初始容量均为48, total 192
    batch = 2
    # La = genLaMatrix()
    # print(La)
    # La = [4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4]  # 每批最多16,共16种基站组合

    distance_BC = genDistance(x, y, x1, y1)
    distance_BC_array = np.array(distance_BC)
    print(distance_BC)
    Q = genQMatrix(distance_BC_array)
    Q = np.array(Q)
    QMatrix = np.array(Q)

    # 对超过续航时间的Q[i][j]赋为255
    for i in range(0,len(QMatrix)):
        for j in range(0,len(QMatrix[0])):
            if QMatrix[i][j] >= Qmax:
                QMatrix[i][j] = 255

    QMatrix = getNormalizedQ(QMatrix)
    print(QMatrix)
    # T = Assignment.KM(QMatrix, La, L, capacity)
    # TMAT = np.array(T)
    # print(TMAT)

    # 找到可行的La
    def availableLa():
        T_row = 16
        T_col = 21
        flag = 1
        La = []
        while True:
            La = genLaMatrix()
            # LaMatrix = [4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4]
            T = Assignment.KM(QMatrix, La, L, outbase, inbase, batch)
            for k in range(batch):
                for i in range(T_row):
                    for j in range(T_col):
                        if T[i][j][k] > 1 or T[i][j][k] < 0:
                            flag = 0
                if flag == 1:
                    print(La)
            break
        return La

    La = availableLa()
    T1 = Assignment.KM(QMatrix, La, L, outbase, inbase, batch)
    # T2 = Assignment.KM(QMatrix, La[1], L, outbase, inbase, batch)

    TMAT1 = np.array(T1)
    # TMAT2 = np.array(T2)
    print(TMAT1)
    # print(TMAT2)


    # countbase_in,countbase_out = countdrones(TMAT)
    # print(countbase_in)
    # print(countbase_out)

    # 指派路径图
    # base1_out_x, base1_out_y, base2_out_x, base2_out_y, base3_out_x, base3_out_y, base4_out_x, base4_out_y, \
    # base1_in_x, base1_in_y, base2_in_x, base2_in_y, base3_in_x, base3_in_y, base4_in_x, base4_in_y = drawpath(TMAT,x,y,x1,y1)
    # print(base1_out_x)
    # print(base1_out_y)
    # print(base1_in_x)
    # print(base1_in_y)
    # print(base2_out_x)
    # print(base2_out_y)
    # print(base2_in_x)
    # print(base2_in_y)
    # print(base3_out_x)
    # print(base3_out_y)
    # print(base3_in_x)
    # print(base3_in_y)
    # print(base4_out_x)
    # print(base4_out_y)
    # print(base4_in_x)
    # print(base4_in_y)
    #
    #
    # # out
    # plt.figure()
    # for i in range(len(base1_out_x)):
    #     plt.plot(base1_out_x[i], base1_out_y[i], linewidth='1', color='g')
    #     plt.scatter(base1_out_x[i], base1_out_y[i], color='b', marker='*')
    #     plt.scatter([18.95, ], [6.92, ], s=150, color='r', marker='*')
    # for i in range(len(base2_out_x)):
    #     plt.plot(base2_out_x[i],base2_out_y[i], linewidth='1', color='purple')
    #     plt.scatter(base2_out_x[i], base2_out_y[i], color='b', marker='*')
    #     plt.scatter([6.42, ], [9.6, ], s=150, color='r', marker='*')
    # for i in range(len(base3_out_x)):
    #     plt.plot(base3_out_x[i], base3_out_y[i], linewidth='1', color='c')
    #     plt.scatter(base3_out_x[i], base3_out_y[i], color='b', marker='*')
    #     plt.scatter([11.52, ], [18.78, ], s=150, color='r', marker='*')
    # for i in range(len(base4_out_x)):
    #     plt.plot(base4_out_x[i], base4_out_y[i], linewidth='1', color='y')
    #     plt.scatter(base4_out_x[i], base4_out_y[i], color='b', marker='*')
    #     plt.scatter([16.77, ], [15.33, ], s=150, color='r', marker='*')
    # plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
    # plt.title('派出路径图')
    # plt.xlim(0, 20, 1)
    # plt.ylim(0, 20, 2)
    # plt.gca().xaxis.set_major_locator(MaxNLocator(integer=True))  # 限制坐标轴刻度为整数
    # plt.gca().yaxis.set_major_locator(MaxNLocator(integer=True))
    # plt.show()
    #
    # # in
    # plt.figure()
    # for i in range(len(base1_in_x)):
    #     plt.plot(base1_in_x[i], base1_in_y[i], linewidth='1', color='g')
    #     plt.scatter(base1_in_x[i], base1_in_y[i], color='b', marker='*')
    #     plt.scatter([18.95, ], [6.92, ], s=150, color='r', marker='*')
    # for i in range(len(base2_in_x)):
    #     plt.plot(base2_in_x[i],base2_in_y[i], linewidth='1', color='purple')
    #     plt.scatter(base2_in_x[i], base2_in_y[i], color='b', marker='*')
    #     plt.scatter([6.42, ], [9.6, ], s=150, color='r', marker='*')
    # for i in range(len(base3_in_x)):
    #     plt.plot(base3_in_x[i], base3_in_y[i], linewidth='1', color='c')
    #     plt.scatter(base3_in_x[i], base3_in_y[i], color='b', marker='*')
    #     plt.scatter([11.52, ], [18.78, ], s=150, color='r', marker='*')
    # for i in range(len(base4_in_x)):
    #     plt.plot(base4_in_x[i], base4_in_y[i], linewidth='1', color='y')
    #     plt.scatter(base4_in_x[i], base4_in_y[i], color='b', marker='*')
    #     plt.scatter([16.77, ], [15.33, ], s=150, color='r', marker='*')
    # plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
    # plt.title('派回路径图')
    # plt.xlim(0, 20, 1)
    # plt.ylim(0, 20, 2)
    # plt.gca().xaxis.set_major_locator(MaxNLocator(integer=True))  # 限制坐标轴刻度为整数
    # plt.gca().yaxis.set_major_locator(MaxNLocator(integer=True))
    # plt.show()








