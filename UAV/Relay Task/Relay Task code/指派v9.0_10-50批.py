import math
import matplotlib.pyplot as plt
from matplotlib.ticker import MaxNLocator
from math import floor
import numpy as np
import xlrd
import pandas as pd
import pulp as pl
import pandas as pd
from math import cos, sin, acos, asin, pi, atan, fabs, ceil, floor
from collections import defaultdict
import random
import time
from gurobipy import *
import openpyxl

#   2022.11.13 新约束（考虑库存） 10-50批
#   2022.11.22 新约束（加防爆仓）

class Assignment:
    @classmethod
    def relaytask(cls, Q, La, L, b_num):
        row = len(Q)
        col = len(Q[0])

        # build a optimal problem
        pro = pl.LpProblem('MIN(connection and coverage )', pl.LpMinimize)
        solver = pl.getSolver('GUROBI_CMD')

        # build variables for the optimal problem
        lpvars = [[[pl.LpVariable("x" + str(i) + "y" + str(j) + "l" + str(b), lowBound=0, upBound=1, cat='Integer') for
                    j in range(col)] for i in range(row)]for b in range(b_num)]

        # build optimal function
        all = pl.LpAffineExpression()
        for b in range(0,b_num):
            for i in range(0,row):
                for j in range(0,col):
                    all += Q[i][j] * lpvars[b][i][j]
        pro += all

        # build constraint
        ## 满足聚集点需求
        for b in range(b_num):
            for j in range(0, col):
                tempSum = 0
                for i in range(0, row):
                    tempSum += lpvars[b][i][j]
                pro += tempSum == L[j]

        # la约束 (第一批飞出)
        for i in range(0, base):
            tempSum = 0
            for j in range(0, col):
                tempSum += lpvars[0][4 * i][j] + lpvars[0][4 * i + 1][j] + lpvars[0][4 * i + 2][j] + lpvars[0][4 * i + 3][j]
            pro += tempSum <= La[i]

        #   容量约束 (考虑基站本身有的无人机数量)
        for b in range(0, b_num - 1):
            for i in range(base):
                sumout = 0
                sumin = 0
                suminnow = lpvars[b][i][j] + lpvars[b][i + 4][j] + lpvars[b][i + 8][j] + lpvars[b][i + 12][j]   # 这一次飞入
                sumoutnow = lpvars[b][4 * i][j] + lpvars[b][4 * i + 1][j] + lpvars[b][4 * i + 2][j] + lpvars[b][4 * i + 3][j]   # 这一次飞出
                sumoutnext = lpvars[b + 1][4 * i][j] + lpvars[b + 1][4 * i + 1][j] + lpvars[b + 1][4 * i + 2][j] + lpvars[b + 1][4 * i + 3][j]  # 下一次飞出
                tempsum = 0
                for temp in range(0, b + 1):
                    for j in range(col):
                        sumin += lpvars[temp][i][j] + lpvars[temp][i + 4][j] + lpvars[temp][i + 8][j] + lpvars[temp][i + 12][j]    #k次飞进总和
                        sumout += lpvars[temp][4 * i][j] + lpvars[temp][4 * i + 1][j] + lpvars[temp][4 * i + 2][j] + lpvars[temp][4 * i + 3][j]    #k次飞出总和
                pro += sumoutnext <= eUAV[i] + sumin - sumout
                pro += sumin - sumout >= -1 * eUAV[i]
                pro += suminnow - sumoutnow - sumoutnext <= eUAV[i]

        # #   防爆仓1
        # for b in range(0, b_num - 1):
        #     for i in range(base):
        #         for temp in range(0, b + 1):
        #             for j in range(col):
        #                 sumin += lpvars[temp][i][j] + lpvars[temp][i + 4][j] + lpvars[temp][i + 8][j] + lpvars[temp][i + 12][j]    #k次飞进
        #                 sumout += lpvars[temp][4 * i][j] + lpvars[temp][4 * i + 1][j] + lpvars[temp][4 * i + 2][j] + lpvars[temp][4 * i + 3][j]    #k次飞出
        #         pro += sumin - sumout >= -1 * eUAV[i]
        #
        # #   防爆仓2
        # for b in range(0, b_num - 1):
        #     for i in range(base):
        #         for temp in range(0, b + 1):
        #             for j in range(col):
        #                 sumin += lpvars[temp][i][j] + lpvars[temp][i + 4][j] + lpvars[temp][i + 8][j] + lpvars[temp][i + 12][j]    #k次飞进
        #                 sumout += lpvars[temp][4 * i][j] + lpvars[temp][4 * i + 1][j] + lpvars[temp][4 * i + 2][j] + lpvars[temp][4 * i + 3][j]    #k次飞出
        #         pro += sumin - sumout <= eUAV[i] * 2

        # 总容量约束
        for i in range(base):
            sumout = 0
            sumin = 0
            for b in range(0, b_num):
                for j in range(col):
                    sumout += lpvars[b][4*i][j] + lpvars[b][4*i+1][j] + lpvars[b][4*i+2][j] + lpvars[b][4*i+3][j]
                    sumin += lpvars[b][i][j] + lpvars[b][i+4][j] + lpvars[b][i+8][j] + lpvars[b][i+12][j]
            pro += sumout == sumin

        # solve optimal problem
        status = pro.solve(solver)
        print("Assignment Status: ", pl.LpStatus[status])
        print("Final Assignment Result", pl.value(pro.objective))

        # get the result of T matrix
        T = [[[lpvars[b][i][j].varValue for j in range(col)] for i in range(row)] for b in range(b_num)]
        return T

# 计算两点之间距离公式
def calDistance(x1, x2, y1, y2):
    result = math.sqrt((x2-x1)**2+(y2-y1)**2)
    return result

# 聚集点与基站组合距离（21*4的矩阵）
def genDistance(x,y,x1,y1):
    distance_BC = []
    for j in range(base):
        tempMatrix = []
        for i in range(cluster):
            tempMatrix.append(calDistance(x[i],x1[j],y[i],y1[j]))
        distance_BC.append(tempMatrix)
    return distance_BC

# 聚集点与基站组合距离算出Q(21*16的矩阵)
def genQMatrix(distance_BC):
    Q = []
    for k in range(base):
        for j in range(base):
            temp = []
            for i in range(cluster):
                total_distance = []
                total_distance = distance_BC[k,i]+distance_BC[j,i]
                temp.append(total_distance)
            Q.append(temp)
    return Q

# 归一化后的Q
def getNormalizedQ(Q):
    max = min = Q[0][0]
    row = len(Q)
    column = len(Q[0])
    for i in range(row):
        for j in range(column):
            if (Q[i, j] > max):
                max = Q[i, j]
            if (Q[i, j] < min):
                min = Q[i, j]
    for i in range(row):
        for j in range(column):
            Q[i, j] = round((Q[i, j] - min) / (max - min), 4)
    return Q

# 随机生成La
def genLaMatrix(role_amount, agent_amount):
		La = []
		temp = agent_amount
		while(temp>=1):
				val = ceil(role_amount/agent_amount)
				val += random.randint(1, agent_amount)
				if val not in La:
						La.append(val)
						temp-=1
		return La

#   Q矩阵汇总
def gennewQmatrix(x,y,x1,y1):
    distance_BC = genDistance(x, y, x1, y1)
    distance_BC_array = np.array(distance_BC)

    Q = genQMatrix(distance_BC_array)
    Q = np.array(Q)
    QMatrix = np.array(Q)

    # 对超过续航时间的Q[i][j]赋为255
    for i in range(0,len(QMatrix)):
        for j in range(0,len(QMatrix[0])):
            if QMatrix[i][j] >= Qmax:
                QMatrix[i][j] = 255

    QMatrix = getNormalizedQ(QMatrix)
    return Q

#   总航迹
def contnewpath(T, b_num):
    condistance = []
    for b in range(b_num):
        condis = 0
        for i in range(combination):
            for j in range(cluster):
                if T[b][i][j] == 1:
                    condis += Q[i][j]
        condistance.append(condis)
    condistance_total = sum(condistance)
    return condistance_total

if __name__ == '__main__':

    b_num_array = [10,20,30,40,50,60,100]
    # b_num = 50
    base = 4
    outbase = 4
    inbase = 4
    cluster = 22
    combination = 16
    ftcharge = 0.75 # 充电45分钟
    fv = 50  # 飞行速度50km/h
    Qmax = ftcharge*fv
    L = [1,2,1,3,3,1,2,2,2,2,1,2,1,2,2,3,2,2,2,3,1,3]   # total 43
    capacity = np.array([45, 39, 42, 36])
    eUAV = capacity/3
    eUAV = np.array(eUAV)

    # La = genLaMatrix(43,4) # 个数
    La = [15, 13, 14, 12]

    # #一次运行结果
    # distance_BC = genDistance(x, y, x1, y1)
    # distance_BC_array = np.array(distance_BC)
    # print(distance_BC)
    # Q = genQMatrix(distance_BC_array)
    # Q = np.array(Q)
    # QMatrix = np.array(Q)
    #
    # # 对超过续航时间的Q[i][j]赋为255
    # for i in range(0,len(QMatrix)):
    #     for j in range(0,len(QMatrix[0])):
    #         if QMatrix[i][j] >= Qmax:
    #             QMatrix[i][j] = 255
    #
    # QMatrix = getNormalizedQ(QMatrix)
    # print(QMatrix)
    #
    # start_1 = time.perf_counter()
    # T1 = Assignment.relaytask(QMatrix, La, L, b_num)
    # total_1 = time.perf_counter() - start_1
    # TMAT = np.array(T1)
    # print(TMAT)
    #
    # condistance_total, condistance = contnewpath(T1, b_num)

    # 1000次随机实验：坐标变,记录每次时间和航迹距离
    scale = 50
    large_time = [[[]for t in range(scale)]for b in range(len(b_num_array))]
    large_path = [[[]for t in range(scale)]for b in range(len(b_num_array))]

    for t in range(0, scale):
        x = np.random.choice(range(0, 21), 22)  # 聚集点坐标
        y = np.random.choice(range(0, 21), 22)
        x1 = np.random.choice(range(0, 21), 4)  # 基站坐标
        y1 = np.random.choice(range(0, 21), 4)
        Q = gennewQmatrix(x, y, x1, y1)
        for b in range(len(b_num_array)):
            b_num = b_num_array[b]
            start = time.perf_counter()
            T = Assignment.relaytask(Q, La, L, b_num)
            total = time.perf_counter() - start
            large_time[b][t] = total
            large_path[b][t] = contnewpath(T, b_num)
            print('当前运行规模：', t)
            print('当前运行批次数：', b_num)


    large_time = np.array(large_time)
    large_path = np.array(large_path)


