#!/usr/bin/env python3
"""
帕累托前沿问题诊断和修复测试
"""

import numpy as np
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
import os

def analyze_original_problems():
    """分析原代码中的问题"""
    print("=" * 60)
    print("原代码中的帕累托前沿问题分析")
    print("=" * 60)
    
    print("\n🔍 发现的主要问题：")
    
    print("\n1. 路径提取逻辑错误 (第1057-1067行):")
    print("   - path_map = {k[1]: k[2] for k in solution_x.keys() if k[0] == i}")
    print("   - 这种构建方式可能导致路径映射错误")
    print("   - 当多个路径段共享相同起点时，字典会覆盖之前的值")
    
    print("\n2. 成本计算中的索引错误 (第1074-1078行):")
    print("   - p1 = roles[loc1][0] if loc1 != 'N' else 'N'")
    print("   - 当loc1是角色索引时，这样访问是正确的")
    print("   - 但路径提取可能产生无效的索引值")
    
    print("\n3. 图表显示问题:")
    print("   - 使用深色背景可能导致显示异常")
    print("   - 颜色映射可能不适合数据范围")
    print("   - 缺少数据验证和边界检查")
    
    print("\n4. 数据验证缺失:")
    print("   - 没有检查帕累托点的有效性")
    print("   - 可能包含负值或无效数据")
    print("   - 缺少异常处理机制")

def create_test_pareto_data():
    """创建测试用的帕累托数据"""
    print("\n📊 创建测试帕累托数据...")
    
    # 模拟真实的帕累托前沿数据
    w1_values = np.linspace(0.1, 0.9, 5)
    pareto_points = []
    
    # 模拟成本和效益的权衡关系
    base_cost = 5000
    base_benefit = 1.0
    
    for w1 in w1_values:
        # 模拟权重对成本和效益的影响
        # w1越高，更注重效益，成本可能更高但效益也更高
        cost_factor = 1.0 + 0.3 * w1  # 成本随w1增加
        benefit_factor = 0.8 + 0.4 * w1  # 效益随w1增加
        
        cost = base_cost * cost_factor + np.random.normal(0, 100)
        benefit = base_benefit * benefit_factor + np.random.normal(0, 0.05)
        
        # 确保数据有效
        cost = max(cost, 1000)
        benefit = max(benefit, 0.1)
        
        pareto_points.append((cost, benefit, w1))
        print(f"  w1={w1:.1f}: 成本={cost:.2f}, 效益={benefit:.4f}")
    
    return pareto_points

def plot_pareto_comparison(original_points, fixed_points, desktop_path):
    """对比原版和修复版的帕累托图"""
    print("\n🎨 绘制对比图表...")
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))
    
    # 原版风格（可能有问题的）
    if original_points:
        costs1 = [p[0] for p in original_points]
        benefits1 = [p[1] for p in original_points]
        weights1 = [p[2] for p in original_points]
        
        ax1.scatter(costs1, benefits1, c=weights1, cmap='jet', s=100)
        ax1.plot(costs1, benefits1, 'r-', linewidth=2)
        ax1.set_title('原版帕累托前沿 (可能有问题)', fontsize=14)
        ax1.set_xlabel('成本')
        ax1.set_ylabel('效益')
        ax1.grid(True, alpha=0.3)
    
    # 修复版风格
    if fixed_points:
        costs2 = [p[0] for p in fixed_points]
        benefits2 = [p[1] for p in fixed_points]
        weights2 = [p[2] for p in fixed_points]
        
        scatter = ax2.scatter(costs2, benefits2, c=weights2, cmap='viridis', s=100, 
                             edgecolors='black', linewidth=1, alpha=0.8)
        ax2.plot(costs2, benefits2, 'red', linewidth=2, alpha=0.7, label='Pareto Frontier')
        
        # 添加标签
        for cost, benefit, w1 in fixed_points:
            ax2.annotate(f'w₁={w1:.1f}', (cost, benefit), 
                        xytext=(5, 5), textcoords='offset points',
                        fontsize=9, bbox=dict(boxstyle='round,pad=0.3', 
                        facecolor='white', alpha=0.7))
        
        ax2.set_title('修复版帕累托前沿', fontsize=14)
        ax2.set_xlabel('成本')
        ax2.set_ylabel('效益')
        ax2.grid(True, alpha=0.3)
        ax2.legend()
        
        # 添加颜色条
        cbar = fig.colorbar(scatter, ax=ax2)
        cbar.set_label('权重 w₁')
    
    plt.tight_layout()
    filepath = os.path.join(desktop_path, "Pareto_Comparison_Test.png")
    plt.savefig(filepath, dpi=300, bbox_inches='tight')
    plt.close()
    print(f"对比图表已保存至: {filepath}")

def test_data_validation():
    """测试数据验证功能"""
    print("\n🔬 测试数据验证功能...")
    
    # 创建包含问题的测试数据
    test_data = [
        (5000, 1.2, 0.1),    # 正常数据
        (-100, 0.8, 0.3),    # 负成本（无效）
        (6000, -0.1, 0.5),   # 负效益（无效）
        (5500, 1.0, 1.5),    # 权重超出范围（无效）
        (0, 0.9, 0.7),       # 零成本（可能无效）
        (4800, 1.1, 0.9),    # 正常数据
    ]
    
    print("原始数据:")
    for i, (cost, benefit, weight) in enumerate(test_data):
        print(f"  点 {i+1}: 成本={cost}, 效益={benefit}, 权重={weight}")
    
    # 数据验证
    valid_points = []
    for point in test_data:
        if len(point) == 3 and all(isinstance(x, (int, float)) for x in point):
            cost, benefit, weight = point
            if cost > 0 and benefit > 0 and 0 <= weight <= 1:
                valid_points.append(point)
    
    print(f"\n验证后有效数据点: {len(valid_points)}/{len(test_data)}")
    for i, (cost, benefit, weight) in enumerate(valid_points):
        print(f"  有效点 {i+1}: 成本={cost}, 效益={benefit}, 权重={weight}")
    
    return valid_points

def main():
    """主测试函数"""
    print("🔧 帕累托前沿问题诊断和修复测试")
    
    # 分析原问题
    analyze_original_problems()
    
    # 测试数据验证
    valid_data = test_data_validation()
    
    # 创建测试数据
    test_data = create_test_pareto_data()
    
    # 设置保存路径
    DESKTOP_PATH = r"H:\OneDrive\Desktop"
    if not os.path.exists(DESKTOP_PATH):
        DESKTOP_PATH = "."
    
    # 绘制对比图
    plot_pareto_comparison(test_data, valid_data, DESKTOP_PATH)
    
    print("\n" + "=" * 60)
    print("✅ 问题诊断和修复测试完成！")
    print("=" * 60)
    
    print("\n📋 修复建议总结:")
    print("1. 使用更稳健的路径提取算法")
    print("2. 添加数据有效性验证")
    print("3. 使用更清晰的图表样式")
    print("4. 增加异常处理和错误恢复")
    print("5. 优化内存管理和性能")

if __name__ == '__main__':
    main()
