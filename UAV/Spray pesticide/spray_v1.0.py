import numpy as np
import gurobipy as gp
from gurobipy import GRB
from collections import defaultdict
import time


# --- 1. 核心：生成基于真实场景的参数 ---
def generate_real_scenario_data(num_drones=3):  # 将无人机数量作为函数参数
    """
    生成一套完整的、基于特定虚拟场景的“真实”模型参数。
    所有无人机性能均相同。
    """
    print("--- 正在生成基于真实场景的参数 ---")

    # 场景基本设定
    # num_drones 由函数参数传入
    num_points = 15
    time_period = 10  # 10天
    num_pesticides = 4  # 4种农药

    # a. 无人机性能参数 (所有无人机性能相同)
    print(f"设定 {num_drones} 架无人机，所有无人机性能均相同。")
    # 设定所有无人机均为同一型号 (例如，高性能的T40)
    single_drone_capacity = 40  # 单台无人机载药量 (L)
    single_drone_endurance = 18  # 单台无人机续航 (可执行任务数)

    La = np.full(num_drones, single_drone_capacity)
    F = np.full(num_drones, single_drone_endurance)

    zeta_max_flow = 6.0  # 单次作业最大喷洒量 (L/任务) - 安全/物理上限
    alpha = 0.05  # 漂移影响系数 (假设使用标准中等雾滴喷头)

    # b. 地理与作物信息 (手动设定)
    all_crop_assignments = {}
    for p in range(num_points):
        if 0 <= p <= 4:
            all_crop_assignments[p] = 0  # 玉米
        elif 5 <= p <= 9:
            all_crop_assignments[p] = 1  # 大豆
        else:
            all_crop_assignments[p] = 2  # 蔬菜

    # c. 农艺学参数 (基于场景需求)
    zeta_standard = np.zeros((num_points, num_pesticides))
    zeta_standard[0:5, 0] = 1.5
    zeta_standard[5:10, 1] = 1.0
    zeta_standard[5:10, 2] = 1.2
    zeta_standard[10:15, 3] = 2.0

    L = np.zeros((num_points, num_pesticides))
    L_max = np.zeros((num_points, num_pesticides))
    L[0:5, 0] = zeta_standard[0:5, 0] * 1
    L[5:10, 1] = zeta_standard[5:10, 1] * 2
    L[5:10, 2] = zeta_standard[5:10, 2] * 1
    L[10:15, 3] = zeta_standard[10:15, 3] * 1.5
    L_max = np.ceil(L * 1.3)

    Q_base = np.zeros((num_points, num_pesticides))
    Q_base[0:5, 0] = 0.7
    Q_base[5:10, 1] = 1.0
    Q_base[5:10, 2] = 0.9
    Q_base[10:15, 3] = 0.8

    mu = np.zeros(num_pesticides)
    delta = np.zeros(num_pesticides)
    mu[0], delta[0] = 5, 3
    mu[1], delta[1] = 6, 2
    mu[2], delta[2] = 3, 2.5
    mu[3], delta[3] = 4, 3

    # d. 环境数据
    wind_speed = np.array([1.5, 2.0, 3.5, 4.0, 7.5, 6.0, 4.0, 2.5, 1.0, 3.0])

    params = {
        'num_drones': num_drones, 'num_points': num_points, 'time_period': time_period,
        'num_pesticides': num_pesticides, 'all_crop_assignments': all_crop_assignments,
        'La': La, 'F': F, 'zeta_max_flow': zeta_max_flow, 'alpha': alpha,
        'L': L, 'L_max': L_max, 'zeta_standard': zeta_standard,
        'Q_base': Q_base, 'mu_vec': mu, 'delta_vec': delta, 'wind_speed': wind_speed
    }
    print("--- 参数生成完毕 ---")
    return params


# --- 2. 核心优化模型 (与上一版完全相同) ---
class Assignment:
    @staticmethod
    def Spraypesticide_real_data(params):
        num_drones, num_points, time_period, num_pesticides = params['num_drones'], params['num_points'], params[
            'time_period'], params['num_pesticides']
        L, L_max, La, F = params['L'], params['L_max'], params['La'], params['F']
        Q_base, mu_vec, delta_vec = params['Q_base'], params['mu_vec'], params['delta_vec']
        zeta_standard, wind_speed, alpha, zeta_max_flow = params['zeta_standard'], params['wind_speed'], params[
            'alpha'], params['zeta_max_flow']

        print("开始创建角色并预计算参数...")
        roles = []
        Q_j, zeta_j, eta_j = {}, {}, {}
        eta_values = np.maximum(0.1, 1 - alpha * wind_speed)

        for p_idx in range(num_points):
            for t_idx in range(time_period):
                for c_idx in range(num_pesticides):
                    if L[p_idx, c_idx] > 0:
                        zeta_dispense = zeta_standard[p_idx, c_idx] / eta_values[t_idx]
                        if zeta_dispense <= zeta_max_flow:
                            roles.append((p_idx, t_idx, c_idx))

        num_roles = len(roles)
        role_to_idx = {role: j for j, role in enumerate(roles)}
        print(f"有效角色数量: {num_roles}")

        for role, j in role_to_idx.items():
            p, t, c = role
            g_value = 1 / (np.sqrt(2 * np.pi) * delta_vec[c]) * np.exp(
                -((t - mu_vec[c]) ** 2) / (2 * delta_vec[c] ** 2))
            Q_j[j] = Q_base[p, c] * g_value
            zeta_j[j] = zeta_standard[p, c] / eta_values[t]
            eta_j[j] = eta_values[t]

        roles_at_pc, roles_of_c, roles_at_t = defaultdict(list), defaultdict(list), defaultdict(list)
        for j, (p, t, c) in enumerate(roles):
            roles_at_pc[(p, c)].append(j)
            roles_of_c[c].append(j)
            roles_at_t[t].append(j)

        m = gp.Model("DronePesticideAssignment_RealData")
        T = m.addVars(num_drones, num_roles, vtype=GRB.BINARY, name="T")
        Y = m.addVars(num_drones, num_pesticides, vtype=GRB.BINARY, name="Y")
        m.setObjective(gp.quicksum(Q_j[j] * T[i, j] for i in range(num_drones) for j in range(num_roles)), GRB.MAXIMIZE)

        print("添加约束...")
        for p in range(num_points):
            for c in range(num_pesticides):
                if L[p, c] > 0:
                    effective_spray = gp.quicksum(T[i, j] * (zeta_j[j] * eta_j[j]) for i in range(num_drones) for j in
                                                  roles_at_pc.get((p, c), []))
                    m.addRange(effective_spray, L[p, c], L_max[p, c], name=f"Demand_p{p}_c{c}")
        m.addConstrs((Y.sum(i, '*') <= 1 for i in range(num_drones)), name="Single_Pesticide_Type")
        for j, (p, t, c) in enumerate(roles):
            m.addConstrs((T[i, j] <= Y[i, c] for i in range(num_drones)), name=f"Link_T_Y_{j}")
        m.addConstrs((gp.quicksum(T[i, j] * zeta_j[j] for j in roles_of_c.get(c, [])) <= La[i] * Y[i, c]
                      for i in range(num_drones) for c in range(num_pesticides)), name="Capacity")
        m.addConstrs((T.sum(i, roles_at_t.get(t, [])) <= 1 for i in range(num_drones) for t in range(time_period)),
                     name="Single_Task")
        m.addConstrs((T.sum(i, '*') <= F[i] for i in range(num_drones)), name="Endurance")

        print("开始求解模型...")
        m.optimize()

        if m.status == GRB.OPTIMAL:
            print(f"最优解找到! 目标值: {m.objVal}")
            solution = m.getAttr('X', T)
            return solution, "Optimal", m.objVal, roles
        else:
            print("未找到最优解。")
            m.computeIIS()
            m.write("model_real_data.ilp")
            print("不可行约束已写入 model_real_data.ilp")
            return None, "Infeasible/Error", None, None


# --- 3. Main 执行部分 ---
if __name__ == '__main__':
    # 1. 生成基于真实场景的参数, 设定无人机数量为3架
    params = generate_real_scenario_data(num_drones=3)

    print("\n部分生成参数检查:")
    print("无人机载药量 La:", params['La'])
    print("无人机续航 F:", params['F'])
    print("10日风速预报 v:", params['wind_speed'])

    # 2. 运行模型
    start_1 = time.perf_counter()
    solution, status, objective_value, roles = Assignment.Spraypesticide_real_data(params)
    total_1 = time.perf_counter() - start_1
    print(f"模型求解耗时: {total_1:.2f} 秒")

    # 3. 结果分析与可视化
    if status == "Optimal":
        print("\n--- 最优分配方案摘要 ---")
        assigned_tasks = 0
        for i in range(params['num_drones']):
            drone_tasks = []
            for j in range(len(roles)):
                if solution[i, j] > 0.5:
                    assigned_tasks += 1
                    drone_tasks.append(roles[j])
            drone_tasks.sort(key=lambda x: x[1])
            print(f"无人机 {i} 分配了 {len(drone_tasks)} 个任务:")
            for task in drone_tasks:
                p, t, c = task
                print(f"  - 第{t}天, 在作业点{p}, 喷洒农药{c}")
        print(f"\n总计分配了 {assigned_tasks} 个任务。")