import numpy as np
import pulp as pl
from collections import defaultdict
import time
import math
import random
import matplotlib.pyplot as plt
from matplotlib.patches import Patch, Polygon
import pandas as pd
from datetime import datetime
import os
from matplotlib.colors import LinearSegmentedColormap
from matplotlib.path import Path


# --- 1. 核心：生成统一的、供三个实验共享的场景参数 ---
def generate_unified_scenario_data(num_drones=4):
    """
    V3.2 更新：恢复为15个点，并生成在四边形区域内稀疏分布的作物。
    """
    print("--- 正在生成统一的、基于真实场景的参数 ---")

    # 基本设定
    num_points = 15
    days = 10;
    hours_per_day = 4;
    time_period = days * hours_per_day;
    num_pesticides = 4

    params = {
        'num_drones': num_drones, 'num_points': num_points, 'time_period': time_period,
        'num_pesticides': num_pesticides
    }

    # V3.2 核心修改：在四边形内稀疏生成点
    print("生成四边形田块内的稀疏作物分布...")
    # 定义三个不重叠的四边形田块 [ (x1,y1), (x2,y2), ... ]
    field_quads = {
        0: Path([(100, 700), (300, 1000), (450, 850), (250, 600)]),  # 玉米区 (P0-P4)
        1: Path([(700, 400), (950, 550), (850, 250), (600, 300)]),  # 大豆区 (P5-P9)
        2: Path([(350, 50), (550, 250), (450, 300), (250, 100)])  # 蔬菜区 (P10-P14)
    }
    params['field_polygons_verts'] = {f'field_{k}': v.vertices for k, v in field_quads.items()}

    params['points_coords'] = {}
    params['all_crop_assignments'] = {p: (0 if 0 <= p <= 4 else (1 if 5 <= p <= 9 else 2)) for p in range(num_points)}

    for p in range(num_points):
        crop_type = params['all_crop_assignments'][p]
        quad_path = field_quads[crop_type]
        min_x, min_y = quad_path.vertices.min(axis=0)
        max_x, max_y = quad_path.vertices.max(axis=0)

        while True:
            # 在边界框内随机生成点
            rand_x, rand_y = random.uniform(min_x, max_x), random.uniform(min_y, max_y)
            # 确认点在多边形内部
            if quad_path.contains_point((rand_x, rand_y)):
                params['points_coords'][p] = (rand_x, rand_y)
                break  # 找到有效点后退出循环

    params['base_station_coord'] = (-100, -100)

    # 恢复为小规模实验的资源设定
    single_drone_capacity = 40;
    single_drone_endurance_tasks = 12;
    single_drone_endurance_time = 90
    params['La'] = np.full(num_drones, single_drone_capacity)
    params['F_tasks'] = np.full(num_drones, single_drone_endurance_tasks)
    params['F_time'] = np.full(num_drones, single_drone_endurance_time)
    params['zeta_max_flow'] = 6.0;
    params['alpha'] = 0.05

    # 恢复为15个点的需求设定
    zeta_standard = np.zeros((num_points, num_pesticides));
    zeta_standard[0:5, 0] = 1.5;
    zeta_standard[5:10, 1] = 1.0;
    zeta_standard[5:10, 2] = 1.2;
    zeta_standard[10:15, 3] = 2.0
    params['zeta_standard'] = zeta_standard
    L = np.zeros((num_points, num_pesticides));
    L[0:5, 0] = zeta_standard[0:5, 0] * 1;
    L[5:10, 1] = zeta_standard[5:10, 1] * 2;
    L[5:10, 2] = zeta_standard[5:10, 2] * 1;
    L[10:15, 3] = zeta_standard[10:15, 3] * 1.5
    params['L'] = L;
    params['L_max'] = np.ceil(L * 1.3)
    Q_base = np.zeros((num_points, num_pesticides));
    Q_base[0:5, 0] = 0.7;
    Q_base[5:10, 1] = 1.0;
    Q_base[5:10, 2] = 0.9;
    Q_base[10:15, 3] = 0.8
    params['Q_base'] = Q_base

    # (其余参数生成逻辑不变)
    mu_days = np.array([5, 4, 3, 6]);
    delta_days = np.array([3, 2, 2.5, 3])
    params['mu_vec'] = (mu_days - 1) * hours_per_day + (hours_per_day / 2);
    params['delta_vec'] = delta_days * (hours_per_day / 2)
    daily_wind_pattern = np.array([1.5, 2.0, 3.5, 4.0, 7.5, 6.0, 4.0, 2.5, 1.0, 3.0]);
    params['wind_speed'] = np.repeat(daily_wind_pattern, hours_per_day)
    params['C_conflict'] = [(1, 2)];
    params['Delta_t_conflict'] = hours_per_day
    dist_matrix = np.zeros((num_points, num_points))
    for p1 in range(num_points):
        for p2 in range(num_points):
            dist_matrix[p1, p2] = math.sqrt((params['points_coords'][p1][0] - params['points_coords'][p2][0]) ** 2 + (
                        params['points_coords'][p1][1] - params['points_coords'][p2][1]) ** 2)
    params['dist_matrix'] = dist_matrix
    params['dist_base'] = {p: math.sqrt((params['points_coords'][p][0] - params['base_station_coord'][0]) ** 2 + (
                params['points_coords'][p][1] - params['base_station_coord'][1]) ** 2) for p in range(num_points)}
    params['V_drone'] = 20;
    params['tau_task'] = 2;
    params['w1'] = 0.6;
    params['w2'] = 0.4;
    params['yield_ref'] = 10.0;
    params['path_ref'] = 15000

    print(f"--- 统一场景参数生成完毕 (共 {num_points} 个作业点) ---")
    return params


# --- 2. 可视化与辅助函数 ---
def plot_crop_distribution(params, desktop_path):
    """V3.2 适配：绘制四边形区域和稀疏作物图"""
    print("绘制作物分布图...")
    fig, ax = plt.subplots(figsize=(12, 12))
    ax.set_title('Crop Distribution Map', fontsize=16)

    for name, vertices in params['field_polygons_verts'].items():
        poly = Polygon(vertices, closed=True, facecolor='lightblue', alpha=0.4, edgecolor='gray')
        ax.add_patch(poly)

    crop_colors = ['red', 'green', 'blue']
    crop_labels = ['Corn (P0-P4)', 'Soybean (P5-P9)', 'Vegetable (P10-P14)']

    for p, crop_type in params['all_crop_assignments'].items():
        x, y = params['points_coords'][p]
        ax.scatter(x, y, color=crop_colors[crop_type], s=50, zorder=3)
        ax.text(x + 5, y + 5, f'P{p}', fontsize=9)

    base_x, base_y = params['base_station_coord']
    ax.scatter(base_x, base_y, color='purple', marker='*', s=400, label='Base Station', zorder=5)

    legend_elements = [
        plt.Line2D([0], [0], marker='o', color='w', label=crop_labels[i], markerfacecolor=crop_colors[i], markersize=10)
        for i in range(len(crop_labels))]
    legend_elements.append(Patch(facecolor='lightblue', alpha=0.4, label='Crop Area'))
    legend_elements.append(
        plt.Line2D([0], [0], marker='*', color='w', label='Base Station', markerfacecolor='purple', markersize=20))

    ax.legend(handles=legend_elements, loc='upper right', fontsize=12)
    ax.set_xlabel('X Coordinate');
    ax.set_ylabel('Y Coordinate');
    ax.grid(True, linestyle='--', alpha=0.6);
    ax.set_aspect('equal', adjustable='box')
    ax.set_xlim(-150, 1050);
    ax.set_ylim(-150, 1050)

    filepath = os.path.join(desktop_path, "Crop_Distribution_Map.png");
    plt.savefig(filepath);
    print(f"作物分布图已保存至: {filepath}");
    plt.show()


def plot_ideal_timing_map(params, desktop_path, title="Ideal_Spraying_Time_Map"):
    """绘制甘特图风格的理想喷洒时间图"""
    print(f"绘制理想时间图: {title}")
    num_points, time_period = params['num_points'], params['time_period'];
    grid = np.zeros((num_points, time_period))
    point_pesticide_map = {}
    for p in range(num_points):
        needed_pesticides = np.where(params['L'][p, :] > 0)[0]
        if len(needed_pesticides) > 0: point_pesticide_map[p] = needed_pesticides
    for p, pesticides in point_pesticide_map.items():
        for c in pesticides:
            ideal_time = int(round(params['mu_vec'][c]))
            if 0 <= ideal_time < time_period: grid[p, ideal_time] = 1.0
    fig, ax = plt.subplots(figsize=(15, 6));
    cax = ax.matshow(grid, cmap='Greens', vmin=0, vmax=1)
    ax.set_xticks(np.arange(time_period));
    ax.set_yticks(np.arange(num_points));
    ax.set_xticklabels(np.arange(time_period));
    ax.set_yticklabels([f'Point {p}' for p in range(num_points)])
    plt.xlabel('Time Slot');
    plt.ylabel('Task Point');
    plt.title(title);
    filepath = os.path.join(desktop_path, f"{title}.png");
    plt.savefig(filepath);
    print(f"理想时间图已保存至: {filepath}");
    plt.show()


def plot_assignment_grid(solution, roles, params, desktop_path, title):
    """绘制任务分配情况网格图(颜色代表时间效益)"""
    if not solution: print(f"无法绘制分配图: {title} (无解)"); return
    print(f"绘制任务分配图: {title}");
    num_drones, time_period = params['num_drones'], params['time_period']
    grid = np.zeros((num_drones, time_period));
    grid_text = {};
    drone_pesticide_map = {}
    for (i, j), val in solution.items():
        if val > 0.5:
            p, t, c = roles[j];
            ideal_time = params['mu_vec'][c];
            quality_score = math.exp(-0.05 * abs(t - ideal_time))
            grid[i, t] = quality_score;
            grid_text[(i, t)] = f'P{p}'
            if i not in drone_pesticide_map: drone_pesticide_map[i] = c
    fig, ax = plt.subplots(figsize=(15, 5));
    cax = ax.matshow(grid, cmap='Greens', vmin=0, vmax=1)
    cbar = fig.colorbar(cax);
    cbar.set_label('Temporal Quality (Closer to ideal time is darker)')
    for (i, t), text in grid_text.items(): ax.text(t, i, text, va='center', ha='center',
                                                   color='black' if grid[i, t] < 0.6 else 'white', fontsize=8)
    ax.set_xticks(np.arange(time_period));
    ax.set_yticks(np.arange(num_drones));
    ax.set_xticklabels(np.arange(time_period));
    ax.set_yticklabels([f'Drone {i} (Pest. {drone_pesticide_map.get(i, "N/A")})' for i in range(num_drones)])
    plt.xlabel('Time Slot');
    plt.ylabel('Drone');
    plt.title(title);
    filepath = os.path.join(desktop_path, f"{title.replace(':', '')}.png");
    plt.savefig(filepath);
    print(f"任务分配图已保存至: {filepath}");
    plt.show()


def generate_heuristic_path(solution_t, roles, params):
    """为实验1和2生成启发式（最近邻）路径"""
    if not solution_t: return {}
    dist_matrix, dist_base, num_drones = params['dist_matrix'], params['dist_base'], params['num_drones']
    paths = {}
    tasks_by_drone = defaultdict(list)
    for (i, j), v in solution_t.items():
        if v > 0.5: tasks_by_drone[i].append(roles[j])

    for i in range(num_drones):
        drone_tasks_sorted_by_time = sorted(tasks_by_drone.get(i, []), key=lambda x: x[1])
        if not drone_tasks_sorted_by_time: continue

        # 使用点的ID进行路径规划
        unvisited_points = list(dict.fromkeys([r[0] for r in drone_tasks_sorted_by_time]))

        path = ['N'];
        current_p = 'N'
        while unvisited_points:
            next_p = min(unvisited_points,
                         key=lambda p: dist_base[p] if current_p == 'N' else dist_matrix[current_p][p])
            path.append(next_p);
            unvisited_points.remove(next_p);
            current_p = next_p
        path.append('N');
        paths[i] = path
    return paths


# (代码与之前版本相同)

def plot_flight_paths(solution_t, solution_x, roles, params, desktop_path, title):
    """V3.2 修改：取消路径偏移，增加重叠路径标签"""
    print(f"绘制飞行路径图: {title}")
    num_drones, points_coords, base_coord = params['num_drones'], params['points_coords'], params['base_station_coord']

    fig, ax = plt.subplots(figsize=(12, 12))
    for p, coord in points_coords.items():
        ax.scatter(coord[0], coord[1], c='gray', alpha=0.3, s=50);
        ax.text(coord[0] + 5, coord[1] + 5, f'P{p}', fontsize=9, color='gray')
    ax.scatter(base_coord[0], base_coord[1], c='black', marker='s', s=150, label='Base Station')
    base_colors = ['blue', 'green', 'red', 'purple', 'orange', 'brown']

    drawn_segments = defaultdict(list)

    for i in range(num_drones):
        path_nodes = []
        if solution_x:  # 实验三
            current_node = 'N';
            drone_path = ['N']
            for _ in range(len(roles) + 2):
                found_next = False
                for k in list(range(len(roles))) + ['N']:
                    if k != current_node and solution_x.get((i, current_node, k), 0) > 0.5:
                        drone_path.append(k);
                        current_node = k;
                        found_next = True;
                        break
                if not found_next or current_node == 'N': break
            if len(drone_path) > 1: path_nodes = drone_path
        else:  # 实验一、二
            heuristic_paths = generate_heuristic_path(solution_t, roles, params)
            path_nodes = heuristic_paths.get(i, [])

        if path_nodes and len(path_nodes) > 1:
            path_coords = []
            if solution_x:
                path_coords = [base_coord if node == 'N' else points_coords[roles[node][0]] for node in path_nodes]
            else:
                path_coords = [base_coord if node == 'N' else points_coords[node] for node in path_nodes]

            # 绘制路径并记录
            for j in range(len(path_coords) - 1):
                start_coord, end_coord = tuple(path_coords[j]), tuple(path_coords[j + 1])
                ax.plot([start_coord[0], end_coord[0]], [start_coord[1], end_coord[1]],
                        color=base_colors[i % len(base_colors)], linewidth=2.0, alpha=0.7, zorder=2)
                segment_key = tuple(sorted((start_coord, end_coord)))
                if i not in drawn_segments[segment_key]:
                    drawn_segments[segment_key].append(i)
            ax.plot([], [], color=base_colors[i % len(base_colors)], label=f'Drone {i} Path')

    # 处理重叠路径的标签
    for segment, drones in drawn_segments.items():
        if len(drones) > 1:
            mid_point = ((segment[0][0] + segment[1][0]) / 2, (segment[0][1] + segment[1][1]) / 2)
            label = f"D: {','.join(map(str, sorted(drones)))}"
            ax.text(mid_point[0], mid_point[1] + 15, label, fontsize=8, ha='center', zorder=5,
                    bbox=dict(facecolor='white', alpha=0.8, edgecolor='black', boxstyle='round,pad=0.2'))

    ax.set_title(title);
    ax.set_xlabel('X Coordinate');
    ax.set_ylabel('Y Coordinate');
    ax.legend()
    ax.grid(True, linestyle='--', alpha=0.6);
    ax.set_aspect('equal', adjustable='box')
    ax.set_xlim(-150, 1050);
    ax.set_ylim(-150, 1050)
    filepath = os.path.join(desktop_path, f"{title.replace(':', '')}.png");
    plt.savefig(filepath);
    print(f"飞行路径图已保存至: {filepath}");
    plt.show()


def save_results_to_excel(params, all_results, desktop_path):
    """将所有参数和结果保存到带时间戳的Excel文件中。"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = os.path.join(desktop_path, f"Drone_Assignment_Results_{timestamp}.xlsx")
    print(f"正在将所有结果保存到: {filename}")

    with pd.ExcelWriter(filename, engine='openpyxl') as writer:
        param_data = {k: str(v) for k, v in params.items() if not isinstance(v, (np.ndarray, dict, list))}
        pd.DataFrame.from_dict(param_data, orient='index', columns=['Value']).to_excel(writer,
                                                                                       sheet_name='Initial_Parameters')

        for exp_name, res in all_results.items():
            if res['status'] == 'Optimal':
                pd.DataFrame({'Objective': [res['objective']]}).to_excel(writer, sheet_name=f'{exp_name}_Objective')

                assignment_list = [];
                num_roles = len(res['roles'])
                for (i, j), val in res['solution_t'].items():
                    if val > 0.5:
                        p, t, c = res['roles'][j];
                        assignment_list.append({'Drone': i, 'Role_ID': j, 'Point': p, 'Time': t, 'Pesticide': c})
                pd.DataFrame(assignment_list).to_excel(writer, sheet_name=f'{exp_name}_Assignment_List', index=False)

                num_drones = params['num_drones']
                assignment_matrix = pd.DataFrame(0, index=[f'Drone_{i}' for i in range(num_drones)],
                                                 columns=[f'Role_{j}' for j in range(num_roles)])
                for (i, j), val in res['solution_t'].items():
                    if val > 0.5: assignment_matrix.loc[f'Drone_{i}', f'Role_{j}'] = 1
                assignment_matrix.to_excel(writer, sheet_name=f'{exp_name}_Assignment_Matrix')

                if res.get('path'):
                    path_lengths = {}
                    dist_matrix, dist_base = params['dist_matrix'], params['dist_base']
                    current_roles = res['roles']  # *** BUG FIX HERE ***
                    for i, p_list in res['path'].items():
                        total_dist = 0
                        for step in range(len(p_list) - 1):
                            loc1, loc2 = p_list[step], p_list[step + 1]
                            p1 = current_roles[loc1][0] if isinstance(loc1, int) else loc1
                            p2 = current_roles[loc2][0] if isinstance(loc2, int) else loc2
                            if p1 == 'N':
                                total_dist += dist_base[p2]
                            elif p2 == 'N':
                                total_dist += dist_base[p1]
                            else:
                                total_dist += dist_matrix[p1][p2]
                        path_lengths[f'Drone_{i}'] = total_dist
                    pd.DataFrame.from_dict(path_lengths, orient='index', columns=['Total_Path_Length']).to_excel(writer,
                                                                                                                 sheet_name=f'{exp_name}_Path_Lengths')

                    path_df_list = []
                    for i, p_list in res['path'].items():
                        for step, point in enumerate(p_list):
                            path_df_list.append({'Drone': i, 'Step': step, 'Location': point})
                    pd.DataFrame(path_df_list).to_excel(writer, sheet_name=f'{exp_name}_Paths_Detail', index=False)
    print("Excel文件保存成功。")


# (代码与之前版本相同)

# --- 3. 三个实验的模型求解函数 (使用Pulp) ---
def solve_baseline_assignment(params):
    """实验一：求解基准分配模型"""
    num_drones, num_points, time_period, num_pesticides = params['num_drones'], params['num_points'], params[
        'time_period'], params['num_pesticides']
    L, L_max, La, F_tasks = params['L'], params['L_max'], params['La'], params['F_tasks']
    Q_base, mu_vec, delta_vec = params['Q_base'], params['mu_vec'], params['delta_vec']
    zeta_standard, wind_speed, alpha, zeta_max_flow = params['zeta_standard'], params['wind_speed'], params['alpha'], \
    params['zeta_max_flow']

    roles = [];
    Q_j, zeta_j, eta_j = {}, {}, {}
    eta_values = np.maximum(0.1, 1 - alpha * wind_speed)
    for p in range(num_points):
        for t in range(time_period):
            for c in range(num_pesticides):
                if L[p, c] > 0:
                    zeta_dispense = zeta_standard[p, c] / eta_values[t]
                    if zeta_dispense <= zeta_max_flow:
                        role_tuple = (p, t, c);
                        roles.append(role_tuple);
                        j = len(roles) - 1
                        g_value = 1 / (np.sqrt(2 * np.pi) * delta_vec[c]) * np.exp(
                            -((t - mu_vec[c]) ** 2) / (2 * delta_vec[c] ** 2))
                        Q_j[j] = Q_base[p, c] * g_value;
                        zeta_j[j] = zeta_dispense;
                        eta_j[j] = eta_values[t]
    num_roles = len(roles);
    print(f"有效角色数量: {num_roles}")

    prob = pl.LpProblem("Drone_Assignment_Baseline", pl.LpMaximize);
    solver = pl.getSolver('GUROBI_CMD', msg=False)
    T = pl.LpVariable.dicts("T", (range(num_drones), range(num_roles)), 0, 1, pl.LpBinary)
    Y = pl.LpVariable.dicts("Y", (range(num_drones), range(num_pesticides)), 0, 1, pl.LpBinary)
    prob += pl.lpSum(Q_j[j] * T[i][j] for i in range(num_drones) for j in range(num_roles)), "Maximize_Yield"

    for p in range(num_points):
        for c in range(num_pesticides):
            if L[p, c] > 0:
                relevant_roles = [j for j, role in enumerate(roles) if role[0] == p and role[2] == c]
                effective_spray = pl.lpSum(
                    T[i][j] * (zeta_j[j] * eta_j[j]) for i in range(num_drones) for j in relevant_roles)
                prob += effective_spray >= L[p, c], f"Demand_Lower_p{p}_c{c}";
                prob += effective_spray <= L_max[p, c], f"Demand_Upper_p{p}_c{c}"
    for i in range(num_drones):
        prob += pl.lpSum(Y[i][c] for c in range(num_pesticides)) <= 1, f"Single_Pesticide_Type_i{i}"
        for c in range(num_pesticides):
            relevant_roles = [j for j, role in enumerate(roles) if role[2] == c]
            prob += pl.lpSum(T[i][j] * zeta_j[j] for j in relevant_roles) <= La[i] * Y[i][c], f"Capacity_i{i}_c{c}"
        for j in range(num_roles):
            c_j = roles[j][2];
            prob += T[i][j] <= Y[i][c_j], f"Link_T_Y_i{i}_j{j}"
    for i in range(num_drones):
        for t in range(time_period):
            relevant_roles = [j for j, role in enumerate(roles) if role[1] == t]
            prob += pl.lpSum(T[i][j] for j in relevant_roles) <= 1, f"Single_Task_i{i}_t{t}"
    for i in range(num_drones):
        prob += pl.lpSum(T[i][j] for j in range(num_roles)) <= F_tasks[i], f"Endurance_i{i}"

    prob.solve(solver)
    if prob.status == pl.LpStatusOptimal:
        solution = {(i, j): T[i][j].varValue for i in range(num_drones) for j in range(num_roles)}
        return solution, "Optimal", pl.value(prob.objective), roles
    else:
        return None, pl.LpStatus[prob.status], None, None


def solve_agronomic_assignment(params):
    """实验二：求解协同调度模型"""
    num_drones, num_points, time_period, num_pesticides = params['num_drones'], params['num_points'], params[
        'time_period'], params['num_pesticides']
    L, L_max, La, F_tasks = params['L'], params['L_max'], params['La'], params['F_tasks']
    Q_base, mu_vec, delta_vec = params['Q_base'], params['mu_vec'], params['delta_vec']
    zeta_standard, wind_speed, alpha, zeta_max_flow = params['zeta_standard'], params['wind_speed'], params['alpha'], \
    params['zeta_max_flow']
    C_conflict, Delta_t_conflict = params['C_conflict'], params['Delta_t_conflict']
    roles = [];
    Q_j, zeta_j, eta_j = {}, {}, {};
    eta_values = np.maximum(0.1, 1 - alpha * wind_speed)
    for p in range(num_points):
        for t in range(time_period):
            for c in range(num_pesticides):
                if L[p, c] > 0:
                    zeta_dispense = zeta_standard[p, c] / eta_values[t]
                    if zeta_dispense <= zeta_max_flow:
                        role_tuple = (p, t, c);
                        roles.append(role_tuple);
                        j = len(roles) - 1
                        g_value = 1 / (np.sqrt(2 * np.pi) * delta_vec[c]) * np.exp(
                            -((t - mu_vec[c]) ** 2) / (2 * delta_vec[c] ** 2))
                        Q_j[j] = Q_base[p, c] * g_value;
                        zeta_j[j] = zeta_dispense;
                        eta_j[j] = eta_values[t]
    num_roles = len(roles);
    print(f"有效角色数量: {num_roles}")

    prob = pl.LpProblem("Drone_Assignment_Agronomic", pl.LpMaximize);
    solver = pl.getSolver('GUROBI_CMD', msg=False)
    T = pl.LpVariable.dicts("T", (range(num_drones), range(num_roles)), 0, 1, pl.LpBinary)
    Y = pl.LpVariable.dicts("Y", (range(num_drones), range(num_pesticides)), 0, 1, pl.LpBinary)
    prob += pl.lpSum(Q_j[j] * T[i][j] for i in range(num_drones) for j in range(num_roles)), "Maximize_Yield"

    for p in range(num_points):
        for c in range(num_pesticides):
            if L[p, c] > 0:
                relevant_roles = [j for j, role in enumerate(roles) if role[0] == p and role[2] == c];
                effective_spray = pl.lpSum(
                    T[i][j] * (zeta_j[j] * eta_j[j]) for i in range(num_drones) for j in relevant_roles)
                prob += effective_spray >= L[p, c];
                prob += effective_spray <= L_max[p, c]
    for i in range(num_drones):
        prob += pl.lpSum(Y[i][c] for c in range(num_pesticides)) <= 1
        for c in range(num_pesticides):
            relevant_roles = [j for j, role in enumerate(roles) if role[2] == c];
            prob += pl.lpSum(T[i][j] * zeta_j[j] for j in relevant_roles) <= La[i] * Y[i][c]
        for j in range(num_roles):
            c_j = roles[j][2];
            prob += T[i][j] <= Y[i][c_j]
    for i in range(num_drones):
        for t in range(time_period):
            relevant_roles = [j for j, role in enumerate(roles) if role[1] == t];
            prob += pl.lpSum(T[i][j] for j in relevant_roles) <= 1
    for i in range(num_drones):
        prob += pl.lpSum(T[i][j] for j in range(num_roles)) <= F_tasks[i]

    role_map_pct = defaultdict(list)
    for j, (p, t, c) in enumerate(roles): role_map_pct[(p, c)].append(j)
    for p in range(num_points):
        for c1, c2 in C_conflict:
            roles_c1, roles_c2 = role_map_pct.get((p, c1), []), role_map_pct.get((p, c2), [])
            for j1 in roles_c1:
                for j2 in roles_c2:
                    if abs(roles[j1][1] - roles[j2][1]) < Delta_t_conflict:
                        prob += pl.lpSum(T[i][j1] for i in range(num_drones)) + pl.lpSum(
                            T[i][j2] for i in range(num_drones)) <= 1, f"Conflict_p{p}_j{j1}_j{j2}"

    prob.solve(solver)
    if prob.status == pl.LpStatusOptimal:
        solution = {(i, j): T[i][j].varValue for i in range(num_drones) for j in range(num_roles)}
        return solution, "Optimal", pl.value(prob.objective), roles
    else:
        return None, pl.LpStatus[prob.status], None, None


def solve_moo_vrp_assignment(params):
    """实验三：求解多目标路径优化模型"""
    num_drones, num_points, time_period, num_pesticides = params['num_drones'], params['num_points'], params[
        'time_period'], params['num_pesticides']
    L, L_max, La, F_time = params['L'], params['L_max'], params['La'], params['F_time']
    Q_base, mu_vec, delta_vec = params['Q_base'], params['mu_vec'], params['delta_vec']
    zeta_standard, wind_speed, alpha, zeta_max_flow = params['zeta_standard'], params['wind_speed'], params['alpha'], \
    params['zeta_max_flow']
    dist_matrix, dist_base, V_drone, tau_task = params['dist_matrix'], params['dist_base'], params['V_drone'], params[
        'tau_task']
    w1, w2, yield_ref, path_ref = params['w1'], params['w2'], params['yield_ref'], params['path_ref']
    C_conflict, Delta_t_conflict = params['C_conflict'], params['Delta_t_conflict']

    roles = [];
    Q_j, zeta_j, eta_j = {}, {}, {};
    eta_values = np.maximum(0.1, 1 - alpha * wind_speed)
    for p in range(num_points):
        for t in range(time_period):
            for c in range(num_pesticides):
                if L[p, c] > 0:
                    zeta_dispense = zeta_standard[p, c] / eta_values[t]
                    if zeta_dispense <= zeta_max_flow:
                        role_tuple = (p, t, c);
                        roles.append(role_tuple);
                        j = len(roles) - 1
                        g_value = 1 / (np.sqrt(2 * np.pi) * delta_vec[c]) * np.exp(
                            -((t - mu_vec[c]) ** 2) / (2 * delta_vec[c] ** 2))
                        Q_j[j] = Q_base[p, c] * g_value;
                        zeta_j[j] = zeta_dispense;
                        eta_j[j] = eta_values[t]
    num_roles = len(roles);
    print(f"有效角色数量: {num_roles}")

    prob = pl.LpProblem("Drone_Assignment_MOO_VRP", pl.LpMaximize);
    solver = pl.getSolver('GUROBI_CMD', msg=True, timeLimit=300)
    T = pl.LpVariable.dicts("T", (range(num_drones), range(num_roles)), 0, 1, pl.LpBinary)
    Y = pl.LpVariable.dicts("Y", (range(num_drones), range(num_pesticides)), 0, 1, pl.LpBinary)
    role_nodes = list(range(num_roles)) + ['N'];
    X = pl.LpVariable.dicts("X", (range(num_drones), role_nodes, role_nodes), 0, 1, pl.LpBinary)

    yield_obj = pl.lpSum(Q_j[j] * T[i][j] for i in range(num_drones) for j in range(num_roles))
    path_cost_obj = pl.lpSum(
        dist_base[roles[k][0]] * X[i]['N'][k] for i in range(num_drones) for k in range(num_roles)) + \
                    pl.lpSum(dist_matrix[roles[j][0]][roles[k][0]] * X[i][j][k] for i in range(num_drones) for j in
                             range(num_roles) for k in range(num_roles) if j != k) + \
                    pl.lpSum(dist_base[roles[j][0]] * X[i][j]['N'] for i in range(num_drones) for j in range(num_roles))
    prob += w1 * (yield_obj / yield_ref) - w2 * (path_cost_obj / path_ref), "Normalized_Multi_Objective"

    # 添加所有约束
    for p in range(num_points):
        for c in range(num_pesticides):
            if L[p, c] > 0:
                relevant_roles = [j for j, role in enumerate(roles) if role[0] == p and role[2] == c];
                effective_spray = pl.lpSum(
                    T[i][j] * (zeta_j[j] * eta_j[j]) for i in range(num_drones) for j in relevant_roles)
                prob += effective_spray >= L[p, c];
                prob += effective_spray <= L_max[p, c]
    for i in range(num_drones):
        prob += pl.lpSum(Y[i][c] for c in range(num_pesticides)) <= 1
        for c in range(num_pesticides):
            relevant_roles = [j for j, role in enumerate(roles) if role[2] == c];
            prob += pl.lpSum(T[i][j] * zeta_j[j] for j in relevant_roles) <= La[i] * Y[i][c]
        for j in range(num_roles):
            c_j = roles[j][2];
            prob += T[i][j] <= Y[i][c_j]
    for i in range(num_drones):
        for t in range(time_period):
            relevant_roles = [j for j, role in enumerate(roles) if role[1] == t];
            prob += pl.lpSum(T[i][j] for j in relevant_roles) <= 1
    role_map_pct = defaultdict(list)
    for j, (p, t, c) in enumerate(roles): role_map_pct[(p, c)].append(j)
    for p in range(num_points):
        for c1, c2 in C_conflict:
            roles_c1, roles_c2 = role_map_pct.get((p, c1), []), role_map_pct.get((p, c2), [])
            for j1 in roles_c1:
                for j2 in roles_c2:
                    if abs(roles[j1][1] - roles[j2][1]) < Delta_t_conflict:
                        prob += pl.lpSum(T[i][j1] for i in range(num_drones)) + pl.lpSum(
                            T[i][j2] for i in range(num_drones)) <= 1, f"Conflict_p{p}_j{j1}_j{j2}"

    for i in range(num_drones):
        task_time = pl.lpSum(T[i][j] * tau_task for j in range(num_roles))
        travel_time = pl.lpSum((dist_base[roles[k][0]] / V_drone / 60) * X[i]['N'][k] for k in range(num_roles)) + \
                      pl.lpSum(
                          (dist_matrix[roles[j][0]][roles[k][0]] / V_drone / 60) * X[i][j][k] for j in range(num_roles)
                          for k in range(num_roles) if j != k) + \
                      pl.lpSum((dist_base[roles[j][0]] / V_drone / 60) * X[i][j]['N'] for j in range(num_roles))
        prob += task_time + travel_time <= F_time[i], f"Full_Endurance_i{i}"
        prob += pl.lpSum(X[i]['N'][k] for k in range(num_roles)) <= 1, f"Depart_From_Base_i{i}"
        for j in range(num_roles):
            prob += pl.lpSum(X[i][k][j] for k in role_nodes if k != j) == T[i][j], f"Flow_In_i{i}_j{j}"
            prob += pl.lpSum(X[i][j][k] for k in role_nodes if k != j) == T[i][j], f"Flow_Out_i{i}_j{j}"
            prob += X[i][j][j] == 0

    prob.solve(solver)
    if prob.status == pl.LpStatusOptimal:
        solution_t = {(i, j): T[i][j].varValue for i in range(num_drones) for j in range(num_roles)}
        solution_x = {(i, j, k): X[i][j][k].varValue for i in range(num_drones) for j in role_nodes for k in role_nodes
                      if j != k and X[i][j][k].varValue > 0.5}
        return solution_t, "Optimal", pl.value(prob.objective), roles, solution_x
    else:
        return None, pl.LpStatus[prob.status], None, None, None

# --- 4. Main 执行部分：依次运行三个实验 ---
if __name__ == '__main__':
    # !!! --- 请务必修改为您自己的桌面路径 --- !!!
    DESKTOP_PATH = r"H:\OneDrive\Desktop"

    params = generate_unified_scenario_data()
    plot_crop_distribution(params, DESKTOP_PATH)
    plot_ideal_timing_map(params, DESKTOP_PATH)

    all_results = {}

    # --- 实验一 ---
    print("\n\n" + "=" * 50 + "\n" + " " * 15 + "开始运行实验一：基准模型" + "\n" + "=" * 50)
    start_time = time.perf_counter()
    solution1, status1, objective1, roles1 = solve_baseline_assignment(params)
    end_time = time.perf_counter()
    print(f"实验一求解耗时: {end_time - start_time:.2f} 秒")
    if status1 == "Optimal":
        print(f"实验一最优目标值: {objective1}")
        path1 = generate_heuristic_path(solution1, roles1, params)
        all_results['Exp1_Baseline'] = {'solution_t': solution1, 'status': status1, 'objective': objective1,
                                        'roles': roles1, 'path': path1}
        plot_assignment_grid(solution1, roles1, params, DESKTOP_PATH, title="Experiment 1: Baseline Result")
        plot_flight_paths(solution1, None, roles1, params, DESKTOP_PATH, title="Experiment 1: Heuristic Flight Paths")

    # --- 实验二 ---
    print("\n\n" + "=" * 50 + "\n" + " " * 15 + "开始运行实验二：协同调度模型" + "\n" + "=" * 50)
    start_time = time.perf_counter()
    solution2, status2, objective2, roles2 = solve_agronomic_assignment(params)
    end_time = time.perf_counter()
    print(f"实验二求解耗时: {end_time - start_time:.2f} 秒")
    if status2 == "Optimal":
        print(f"实验二最优目标值: {objective2}")
        path2 = generate_heuristic_path(solution2, roles2, params)
        all_results['Exp2_Agronomic'] = {'solution_t': solution2, 'status': status2, 'objective': objective2,
                                         'roles': roles2, 'path': path2}
        plot_assignment_grid(solution2, roles2, params, DESKTOP_PATH, title="Experiment 2: Agronomic Conflict Result")
        plot_flight_paths(solution2, None, roles2, params, DESKTOP_PATH, title="Experiment 2: Heuristic Flight Paths")


    # --- 实验三 ---
    print("\n\n" + "=" * 50 + "\n" + " " * 15 + "开始运行实验三：多目标路径优化模型" + "\n" + "=" * 50)

    start_time = time.perf_counter()
    solution3, status3, objective3, roles3, solution_x3 = solve_moo_vrp_assignment(params)
    end_time = time.perf_counter()
    if status3 == "Optimal":
        print(f"实验三最优目标值 (归一化): {objective3}")
        path3 = {}
        for i in range(params['num_drones']):
            current_node = 'N';
            drone_path = ['N']
            for _ in range(len(roles3) + 2):
                found_next = False
                for k in list(range(len(roles3))) + ['N']:
                    if k != current_node and solution_x3.get((i, current_node, k), 0) > 0.5:
                        drone_path.append(k);
                        current_node = k;
                        found_next = True;
                        break
                if not found_next or current_node == 'N': break
            if len(drone_path) > 2: path3[i] = drone_path

        all_results['Exp3_MOO_VRP'] = {'solution_t': solution3, 'status': status3, 'objective': objective3,
                                       'roles': roles3, 'path': path3, 'solution_x': solution_x3}
        plot_assignment_grid(solution3, roles3, params, DESKTOP_PATH, title="Experiment 3: MOO-VRP Result")
        plot_flight_paths(solution3, solution_x3, roles3, params, DESKTOP_PATH,
                          title="Experiment 3: Optimal Flight Paths")

    if all_results:
        save_results_to_excel(params, all_results, DESKTOP_PATH)