
Gurobi 12.0.0 (win64, gurobi_cl) logging started Sun Sep 28 16:12:46 2025

Set parameter LicenseID to value 2584306
Set parameter MIPGap to value 0.05
Set parameter LogFile to value "gurobi.log"
Using license file c:\gurobi\gurobi.lic

Gurobi Optimizer version 12.0.0 build v12.0.0rc1 (win64 - Windows 11.0 (26100.2))
Copyright (c) 2024, Gurobi Optimization, LLC

Read LP format model from file C:\Users\<USER>\AppData\Local\Temp\d750f81b4bee450aa54fac0b67cb441a-pulp.lp
Reading time = 3.39 seconds
Normalized_Multi_Objective: 13028 rows, 2569616 columns, 7529632 nonzeros

Using Gurobi shared library D:\gurobi1200\win64\bin\gurobi120.dll

CPU model: AMD Ryzen 9 7900X 12-Core Processor, instruction set [SSE2|AVX|AVX2|AVX512]
Thread count: 12 physical cores, 24 logical processors, using up to 24 threads

Non-default parameters:
MIPGap  0.05

Optimize a model with 13028 rows, 2569616 columns and 7529632 nonzeros
Model fingerprint: 0xef1e0fc2
Variable types: 0 continuous, 2569616 integer (2569616 binary)
Coefficient statistics:
  Matrix range     [8e-02, 4e+01]
  Objective range  [2e-11, 3e-02]
  Bounds range     [1e+00, 1e+00]
  RHS range        [8e-01, 9e+01]
Presolve removed 3210 rows and 3200 columns (presolve time = 5s)...
Presolve removed 3210 rows and 3200 columns (presolve time = 10s)...
Presolve removed 3210 rows and 3200 columns (presolve time = 15s)...
Presolve removed 3210 rows and 3200 columns (presolve time = 20s)...
Presolve removed 3210 rows and 3200 columns (presolve time = 25s)...
Presolve removed 3210 rows and 3200 columns (presolve time = 30s)...
Presolve removed 3210 rows and 3200 columns (presolve time = 35s)...
Presolve removed 3210 rows and 3200 columns (presolve time = 40s)...
Presolve removed 3210 rows and 3200 columns (presolve time = 45s)...
Presolve removed 3210 rows and 3200 columns (presolve time = 50s)...
Presolve removed 3210 rows and 3200 columns (presolve time = 55s)...
Presolve removed 3210 rows and 3200 columns (presolve time = 60s)...
Presolve removed 3210 rows and 3200 columns (presolve time = 65s)...
Presolve removed 3210 rows and 3200 columns (presolve time = 70s)...
Presolve removed 3210 rows and 3200 columns (presolve time = 75s)...
Presolve removed 3210 rows and 3200 columns (presolve time = 80s)...
Presolve removed 3210 rows and 3200 columns (presolve time = 85s)...
