import numpy as np
import pulp as pl
from collections import defaultdict
import time
import math
import random
import matplotlib.pyplot as plt
from matplotlib.patches import Patch

# 问题：第一个是，作物分布好像有点不符合现实场景，现实场景中好像同种作物会在一片区域聚集分布或者以某些方式排列，而现在的作物分布好像有点太随机混乱。第二个是，现在能得到三个任务指派网格图，但我希望进一步改进，我需要生成四个这样的指派网格图，第一个是每种作物最适合喷洒的时间；第二至第四个是实验结果一到三的图，其中这些图的颜色条需要更改，改成绿色渐变，如果最后指派的刚好是最适合喷洒的时间，则该网格用深绿色表示，同理如果越远离最适合喷洒的时间，则颜色逐渐递减到浅绿。第三个点是，我其实想得到每个实验中无人机的飞行路径图，但是最后并没有得到。第四个点是我希望最后输出目标函数值，无人机飞行路径、指派矩阵以及初始的所有设定参数的值，结果以excel保存在桌面上，我的桌面地址是H:\OneDrive\Desktop，最好保存在一个excel的不同工作表，同时命名需要增加时间戳。

# --- 1. 核心：生成统一的、供三个实验共享的场景参数 ---
def generate_unified_scenario_data(num_drones=4):
    """
    生成一套完整的、统一的场景参数，供所有三个实验使用。
    """
    print("--- 正在生成统一的、基于真实场景的参数 ---")

    # 基本设定
    num_points = 15
    days = 10
    hours_per_day = 4  # 减少时间粒度以加快求解
    time_period = days * hours_per_day
    num_pesticides = 4

    # 无人机参数 (同质化)
    single_drone_capacity = 40
    single_drone_endurance_tasks = 12  # 简化续航（任务数）
    single_drone_endurance_time = 90  # 完整续航（分钟）

    params = {
        'num_drones': num_drones, 'num_points': num_points, 'time_period': time_period,
        'num_pesticides': num_pesticides,
        'La': np.full(num_drones, single_drone_capacity),
        'F_tasks': np.full(num_drones, single_drone_endurance_tasks),
        'F_time': np.full(num_drones, single_drone_endurance_time),
        'zeta_max_flow': 6.0, 'alpha': 0.05
    }

    # 地理与作物信息
    params['points_coords'] = {p: (random.uniform(0, 1000), random.uniform(0, 1000)) for p in range(num_points)}
    params['base_station_coord'] = (-100, -100)
    params['all_crop_assignments'] = {p: (0 if 0 <= p <= 4 else (1 if 5 <= p <= 9 else 2)) for p in range(num_points)}

    # 农艺学参数
    zeta_standard = np.zeros((params['num_points'], params['num_pesticides']))
    zeta_standard[0:5, 0] = 1.5;
    zeta_standard[5:10, 1] = 1.0;
    zeta_standard[5:10, 2] = 1.2;
    zeta_standard[10:15, 3] = 2.0
    params['zeta_standard'] = zeta_standard

    L = np.zeros((params['num_points'], params['num_pesticides']))
    L[0:5, 0] = zeta_standard[0:5, 0] * 1;
    L[5:10, 1] = zeta_standard[5:10, 1] * 2;
    L[5:10, 2] = zeta_standard[5:10, 2] * 1;
    L[10:15, 3] = zeta_standard[10:15, 3] * 1.5
    params['L'] = L
    params['L_max'] = np.ceil(L * 1.3)

    Q_base = np.zeros((params['num_points'], params['num_pesticides']))
    Q_base[0:5, 0] = 0.7;
    Q_base[5:10, 1] = 1.0;
    Q_base[5:10, 2] = 0.9;
    Q_base[10:15, 3] = 0.8
    params['Q_base'] = Q_base

    mu_days = np.array([5, 4, 3, 4]);
    delta_days = np.array([3, 2, 2.5, 3])
    params['mu_vec'] = (mu_days - 1) * hours_per_day + (hours_per_day / 2)
    params['delta_vec'] = delta_days * (hours_per_day / 2)

    # 环境与冲突参数
    daily_wind_pattern = np.array([1.5, 2.0, 3.5, 4.0, 7.5, 6.0, 4.0, 2.5, 1.0, 3.0])
    params['wind_speed'] = np.repeat(daily_wind_pattern, hours_per_day)
    params['C_conflict'] = [(1, 2)]
    params['Delta_t_conflict'] = hours_per_day  # 假设安全间隔为1天

    # 路径相关参数
    dist_matrix = np.zeros((num_points, num_points))
    for p1 in range(num_points):
        for p2 in range(num_points):
            dist_matrix[p1, p2] = math.sqrt((params['points_coords'][p1][0] - params['points_coords'][p2][0]) ** 2 + (
                        params['points_coords'][p1][1] - params['points_coords'][p2][1]) ** 2)
    params['dist_matrix'] = dist_matrix
    params['dist_base'] = {p: math.sqrt((params['points_coords'][p][0] - params['base_station_coord'][0]) ** 2 + (
                params['points_coords'][p][1] - params['base_station_coord'][1]) ** 2) for p in range(num_points)}
    params['V_drone'] = 10
    params['tau_task'] = 3

    # 多目标权重与归一化参考值
    params['w1'] = 0.5
    params['w2'] = 0.5
    params['yield_ref'] = 2.0  # 估算的效益参考值
    params['path_ref'] = 50000  # 估算的路径参考值

    print("--- 统一场景参数生成完毕 ---")
    return params


# --- 2. 可视化函数 ---
def plot_crop_distribution(params):
    """绘制基础的作物分布图"""
    print("绘制作物分布图...")
    plt.figure(figsize=(8, 8))
    plt.title('Crop Distribution Map')

    crop_colors = ['#95BE54', '#FAC5C6', '#A0B3DC']
    crop_labels = ['Corn (Crop 0)', 'Soybean (Crop 1)', 'Vegetable (Crop 2)']

    for p, crop_type in params['all_crop_assignments'].items():
        x, y = params['points_coords'][p]
        plt.scatter(x, y, color=crop_colors[crop_type], s=100)
        plt.text(x + 5, y + 5, f'P{p}', fontsize=9)

    base_x, base_y = params['base_station_coord']
    plt.scatter(base_x, base_y, color='black', marker='s', s=150, label='Base Station')

    legend_elements = [Patch(facecolor=crop_colors[i], label=crop_labels[i]) for i in range(3)]
    plt.legend(handles=legend_elements, loc='upper right')
    plt.xlabel('X Coordinate')
    plt.ylabel('Y Coordinate')
    plt.grid(True, linestyle='--', alpha=0.6)
    plt.gca().set_aspect('equal', adjustable='box')
    plt.show()


def plot_assignment_grid(solution, roles, params, title):
    """绘制最终的任务分配情况网格图"""
    if not solution:
        print(f"无法绘制分配图: {title} (无解)")
        return

    print(f"绘制任务分配图: {title}")
    num_drones = params['num_drones']
    time_period = params['time_period']

    grid = np.zeros((num_drones, time_period))
    drone_pesticide_map = {}

    for (i, j), val in solution.items():
        if val > 0.5:
            p, t, c = roles[j]
            grid[i, t] = p + 1
            if i not in drone_pesticide_map:
                drone_pesticide_map[i] = c

    fig, ax = plt.subplots(figsize=(15, 5))
    cax = ax.matshow(grid, cmap='viridis')
    fig.colorbar(cax, label='Task Point ID')

    for i in range(num_drones):
        for t in range(time_period):
            if grid[i, t] > 0:
                ax.text(t, i, f'P{int(grid[i, t] - 1)}', va='center', ha='center', color='white', fontsize=8)

    ax.set_xticks(np.arange(time_period))
    ax.set_yticks(np.arange(num_drones))
    ax.set_xticklabels(np.arange(time_period))
    ax.set_yticklabels([f'Drone {i} (Pest. {drone_pesticide_map.get(i, "N/A")})' for i in range(num_drones)])

    plt.xlabel('Time Slot')
    plt.ylabel('Drone')
    plt.title(title)
    plt.show()


def plot_flight_paths(solution_x, roles, params, title):
    """为实验三绘制无人机飞行路径图"""
    if not solution_x:
        print(f"无法绘制路径图: {title} (无解或无路径变量)")
        return

    print(f"绘制飞行路径图: {title}")
    num_drones = params['num_drones']
    points_coords = params['points_coords']
    base_coord = params['base_station_coord']

    plt.figure(figsize=(10, 10))
    for p, coord in points_coords.items():
        plt.scatter(coord[0], coord[1], c='gray', alpha=0.5)
        plt.text(coord[0] + 5, coord[1] + 5, f'P{p}', fontsize=9)
    plt.scatter(base_coord[0], base_coord[1], c='black', marker='s', s=150, label='Base Station')

    drone_colors = plt.cm.jet(np.linspace(0, 1, num_drones))

    for i in range(num_drones):
        path = []
        current_node = 'N'

        # Follow the path for a max number of steps to avoid infinite loops in case of model error
        for _ in range(len(roles) + 2):
            found_next = False
            for k in list(range(len(roles))) + ['N']:
                if k != current_node and solution_x.get((i, current_node, k), 0) > 0.5:
                    path.append(k)
                    current_node = k
                    found_next = True
                    break
            if not found_next or current_node == 'N':
                break

        if path:
            path_coords = [base_coord]
            for node in path:
                if node != 'N':
                    p = roles[node][0]
                    path_coords.append(points_coords[p])
                else:
                    path_coords.append(base_coord)

            px, py = zip(*path_coords)
            plt.plot(px, py, marker='o', linestyle='-', color=drone_colors[i], label=f'Drone {i} Path')
            for idx, coord in enumerate(path_coords):
                if idx > 0:
                    plt.annotate("", xy=coord, xytext=path_coords[idx - 1],
                                 arrowprops=dict(arrowstyle="->", color=drone_colors[i]))

    plt.title(title)
    plt.xlabel('X Coordinate')
    plt.ylabel('Y Coordinate')
    plt.legend()
    plt.grid(True, linestyle='--', alpha=0.6)
    plt.gca().set_aspect('equal', adjustable='box')
    plt.show()


# --- 3. 三个实验的模型求解函数 (使用Pulp) ---
def solve_baseline_assignment(params):
    """实验一：求解基准分配模型"""
    num_drones, num_points, time_period, num_pesticides = params['num_drones'], params['num_points'], params[
        'time_period'], params['num_pesticides']
    L, L_max, La, F_tasks = params['L'], params['L_max'], params['La'], params['F_tasks']
    Q_base, mu_vec, delta_vec = params['Q_base'], params['mu_vec'], params['delta_vec']
    zeta_standard, wind_speed, alpha, zeta_max_flow = params['zeta_standard'], params['wind_speed'], params['alpha'], \
    params['zeta_max_flow']

    roles = [];
    Q_j, zeta_j, eta_j = {}, {}, {}
    eta_values = np.maximum(0.1, 1 - alpha * wind_speed)
    for p in range(num_points):
        for t in range(time_period):
            for c in range(num_pesticides):
                if L[p, c] > 0:
                    zeta_dispense = zeta_standard[p, c] / eta_values[t]
                    if zeta_dispense <= zeta_max_flow:
                        role_tuple = (p, t, c);
                        roles.append(role_tuple);
                        j = len(roles) - 1
                        g_value = 1 / (np.sqrt(2 * np.pi) * delta_vec[c]) * np.exp(
                            -((t - mu_vec[c]) ** 2) / (2 * delta_vec[c] ** 2))
                        Q_j[j] = Q_base[p, c] * g_value;
                        zeta_j[j] = zeta_dispense;
                        eta_j[j] = eta_values[t]
    num_roles = len(roles);
    print(f"有效角色数量: {num_roles}")

    prob = pl.LpProblem("Drone_Assignment_Baseline", pl.LpMaximize);
    solver = pl.getSolver('GUROBI_CMD', msg=False)
    T = pl.LpVariable.dicts("T", (range(num_drones), range(num_roles)), 0, 1, pl.LpBinary)
    Y = pl.LpVariable.dicts("Y", (range(num_drones), range(num_pesticides)), 0, 1, pl.LpBinary)
    prob += pl.lpSum(Q_j[j] * T[i][j] for i in range(num_drones) for j in range(num_roles)), "Maximize_Yield"

    for p in range(num_points):
        for c in range(num_pesticides):
            if L[p, c] > 0:
                relevant_roles = [j for j, role in enumerate(roles) if role[0] == p and role[2] == c]
                effective_spray = pl.lpSum(
                    T[i][j] * (zeta_j[j] * eta_j[j]) for i in range(num_drones) for j in relevant_roles)
                prob += effective_spray >= L[p, c], f"Demand_Lower_p{p}_c{c}";
                prob += effective_spray <= L_max[p, c], f"Demand_Upper_p{p}_c{c}"
    for i in range(num_drones):
        prob += pl.lpSum(Y[i][c] for c in range(num_pesticides)) <= 1, f"Single_Pesticide_Type_i{i}"
        for c in range(num_pesticides):
            relevant_roles = [j for j, role in enumerate(roles) if role[2] == c]
            prob += pl.lpSum(T[i][j] * zeta_j[j] for j in relevant_roles) <= La[i] * Y[i][c], f"Capacity_i{i}_c{c}"
        for j in range(num_roles):
            c_j = roles[j][2];
            prob += T[i][j] <= Y[i][c_j], f"Link_T_Y_i{i}_j{j}"
    for i in range(num_drones):
        for t in range(time_period):
            relevant_roles = [j for j, role in enumerate(roles) if role[1] == t]
            prob += pl.lpSum(T[i][j] for j in relevant_roles) <= 1, f"Single_Task_i{i}_t{t}"
    for i in range(num_drones):
        prob += pl.lpSum(T[i][j] for j in range(num_roles)) <= F_tasks[i], f"Endurance_i{i}"

    prob.solve(solver)
    if prob.status == pl.LpStatusOptimal:
        solution = {(i, j): T[i][j].varValue for i in range(num_drones) for j in range(num_roles)}
        return solution, "Optimal", pl.value(prob.objective), roles
    else:
        return None, pl.LpStatus[prob.status], None, None


def solve_agronomic_assignment(params):
    """实验二：求解协同调度模型"""
    num_drones, num_points, time_period, num_pesticides = params['num_drones'], params['num_points'], params[
        'time_period'], params['num_pesticides']
    L, L_max, La, F_tasks = params['L'], params['L_max'], params['La'], params['F_tasks']
    Q_base, mu_vec, delta_vec = params['Q_base'], params['mu_vec'], params['delta_vec']
    zeta_standard, wind_speed, alpha, zeta_max_flow = params['zeta_standard'], params['wind_speed'], params['alpha'], \
    params['zeta_max_flow']
    C_conflict, Delta_t_conflict = params['C_conflict'], params['Delta_t_conflict']
    roles = [];
    Q_j, zeta_j, eta_j = {}, {}, {};
    eta_values = np.maximum(0.1, 1 - alpha * wind_speed)
    for p in range(num_points):
        for t in range(time_period):
            for c in range(num_pesticides):
                if L[p, c] > 0:
                    zeta_dispense = zeta_standard[p, c] / eta_values[t]
                    if zeta_dispense <= zeta_max_flow:
                        role_tuple = (p, t, c);
                        roles.append(role_tuple);
                        j = len(roles) - 1
                        g_value = 1 / (np.sqrt(2 * np.pi) * delta_vec[c]) * np.exp(
                            -((t - mu_vec[c]) ** 2) / (2 * delta_vec[c] ** 2))
                        Q_j[j] = Q_base[p, c] * g_value;
                        zeta_j[j] = zeta_dispense;
                        eta_j[j] = eta_values[t]
    num_roles = len(roles);
    print(f"有效角色数量: {num_roles}")

    prob = pl.LpProblem("Drone_Assignment_Agronomic", pl.LpMaximize);
    solver = pl.getSolver('GUROBI_CMD', msg=False)
    T = pl.LpVariable.dicts("T", (range(num_drones), range(num_roles)), 0, 1, pl.LpBinary)
    Y = pl.LpVariable.dicts("Y", (range(num_drones), range(num_pesticides)), 0, 1, pl.LpBinary)
    prob += pl.lpSum(Q_j[j] * T[i][j] for i in range(num_drones) for j in range(num_roles)), "Maximize_Yield"

    for p in range(num_points):
        for c in range(num_pesticides):
            if L[p, c] > 0:
                relevant_roles = [j for j, role in enumerate(roles) if role[0] == p and role[2] == c];
                effective_spray = pl.lpSum(
                    T[i][j] * (zeta_j[j] * eta_j[j]) for i in range(num_drones) for j in relevant_roles)
                prob += effective_spray >= L[p, c];
                prob += effective_spray <= L_max[p, c]
    for i in range(num_drones):
        prob += pl.lpSum(Y[i][c] for c in range(num_pesticides)) <= 1
        for c in range(num_pesticides):
            relevant_roles = [j for j, role in enumerate(roles) if role[2] == c];
            prob += pl.lpSum(T[i][j] * zeta_j[j] for j in relevant_roles) <= La[i] * Y[i][c]
        for j in range(num_roles):
            c_j = roles[j][2];
            prob += T[i][j] <= Y[i][c_j]
    for i in range(num_drones):
        for t in range(time_period):
            relevant_roles = [j for j, role in enumerate(roles) if role[1] == t];
            prob += pl.lpSum(T[i][j] for j in relevant_roles) <= 1
    for i in range(num_drones):
        prob += pl.lpSum(T[i][j] for j in range(num_roles)) <= F_tasks[i]

    role_map_pct = defaultdict(list)
    for j, (p, t, c) in enumerate(roles): role_map_pct[(p, c)].append(j)
    for p in range(num_points):
        for c1, c2 in C_conflict:
            roles_c1, roles_c2 = role_map_pct.get((p, c1), []), role_map_pct.get((p, c2), [])
            for j1 in roles_c1:
                for j2 in roles_c2:
                    if abs(roles[j1][1] - roles[j2][1]) < Delta_t_conflict:
                        prob += pl.lpSum(T[i][j1] for i in range(num_drones)) + pl.lpSum(
                            T[i][j2] for i in range(num_drones)) <= 1, f"Conflict_p{p}_j{j1}_j{j2}"

    prob.solve(solver)
    if prob.status == pl.LpStatusOptimal:
        solution = {(i, j): T[i][j].varValue for i in range(num_drones) for j in range(num_roles)}
        return solution, "Optimal", pl.value(prob.objective), roles
    else:
        return None, pl.LpStatus[prob.status], None, None


def solve_moo_vrp_assignment(params):
    """实验三：求解多目标路径优化模型"""
    num_drones, num_points, time_period, num_pesticides = params['num_drones'], params['num_points'], params[
        'time_period'], params['num_pesticides']
    L, L_max, La, F_time = params['L'], params['L_max'], params['La'], params['F_time']
    Q_base, mu_vec, delta_vec = params['Q_base'], params['mu_vec'], params['delta_vec']
    zeta_standard, wind_speed, alpha, zeta_max_flow = params['zeta_standard'], params['wind_speed'], params['alpha'], \
    params['zeta_max_flow']
    dist_matrix, dist_base, V_drone, tau_task = params['dist_matrix'], params['dist_base'], params['V_drone'], params[
        'tau_task']
    w1, w2, yield_ref, path_ref = params['w1'], params['w2'], params['yield_ref'], params['path_ref']
    C_conflict, Delta_t_conflict = params['C_conflict'], params['Delta_t_conflict']  # 也加入冲突约束

    roles = [];
    Q_j, zeta_j, eta_j = {}, {}, {};
    eta_values = np.maximum(0.1, 1 - alpha * wind_speed)
    for p in range(num_points):
        for t in range(time_period):
            for c in range(num_pesticides):
                if L[p, c] > 0:
                    zeta_dispense = zeta_standard[p, c] / eta_values[t]
                    if zeta_dispense <= zeta_max_flow:
                        role_tuple = (p, t, c);
                        roles.append(role_tuple);
                        j = len(roles) - 1
                        g_value = 1 / (np.sqrt(2 * np.pi) * delta_vec[c]) * np.exp(
                            -((t - mu_vec[c]) ** 2) / (2 * delta_vec[c] ** 2))
                        Q_j[j] = Q_base[p, c] * g_value;
                        zeta_j[j] = zeta_dispense;
                        eta_j[j] = eta_values[t]
    num_roles = len(roles);
    print(f"有效角色数量: {num_roles}")

    prob = pl.LpProblem("Drone_Assignment_MOO_VRP", pl.LpMaximize);
    solver = pl.getSolver('GUROBI_CMD', msg=True, timeLimit=300)  # 增加求解时间限制
    T = pl.LpVariable.dicts("T", (range(num_drones), range(num_roles)), 0, 1, pl.LpBinary)
    Y = pl.LpVariable.dicts("Y", (range(num_drones), range(num_pesticides)), 0, 1, pl.LpBinary)
    role_nodes = list(range(num_roles)) + ['N'];
    X = pl.LpVariable.dicts("X", (range(num_drones), role_nodes, role_nodes), 0, 1, pl.LpBinary)

    yield_obj = pl.lpSum(Q_j[j] * T[i][j] for i in range(num_drones) for j in range(num_roles))
    path_cost_obj = pl.lpSum(
        dist_base[roles[k][0]] * X[i]['N'][k] for i in range(num_drones) for k in range(num_roles)) + \
                    pl.lpSum(dist_matrix[roles[j][0]][roles[k][0]] * X[i][j][k] for i in range(num_drones) for j in
                             range(num_roles) for k in range(num_roles) if j != k) + \
                    pl.lpSum(dist_base[roles[j][0]] * X[i][j]['N'] for i in range(num_drones) for j in range(num_roles))
    prob += w1 * (yield_obj / yield_ref) - w2 * (path_cost_obj / path_ref), "Normalized_Multi_Objective"

    # 添加所有约束
    for p in range(num_points):
        for c in range(num_pesticides):
            if L[p, c] > 0:
                relevant_roles = [j for j, role in enumerate(roles) if role[0] == p and role[2] == c];
                effective_spray = pl.lpSum(
                    T[i][j] * (zeta_j[j] * eta_j[j]) for i in range(num_drones) for j in relevant_roles)
                prob += effective_spray >= L[p, c];
                prob += effective_spray <= L_max[p, c]
    for i in range(num_drones):
        prob += pl.lpSum(Y[i][c] for c in range(num_pesticides)) <= 1
        for c in range(num_pesticides):
            relevant_roles = [j for j, role in enumerate(roles) if role[2] == c];
            prob += pl.lpSum(T[i][j] * zeta_j[j] for j in relevant_roles) <= La[i] * Y[i][c]
        for j in range(num_roles):
            c_j = roles[j][2];
            prob += T[i][j] <= Y[i][c_j]
    for i in range(num_drones):
        for t in range(time_period):
            relevant_roles = [j for j, role in enumerate(roles) if role[1] == t];
            prob += pl.lpSum(T[i][j] for j in relevant_roles) <= 1
    role_map_pct = defaultdict(list)
    for j, (p, t, c) in enumerate(roles): role_map_pct[(p, c)].append(j)
    for p in range(num_points):
        for c1, c2 in C_conflict:
            roles_c1, roles_c2 = role_map_pct.get((p, c1), []), role_map_pct.get((p, c2), [])
            for j1 in roles_c1:
                for j2 in roles_c2:
                    if abs(roles[j1][1] - roles[j2][1]) < Delta_t_conflict:
                        prob += pl.lpSum(T[i][j1] for i in range(num_drones)) + pl.lpSum(
                            T[i][j2] for i in range(num_drones)) <= 1, f"Conflict_p{p}_j{j1}_j{j2}"

    for i in range(num_drones):
        task_time = pl.lpSum(T[i][j] * tau_task for j in range(num_roles))
        travel_time = pl.lpSum((dist_base[roles[k][0]] / V_drone / 60) * X[i]['N'][k] for k in range(num_roles)) + \
                      pl.lpSum(
                          (dist_matrix[roles[j][0]][roles[k][0]] / V_drone / 60) * X[i][j][k] for j in range(num_roles)
                          for k in range(num_roles) if j != k) + \
                      pl.lpSum((dist_base[roles[j][0]] / V_drone / 60) * X[i][j]['N'] for j in range(num_roles))
        prob += task_time + travel_time <= F_time[i], f"Full_Endurance_i{i}"
        prob += pl.lpSum(X[i]['N'][k] for k in range(num_roles)) <= 1, f"Depart_From_Base_i{i}"
        for j in range(num_roles):
            prob += pl.lpSum(X[i][k][j] for k in role_nodes if k != j) == T[i][j], f"Flow_In_i{i}_j{j}"
            prob += pl.lpSum(X[i][j][k] for k in role_nodes if k != j) == T[i][j], f"Flow_Out_i{i}_j{j}"
            prob += X[i][j][j] == 0

    prob.solve(solver)
    if prob.status == pl.LpStatusOptimal:
        solution_t = {(i, j): T[i][j].varValue for i in range(num_drones) for j in range(num_roles)}
        solution_x = {(i, j, k): X[i][j][k].varValue for i in range(num_drones) for j in role_nodes for k in role_nodes
                      if j != k and X[i][j][k].varValue > 0.5}
        return solution_t, "Optimal", pl.value(prob.objective), roles, solution_x
    else:
        return None, pl.LpStatus[prob.status], None, None, None


# --- 4. Main 执行部分：依次运行三个实验 ---
if __name__ == '__main__':
    params = generate_unified_scenario_data(num_drones=4)
    plot_crop_distribution(params)

    # --- 实验一 ---
    print("\n\n" + "=" * 50 + "\n" + " " * 15 + "开始运行实验一：基准模型" + "\n" + "=" * 50)
    start_time = time.perf_counter()
    solution1, status1, objective1, roles1 = solve_baseline_assignment(params)
    end_time = time.perf_counter()
    print(f"实验一求解耗时: {end_time - start_time:.2f} 秒")
    if status1 == "Optimal":
        print(f"实验一最优目标值: {objective1}")
        plot_assignment_grid(solution1, roles1, params, title="Experiment 1: Baseline Assignment Result")

    # --- 实验二 ---
    print("\n\n" + "=" * 50 + "\n" + " " * 15 + "开始运行实验二：协同调度模型" + "\n" + "=" * 50)
    start_time = time.perf_counter()
    solution2, status2, objective2, roles2 = solve_agronomic_assignment(params)
    end_time = time.perf_counter()
    print(f"实验二求解耗时: {end_time - start_time:.2f} 秒")
    if status2 == "Optimal":
        print(f"实验二最优目标值: {objective2}")
        plot_assignment_grid(solution2, roles2, params, title="Experiment 2: Agronomic Conflict Result")

    # --- 实验三 ---
    print("\n\n" + "=" * 50 + "\n" + " " * 15 + "开始运行实验三：多目标路径优化模型" + "\n" + "=" * 50)
    print("注意：实验三为复杂的VRP模型，求解可能需要较长时间...")

    # --- !!! 注意：默认注释掉实验三，因为它非常耗时 !!! ---
    # --- !!! 当您准备好后，可以取消以下代码行的注释来运行它 !!! ---

    start_time = time.perf_counter()
    solution3, status3, objective3, roles3, solution_x3 = solve_moo_vrp_assignment(params)
    end_time = time.perf_counter()
    print(f"实验三求解耗си: {end_time - start_time:.2f} 秒")
    if status3 == "Optimal":
        print(f"实验三最优目标值: {objective3}")
        plot_assignment_grid(solution3, roles3, params, title="Experiment 3: MOO-VRP Assignment Result")
        plot_flight_paths(solution_x3, roles3, params, title="Experiment 3: Drone Flight Paths")