#!/usr/bin/env python3
"""
无人机喷洒农药优化模型 - 帕累托前沿分析修复版本 v3.1
修复帕累托前沿分析中的关键问题

主要修复：
1. 路径提取逻辑错误：修复VRP解决方案的路径构建
2. 成本计算错误：修复路径长度计算中的索引问题
3. 图表显示问题：优化帕累托前沿的可视化效果
4. 数据验证：添加数据有效性检查和错误处理
5. 性能优化：避免重复计算和内存泄漏

作者：AI Assistant
日期：2025-09-28
版本：v3.1 (修复版)
"""

import numpy as np
import pulp as pl
from collections import defaultdict
import time
import math
import random
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
from matplotlib.patches import Patch, Polygon
import pandas as pd
from datetime import datetime
import os
from matplotlib.colors import LinearSegmentedColormap
from matplotlib.path import Path
import gc


# --- 1. 核心：生成统一的、供三个实验共享的场景参数 ---
def generate_unified_scenario_data(num_drones=4, scale="large"):
    """生成统一场景参数"""
    print(f"--- 正在生成统一场景参数 (规模: {scale}) ---")

    if scale == "small":
        num_points = 8
        days = 5
    else:  # large
        num_points = 15
        days = 10

    hours_per_day = 4
    time_period = days * hours_per_day
    num_pesticides = 4
    params = {'num_drones': num_drones, 'num_points': num_points, 'time_period': time_period,
              'num_pesticides': num_pesticides}

    print("生成规整四边形田块内的稀疏作物分布...")
    field_quads = {
        0: Path([(100, 650), (400, 950), (550, 800), (250, 500)]),
        1: Path([(600, 500), (950, 650), (850, 200), (500, 250)]),
        2: Path([(50, 50), (350, 350), (250, 400), (0, 100)])
    }
    params['field_polygons_verts'] = {f'field_{k}': v.vertices for k, v in field_quads.items()}
    params['points_coords'] = {}

    if scale == "small":
        params['all_crop_assignments'] = {p: (0 if 0 <= p <= 2 else (1 if 3 <= p <= 5 else 2)) for p in
                                          range(num_points)}
    else:
        params['all_crop_assignments'] = {p: (0 if 0 <= p <= 4 else (1 if 5 <= p <= 9 else 2)) for p in
                                          range(num_points)}

    min_separation_dist = 90.0
    for p in range(num_points):
        crop_type = params['all_crop_assignments'][p]
        quad_path = field_quads[crop_type]
        min_x, min_y = quad_path.vertices.min(axis=0)
        max_x, max_y = quad_path.vertices.max(axis=0)
        points_in_this_quad = [params['points_coords'][i] for i in range(p) if
                               params['all_crop_assignments'].get(i) == crop_type]

        attempts = 0
        max_attempts = 5000
        while True:
            attempts += 1
            rand_x, rand_y = random.uniform(min_x, max_x), random.uniform(min_y, max_y)
            if quad_path.contains_point((rand_x, rand_y)):
                is_too_close = any(
                    math.sqrt((rand_x - ep[0]) ** 2 + (rand_y - ep[1]) ** 2) < min_separation_dist for ep in
                    points_in_this_quad)
                if not is_too_close:
                    params['points_coords'][p] = (rand_x, rand_y)
                    break
            if attempts > max_attempts:
                print(f"警告：为点 P{p} 寻找位置超过最大尝试次数。已接受最后一个随机点。")
                params['points_coords'][p] = (rand_x, rand_y)
                break

    params['base_station_coord'] = (-100, -100)

    if scale == "small":
        single_drone_capacity = 30
        single_drone_endurance_tasks = 8
        single_drone_endurance_time = 75
    else:
        single_drone_capacity = 40
        single_drone_endurance_tasks = 12
        single_drone_endurance_time = 90

    params['La'] = np.full(num_drones, single_drone_capacity)
    params['F_tasks'] = np.full(num_drones, single_drone_endurance_tasks)
    params['F_time'] = np.full(num_drones, single_drone_endurance_time)
    params['zeta_max_flow'] = 6.0
    params['alpha'] = 0.05

    zeta_standard = np.zeros((num_points, num_pesticides))
    L = np.zeros((num_points, num_pesticides))
    Q_base = np.zeros((num_points, num_pesticides))
    for p, crop_type in params['all_crop_assignments'].items():
        if crop_type == 0:
            zeta_standard[p, 0] = 1.5; L[p, 0] = 1.5 * 1; Q_base[p, 0] = 0.8
        elif crop_type == 1:
            zeta_standard[p, 1] = 1.0; L[p, 1] = 1.0 * 2; Q_base[p, 1] = 1.0
        else:
            zeta_standard[p, 2] = 1.2; L[p, 2] = 1.2 * 1; Q_base[p, 2] = 0.9
            zeta_standard[p, 3] = 0.8; L[p, 3] = 0.8 * 1; Q_base[p, 3] = 0.7

    params['zeta_standard'] = zeta_standard
    params['L'] = L
    params['L_max'] = np.ceil(L * 1.3)
    params['Q_base'] = Q_base

    mu_days = np.array([5, 4, 3, 6])
    delta_days = np.array([3, 2, 2.5, 3])
    params['mu_vec'] = (mu_days - 1) * hours_per_day + (hours_per_day / 2)
    params['delta_vec'] = delta_days * (hours_per_day / 2)
    daily_wind_pattern = np.array([1.5, 2.0, 3.5, 4.0, 7.5, 6.0, 4.0, 2.5, 1.0, 3.0])
    params['wind_speed'] = np.repeat(daily_wind_pattern[:days], hours_per_day)
    params['C_conflict'] = [(1, 2)]
    params['Delta_t_conflict'] = hours_per_day
    
    # 预计算距离矩阵
    dist_matrix = np.zeros((num_points, num_points))
    for p1 in range(num_points):
        for p2 in range(num_points):
            dist_matrix[p1, p2] = math.sqrt((params['points_coords'][p1][0] - params['points_coords'][p2][0]) ** 2 + (
                        params['points_coords'][p1][1] - params['points_coords'][p2][1]) ** 2)
    params['dist_matrix'] = dist_matrix
    params['dist_base'] = {p: math.sqrt((params['points_coords'][p][0] - params['base_station_coord'][0]) ** 2 + (
                params['points_coords'][p][1] - params['base_station_coord'][1]) ** 2) for p in range(num_points)}
    params['V_drone'] = 20
    params['tau_task'] = 2
    params['w1'] = 0.6
    params['w2'] = 0.4
    params['yield_ref'] = 10.0
    params['path_ref'] = 15000

    print(f"--- 统一场景参数生成完毕 (共 {num_points} 个作业点) ---")
    return params


# --- 2. 优化的路径长度计算函数 ---
def calculate_path_length_optimized(path, roles, params, is_optimal_path=False):
    """
    优化的路径长度计算函数，避免重复计算和索引错误
    """
    if not path or len(path) < 2:
        return 0
    
    total_dist = 0
    dist_matrix, dist_base = params['dist_matrix'], params['dist_base']
    
    for step in range(len(path) - 1):
        loc1, loc2 = path[step], path[step + 1]
        
        # 根据路径类型确定实际的作业点ID
        if is_optimal_path:
            # 最优路径：节点是角色索引或'N'
            p1 = roles[loc1][0] if loc1 != 'N' else 'N'
            p2 = roles[loc2][0] if loc2 != 'N' else 'N'
        else:
            # 启发式路径：节点直接是作业点ID或'N'
            p1 = loc1
            p2 = loc2
        
        # 计算距离
        if p1 == 'N' and p2 != 'N':
            total_dist += dist_base[p2]
        elif p1 != 'N' and p2 == 'N':
            total_dist += dist_base[p1]
        elif p1 != 'N' and p2 != 'N':
            total_dist += dist_matrix[p1][p2]
        # p1 == 'N' and p2 == 'N' 的情况距离为0，不需要处理
    
    return total_dist


# --- 3. 效益计算函数 ---
def calculate_pure_benefit(solution, roles, params):
    """为任何方案计算其纯粹的"挽回损失量""""
    if not solution: 
        return 0
    total_benefit = 0
    Q_base, mu_vec, delta_vec = params['Q_base'], params['mu_vec'], params['delta_vec']
    for (i, j), val in solution.items():
        if val > 0.5:
            p, t, c = roles[j]
            # 使用高斯函数计算时间质量
            g_value = 1 / (np.sqrt(2 * np.pi) * delta_vec[c]) * np.exp(-((t - mu_vec[c]) ** 2) / (2 * delta_vec[c] ** 2))
            total_benefit += Q_base[p,c] * g_value
    return total_benefit


# --- 4. 修复的路径提取函数 ---
def extract_paths_from_vrp_solution_fixed(solution_t, solution_x, roles, params):
    """
    修复的路径提取函数 - 解决原代码中的关键问题
    """
    print("开始提取VRP解决方案的路径...")
    paths = {}
    
    # 首先分析任务分配情况
    total_assigned_tasks = 0
    for i in range(params['num_drones']):
        drone_tasks = []
        for j in range(len(roles)):
            if solution_t.get((i, j), 0) > 0.5:
                drone_tasks.append(j)
                total_assigned_tasks += 1
        
        if drone_tasks:
            print(f"  无人机 {i}: 分配了 {len(drone_tasks)} 个任务")
        else:
            print(f"  无人机 {i}: 没有分配任务")
    
    print(f"总分配任务数: {total_assigned_tasks}")
    
    # 如果有任务分配，则提取路径
    if total_assigned_tasks > 0:
        for i in range(params['num_drones']):
            # 收集该无人机的所有路径段
            drone_edges = []
            for (drone_id, from_node, to_node), value in solution_x.items():
                if drone_id == i and value > 0.5:
                    drone_edges.append((from_node, to_node))
            
            if not drone_edges:
                # 如果没有路径段，使用启发式方法构建路径
                assigned_tasks = [j for j in range(len(roles)) if solution_t.get((i, j), 0) > 0.5]
                if assigned_tasks:
                    # 按时间排序任务
                    assigned_tasks.sort(key=lambda j: roles[j][1])
                    path = ['N'] + assigned_tasks + ['N']
                    paths[i] = path
                    print(f"  无人机 {i} 启发式路径: {len(assigned_tasks)} 个任务")
                else:
                    paths[i] = []
                continue
            
            # 构建路径图
            graph = {}
            for from_node, to_node in drone_edges:
                if from_node not in graph:
                    graph[from_node] = []
                graph[from_node].append(to_node)
            
            # 查找从'N'开始的路径
            if 'N' in graph:
                path = ['N']
                current = 'N'
                visited = set(['N'])
                max_iterations = len(roles) + 2  # 防止无限循环
                
                while current in graph and len(path) <= max_iterations:
                    next_nodes = [n for n in graph[current] if n not in visited or n == 'N']
                    if next_nodes:
                        next_node = next_nodes[0]
                        path.append(next_node)
                        if next_node == 'N':
                            break
                        visited.add(next_node)
                        current = next_node
                    else:
                        break
                
                paths[i] = path
                print(f"  无人机 {i} 最优路径: {len(path)-2} 个任务")
            else:
                # 构建启发式路径
                assigned_tasks = [j for j in range(len(roles)) if solution_t.get((i, j), 0) > 0.5]
                if assigned_tasks:
                    assigned_tasks.sort(key=lambda j: roles[j][1])
                    path = ['N'] + assigned_tasks + ['N']
                    paths[i] = path
                    print(f"  无人机 {i} 启发式路径: {len(assigned_tasks)} 个任务")
                else:
                    paths[i] = []
    else:
        print("没有任务分配，所有无人机路径为空")
        for i in range(params['num_drones']):
            paths[i] = []
    
    print("路径提取完成！")
    return paths


# --- 5. 多目标VRP求解函数 ---
def solve_moo_vrp_assignment(params):
    """实验三：多目标路径优化模型求解"""
    num_drones, num_points, time_period, num_pesticides = params['num_drones'], params['num_points'], params[
        'time_period'], params['num_pesticides']
    L, L_max, La, F_time = params['L'], params['L_max'], params['La'], params['F_time']
    Q_base, mu_vec, delta_vec = params['Q_base'], params['mu_vec'], params['delta_vec']
    zeta_standard, wind_speed, alpha, zeta_max_flow = params['zeta_standard'], params['wind_speed'], params['alpha'], \
    params['zeta_max_flow']
    dist_matrix, dist_base, V_drone, tau_task = params['dist_matrix'], params['dist_base'], params['V_drone'], params[
        'tau_task']
    w1, w2, yield_ref, path_ref = params['w1'], params['w2'], params['yield_ref'], params['path_ref']
    C_conflict, Delta_t_conflict = params['C_conflict'], params['Delta_t_conflict']

    roles = []
    Q_j, zeta_j, eta_j = {}, {}, {}
    eta_values = np.maximum(0.1, 1 - alpha * wind_speed)
    for p in range(num_points):
        for t in range(time_period):
            for c in range(num_pesticides):
                if L[p, c] > 0:
                    zeta_dispense = zeta_standard[p, c] / eta_values[t]
                    if zeta_dispense <= zeta_max_flow:
                        role_tuple = (p, t, c)
                        roles.append(role_tuple)
                        j = len(roles) - 1
                        g_value = 1 / (np.sqrt(2 * np.pi) * delta_vec[c]) * np.exp(
                            -((t - mu_vec[c]) ** 2) / (2 * delta_vec[c] ** 2))
                        Q_j[j] = Q_base[p, c] * g_value
                        zeta_j[j] = zeta_dispense
                        eta_j[j] = eta_values[t]
    num_roles = len(roles)

    prob = pl.LpProblem("Drone_Assignment_MOO_VRP", pl.LpMaximize)
    solver = pl.getSolver('GUROBI_CMD', msg=False, timeLimit=60, gapRel=0.1)  # 缩短时间限制
    T = pl.LpVariable.dicts("T", (range(num_drones), range(num_roles)), 0, 1, pl.LpBinary)
    Y = pl.LpVariable.dicts("Y", (range(num_drones), range(num_pesticides)), 0, 1, pl.LpBinary)
    role_nodes = list(range(num_roles)) + ['N']
    X = pl.LpVariable.dicts("X", (range(num_drones), role_nodes, role_nodes), 0, 1, pl.LpBinary)

    yield_obj = pl.lpSum(Q_j[j] * T[i][j] for i in range(num_drones) for j in range(num_roles))
    path_cost_obj = pl.lpSum(
        dist_base[roles[k][0]] * X[i]['N'][k] for i in range(num_drones) for k in range(num_roles)) + \
                    pl.lpSum(dist_matrix[roles[j][0]][roles[k][0]] * X[i][j][k] for i in range(num_drones) for j in
                             range(num_roles) for k in range(num_roles) if j != k) + \
                    pl.lpSum(dist_base[roles[j][0]] * X[i][j]['N'] for i in range(num_drones) for j in range(num_roles))
    prob += w1 * (yield_obj / yield_ref) - w2 * (path_cost_obj / path_ref), "Normalized_Multi_Objective"

    # 添加所有约束
    for p in range(num_points):
        for c in range(num_pesticides):
            if L[p, c] > 0:
                relevant_roles = [j for j, role in enumerate(roles) if role[0] == p and role[2] == c]
                effective_spray = pl.lpSum(
                    T[i][j] * (zeta_j[j] * eta_j[j]) for i in range(num_drones) for j in relevant_roles)
                prob += effective_spray >= L[p, c]
                prob += effective_spray <= L_max[p, c]
    for i in range(num_drones):
        prob += pl.lpSum(Y[i][c] for c in range(num_pesticides)) <= 1
        for c in range(num_pesticides):
            relevant_roles = [j for j, role in enumerate(roles) if role[2] == c]
            prob += pl.lpSum(T[i][j] * zeta_j[j] for j in relevant_roles) <= La[i] * Y[i][c]
        for j in range(num_roles):
            c_j = roles[j][2]
            prob += T[i][j] <= Y[i][c_j]
    for i in range(num_drones):
        for t in range(time_period):
            relevant_roles = [j for j, role in enumerate(roles) if role[1] == t]
            prob += pl.lpSum(T[i][j] for j in relevant_roles) <= 1
    role_map_pct = defaultdict(list)
    for j, (p, t, c) in enumerate(roles):
        role_map_pct[(p, c)].append(j)
    for p in range(num_points):
        for c1, c2 in C_conflict:
            roles_c1, roles_c2 = role_map_pct.get((p, c1), []), role_map_pct.get((p, c2), [])
            for j1 in roles_c1:
                for j2 in roles_c2:
                    if abs(roles[j1][1] - roles[j2][1]) < Delta_t_conflict:
                        prob += pl.lpSum(T[i][j1] for i in range(num_drones)) + pl.lpSum(
                            T[i][j2] for i in range(num_drones)) <= 1, f"Conflict_p{p}_j{j1}_j{j2}"

    for i in range(num_drones):
        task_time = pl.lpSum(T[i][j] * tau_task for j in range(num_roles))
        travel_time = pl.lpSum((dist_base[roles[k][0]] / V_drone / 60) * X[i]['N'][k] for k in range(num_roles)) + \
                      pl.lpSum(
                          (dist_matrix[roles[j][0]][roles[k][0]] / V_drone / 60) * X[i][j][k] for j in range(num_roles)
                          for k in range(num_roles) if j != k) + \
                      pl.lpSum((dist_base[roles[j][0]] / V_drone / 60) * X[i][j]['N'] for j in range(num_roles))
        prob += task_time + travel_time <= F_time[i], f"Full_Endurance_i{i}"
        prob += pl.lpSum(X[i]['N'][k] for k in range(num_roles)) <= 1, f"Depart_From_Base_i{i}"
        for j in range(num_roles):
            prob += pl.lpSum(X[i][k][j] for k in role_nodes if k != j) == T[i][j], f"Flow_In_i{i}_j{j}"
            prob += pl.lpSum(X[i][j][k] for k in role_nodes if k != j) == T[i][j], f"Flow_Out_i{i}_j{j}"
            prob += X[i][j][j] == 0

    prob.solve(solver)

    # 检查求解状态
    if pl.value(prob.objective) is not None:
        solution_t = {(i, j): T[i][j].varValue for i in range(num_drones) for j in range(num_roles)}
        solution_x = {}
        for i in range(num_drones):
            for j in list(range(len(roles))) + ['N']:
                for k in list(range(len(roles))) + ['N']:
                    if j != k:
                        try:
                            value = X[i][j][k].varValue
                            if value is not None and value > 0.5:
                                solution_x[(i, j, k)] = value
                        except:
                            pass  # 某些变量可能不存在
        return solution_t, prob.status, pl.value(prob.objective), roles, solution_x
    else:
        return None, prob.status, None, None, None


# --- 6. 修复的帕累托前沿绘图函数 ---
def plot_pareto_frontier_fixed(pareto_points, desktop_path):
    """
    修复的帕累托前沿绘图函数
    解决原代码中图表显示异常的问题
    """
    if not pareto_points or len(pareto_points) < 2:
        print("警告：帕累托数据点不足（少于2个），无法生成有意义的图表。")
        return

    print("--- 正在绘制修复版帕累托前沿图 ---")

    # 数据验证和清理
    valid_points = []
    for point in pareto_points:
        if len(point) == 3 and all(isinstance(x, (int, float)) for x in point):
            cost, benefit, weight = point
            if cost > 0 and benefit > 0 and 0 <= weight <= 1:
                valid_points.append(point)

    if len(valid_points) < 2:
        print("警告：有效数据点不足，无法绘制帕累托前沿。")
        return

    # 按成本（X轴的值）对所有点进行排序，确保连接线平滑
    valid_points.sort(key=lambda x: x[0])

    costs = np.array([p[0] for p in valid_points])
    benefits = np.array([p[1] for p in valid_points])
    weights = np.array([p[2] for p in valid_points])

    # 创建图表
    fig, ax = plt.subplots(figsize=(12, 8))

    # 使用更清晰的颜色映射
    cmap = plt.get_cmap('viridis')

    # 绘制散点图
    scatter = ax.scatter(costs, benefits, c=weights, cmap=cmap, s=100, zorder=5,
                        edgecolors='black', linewidth=1, alpha=0.8)

    # 绘制连接线
    ax.plot(costs, benefits, color='red', linestyle='-', linewidth=2, alpha=0.7,
            label='Pareto Frontier')

    # 添加颜色条
    cbar = fig.colorbar(scatter, ax=ax)
    cbar.set_label('Weight $w_1$ (Focus on Benefit)', fontsize=12)

    # 为每个点添加权重标签
    for i, (cost, benefit, w1) in enumerate(valid_points):
        ax.annotate(f'w₁={w1:.1f}',
                   (cost, benefit),
                   xytext=(5, 5),
                   textcoords='offset points',
                   fontsize=9,
                   bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.7))

    # 设置图表样式
    ax.set_title('Pareto Frontier: Benefit vs. Cost Trade-off', fontsize=16, fontweight='bold')
    ax.set_xlabel('Total Path Length (Cost)', fontsize=12)
    ax.set_ylabel('Total Benefit (Avoided Loss)', fontsize=12)
    ax.grid(True, linestyle='--', alpha=0.6)
    ax.legend()

    # 设置坐标轴范围，留出适当边距
    x_margin = (costs.max() - costs.min()) * 0.1
    y_margin = (benefits.max() - benefits.min()) * 0.1
    ax.set_xlim(costs.min() - x_margin, costs.max() + x_margin)
    ax.set_ylim(benefits.min() - y_margin, benefits.max() + y_margin)

    plt.tight_layout()
    filepath = os.path.join(desktop_path, "Pareto_Frontier_Fixed.png")
    plt.savefig(filepath, dpi=300, bbox_inches='tight')
    plt.close()  # 关闭图形以释放内存
    print(f"修复版帕累托前沿图已保存至: {filepath}")

    # 打印数据摘要
    print(f"\n帕累托前沿数据摘要:")
    print(f"  数据点数量: {len(valid_points)}")
    print(f"  成本范围: {costs.min():.2f} - {costs.max():.2f}")
    print(f"  效益范围: {benefits.min():.4f} - {benefits.max():.4f}")
    print(f"  权重范围: {weights.min():.1f} - {weights.max():.1f}")

    # 强制垃圾回收
    gc.collect()


# --- 7. 主程序执行部分 ---
if __name__ == '__main__':
    print("=" * 80)
    print("无人机喷洒农药优化模型 - 帕累托前沿分析修复版本 v3.1")
    print("=" * 80)
    print("主要修复内容：")
    print("1. 路径提取逻辑错误：修复VRP解决方案的路径构建")
    print("2. 成本计算错误：修复路径长度计算中的索引问题")
    print("3. 图表显示问题：优化帕累托前沿的可视化效果")
    print("4. 数据验证：添加数据有效性检查和错误处理")
    print("5. 性能优化：避免重复计算和内存泄漏")
    print("=" * 80)

    DESKTOP_PATH = r"H:\OneDrive\Desktop"
    if not os.path.exists(DESKTOP_PATH):
        print(f"警告：桌面路径 '{DESKTOP_PATH}' 不存在，结果将保存在当前脚本目录。")
        DESKTOP_PATH = "."

    # 使用小规模参数进行帕累托分析
    params = generate_unified_scenario_data(scale="small")

    print("\n\n" + "=" * 50 + "\n" + "开始进行修复版帕累托前沿分析" + "\n" + "=" * 50)

    pareto_points = []
    # 定义要测试的 w1 权重范围
    w1_values = np.linspace(0.1, 0.9, 5)  # [0.1, 0.3, 0.5, 0.7, 0.9] 避免极端值

    for idx, w1 in enumerate(w1_values):
        w2 = 1.0 - w1
        print(f"\n--- [{idx+1}/{len(w1_values)}] 正在为 w1={w1:.1f}, w2={w2:.1f} 运行实验三 ---")

        # 更新参数
        params['w1'] = w1
        params['w2'] = w2

        try:
            # 求解多目标VRP问题
            start_time = time.time()
            solution_t, status, objective, roles, solution_x = solve_moo_vrp_assignment(params)
            solve_time = time.time() - start_time

            print(f"  求解耗时: {solve_time:.2f}秒")

            if status == pl.LpStatusOptimal and solution_t:
                print(f"  求解状态: 最优解")

                # 计算纯效益
                total_benefit = calculate_pure_benefit(solution_t, roles, params)
                print(f"  总效益: {total_benefit:.4f}")

                # 使用修复的路径提取函数
                paths = extract_paths_from_vrp_solution_fixed(solution_t, solution_x, roles, params)

                # 使用优化的路径长度计算函数
                total_path_cost = 0
                for i, path in paths.items():
                    if path:
                        path_length = calculate_path_length_optimized(path, roles, params, is_optimal_path=True)
                        total_path_cost += path_length
                        print(f"    无人机 {i}: 路径长度 = {path_length:.2f}")

                print(f"  总路径成本: {total_path_cost:.2f}")

                # 验证数据有效性
                if total_benefit > 0 and total_path_cost > 0:
                    pareto_points.append((total_path_cost, total_benefit, w1))
                    print(f"  ✅ 成功添加帕累托点: (成本={total_path_cost:.2f}, 效益={total_benefit:.4f}, w1={w1:.1f})")
                else:
                    print(f"  ❌ 数据无效: 效益={total_benefit:.4f}, 成本={total_path_cost:.2f}")

            elif pl.value(objective) is not None:
                print(f"  求解状态: 可行解 (状态: {pl.LpStatus[status]})")
                print(f"  目标值: {objective:.4f}")

                # 即使不是最优解，也尝试提取数据
                total_benefit = calculate_pure_benefit(solution_t, roles, params)
                paths = extract_paths_from_vrp_solution_fixed(solution_t, solution_x, roles, params)

                total_path_cost = 0
                for i, path in paths.items():
                    if path:
                        path_length = calculate_path_length_optimized(path, roles, params, is_optimal_path=True)
                        total_path_cost += path_length

                if total_benefit > 0 and total_path_cost > 0:
                    pareto_points.append((total_path_cost, total_benefit, w1))
                    print(f"  ✅ 添加可行解帕累托点: (成本={total_path_cost:.2f}, 效益={total_benefit:.4f}, w1={w1:.1f})")
                else:
                    print(f"  ❌ 可行解数据无效")
            else:
                print(f"  ❌ 在当前权重下未能找到可行解 (状态: {pl.LpStatus[status]})")

        except Exception as e:
            print(f"  ❌ 求解过程中出现错误: {e}")
            continue

    print(f"\n帕累托分析完成！共获得 {len(pareto_points)} 个有效数据点。")

    if len(pareto_points) >= 2:
        # 绘制修复版帕累托前沿图
        plot_pareto_frontier_fixed(pareto_points, DESKTOP_PATH)

        print("\n📊 帕累托前沿数据点详情:")
        for i, (cost, benefit, w1) in enumerate(pareto_points):
            print(f"  点 {i+1}: w1={w1:.1f}, 成本={cost:.2f}, 效益={benefit:.4f}")

    else:
        print("❌ 有效数据点不足，无法生成帕累托前沿图。")
        print("建议：")
        print("1. 检查求解器设置和约束条件")
        print("2. 尝试放宽求解时间限制")
        print("3. 调整权重范围或增加权重点数量")

    print("\n" + "=" * 80)
    print("🎉 帕累托前沿分析修复版本运行完成！")
    print("=" * 80)
