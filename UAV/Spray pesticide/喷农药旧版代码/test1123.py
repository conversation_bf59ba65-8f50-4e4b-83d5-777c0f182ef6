# from pulp import getSolver
# solver = getSolver("GUROBI_CMD")
# if solver is None:
#     print("Gurobi Solver not found!")

import math
import matplotlib.pyplot as plt
from matplotlib.ticker import MaxNLocator
from math import floor
import numpy as np
import xlrd
import pandas as pd
import pulp as pl
import pandas as pd
from math import cos, sin, acos, asin, pi, atan, fabs, ceil, floor
from collections import defaultdict
import random
import time
from gurobipy import *
import openpyxl
import sys
import os
from shapely.geometry import Polygon, Point
from shapely.ops import unary_union
import gurobipy as gp
from gurobipy import GRB

# 生成基础系数矩阵
def genRanCoefficientMat(num_drones, num_points, num_crops):
    Coe_matrix = np.zeros((num_drones, num_points, num_crops))
    for i in range(num_drones):
        for j in range(num_points):
            for p in range(num_crops):
                Coe_matrix[i][j][p] = round(np.random.random(), 2)
    return Coe_matrix

# 定义 g(t) 正态分布函数
def g(t, mu, delta):
    coefficient = 1 / (np.sqrt(2 * np.pi) * delta)
    exponent = np.exp(-((t - mu) ** 2) / (2 * delta ** 2))
    g_value = coefficient * exponent
    return g_value

# 计算Q矩阵
def genQMatrix(Q_base, t, mu, delta):
    g_value = g(t, mu, delta)  # 计算给定时间 t 的 g(t) 值
    Q = Coe_matrix * g_value  # 将每个元素乘以 g(t)
    return QMatrix

# 对Q矩阵进行归一化处理
def normalizeQMatrix(QMatrix):
    min_val = np.min(QMatrix)
    max_val = np.max(QMatrix)
    normalized_Q_matrix = (QMatrix - min_val) / (max_val - min_val)
    return normalized_Q_matrix

# 随机生成La矩阵，每个无人机只能携带一种或两种农药
def generate_La_matrix(num_drones, num_pesticides):

    # 初始化矩阵
    La = np.zeros((num_drones, num_pesticides), dtype=int)

    # 随机生成每个无人机的农药种类分配
    for drone in range(num_drones):
        # 随机选择1或2种农药
        num_pesticides_selected = np.random.choice([1, 2])
        # 随机分配农药种类
        selected_pesticides = np.random.choice(num_pesticides, size=num_pesticides_selected, replace=False)
        La[drone, selected_pesticides] = 1

    # 转换为 DataFrame
    La = pd.DataFrame(La,
                         columns=[f"Pesticide_{j+1}" for j in range(num_pesticides)],
                         index=[f"Drone_{d+1}" for d in range(num_drones)])
    return La


if __name__ == '__main__':
    num_drones = 3
    num_points = 5
    time_period = 100
    num_crops = 2

    # 正态分布参数
    mu = 5
    delta = 2



    # Coe_matrix = genRanCoefficientMat(num_drones, num_points, num_crops)

    # # 计算g(t)生成自适应Q
    # Q_values = np.zeros((num_drones, num_points, time_period, num_crops))
    # for i in range(num_drones):
    #     for j in range(num_points):
    #         for t in range(time_period):
    #             for p in range(num_crops):
    #                 g_value = g(t, mu, delta)
    #                 Q_values[i][j][t][p] = Coe_matrix[i][j][p] * g_value

    # time_steps = np.arange(0, time_period)  # 时间范围 0 到 t
    # g_value = g(time_steps, mu, delta)
    #
    # # 扩展 Coe_matrix 到 (i, j, t, p)
    # Coe_matrix_expanded = np.expand_dims(Coe_matrix, axis=2)  # 在第三维度增加一个维度
    # Coe_matrix_expanded = np.tile(Coe_matrix_expanded, (1, 1, time_period, 1))  # 沿时间维度复制
    #
    # # 应用 g(t) 的权重
    # g_value_expanded = g_value[np.newaxis, np.newaxis, :, np.newaxis]  # 调整 g_t 形状为 (1, 1, t, 1)
    # Q = Coe_matrix_expanded * g_value_expanded  # 按时间维度逐元素相乘
    #
    # # 将 all_values 数组归一化
    # normalized_Qvalues = normalizeQMatrix(Q)

