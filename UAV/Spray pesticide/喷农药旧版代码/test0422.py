import math
import matplotlib.pyplot as plt
from matplotlib.ticker import MaxNLocator
from math import floor
import numpy as np
import xlrd
import pandas as pd
import pulp as pl
import pandas as pd
from math import cos, sin, acos, asin, pi, atan, fabs, ceil, floor
from collections import defaultdict
import random
import time
from gurobipy import *
import openpyxl
import sys
import os
from shapely.geometry import Polygon, Point
from shapely.ops import unary_union
import gurobipy as gp
from gurobipy import GRB

# 参数定义
num_points = 5
num_crops = 3
num_pesticides = 3
time_period = 90


# GMRACCF相关
time_intervals = np.arange(0, time_period - 1)  # 时间间隔范围
crops = [f"C{i}" for i in range(1, num_crops + 1)]  # 作物编号
pesticides = [f"P{i}" for i in range(1, num_pesticides + 1)]  # 农药编号
sigma = 10  # 标准差，控制正态分布的宽度

# 生成不同作物情况下农药之间的最佳间隔时间
default_value = 0  # 未定义时的默认值(两农药相同的情况下为合作冲突值为0)
optimal_intervals_matrix = np.full((num_crops, num_pesticides, num_pesticides), default_value)
for crop_idx in range(num_crops):
    for p1_idx in range(num_pesticides):
        for p2_idx in range(num_pesticides):
            if p1_idx != p2_idx:  # 只对不同农药组合赋值
                optimal_intervals_matrix[crop_idx, p1_idx, p2_idx] = np.random.randint(1, 11)

# 生成CCF矩阵  (作物类型，时间间隔，农药1，农药2)
ccf_matrix = np.zeros((num_crops, len(time_intervals), num_pesticides, num_pesticides))
for crop_idx in range(num_crops):
    for p1_idx in range(num_pesticides):
        for p2_idx in range(num_pesticides):
            if p1_idx != p2_idx:  # 农药不同
                # 获取最合适时间间隔
                optimal_interval = optimal_intervals_matrix[crop_idx, p1_idx, p2_idx]
                for t_idx, dt in enumerate(time_intervals):
                    # 正态分布计算合作/冲突效果
                    effect = np.exp(-((dt - optimal_interval) ** 2) / (2 * sigma ** 2))
                    # 映射到 [-1, 1] 的区间
                    if dt < optimal_interval:  # 时间间隔过短，有可能产生冲突，值范围 [-1, 0]
                        effect = -1 + effect
                    else:  # 时间间隔过长，合作效果下降，值范围 [0, 1]
                        effect = effect
                    ccf_matrix[crop_idx, t_idx, p1_idx, p2_idx] = round(effect, 2)

# 扩展CCF矩阵维度 （作物点，时间间隔，农药1，农药2）
CCF_MATRIX = np.zeros((num_points, len(time_intervals), num_pesticides, num_pesticides))
# 扩展逻辑
for crop_point in range(num_points):  # 遍历作物点
    for crop_type in range(num_crops):  # 遍历作物类型
        if task_crop_matrix[crop_point, crop_type] == 1:  # 如果作物点属于当前作物类型
            CCF_MATRIX[crop_point] += ccf_matrix[crop_type]

dimension_relationMat = []
# 遍历无人机、任务点、时刻和农药组合v1.1
for i1 in range(La.shape[0]):  # 遍历无人机 i1
    for point in range(CCF_MATRIX.shape[0]):  # 遍历任务点
        for t1 in range(CCF_MATRIX.shape[1]):  # 时刻 t1
            for p1 in range(CCF_MATRIX.shape[2]):  # 农药1
                for i2 in range(La.shape[0]):  # 遍历无人机 i2
                    for t2 in range(CCF_MATRIX.shape[1]):  # 时刻 t2
                        for p2 in range(CCF_MATRIX.shape[3]):  # 农药2
                            if p1 != p2 and t1 != t2:  # 农药1和农药2必须不同，时刻t1和t2必须不同
                                # 计算时间间隔
                                time_diff = abs(t2 - t1)

                                # 检查时间间隔是否在 CCF_MATRIX 的范围内
                                if t1 + time_diff <= time_period:
                                    # 从 CCF_MATRIX 提取合作/冲突效果
                                    effect = CCF_MATRIX[point, time_diff, p1, p2]

                                    # 判断是否满足无人机的农药量约束
                                    if La[i1, p1] >= task_crop_matrix[point, p1] and La[i2, p2] >= task_crop_matrix[
                                        point, p2]:
                                        # 将有效情境存储到矩阵中
                                        if effect != 0:
                                            # 新的矩阵结构：[i1, point, t1, p1, i2, point, t2, p2, time_diff, effect]
                                            dimension_relationMat.append(
                                                [i1, point, t1, p1, i2, point, t2, p2, time_diff, effect])