import math
import matplotlib.pyplot as plt
from matplotlib.ticker import MaxNLocator
from math import floor
import numpy as np
import xlrd
import pandas as pd
import pulp as pl
import pandas as pd
from math import cos, sin, acos, asin, pi, atan, fabs, ceil, floor
from collections import defaultdict
import random
import time
from gurobipy import *
import openpyxl
import sys
import os
from shapely.geometry import Polygon, Point
from shapely.ops import unary_union
import gurobipy as gp
from gurobipy import GRB

# 生成略微不规则的四边形的函数
def generate_irregular_quadrilateral(scale=10):
    # 随机生成矩形的中心和尺寸（长和宽）
    center_x = random.uniform(0, scale)
    center_y = random.uniform(0, scale)
    width = random.uniform(3, 5)  # 宽度范围
    height = random.uniform(5, 5)  # 高度范围

    # 随机角度变化和偏移量
    angle_offset = random.uniform(-0.2, 0.2)  # 角度偏移，稍微改变角度
    width_offset = random.uniform(-0.3, 0.3)  # 随机变化宽度
    height_offset = random.uniform(-0.3, 0.3)  # 随机变化高度

    half_width = (width + width_offset) / 2
    half_height = (height + height_offset) / 2

    # 创建四个顶点，稍微倾斜形成不规则的四边形
    points = [
        (center_x - half_width, center_y - half_height),
        (center_x + half_width * (1 + angle_offset), center_y - half_height),
        (center_x + half_width, center_y + half_height * (1 - angle_offset)),
        (center_x - half_width * (1 - angle_offset), center_y + half_height)
    ]

    polygon = Polygon(points)

    # 如果生成的多边形不有效（例如，面积为零），则重新生成
    while not polygon.is_valid:
        points = [
            (center_x - half_width, center_y - half_height),
            (center_x + half_width * (1 + angle_offset), center_y - half_height),
            (center_x + half_width, center_y + half_height * (1 - angle_offset)),
            (center_x - half_width * (1 - angle_offset), center_y + half_height)
        ]
        polygon = Polygon(points)

    return polygon

# 生成多个不重叠且不相交的四边形
def generate_non_overlapping_quadrilaterals(num_polygons=3, scale=10):
    polygons = []

    while len(polygons) < num_polygons:
        # 生成一个新的略微不规则四边形
        new_polygon = generate_irregular_quadrilateral(scale)

        # 检查新四边形是否与已有的四边形相交
        overlap = False
        for polygon in polygons:
            if polygon.intersects(new_polygon):
                overlap = True
                break

        # 如果没有相交，则加入到列表中
        if not overlap:
            polygons.append(new_polygon)

    return polygons

# 网格化处理：检查每个网格单元是否在四边形内，并进行编号
def gridify_polygon_with_numbers(polygon, grid_size=1, start_id=0, scale=10):
    # 生成网格点
    grid_points = []
    grid_point_ids = {}
    id_counter = start_id

    min_x, min_y, max_x, max_y = polygon.bounds

    for y in np.arange(min_y, max_y + grid_size, grid_size):  # 注意边界包含
        for x in np.arange(min_x, max_x + grid_size, grid_size):
            x = round(x, 2)
            y = round(y, 2)
            if 0 <= x <= scale and 0 <= y <= scale:  # 检查范围
                point = Point(x, y)
                if polygon.contains(point) or polygon.touches(point):  # 包括边界点
                    grid_points.append((x, y))
                    grid_point_ids[(x, y)] = id_counter
                    id_counter += 1

    return grid_points, grid_point_ids, id_counter

# 对网格点进行区域划分分配作物类型，确保同类作物尽量相邻(限制为整数)
def assign_crop_types(grid_points, num_crops):
    crop_assignments = {}
    num_points = len(grid_points)
    points_per_crop = num_points // num_crops

    # 根据坐标对网格点排序（优先按 x 排序，再按 y 排序）
    grid_points = sorted(grid_points, key=lambda p: (p[0], p[1]))

    # 按区域划分网格点，每个区域分配一种作物类型
    for i in range(num_crops):
        start_index = i * points_per_crop
        end_index = (i + 1) * points_per_crop if i != num_crops - 1 else num_points
        for point in grid_points[start_index:end_index]:
            crop_assignments[point] = i

    return crop_assignments

# 转换为数组
def create_crop_assignments_array(grid_points, crop_assignments, num_crops):
    crop_assignments_array = np.zeros((num_crops, len(grid_points)), dtype=int)
    for idx, point in enumerate(grid_points):
        crop_type = crop_assignments.get(point, -1)
        if crop_type != -1:
            crop_assignments_array[crop_type, idx] = 1
    return crop_assignments_array

# 可视化多个不重叠且不相交的四边形和网格（包括编号）及基站位置
def plot_multiple_quadrilaterals_and_grid_with_numbers(polygons, grid_points_list, grid_point_ids_list, crop_assignments_list):
    plt.figure(figsize=(8, 8))
    plt.title('Crop Distribution Map', pad=20)

    # 绘制所有四边形，使用蓝色填充（只添加一次图例）
    for i, polygon in enumerate(polygons):
        x, y = polygon.exterior.xy
        if i == 0:
            plt.fill(x, y, alpha=0.5, color='lightblue', label='Crop Area')
        else:
            plt.fill(x, y, alpha=0.5, color='lightblue')

    # 定义作物的颜色和图例标签
    crop_colors = ['red', 'green', 'blue']
    crop_labels = ['Crop Type 1', 'Crop Type 2', 'Crop Type 3']

    # 用于跟踪是否已经添加过某种作物类型的图例
    added_labels = [False] * len(crop_colors)

    # 绘制所有网格点并添加编号
    for grid_points, grid_point_ids, crop_assignments in zip(grid_points_list, grid_point_ids_list, crop_assignments_list):
        if grid_points:
            for point in grid_points:
                crop_type = crop_assignments[point]
                # 仅在第一次遇到该作物类型时添加图例
                if not added_labels[crop_type]:
                    plt.scatter(point[0], point[1], color=crop_colors[crop_type], s=10, label=crop_labels[crop_type])
                    added_labels[crop_type] = True
                else:
                    plt.scatter(point[0], point[1], color=crop_colors[crop_type], s=10)

            # 添加编号（编号保持不变）
            for (x, y), id in grid_point_ids.items():
                plt.text(x + 0.1, y + 0.1, str(id), fontsize=8, color='black')

    # # 绘制基站点
    # plt.scatter(base_station.x, base_station.y, color='darkviolet', s=150, label='Base Station', marker='*')

    plt.xlim(0, 10)
    plt.ylim(0, 10)
    plt.xlabel('X')
    plt.ylabel('Y')

    # 自动调整图例位置，避免遮挡
    plt.legend(loc='best', fontsize=10, markerscale=1, ncol=2)  # 自动选择最佳位置

    # 使图像的纵横比相等
    plt.gca().set_aspect('equal', adjustable='box')
    plt.show()

# 生成多个不重叠且不相交的四边形并网格化
def generate_and_gridify_multiple_quadrilaterals(num_polygons=3, grid_size=1, scale=10):
    polygons = generate_non_overlapping_quadrilaterals(num_polygons, scale)
    polygons = sorted(polygons, key=lambda p: (p.bounds[0], p.bounds[1]))  # 按最左边和最底边坐标排序
    all_grid_points = []
    all_grid_point_ids = []
    start_id = 1

    for polygon in polygons:
        grid_points, grid_point_ids, start_id = gridify_polygon_with_numbers(polygon, grid_size, start_id)
        all_grid_points.append(grid_points)
        all_grid_point_ids.append(grid_point_ids)

    return polygons, all_grid_points, all_grid_point_ids

if __name__ == '__main__':
    num_crops = 3
    polygons, grid_points_list, grid_point_ids_list = generate_and_gridify_multiple_quadrilaterals()
    polygon = generate_irregular_quadrilateral(scale=10)
    grid_points, grid_point_id, id_counter = gridify_polygon_with_numbers(polygon)
    crop_assignments = assign_crop_types(grid_points, num_crops)

    # 对网格点进行分配作物类型
    crop_assignments_list = []
    for grid_points in grid_points_list:
        crop_assignments = assign_crop_types(grid_points, num_crops)
        crop_assignments_list.append(crop_assignments)

    plot_multiple_quadrilaterals_and_grid_with_numbers(polygons, grid_points_list, grid_point_ids_list,
                                                       crop_assignments_list)
