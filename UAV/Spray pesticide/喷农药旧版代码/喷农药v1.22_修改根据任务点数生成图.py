import math
import matplotlib.pyplot as plt
from matplotlib.ticker import MaxNLocator
from math import floor
import numpy as np
import xlrd
import pandas as pd
import pulp as pl
import pandas as pd
from math import cos, sin, acos, asin, pi, atan, fabs, ceil, floor
from collections import defaultdict
import random
import time
from gurobipy import *
import openpyxl
import sys
import os
from shapely.geometry import Polygon, Point
from shapely.ops import unary_union
import gurobipy as gp
from gurobipy import GRB
from collections import defaultdict
import heapq

# v1.9 改成GMRACCF
# 1216之前写完了GMRACCF，但是跑不通
# 1216早上发现关系矩阵没有做降维处理，猜可能是出错的原因
# 1217GMRACCF能跑通了，但是GMRACCF结果比GMRA结果差（好像也合理？增加约束导致效果变差；
# 但是别的GMRACCF论文加了CCF是提升了结果；想了一下可能是限制的时间间隔太死了？导致必须要在某个时间间隔降低了效果？不确定是不是这个原因，未找出）v1.9版截止
# v1.10 发现v1.9中的GMRACCF不是每次都能跑通，有时候很长时间都求不出解，怀疑是GMRACCF的约束有问题，确定在v1.9版本中没有考虑时间间隔
# 在v1.10版本上发现CCF矩阵定义错了，没有考虑到无人机i以及顺序有问题，不符合朱老师以前论文的定义 ##20250307
# 20250307决定新开一版v1.11修改以上发现的问题
# v1.11 主要修改：修改La（发现输出并没有限制农药种类在1-2种）；修改dimension_relationMat的维度（加入i，并且按照顺序重新排列）;修改了Q生成的不合理部分；发现无人机续航约束有问题，原版并没有和T结合
# v1.12 修改续航约束，添加路径变量，跑不通，同时考虑基础模型不需要弄这么复杂。考虑在v1.13中直接使用时间作为约束。
# v1.13 实验一跑通：续航使用的是 每架无人机在所有任务点、时间和农药类型上的总任务时间不超过其最大续航时间（简化了续航约束，具体的路径以及顺序等问题放在后面的改进实验）
# v1.15 修改GMRACCF 改进目标：对CCF矩阵降维、或者简化求解过程至定值
# v1.16 小规模测试版本：验证了GMRA模型的可行性，发现没有解的情况应该是无人机数量不够导致，后续可以作为一个实验讨论
# 需要注意在v1.16中GMRA增加了唯一性约束 表示无人机i在时间t只能执行一个任务
# v1.17 根据写的定义进一步修改代码,GMRA的改好了，GMRACCF未修改（将在v1.18改）
# v1.18 修改GMRACCF
# v1.19 修改指示性约束
# v1.20 CCF降维用了topk的方法
# v1.21 考虑第三个大实验：无人机路径连续+优化(已经初步完成第三个实验，可以跑通)
# v1.22 修改了四边形根据num_points生成点
# v1.23 保存基本数据

# 生成略微不规则的四边形的函数
def generate_irregular_quadrilateral(scale=10):
    while True:
        # 随机生成矩形的中心和尺寸（长和宽）
        center_x = random.uniform(0, scale)
        center_y = random.uniform(0, scale)
        width = random.uniform(3, 5)  # 宽度范围
        height = random.uniform(3, 5)  # 高度范围

        # 随机角度变化和偏移量
        angle_offset = random.uniform(-0.2, 0.2)  # 角度偏移，稍微改变角度
        width_offset = random.uniform(-0.3, 0.3)  # 随机变化宽度
        height_offset = random.uniform(-0.3, 0.3)  # 随机变化高度

        half_width = (width + width_offset) / 2
        half_height = (height + height_offset) / 2

        # 创建四个顶点，稍微倾斜形成不规则的四边形
        points = [
            (center_x - half_width, center_y - half_height),
            (center_x + half_width * (1 + angle_offset), center_y - half_height),
            (center_x + half_width, center_y + half_height * (1 - angle_offset)),
            (center_x - half_width * (1 - angle_offset), center_y + half_height)
        ]

        polygon = Polygon(points)

        # 如果生成的多边形有效（例如，面积不为零）并且完全在10x10范围内，则返回
        if polygon.is_valid and polygon.area > 0 and polygon.bounds[0] >= 0 and polygon.bounds[1] >= 0 and polygon.bounds[2] <= scale and polygon.bounds[3] <= scale:
            return polygon

# 生成多个不重叠且不相交的四边形
def generate_non_overlapping_quadrilaterals(num_polygons, scale=10):
    polygons = []

    while len(polygons) < num_polygons:
        new_polygon = generate_irregular_quadrilateral(scale)

        # 检查新四边形是否与已有的四边形相交
        if all(not polygon.intersects(new_polygon) for polygon in polygons):
            polygons.append(new_polygon)

    return polygons

# 网格化处理：检查每个网格单元是否在四边形内，并进行编号
def gridify_polygon_with_numbers(polygon, num_points, start_id=0, scale=10):
    grid_points = []
    grid_point_ids = {}
    id_counter = start_id

    area = polygon.area
    if num_points <= 0:
        raise ValueError("num_points must be positive")

    # 自动计算 grid_size，使得生成点的数量大约等于目标
    estimated_cell_area = area / num_points
    grid_size = np.sqrt(estimated_cell_area)

    min_x, min_y, max_x, max_y = polygon.bounds

    # 调整采样范围，稍微向内收缩，进一步减少边界点
    margin = grid_size * 0.1  # 可调节的边界收缩量

    for y in np.arange(min_y + margin, max_y - margin, grid_size):
        for x in np.arange(min_x + margin, max_x - margin, grid_size):
            point = Point(round(x, 2), round(y, 2))
            if polygon.contains(point):  # 删除 polygon.touches(point)
                grid_points.append((point.x, point.y))
                grid_point_ids[(point.x, point.y)] = id_counter
                id_counter += 1

    return grid_points, grid_point_ids, id_counter

# 对网格点进行区域划分分配作物类型，确保同类作物尽量相邻(限制为整数)
def assign_crop_types(grid_points, num_crops):
    crop_assignments = {}
    num_points = len(grid_points)
    points_per_crop = num_points // num_crops

    # 根据坐标对网格点排序（优先按 x 排序，再按 y 排序）
    grid_points = sorted(grid_points, key=lambda p: (p[0], p[1]))

    # 按区域划分网格点，每个区域分配一种作物类型
    for i in range(num_crops):
        start_index = i * points_per_crop
        end_index = (i + 1) * points_per_crop if i != num_crops - 1 else num_points
        for point in grid_points[start_index:end_index]:
            crop_assignments[point] = i

    # 确认返回类型是字典
    print(f"assign_crop_types: {type(crop_assignments)}")  # 打印作物分配的类型
    return crop_assignments

# 转换为数组：将作物分配信息整合到二维数组中
def create_crop_assignments_array(crop_assignments_list, num_crops):
    # 统计所有区域的所有点
    all_points = []
    for crop_assignments in crop_assignments_list:
        # 由于我们不再做类型检查，直接假定 crop_assignments 是可迭代的字典
        try:
            all_points.extend(crop_assignments.keys())
        except AttributeError:
            print(f"Warning: Skipping invalid element in crop_assignments_list. Type: {type(crop_assignments)}")
            continue  # 如果有不是字典的元素，跳过它

    # 去重并排序，确保点的顺序一致
    all_points = sorted(list(set(all_points)))

    # 创建一个空的作物分配数组
    total_points = len(all_points)
    crop_assignments_array = np.zeros((num_crops, total_points), dtype=int)

    # 建立点到索引的映射
    point_to_index = {point: idx for idx, point in enumerate(all_points)}

    # 填充作物分配数组
    for crop_assignments in crop_assignments_list:
        # 这里使用 try-except 防止不是字典类型的元素影响整体处理
        try:
            for point, crop_type in crop_assignments.items():
                if point in point_to_index:
                    idx = point_to_index[point]
                    crop_assignments_array[crop_type, idx] = 1
        except AttributeError:
            print(f"Warning: Skipping invalid element in crop_assignments_list. Type: {type(crop_assignments)}")
            continue

    return crop_assignments_array, all_points


# 均匀分配作物类型的函数
def assign_crop_types_uniform(grid_points, num_crops):
    """均匀分配作物类型，实现棋盘式分布"""
    crop_assignments = {}
    # 按坐标排序确保可重复性
    grid_points = sorted(grid_points, key=lambda p: (p[0], p[1]))

    # 使用模运算实现均匀分布
    for idx, point in enumerate(grid_points):
        crop_assignments[point] = idx % num_crops

    return crop_assignments

# 可视化多个不重叠且不相交的四边形和网格（包括编号）及基站位置
def plot_multiple_quadrilaterals_and_grid_with_numbers(polygons, all_grid_point_ids, base_station, crop_assignments_list, num_crops):
    plt.figure(figsize=(10, 10))
    plt.title('Crop Distribution Map', pad=20)

    # 绘制所有四边形，使用蓝色填充（只添加一次图例）
    for i, polygon in enumerate(polygons):
        x, y = polygon.exterior.xy
        if i == 0:
            plt.fill(x, y, alpha=0.5, color='lightblue', label='Crop Area')
        else:
            plt.fill(x, y, alpha=0.5, color='lightblue')

    # 定义作物的颜色和图例标签
    crop_colors = ['red', 'green', 'blue']
    crop_labels = ['Crop Type 1', 'Crop Type 2', 'Crop Type 3']

    # 用于跟踪是否已经添加过某种作物类型的图例
    added_labels = [False] * len(crop_colors)

    # 绘制所有网格点并添加编号
    for point, id in all_grid_point_ids.items():
        # 检查点是否在任何一个四边形内
        is_inside = False
        for polygon in polygons:
            if polygon.contains(Point(point)) or polygon.touches(Point(point)):
                is_inside = True
                break

        if is_inside:
            # 获取作物类型
            crop_type = \
            [crop_assignments[point] for crop_assignments in crop_assignments_list if point in crop_assignments][0]
            # 仅在第一次遇到该作物类型时添加图例
            if not added_labels[crop_type]:
                plt.scatter(point[0], point[1], color=crop_colors[crop_type], s=10, label=crop_labels[crop_type])
                added_labels[crop_type] = True
            else:
                plt.scatter(point[0], point[1], color=crop_colors[crop_type], s=10)
            # 添加编号
            plt.text(point[0] + 0.1, point[1] + 0.1, str(id), fontsize=8, color='black')

    # 绘制基站点
    plt.scatter(base_station.x, base_station.y, color='darkviolet', s=150, label='Base Station', marker='*')

    plt.xlim(0, 10)
    plt.ylim(0, 10)
    plt.xlabel('X')
    plt.ylabel('Y')

    # 自动调整图例位置，避免遮挡
    plt.legend(loc='best', fontsize=10, markerscale=1, ncol=2)  # 自动选择最佳位置

    # 使图像的纵横比相等
    plt.gca().set_aspect('equal', adjustable='box')
    plt.show()

# 生成多个不重叠且不相交的四边形并网格化
def generate_and_gridify_multiple_quadrilaterals(num_polygons=None, num_points = None, scale=10, uniform=False):
    # 生成多个不重叠的四边形
    polygons = generate_non_overlapping_quadrilaterals(num_polygons, scale)
    polygons = sorted(polygons, key=lambda p: (p.bounds[0], p.bounds[1]))

    all_grid_points = []
    all_grid_point_ids = {}
    all_crop_assignments = {}  # 保留这个重要变量
    crop_assignments_list = []
    start_id = 1

    # 计算总面积和每个多边形的面积
    total_area = sum(polygon.area for polygon in polygons)
    polygon_areas = [polygon.area for polygon in polygons]

    # 计算每个多边形应该生成的点数（至少1个点）
    points_per_polygon = [max(1, int(round(area / total_area * num_points)))
                          for area in polygon_areas]

    # 调整点数总和确保等于num_points
    while sum(points_per_polygon) != num_points:
        if sum(points_per_polygon) < num_points:
            min_idx = points_per_polygon.index(min(points_per_polygon))
            points_per_polygon[min_idx] += 1
        else:
            max_idx = points_per_polygon.index(max(points_per_polygon))
            if points_per_polygon[max_idx] > 1:
                points_per_polygon[max_idx] -= 1

        # 为每个多边形生成精确数量的网格点
    for polygon, target_points in zip(polygons, points_per_polygon):
        grid_points = []
        attempts = 0

        # 改进的网格点生成逻辑
        while len(grid_points) < target_points and attempts < 10:
            estimated_cell_area = polygon.area / target_points
            grid_size = np.sqrt(estimated_cell_area)

            min_x, min_y, max_x, max_y = polygon.bounds
            temp_points = []

            # 生成网格点
            for y in np.arange(min_y, max_y + grid_size, grid_size):
                for x in np.arange(min_x, max_x + grid_size, grid_size):
                    point = Point(round(x, 2), round(y, 2))
                    if polygon.contains(point):
                        temp_points.append((point.x, point.y))
                        if len(temp_points) >= target_points:
                            break
                if len(temp_points) >= target_points:
                    break

            temp_points = list(set(temp_points))  # 去重
            if len(temp_points) > target_points:
                temp_points = temp_points[:target_points]
            grid_points = temp_points
            attempts += 1

        # 如果仍然不足，随机生成点
        while len(grid_points) < target_points:
            x = random.uniform(polygon.bounds[0], polygon.bounds[2])
            y = random.uniform(polygon.bounds[1], polygon.bounds[3])
            point = Point(x, y)
            if polygon.contains(point):
                grid_points.append((x, y))

        # 分配ID
        grid_point_ids = {(x, y): i + start_id for i, (x, y) in enumerate(grid_points)}
        start_id += len(grid_points)

        # 根据uniform参数选择分配方式
        if uniform:
            crop_assignments = assign_crop_types_uniform(grid_points, num_crops)
        else:
            crop_assignments = assign_crop_types(grid_points, num_crops)

        all_grid_points.extend(grid_points)
        all_grid_point_ids.update(grid_point_ids)
        all_crop_assignments.update(crop_assignments)
        crop_assignments_list.append(crop_assignments)

        # 确保总点数正确
    if len(all_grid_points) > num_points:
        all_grid_points = all_grid_points[:num_points]
        all_grid_point_ids = {k: v for k, v in all_grid_point_ids.items() if k in all_grid_points}
        all_crop_assignments = {k: v for k, v in all_crop_assignments.items() if k in all_grid_points}
        crop_assignments_list = [dict((k, v) for k, v in ca.items() if k in all_grid_points)
                                 for ca in crop_assignments_list]

    return polygons, all_grid_points, all_grid_point_ids, crop_assignments_list, all_crop_assignments


# 记录生成的任务点以及作物类型（列表示任务点，行表示作物类型）0-1矩阵
def generate_crop_type_matrix(crop_assignments_list, num_crops):
    # 统计所有区域的所有点
    all_points = []
    for crop_assignments in crop_assignments_list:
        all_points.extend(crop_assignments.keys())
    all_points = sorted(list(set(all_points)))  # 去重并排序，确保点的顺序一致

    # # 创建一个空的作物分配数组，行表示作物类型，列表示所有网格点
    total_points = len(all_crop_assignments)
    crop_type_matrix = np.zeros((num_crops, total_points), dtype=int)

    # 填充作物分配矩阵
    for idx, (point, crop_type) in enumerate(all_crop_assignments.items()):
        crop_type_matrix[crop_type, idx] = 1

    return crop_type_matrix, all_points

# 在多边形外部生成一个随机点作为无人机基站
def generate_base_station_outside(polygons, scale=10):
    combined_polygon = unary_union(polygons)
    centroid = combined_polygon.centroid
    base_station = centroid

    # 确保基站位置在所有多边形之外
    while combined_polygon.contains(base_station):
        base_x = round(centroid.x + random.uniform(-2, 2), 2)  # 保留两位小数
        base_y = round(centroid.y + random.uniform(-2, 2), 2)  # 保留两位小数
        base_station = Point(base_x, base_y)
    base_station = Point(round(base_station.x, 2), round(base_station.y, 2)) # 再次格式化成两位小数

    return base_station

# 生成无人机的续航时间向量
def generate_drone_endurance_vector(num_drones):
    endurance_vector = [random.randint(20, 60) for i in range(num_drones)]  # 假设续航时间在20到60分钟之间
    return endurance_vector

# 生成基础系数矩阵
def genRanCoefficientMat(num_crops, num_pesticides, task_crop_matrix):
    Coe_matrix = np.zeros((num_crops, num_pesticides))
    for i in range(num_crops):
        for p in range(num_pesticides):
            Coe_matrix[i][p] = round(np.random.random(), 2)  # 作物种类*农药种类
    Coe_matrix = task_crop_matrix @ Coe_matrix # 矩阵乘法扩充维数：作物点*农药种类
    return Coe_matrix

# 生成每个作物对于不同农药最合适的喷洒时间
def gen_best_spray_times(num_crops, num_pesticides, low = 0, high = 5):
    best_spray_times = np.random.randint(low, high,  size=(num_crops, num_pesticides))
    best_spray_times = np.dot(task_crop_matrix, best_spray_times) # 扩充维数：作物点*农药种类
    # best_spray_times = task_crop_matrix @ best_spray_times # 扩充维数：作物点*农药种类
    return best_spray_times

# # 定义 g(t) 的sin函数
# def g(t_values, mu_matrix, delta):
#     # num_crops, num_pesticides = mu_matrix.shape
#     num_points, num_pesticides = mu_matrix.shape # 修改维度至作物点
#     T = len(t_values)
#     # 重塑t_values和best_spray_times以便广播计算
#     t_mesh = t_values.reshape(1, 1, T)
#     # mu_mesh = mu_matrix.reshape(num_crops, num_pesticides, 1)
#     mu_mesh = mu_matrix.reshape(num_points, num_pesticides, 1)
#     # 根据公式构造正弦函数参数
#     argument = (t_mesh - mu_mesh) * (np.pi / (2 * delta)) + (np.pi / 2)
#     g_matrix = (np.sin(argument) + 1) / 2
#     return g_matrix

# 定义 g(t) 的高斯函数
def g(t_values, mu_matrix, delta):
    num_points, num_pesticides = mu_matrix.shape
    T = len(t_values)
    t_mesh = t_values.reshape(1, 1, T)  # 形状 (1, 1, T)
    mu_mesh = mu_matrix.reshape(num_points, num_pesticides, 1)  # 形状 (N, P, 1)
    # 计算高斯函数：系数为 1/(sqrt(2π)δ)，指数为 -(t - μ)^2/(2δ²)
    exponent = -(t_mesh - mu_mesh) ** 2 / (2 * delta ** 2)
    g_matrix = (1 / (delta * np.sqrt(2 * np.pi))) * np.exp(exponent)
    return g_matrix

# # 计算Q矩阵
# def genQMatrix(Q_base, t, mu, delta):
#     g_value = g(t, mu, delta)  # 计算给定时间 t 的 g(t) 值
#     Q = Coe_matrix * g_value  # 将每个元素乘以 g(t)
#     return QMatrix

# 计算Q矩阵更新版（考虑到不同作物对于不同农药最合适的喷洒时间）改成高斯
def compute_Q_matrix(Coe_matrix, task_crop_matrix, best_spray_times, t_values, delta):
    num_points, num_pesticides = Coe_matrix.shape
    # num_crops, _ = best_spray_times.shape
    num_points, num_pesticides = best_spray_times.shape
    T = len(t_values)

    # 计算g(j,t,p)
    t_mesh = t_values.reshape(1, 1, T)  # (1,1,T)
    mu_mesh = best_spray_times.reshape(num_points, num_pesticides, 1)  # (J,P,1)
    argument = -(t_mesh - mu_mesh) ** 2 / (2 * delta ** 2)
    g_matrix = (1 / (delta * np.sqrt(2 * np.pi))) * np.exp(argument)  # (J,P,T)

    # 调整Coe_matrix的形状以匹配广播
    Coe_matrix_reshaped = Coe_matrix.reshape(num_points, num_pesticides, 1)  # 添加时间维度 -> (J, P, 1)

    Q_matrix = g_matrix * Coe_matrix_reshaped  # 结果形状 (J, P, T)

    # 调整形状为 (J, T, P)
    Q_matrix = np.transpose(Q_matrix, (0, 2, 1))

    return Q_matrix

# 对Q矩阵进行归一化处理
def normalizeQMatrix(QMatrix):
    min_val = np.min(QMatrix)
    max_val = np.max(QMatrix)
    normalized_Q_matrix = (QMatrix - min_val) / (max_val - min_val)
    return normalized_Q_matrix

# 生成L[j,p]矩阵，表示任务点j需要的p种类农药的量，需要注意的是同种作物所需农药及农药量须一致
def generateL_matrix(crop_assignments_list, num_crops, num_pesticides, min_amount=1, max_amount=10):

    # 创建一个空的农药需求矩阵，行表示任务点，列表示农药种类
    total_points = len(all_crop_assignments)
    pesticide_requirements_matrix = np.zeros((total_points, num_pesticides), dtype=int)

    # 为每种作物类型生成随机的农药需求，并分配给相同作物类型的任务点
    crop_pesticide_requirements = {}
    for crop_type in set(all_crop_assignments.values()):
        # 随机选择需要的农药种类数量
        num_pesticides_required = np.random.randint(2, num_pesticides + 1)
        pesticide_indices = np.random.choice(range(num_pesticides), size=num_pesticides_required, replace=False)

        # 为选中的农药种类生成需求量
        pesticide_requirements = np.zeros(num_pesticides, dtype=int)
        for idx in pesticide_indices:
            pesticide_requirements[idx] = np.random.randint(min_amount, max_amount + 1)

        crop_pesticide_requirements[crop_type] = pesticide_requirements

    # 填充农药需求矩阵，按照任务点顺序
    for idx, (point, crop_type) in enumerate(all_crop_assignments.items()):
        pesticide_requirements_matrix[idx, :] = crop_pesticide_requirements[crop_type]

    return pesticide_requirements_matrix

# 生成La[i,p]矩阵，每个无人机只能携带一种或两种农药，所有无人机的总农药量能满足任务基本需求v1.1（单个无人机携带1~2种农药）
def generateLa_matrix(num_drones, num_pesticides, pesticide_requirement_matrix):

    # 统计每种农药的总需求量
    total_pesticide_requirements = np.sum(pesticide_requirement_matrix, axis=0)

    # 初始化无人机农药容量矩阵
    drone_capacity_matrix = np.zeros((num_drones, num_pesticides), dtype=int)

    # 分配无人机的农药容量，确保每个无人机携带1或2种农药
    for i in range(num_drones):
        # # 随机选择携带1或2种农药
        # num_pesticides_to_carry = np.random.choice([1, 2])
        # pesticide_indices = np.random.choice(range(num_pesticides), size=num_pesticides_to_carry, replace=False)

        # 每个无人机携带 2 种农药
        pesticide_indices = np.random.choice(range(num_pesticides), size=2, replace=False)

        # 为选中的农药种类分配容量
        for idx in pesticide_indices:
            max_possible = total_pesticide_requirements[idx] // num_drones + 20
            amount = np.random.randint(10, max_possible + 1)
            drone_capacity_matrix[i, idx] = amount

    # 确保所有农药的总携带量略大于任务点的需求量
    for p in range(num_pesticides):
        required_amount = total_pesticide_requirements[p]
        current_amount = np.sum(drone_capacity_matrix[:, p])

        # 设定额外携带比例，例如多携带10%至20%
        additional_amount_needed = required_amount * np.random.uniform(0.1, 0.2)
        target_amount = required_amount + additional_amount_needed

        while current_amount < target_amount:
            # 找到携带该农药的无人机
            drones_with_pesticide = np.where(drone_capacity_matrix[:, p] > 0)[0]
            if len(drones_with_pesticide) > 0:
                # 随机选择已经携带该农药的无人机
                drone_idx = np.random.choice(drones_with_pesticide)
            else:
                # 选择仍然有空余携带能力（小于2种农药）的无人机
                possible_drones = [i for i in range(num_drones) if np.count_nonzero(drone_capacity_matrix[i, :]) < 2]
                if not possible_drones:
                    break  # 如果所有无人机都已携带2种农药，停止分配
                drone_idx = np.random.choice(possible_drones)

            # 增加农药携带量
            additional_amount = np.random.randint(5, 15)
            drone_capacity_matrix[drone_idx, p] += additional_amount

            # 更新当前的总携带量
            current_amount = np.sum(drone_capacity_matrix[:, p])

    return drone_capacity_matrix

# 计算两点之间距离公式
def calDistance(x1, x2, y1, y2):
    result = math.sqrt((x2-x1)**2+(y2-y1)**2)
    return result

# 网格点之间距离
def genDistanceGG(grid_x, grid_y):
    distance_GG = []
    for j in range(num_points):
        tempMatrix = []
        for i in range(num_points):
            tempMatrix.append(calDistance(grid_x[i],grid_x[j],grid_y[i],grid_y[j]))
        distance_GG.append(tempMatrix)
    return distance_GG

# 网格点与基站之间距离
def genDistanceGB(grid_x,grid_y,basestation_x,basestation_y):
    distance_GB = []  # 存储基站与任务点的距离
    for i in range(num_points):
        tempMatrix = calDistance(grid_x[i],grid_y[i],basestation_x,basestation_y)
        distance_GB.append(tempMatrix)
    return distance_GB

# GMRA
class Assignment:
    @classmethod
    def Spraypesticide(cls, Q_matrix, La, L, num_drones, num_points, num_crops, time_period, num_pesticides, endurance_vector, distance_GB_array, distance_GG_array, speed):

        # build a optimal problem
        pro = pl.LpProblem('MAX(connection and coverage )', pl.LpMaximize)
        solver = pl.getSolver('GUROBI_CMD', msg = True)

        # build variables for the optimal problem
        lpvars = [[[[pl.LpVariable("x" + str(i) + "y" + str(j) + "l" + str(t) + "z" + str(p), lowBound=0, upBound=1, cat='Integer') for p in range(num_pesticides)]
                    for t in range(time_period)] for j in range(num_points)] for i in range(num_drones)]

        # 不均匀喷洒 （早期喷洒较多，后期喷洒较小）
        # 定义随时间变化的调整系数函数 f(t)
        def f(t, Tmax):
            return 1 - t / Tmax

        def S_i(t, La, L, i, j, p, time_period):
            # 计算初始喷洒量 S_0
            S_0 = min(La[i][p] // time_period, L[j][p] // time_period)  # 向下取整，确保喷洒量可用
            # 使用调整系数 f(t) 计算喷洒量，并确保为整数
            spray_amount = S_0 * f(t, time_period)
            return max(1, int(spray_amount))  # 确保至少为 1

        # 均匀喷洒
        def S_i_uniform(t, La, L, i, j, p, time_period):
            s_0 = min(La[i][p] // time_period, L[j][p] // time_period) # 均匀分配，不超过无人机可携带的最大量、也不超过作物需求
            return max(1, int(s_0))

        # 用归一化后的值构建目标函数
        all = pl.LpAffineExpression()
        for i in range(num_drones):
            for j in range(num_points):
                for t in range(time_period):
                    for p in range(num_pesticides):
                        all += Q_matrix[j][t][p] * lpvars[i][j][t][p]
        pro += all

        # 满足网格点需求 L
        for j in range(num_points):
            for p in range(num_pesticides):
                tempsum_L = 0
                for i in range(num_drones):
                    for t in range(time_period):
                        # spray_amount = S_i(t, La, L, i, j, p, time_period)
                        spray_amount = S_i_uniform(t, La, L, i, j, p, time_period)
                        tempsum_L += spray_amount * lpvars[i][j][t][p]
                pro += tempsum_L == L[j][p]

        # 不能超过单个无人机可承载的农药量 La
        for i in range(num_drones):
            for p in range(num_pesticides):
                tempsum_La = 0
                for j in range(num_points):
                    for t in range(time_period):
                        spray_amount = S_i_uniform(t, La, L, i, j, p, time_period)
                        tempsum_La += spray_amount * lpvars[i][j][t][p]
                pro += tempsum_La <= La[i][p]

        # 唯一性约束：i无人机在t时间只能执行一个任务
        for i in range(num_drones):
            for t in range(time_period):
                temptask = 0
                for j in range(num_points):
                    for p in range(num_pesticides):
                        temptask += lpvars[i][j][t][p]
                pro += temptask <= 1

        # 续航约束
        for i in range(num_drones):
            temptime = 0
            for j in range(num_points):
                for t in range(time_period):
                    for p in range(num_pesticides):
                        temptime += lpvars[i][j][t][p]
            pro += temptime <= endurance_vector[i]

        # solve optimal problem
        status = pro.solve(solver)
        # status = pro.solve()
        print("Assignment Status: ", pl.LpStatus[status])
        print("Final Assignment Result", pl.value(pro.objective))

        # get the result of T matrix
        T = [[[[lpvars[i][j][t][p].varValue for p in range(num_pesticides)] for t in range(time_period)] for j in range(num_points)]
             for i in range(num_drones)]

        return T, pl.LpStatus[status], pl.value(pro.objective)

# Gurobi API GMRA
class Assignment_Gurobi_GMRA:
    @classmethod
    def Spraypesticide_GMRA_g(cls, Q_matrix, La, L, num_drones, num_points, num_crops, time_period, num_pesticides, endurance_vector, distance_GB_array, distance_GG_array, speed, dimension_relationMat_res):

        model = Model("MAX_connection_and_coverage")

        # 农药喷洒变量：x[i,j,t,p] ∈ {0,1}
        x = {}
        for i in range(num_drones):
            for j in range(num_points):
                for t in range(time_period):
                    for p in range(num_pesticides):
                        x[i, j, t, p] = model.addVar(vtype=GRB.BINARY, name=f"x_{i}_{j}_{t}_{p}")

        model.update()

        # 均匀喷洒
        def S_i_uniform(t, La, L, i, j, p, time_period):
            s_0 = min(La[i][p] // time_period, L[j][p] // time_period) # 均匀分配，不超过无人机可携带的最大量、也不超过作物需求
            return max(1, int(s_0))

        # Objective function
        obj = quicksum(Q_matrix[j][t][p] * x[i, j, t, p]
                       for i in range(num_drones)
                       for j in range(num_points)
                       for t in range(time_period)
                       for p in range(num_pesticides))

        model.setObjective(obj, GRB.MAXIMIZE)

        # Constraints
        # 约束 1: 满足网格点需求 L
        for j in range(num_points):
            for p in range(num_pesticides):
                model.addConstr(
                    quicksum(S_i_uniform(t, La, L, i, j, p, time_period) * x[i, j, t, p]
                              for i in range(num_drones) for t in range(time_period)) == L[j][p],
                    name=f"Demand_point_{j}_pesticide_{p}")

        # 约束 2: 不超过无人机承载量 La
        for i in range(num_drones):
            for p in range(num_pesticides):
                model.addConstr(
                    quicksum(S_i_uniform(t, La, L, i, j, p, time_period) * x[i, j, t, p]
                              for j in range(num_points) for t in range(time_period)) <= La[i][p],
                    name=f"Capacity_i{i}_p{p}")

        # 约束 3: 唯一性约束——每架无人机每个时刻最多做一个任务
        for i in range(num_drones):
            for t in range(time_period):
                model.addConstr(
                    quicksum(x[i, j, t, p] for j in range(num_points) for p in range(num_pesticides)) <= 1,
                    name=f"Single_task_{i}_t{t}")

        # 约束 4: 续航时间约束
        for i in range(num_drones):
            model.addConstr(
                quicksum(x[i, j, t, p] for j in range(num_points) for t in range(time_period) for p in range(num_pesticides)) <= endurance_vector[i],
                name=f"Endurance_i{i}")

        # 求解模型
        model.optimize()

        # 输出结果
        if model.status == GRB.OPTIMAL:
            print("Assignment Status: Optimal")
            print("Final Assignment Result:", model.objVal)
        else:
            print("Assignment Status:", model.status)

        # 提取结果变量
        T = [[[[x[i, j, t, p].getAttr('X') if x[i, j, t, p].getAttr('X') is not None else 0
                for p in range(num_pesticides)]
               for t in range(time_period)]
              for j in range(num_points)]
             for i in range(num_drones)]

        return T, model.status, model.objVal if model.status == GRB.OPTIMAL else None


# Gurobi API
class Assignment2_Gurobi:
    @classmethod
    def Spraypesticide_GMRACCF(cls, Q_matrix, La, L, num_drones, num_points, num_crops, time_period, num_pesticides, endurance_vector, distance_GB_array, distance_GG_array, speed, dimension_relationMat_res):

        model = Model("MAX_connection_and_coverage")

        len_relationMat = len(dimension_relationMat_res)

        # Decision variables
        # x = model.addVars(num_drones, num_points, time_period, num_pesticides, vtype=GRB.BINARY, name="x")

        # 定义变量
        # 农药喷洒变量：x[i,j,t,p] ∈ {0,1}
        x = {}
        for i in range(num_drones):
            for j in range(num_points):
                for t in range(time_period):
                    for p in range(num_pesticides):
                        x[i, j, t, p] = model.addVar(vtype=GRB.BINARY, name=f"x_{i}_{j}_{t}_{p}")

        val_k = model.addVars(len_relationMat, vtype=GRB.BINARY, name="val_k")
        u1 = model.addVars(len_relationMat, vtype=GRB.BINARY, name="u1")
        u2 = model.addVars(len_relationMat, vtype=GRB.BINARY, name="u2")

        model.update()

        # 均匀喷洒
        def S_i_uniform(t, La, L, i, j, p, time_period):
            s_0 = min(La[i][p] // time_period, L[j][p] // time_period) # 均匀分配，不超过无人机可携带的最大量、也不超过作物需求
            return max(1, int(s_0))

        # Objective function
        obj = quicksum(Q_matrix[j][t][p] * x[i, j, t, p]
                       for i in range(num_drones)
                       for j in range(num_points)
                       for t in range(time_period)
                       for p in range(num_pesticides))

        for k in range(len_relationMat):
            j2, t2, p2 = dimension_relationMat_res[k][1:4]
            obj += dimension_relationMat_res[k][9] * Q_matrix[j2][t2][p2] * val_k[k]

        model.setObjective(obj, GRB.MAXIMIZE)

        # Constraints
        # 约束 1: 满足网格点需求 L
        for j in range(num_points):
            for p in range(num_pesticides):
                model.addConstr(
                    quicksum(S_i_uniform(t, La, L, i, j, p, time_period) * x[i, j, t, p]
                              for i in range(num_drones) for t in range(time_period)) == L[j][p],
                    name=f"Demand_point_{j}_pesticide_{p}")

        # 约束 2: 不超过无人机承载量 La
        for i in range(num_drones):
            for p in range(num_pesticides):
                model.addConstr(
                    quicksum(S_i_uniform(t, La, L, i, j, p, time_period) * x[i, j, t, p]
                              for j in range(num_points) for t in range(time_period)) <= La[i][p],
                    name=f"Capacity_i{i}_p{p}")

        # 约束 3: 唯一性约束——每架无人机每个时刻最多做一个任务
        for i in range(num_drones):
            for t in range(time_period):
                model.addConstr(
                    quicksum(x[i, j, t, p] for j in range(num_points) for p in range(num_pesticides)) <= 1,
                    name=f"Single_task_{i}_t{t}")

        # 约束 4: 续航时间约束
        for i in range(num_drones):
            model.addConstr(
                quicksum(x[i, j, t, p] for j in range(num_points) for t in range(time_period) for p in range(num_pesticides)) <= endurance_vector[i],
                name=f"Endurance_i{i}")

        # 时间差约束+CCF约束
        for k in range(len_relationMat):
            t1 = dimension_relationMat_res[k][2]
            t2 = dimension_relationMat_res[k][6]
            time_diff = dimension_relationMat_res[k][8]

            # 时间差计算
            actual_diff = t2 - t1

            # 修正 u1 的约束
            model.addGenConstrIndicator(
                u1[k],
                True,
                actual_diff,  # LHS（左边表达式）
                GRB.EQUAL,  # Sense（关系运算符）
                time_diff,  # RHS（右边常量）
                name=f"u1_eq_{k}"
            )

            # 修正 u2 的约束
            model.addGenConstrIndicator(
                u2[k],
                True,
                actual_diff,  # LHS（左边表达式）
                GRB.EQUAL,  # Sense（关系运算符）
                -time_diff,  # RHS（右边常量，注意负号）
                name=f"u2_eq_{k}"
            )

            # 时间差成立
            model.addConstr(u1[k] + u2[k] <= 1, name=f"val_ge_u1u2_{k}")
            model.addConstr(val_k[k] == u1[k] + u2[k], name=f"val_ge_u1u2_{k}")

            # CCF约束
            model.addConstr(val_k[k] * 2 <= x[dimension_relationMat_res[k][0], dimension_relationMat_res[k][1], dimension_relationMat_res[k][2], dimension_relationMat_res[k][3]]
                            + x[dimension_relationMat_res[k][4], dimension_relationMat_res[k][5], dimension_relationMat_res[k][6], dimension_relationMat_res[k][7]], name=f"CCF1_{k}")

            model.addConstr(x[dimension_relationMat_res[k][0], dimension_relationMat_res[k][1], dimension_relationMat_res[k][2], dimension_relationMat_res[k][3]]
                            + x[dimension_relationMat_res[k][4], dimension_relationMat_res[k][5], dimension_relationMat_res[k][6], dimension_relationMat_res[k][7]] <= val_k[k] + 1, name=f"CCF2_{k}")

        # 求解模型
        model.optimize()

        # 输出结果
        if model.status == GRB.OPTIMAL:
            print("Assignment Status: Optimal")
            print("Final Assignment Result:", model.objVal)
        else:
            print("Assignment Status:", model.status)

        # 提取结果变量
        T = [[[[x[i, j, t, p].getAttr('X') if x[i, j, t, p].getAttr('X') is not None else 0
                for p in range(num_pesticides)]
               for t in range(time_period)]
              for j in range(num_points)]
             for i in range(num_drones)]

        return T, model.status, model.objVal if model.status == GRB.OPTIMAL else None


# Gurobi API
class Assignment3_Gurobi:
    @classmethod
    def Pareto_Optimization_withpath(cls, Q_matrix, La, L, num_drones, num_points, num_crops, time_period, num_pesticides, endurance_vector, distance_GB_array, distance_GG_array, speed, dimension_relationMat_res, path_weight):

        model = Model("Pareto_Optimization_Path")

        # Pareto解集
        pareto_solutions = []

        len_relationMat = len(dimension_relationMat_res)

        # Decision variables
        x = {}
        for i in range(num_drones):
            for j in range(num_points):
                for t in range(time_period):
                    for p in range(num_pesticides):
                        x[i, j, t, p] = model.addVar(vtype=GRB.BINARY, name=f"x_{i}_{j}_{t}_{p}")

        y = {}  # 决策变量：无人机的路径选择（是否从点j到点k）
        for i in range(num_drones):
            for j in range(num_points):
                for k in range(num_points):
                    # y[i,j,k] 表示无人机i是否从点j跳跃到点k
                    y[i, j, k] = model.addVar(vtype=GRB.BINARY, name=f"y_{i}_{j}_{k}")

        val_k = model.addVars(len_relationMat, vtype=GRB.BINARY, name="val_k")
        u1 = model.addVars(len_relationMat, vtype=GRB.BINARY, name="u1")
        u2 = model.addVars(len_relationMat, vtype=GRB.BINARY, name="u2")

        model.update()

        # 均匀喷洒
        def S_i_uniform(t, La, L, i, j, p, time_period):
            s_0 = min(La[i][p] // time_period, L[j][p] // time_period) # 均匀分配，不超过无人机可携带的最大量、也不超过作物需求
            return max(1, int(s_0))

        # 第一部分：最大化作物提升效果
        obj = quicksum(Q_matrix[j][t][p] * x[i, j, t, p]
                       for i in range(num_drones)
                       for j in range(num_points)
                       for t in range(time_period)
                       for p in range(num_pesticides))

        for k in range(len_relationMat):
            j2, t2, p2 = dimension_relationMat_res[k][1:4]
            obj += dimension_relationMat_res[k][9] * Q_matrix[j2][t2][p2] * val_k[k]

        # 第二部分减去路径跳跃的代价
        obj -= path_weight * quicksum(distance_GG_array[j][k] * y[i,j,k]
                                      for i in range(num_drones)
                                      for j in range(num_points)
                                      for k in range(num_points))

        model.setObjective(obj, GRB.MAXIMIZE)

        # Constraints
        # 约束 1: 满足网格点需求 L
        for j in range(num_points):
            for p in range(num_pesticides):
                model.addConstr(
                    quicksum(S_i_uniform(t, La, L, i, j, p, time_period) * x[i, j, t, p]
                              for i in range(num_drones) for t in range(time_period)) == L[j][p],
                    name=f"Demand_point_{j}_pesticide_{p}")

        # 约束 2: 不超过无人机承载量 La
        for i in range(num_drones):
            for p in range(num_pesticides):
                model.addConstr(
                    quicksum(S_i_uniform(t, La, L, i, j, p, time_period) * x[i, j, t, p]
                              for j in range(num_points) for t in range(time_period)) <= La[i][p],
                    name=f"Capacity_i{i}_p{p}")

        # 约束 3: 唯一性约束——每架无人机每个时刻最多做一个任务
        for i in range(num_drones):
            for t in range(time_period):
                model.addConstr(
                    quicksum(x[i, j, t, p] for j in range(num_points) for p in range(num_pesticides)) <= 1,
                    name=f"Single_task_{i}_t{t}")

        # 约束 4: 续航时间约束
        for i in range(num_drones):
            model.addConstr(
                quicksum(x[i, j, t, p] for j in range(num_points) for t in range(time_period) for p in range(num_pesticides)) <= endurance_vector[i],
                name=f"Endurance_i{i}")

        # 时间差约束+CCF约束
        for k in range(len_relationMat):
            t1 = dimension_relationMat_res[k][2]
            t2 = dimension_relationMat_res[k][6]
            time_diff = dimension_relationMat_res[k][8]

            # 时间差计算
            actual_diff = t2 - t1

            # 修正 u1 的约束
            model.addGenConstrIndicator(
                u1[k],
                True,
                actual_diff,  # LHS（左边表达式）
                GRB.EQUAL,  # Sense（关系运算符）
                time_diff,  # RHS（右边常量）
                name=f"u1_eq_{k}"
            )

            # 修正 u2 的约束
            model.addGenConstrIndicator(
                u2[k],
                True,
                actual_diff,  # LHS（左边表达式）
                GRB.EQUAL,  # Sense（关系运算符）
                -time_diff,  # RHS（右边常量，注意负号）
                name=f"u2_eq_{k}"
            )

            # 时间差成立
            model.addConstr(u1[k] + u2[k] <= 1, name=f"val_ge_u1u2_{k}")
            model.addConstr(val_k[k] == u1[k] + u2[k], name=f"val_ge_u1u2_{k}")

            # CCF约束
            model.addConstr(val_k[k] * 2 <= x[dimension_relationMat_res[k][0], dimension_relationMat_res[k][1], dimension_relationMat_res[k][2], dimension_relationMat_res[k][3]]
                            + x[dimension_relationMat_res[k][4], dimension_relationMat_res[k][5], dimension_relationMat_res[k][6], dimension_relationMat_res[k][7]], name=f"CCF1_{k}")

            model.addConstr(x[dimension_relationMat_res[k][0], dimension_relationMat_res[k][1], dimension_relationMat_res[k][2], dimension_relationMat_res[k][3]]
                            + x[dimension_relationMat_res[k][4], dimension_relationMat_res[k][5], dimension_relationMat_res[k][6], dimension_relationMat_res[k][7]] <= val_k[k] + 1, name=f"CCF2_{k}")

        # 约束 5： 路径跳跃约束
        for i in range(num_drones):
            for j in range(num_points):
                model.addConstr(
                    quicksum(y[i, j, k] for k in range(num_points)) == 1,
                    name=f"Outflow_{i}_{j}")  # 确保每个点都有一条出去的路径

                model.addConstr(
                    quicksum(y[i, k, j] for k in range(num_points)) == 1,
                    name=f"Influx_{i}_{j}")  # 确保每个点都有一条进来的路径

        # 约束 6： 唯一性约束，无人机要按照某个顺序喷洒
        for i in range(num_drones):
            for j in range(num_points):
                for k in range(num_points):
                    model.addConstr(y[i, j, k] + y[i, k, j] <= 1, name=f"Path_constraint_{i}_{j}_{k}")


        # 求解模型
        model.optimize()

        # 输出结果
        if model.status == GRB.OPTIMAL:
            print("Assignment Status: Optimal")
            print("Final Assignment Result:", model.objVal)
            pareto_solutions.append((model.objVal, path_weight))

        else:
            print("Assignment Status:", model.status)

        # 提取结果变量
        # 任务分配矩阵
        T = [[[[x[i, j, t, p].getAttr('X') if x[i, j, t, p].getAttr('X') is not None else 0
                for p in range(num_pesticides)]
               for t in range(time_period)]
              for j in range(num_points)]
             for i in range(num_drones)]

        # 计算跳跃路径长度总和
        sumpath_jump = []  # 路径细节
        total_jump = 0
        for i in range(num_drones):
            for j in range(num_points):
                for k in range(num_points):
                    if y[i, j, k].X > 0.5:  # 表示无人机i从j到了k（>0.5，也就是=1，取到了）
                        jump_distance = distance_GG_array[j][k]
                        total_jump += jump_distance
                        sumpath_jump.append((i, j, k, jump_distance))  # 可选：记录每个跳跃细节

        # 你可以选择只返回总值：
        total_jump_sum = sum([item[3] for item in sumpath_jump])

        return pareto_solutions, T, total_jump_sum, model.status, model.objVal if model.status == GRB.OPTIMAL else None, sumpath_jump


# 生成二维方块图
def visualize_spray_2d_heatmap_multicolor_horizontal(T, num_drones, num_points, time_period, num_pesticides):

    # Initialize the 2D array to aggregate data
    aggregated_data = np.zeros((time_period, num_points))
    # pesticide_colors = ['#D5A6A0', '#5BA9BD', '#B0783D', '#95BE54']
    # pesticide_colors = ['#BEA4BD', '#FAC5C6', '#A0B3DC', '#FCDFC7']
    pesticide_colors = ['#656D14', '#BDC314', '#E5E7A2', '#C2C5A4']

    # Aggregate data: collect all pesticide types used in each cell
    pesticide_types = [[[] for _ in range(num_points)] for _ in range(time_period)]
    for i in range(num_drones):
        for j in range(num_points):
            for t in range(time_period):
                for p in range(num_pesticides):
                    if T[i][j][t][p] > 0:
                        aggregated_data[t, j] = 1
                        if p + 1 not in pesticide_types[t][j]:
                            pesticide_types[t][j].append(p + 1)

    # Determine the maximum time period with any spraying
    max_time_period_with_data = 0
    for t in range(time_period):
        if np.any(aggregated_data[t, :]):
            max_time_period_with_data = t

    # Create a figure
    fig, ax = plt.subplots(figsize=(12, 8))
    fig.subplots_adjust(left=0.1, right=0.85)  # 调整图形布局，向左靠近

    # Plot the heatmap
    for t in range(max_time_period_with_data + 1):
        for j in range(num_points):
            if aggregated_data[t, j] == 1:
                pesticides = pesticide_types[t][j]
                num_pesticides_in_cell = len(pesticides)

                # 绘制整个方格的边框（黑色框线）
                ax.add_patch(plt.Rectangle(
                    (j, max_time_period_with_data - t),  # 整个方格的位置
                    1,  # 宽度
                    1,  # 高度
                    facecolor='none',  # 不填充颜色
                    edgecolor='black',  # 黑色边框
                    linewidth=1.5 # 边框宽度
                ))

                # 在方格内绘制多种颜色，不使用内部分隔线
                for k, p in enumerate(pesticides):
                    rect = plt.Rectangle(
                        (j, max_time_period_with_data - t + k / num_pesticides_in_cell),  # 每种颜色的位置
                        1,  # 宽度
                        1 / num_pesticides_in_cell,  # 高度（根据农药种类平分）
                        facecolor=pesticide_colors[p - 1],  # 填充颜色
                        linewidth=0,  # 无内部边框
                        alpha=0.8  # 透明度
                    )
                    ax.add_patch(rect)
                # Add a number to indicate spraying occurred
                ax.text(j + 0.5, max_time_period_with_data - t + 0.5, '1', color='black', ha='center', va='center',
                        fontsize=10)

    # Set axis labels and ticks
    ax.set_xlabel('Task Point', fontsize=14)
    ax.set_ylabel('Time Period', fontsize=14)
    ax.set_title(plot_title, fontsize=16, pad=20)

    ax.set_xticks(np.arange(num_points) + 0.5)
    ax.set_xticklabels([f'{i + 1}' for i in range(num_points)])
    ax.set_yticks(np.arange(max_time_period_with_data + 1) + 0.5)
    ax.set_yticklabels([f'{i}' for i in range(max_time_period_with_data + 1)])

    # Add gridlines for better visualization
    ax.set_xticks(np.arange(num_points + 1), minor=True)
    ax.set_yticks(np.arange(max_time_period_with_data + 2), minor=True)
    ax.grid(which='minor', color='gray', linestyle='--', linewidth=0.5)
    ax.tick_params(which="minor", size=0)

    # Add a legend for pesticide types
    legend_patches = [plt.Line2D([0], [0], color=pesticide_colors[p], lw=4, label=f'Pesticide Type {p + 1}')
                      for p in range(num_pesticides)]
    ax.legend(handles=legend_patches, loc='upper right', bbox_to_anchor=(1.2, 1))

    plt.show()

if __name__ == '__main__':

    # 生成作物分布图
    num_crops = 3  # 作物类型上界
    num_points = 20
    num_polygons = 4

    # 生成多个不重叠且不相交的四边形并进行网格化
    polygons, all_grid_points, all_grid_point_ids, crop_assignments_list, all_crop_assignments = generate_and_gridify_multiple_quadrilaterals(num_polygons=num_polygons, num_points=num_points, scale=10, uniform = True)

    # 在多边形外部生成基站
    base_station = generate_base_station_outside(polygons)

    # 可视化多个四边形、网格点（带编号）以及基站
    plot_multiple_quadrilaterals_and_grid_with_numbers(polygons, all_grid_point_ids, base_station,
                                                       crop_assignments_list, num_crops)

    # 创建作物分配数组
    crop_assignments_array = create_crop_assignments_array(all_crop_assignments, num_crops)

    # 输出网格点的编号和作物分配信息
    points = []
    point_ids = []
    for point, point_id in all_grid_point_ids.items():
        points.append(point)  # 提取坐标
        point_ids.append(point_id)  # 提取编号
        # crop_type = all_crop_assignments[point]
        # print(f"Point {point} (ID: {id}) -> Crop Type: {crop_type}")

    # 作物类型*任务点的0-1矩阵
    crop_type_matrix, all_points = generate_crop_type_matrix(crop_assignments_list, num_crops)
    task_crop_matrix = crop_type_matrix.T  # 将任务点种类矩阵转置到 任务点*作物类型

    # 指派基础模型
    # 模型参数设置
    num_drones = 5
    num_points = len(all_grid_points)  # 统计编号的总数
    time_period = 60  # 时间t上界
    num_pesticides = 3
    endurance_vector = generate_drone_endurance_vector(num_drones)

    # L和La
    L = generateL_matrix(crop_assignments_list, num_crops, num_pesticides, min_amount=1, max_amount=10)
    La = generateLa_matrix(num_drones, num_pesticides, L)

    # 分别提取网格点和基站坐标数组
    grid_x = [point[0] for point in all_points]
    grid_y = [point[1] for point in all_points]
    basestation_x = base_station.x
    basestation_y = base_station.y
    distance_GG = genDistanceGG(grid_x, grid_y)  # 网格点之间距离矩阵
    distance_GG = np.array(distance_GG)
    distance_GB = genDistanceGB(grid_x, grid_y, basestation_x, basestation_y)  # 网格点与基站之间距离矩阵
    distance_GB = np.array(distance_GB)
    distance_GB = np.tile(distance_GB, (num_drones, 1))  # 扩展为基站*网格点的二维数组

    # 每个任务点对于不同农药最合适的喷洒时间
    best_spray_times = gen_best_spray_times(num_crops, num_pesticides, 0, 5)
    # Q矩阵
    Coe_matrix = genRanCoefficientMat(num_crops, num_pesticides, task_crop_matrix)  # 基础系数矩阵
    t_values = np.arange(time_period)  # sin函数中需要的参数
    delta = 2  # sin函数中需要的参数
    Q_matrix = compute_Q_matrix(Coe_matrix, task_crop_matrix, best_spray_times, t_values, delta)
    Q_matrix = normalizeQMatrix(Q_matrix)

    # 无人机飞行速度
    speed = 4

    # GMRACCF相关
    time_intervals = np.arange(0, time_period - 1)  # 时间间隔范围
    crops = [f"C{i}" for i in range(1, num_crops + 1)]  # 作物编号
    pesticides = [f"P{i}" for i in range(1, num_pesticides + 1)]  # 农药编号
    sigma = 10  # 标准差，控制正态分布的宽度

    # 生成不同作物情况下农药之间的最佳间隔时间
    default_value = 0  # 未定义时的默认值(两农药相同的情况下为合作冲突值为0)
    optimal_intervals_matrix = np.full((num_crops, num_pesticides, num_pesticides), default_value)
    for crop_idx in range(num_crops):
        for p1_idx in range(num_pesticides):
            for p2_idx in range(num_pesticides):
                if p1_idx != p2_idx:  # 只对不同农药组合赋值
                    optimal_intervals_matrix[crop_idx, p1_idx, p2_idx] = np.random.randint(1, 11)

    # 生成CCF矩阵  (作物类型，时间间隔，农药1，农药2)
    ccf_matrix = np.zeros((num_crops, len(time_intervals), num_pesticides, num_pesticides))
    for crop_idx in range(num_crops):
        for p1_idx in range(num_pesticides):
            for p2_idx in range(num_pesticides):
                if p1_idx != p2_idx:  # 农药不同
                    # 获取最合适时间间隔
                    optimal_interval = optimal_intervals_matrix[crop_idx, p1_idx, p2_idx]
                    for t_idx, dt in enumerate(time_intervals):
                        # 正态分布计算合作/冲突效果
                        effect = np.exp(-((dt - optimal_interval) ** 2) / (2 * sigma ** 2))
                        # 映射到 [-1, 1] 的区间
                        if dt < optimal_interval:  # 时间间隔过短，有可能产生冲突，值范围 [-1, 0]
                            effect = -1 + effect
                        else:  # 时间间隔过长，合作效果下降，值范围 [0, 1]
                            effect = effect
                        ccf_matrix[crop_idx, t_idx, p1_idx, p2_idx] = round(effect, 2)

    # 扩展CCF矩阵维度 （作物点，时间间隔，农药1，农药2）
    CCF_MATRIX = np.zeros((num_points, len(time_intervals), num_pesticides, num_pesticides))
    # 扩展逻辑
    for crop_point in range(num_points):  # 遍历作物点
        for crop_type in range(num_crops):  # 遍历作物类型
            if task_crop_matrix[crop_point, crop_type] == 1:  # 如果作物点属于当前作物类型
                CCF_MATRIX[crop_point] += ccf_matrix[crop_type]


    dimension_relationMat = []
    # 遍历无人机、任务点、时刻和农药组合v1.1
    for i1 in range(La.shape[0]):  # 遍历无人机 i1
        for point in range(CCF_MATRIX.shape[0]):  # 遍历任务点
            for t1 in range(CCF_MATRIX.shape[1]):  # 时刻 t1
                for p1 in range(CCF_MATRIX.shape[2]):  # 农药1
                    for i2 in range(La.shape[0]):  # 遍历无人机 i2
                        for t2 in range(CCF_MATRIX.shape[1]):  # 时刻 t2
                            for p2 in range(CCF_MATRIX.shape[3]):  # 农药2
                                if p1 != p2 and t1 != t2:  # 农药1和农药2必须不同，时刻t1和t2必须不同
                                    # 计算时间间隔
                                    time_diff = abs(t2 - t1)

                                    # 检查时间间隔是否在 CCF_MATRIX 的范围内
                                    if t1 + time_diff <= time_period:
                                        # 从 CCF_MATRIX 提取合作/冲突效果
                                        effect = CCF_MATRIX[point, time_diff, p1, p2]

                                        # 判断是否满足无人机的农药量约束
                                        if La[i1, p1] >= task_crop_matrix[point, p1] and La[i2, p2] >= task_crop_matrix[
                                            point, p2]:
                                            # 将有效情境存储到矩阵中
                                            if effect != 0:
                                                # 新的矩阵结构：[i1, point, t1, p1, i2, point, t2, p2, time_diff, effect]
                                                dimension_relationMat.append(
                                                    [i1, point, t1, p1, i2, point, t2, p2, time_diff, effect])


    # # 降维v1.2 （最好情况试过阈值=0.7时37s出结果）
    # dimension_relationMat_res = []
    #
    # # 定义一个降维函数
    # def reduce_dimension(dimension_relationMat, threshold_effect=0.9, max_time_diff=5):
    #     reduced_matrix = []
    #     for entry in dimension_relationMat:
    #         i1, point, t1, p1, i2, _, t2, p2, time_diff, effect = entry
    #
    #         # 只保留效果大于阈值的情境
    #         if abs(effect) > threshold_effect:
    #                 reduced_matrix.append(entry)
    #     return reduced_matrix
    #
    # dimension_relationMat_res = reduce_dimension(dimension_relationMat)

    # 降维v1.3
    # topk，保留每个任务点最显著的效应
    def top_k_effects(dimension_relationMat, k):
        grouped = defaultdict(list)

        # 用任务点 + 时间差 + 农药组合作为 key 聚合
        for entry in dimension_relationMat:
            i1, point, t1, p1, i2, _, t2, p2, time_diff, effect = entry
            key = (point, time_diff, p1, p2)
            heapq.heappush(grouped[key], (-abs(effect), entry))  # 用最大堆按 |effect| 排序

        reduced = []
        for key, entries in grouped.items():
            top_entries = heapq.nsmallest(k, entries)
            reduced.extend([e for _, e in top_entries])

        return reduced

    dimension_relationMat_res = top_k_effects(dimension_relationMat, k=100)

    path_weight = 0.5

    # 模型运行_GMRA
    start_1 = time.perf_counter()
    T, status, object = Assignment.Spraypesticide(Q_matrix, La, L, num_drones, num_points, num_crops, time_period, num_pesticides, endurance_vector, distance_GB, distance_GG, speed)
    total_1 = time.perf_counter() - start_1
    TMAT = np.array(T)

    # # 模型运行_GMRACCF
    # start_2 = time.perf_counter()
    # T_GMRACCF, status_GMRACCF, object_GMRACCF = Assignment2_Gurobi.Spraypesticide_GMRACCF(Q_matrix, La, L, num_drones, num_points, num_crops, time_period, num_pesticides, endurance_vector, distance_GB, distance_GG, speed, dimension_relationMat_res)
    # total_2 = time.perf_counter() - start_2
    # TMAT_GMRACCF = np.array(T_GMRACCF)
    #
    # # 模型运行_pareto
    # start_3 = time.perf_counter()
    # pareto_solutions, T_pareto, total_jump_sum, status_pareto, object_pareto, detail_pathjump = Assignment3_Gurobi.Pareto_Optimization_withpath(Q_matrix, La, L, num_drones, num_points, num_crops, time_period, num_pesticides, endurance_vector, distance_GB, distance_GG, speed, dimension_relationMat_res, path_weight)
    # total_3 = time.perf_counter() - start_3
    # T_pareto = np.array(T_pareto)
    #
    # # 输出sigma
    # print('GMRA_sigma', object)
    # print('GMRACCF_sigma', object_GMRACCF)
    # print('pareto_result', object_pareto)
    #
    # # 输出运行时间
    # print('GMRA的运行时间', total_1)
    # print('GMRACCF的运行时间', total_2)
    # print('pareto的运行时间', total_3)
    #
    # # 不同权重
    # #设置权重，步长0.1
    # path_weight_list = [round(w * 0.1, 1) for w in range(11)]
    #
    # # 初始化结果列表
    # pareto_result_list = []
    #
    # # 遍历不同权重
    # for path_weight in path_weight_list:
    #     print(f"\n🚀 当前 path_weight = {path_weight}")
    #     # 调用模型主函数
    #     pareto_solutions, T_pareto, total_jump_sum, status_pareto, object_pareto, detail_pathjump = Assignment3_Gurobi.Pareto_Optimization_withpath(
    #         Q_matrix, La, L, num_drones, num_points, num_crops, time_period, num_pesticides, endurance_vector,
    #         distance_GB, distance_GG, speed, dimension_relationMat_res, path_weight)
    #
    #     if status == GRB.OPTIMAL and pareto_solutions:
    #         for sol in pareto_solutions:
    #             pareto_result_list.append({
    #                 'path_weight': path_weight,
    #                 'result': sol['obj1'],  # 作物效果
    #             })
    #     else:
    #         print(f"⚠️ 未找到最优解，跳过 path_weight={path_weight}")
    #
    # # # 指派结果图
    # # # GMRA
    # # plot_title = "Pesticide Spraying Assignment Map (GMRA)"
    # # visualize_spray_2d_heatmap_multicolor_horizontal(T, num_drones, num_points, time_period, num_pesticides)  # 二维方块图
    # # plot_title = "Pesticide Spraying Assignment Map (GMRACCF)"
    # # visualize_spray_2d_heatmap_multicolor_horizontal(T_GMRACCF, num_drones, num_points, time_period, num_pesticides)  # 二维方块图
    # #
    # # print('GMRA指派结果：', object)
    # # print('GMRACCF指派结果：', object_GMRACCF)
    #




