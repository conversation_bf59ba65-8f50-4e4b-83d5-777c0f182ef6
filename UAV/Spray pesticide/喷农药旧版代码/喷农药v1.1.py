import math
import matplotlib.pyplot as plt
from matplotlib.ticker import MaxNLocator
from math import floor
import numpy as np
import xlrd
import pandas as pd
import pulp as pl
import pandas as pd
from math import cos, sin, acos, asin, pi, atan, fabs, ceil, floor
from collections import defaultdict
import random
import time
from gurobipy import *
import openpyxl
import sys
import os
from shapely.geometry import Polygon, Point
from shapely.ops import unary_union
import gurobipy as gp
from gurobipy import GRB

# v1.1
# 实验进度：1123输出结果T矩阵出现小数问题（可能是由于喷洒量约束使用乘法导致的）
# 遇到问题：S(t)设置很难合理，会导致除不尽任务分配不合理
# 打算更改T的定义，从是否到任务点执行任务，改为喷洒量

# 生成略微不规则的四边形的函数
def generate_irregular_quadrilateral(scale=10):
    # 随机生成矩形的中心和尺寸（长和宽）
    center_x = random.uniform(0, scale)
    center_y = random.uniform(0, scale)
    width = random.uniform(3, 5)  # 宽度范围
    height = random.uniform(5, 5)  # 高度范围

    # 随机角度变化和偏移量
    angle_offset = random.uniform(-0.2, 0.2)  # 角度偏移，稍微改变角度
    width_offset = random.uniform(-0.3, 0.3)  # 随机变化宽度
    height_offset = random.uniform(-0.3, 0.3)  # 随机变化高度

    half_width = (width + width_offset) / 2
    half_height = (height + height_offset) / 2

    # 创建四个顶点，稍微倾斜形成不规则的四边形
    points = [
        (center_x - half_width, center_y - half_height),
        (center_x + half_width * (1 + angle_offset), center_y - half_height),
        (center_x + half_width, center_y + half_height * (1 - angle_offset)),
        (center_x - half_width * (1 - angle_offset), center_y + half_height)
    ]

    polygon = Polygon(points)

    # 如果生成的多边形不有效（例如，面积为零），则重新生成
    while not polygon.is_valid:
        points = [
            (center_x - half_width, center_y - half_height),
            (center_x + half_width * (1 + angle_offset), center_y - half_height),
            (center_x + half_width, center_y + half_height * (1 - angle_offset)),
            (center_x - half_width * (1 - angle_offset), center_y + half_height)
        ]
        polygon = Polygon(points)

    return polygon

# 生成多个不重叠且不相交的四边形
def generate_non_overlapping_quadrilaterals(num_polygons=3, scale=10):
    polygons = []

    while len(polygons) < num_polygons:
        # 生成一个新的略微不规则四边形
        new_polygon = generate_irregular_quadrilateral(scale)

        # 检查新四边形是否与已有的四边形相交
        overlap = False
        for polygon in polygons:
            if polygon.intersects(new_polygon):
                overlap = True
                break

        # 如果没有相交，则加入到列表中
        if not overlap:
            polygons.append(new_polygon)

    return polygons

# 网格化处理：检查每个网格单元是否在四边形内，并进行编号
def gridify_polygon_with_numbers(polygon, grid_size=1, start_id=1, scale=10):
    # 生成网格点
    grid_points = []
    grid_point_ids = {}
    id_counter = start_id

    min_x, min_y, max_x, max_y = polygon.bounds

    for y in np.arange(min_y, max_y, grid_size):  # 从左下角开始编号，优先纵向遍历
        for x in np.arange(min_x, max_x, grid_size):
            if 0 <= x <= scale and 0 <= y <= scale:  # 仅考虑在10x10范围内的点
                point = Point(x, y)
                if polygon.contains(point):  # 如果点在多边形内
                    grid_points.append((x, y))
                    grid_point_ids[(x, y)] = id_counter  # 分配编号
                    id_counter += 1

    return grid_points, grid_point_ids, id_counter

# 对网格点进行区域划分分配作物类型，确保同类作物尽量相邻
def assign_crop_types(grid_points, num_crops):
    crop_assignments = {}
    num_points = len(grid_points)
    points_per_crop = num_points // num_crops

    # 按区域划分网格点，每个区域分配一种作物类型
    for i in range(num_crops):
        start_index = i * points_per_crop
        end_index = (i + 1) * points_per_crop if i != num_crops - 1 else num_points
        for point in grid_points[start_index:end_index]:
            crop_assignments[point] = i

    return crop_assignments

# 可视化多个不重叠且不相交的四边形和网格（包括编号）及基站位置
def plot_multiple_quadrilaterals_and_grid_with_numbers(polygons, grid_points_list, grid_point_ids_list, base_station, crop_assignments_list):
    plt.figure(figsize=(8, 8))
    plt.title('Crop Distribution Map', pad=20)

    # 绘制所有四边形，使用蓝色填充（只添加一次图例）
    for i, polygon in enumerate(polygons):
        x, y = polygon.exterior.xy
        if i == 0:
            plt.fill(x, y, alpha=0.5, color='lightblue', label='Crop Area')
        else:
            plt.fill(x, y, alpha=0.5, color='lightblue')

    # 定义作物的颜色和图例标签
    crop_colors = ['red', 'green', 'blue']
    crop_labels = ['Crop Type 1', 'Crop Type 2', 'Crop Type 3']

    # 用于跟踪是否已经添加过某种作物类型的图例
    added_labels = [False] * len(crop_colors)

    # 绘制所有网格点并添加编号
    for grid_points, grid_point_ids, crop_assignments in zip(grid_points_list, grid_point_ids_list, crop_assignments_list):
        if grid_points:
            for point in grid_points:
                crop_type = crop_assignments[point]
                # 仅在第一次遇到该作物类型时添加图例
                if not added_labels[crop_type]:
                    plt.scatter(point[0], point[1], color=crop_colors[crop_type], s=10, label=crop_labels[crop_type])
                    added_labels[crop_type] = True
                else:
                    plt.scatter(point[0], point[1], color=crop_colors[crop_type], s=10)

            # 添加编号（编号保持不变）
            for (x, y), id in grid_point_ids.items():
                plt.text(x + 0.1, y + 0.1, str(id), fontsize=8, color='black')

    # 绘制基站点
    plt.scatter(base_station.x, base_station.y, color='darkviolet', s=150, label='Base Station', marker='*')

    plt.xlim(0, 10)
    plt.ylim(0, 10)
    plt.xlabel('X')
    plt.ylabel('Y')

    # 自动调整图例位置，避免遮挡
    plt.legend(loc='best', fontsize=10, markerscale=1, ncol=2)  # 自动选择最佳位置

    # 使图像的纵横比相等
    plt.gca().set_aspect('equal', adjustable='box')
    plt.show()

# 生成多个不重叠且不相交的四边形并网格化
def generate_and_gridify_multiple_quadrilaterals(num_polygons=3, grid_size=1, scale=10):
    polygons = generate_non_overlapping_quadrilaterals(num_polygons, scale)
    polygons = sorted(polygons, key=lambda p: (p.bounds[0], p.bounds[1]))  # 按最左边和最底边坐标排序
    all_grid_points = []
    all_grid_point_ids = []
    start_id = 1

    for polygon in polygons:
        grid_points, grid_point_ids, start_id = gridify_polygon_with_numbers(polygon, grid_size, start_id)
        all_grid_points.append(grid_points)
        all_grid_point_ids.append(grid_point_ids)

    return polygons, all_grid_points, all_grid_point_ids

# 在多边形外部生成一个随机点作为无人机基站
def generate_base_station_outside(polygons, scale=10):
    combined_polygon = unary_union(polygons)
    centroid = combined_polygon.centroid
    base_station = centroid

    # 确保基站位置在所有多边形之外
    while combined_polygon.contains(base_station):
        base_x = centroid.x + random.uniform(-2, 2)
        base_y = centroid.y + random.uniform(-2, 2)
        base_station = Point(base_x, base_y)

    return base_station

# 生成无人机的续航时间向量
def generate_drone_endurance_vector(num_drones):
    endurance_vector = [random.randint(15, 60) for i in range(num_drones)]  # 假设续航时间在20到60分钟之间
    return endurance_vector

# 生成基础系数矩阵
def genRanCoefficientMat(num_drones, num_points, num_crops):
    Coe_matrix = np.zeros((num_drones, num_points, num_crops))
    for i in range(num_drones):
        for j in range(num_points):
            for p in range(num_crops):
                Coe_matrix[i][j][p] = round(np.random.random(), 2)
    return Coe_matrix

# 定义 g(t) 正态分布函数
def g(t, mu, delta):
    coefficient = 1 / (np.sqrt(2 * np.pi) * delta)
    exponent = np.exp(-((t - mu) ** 2) / (2 * delta ** 2))
    g_value = coefficient * exponent
    return g_value

# 计算Q矩阵
def genQMatrix(Q_base, t, mu, delta):
    g_value = g(t, mu, delta)  # 计算给定时间 t 的 g(t) 值
    Q = Coe_matrix * g_value  # 将每个元素乘以 g(t)
    return QMatrix

# 对Q矩阵进行归一化处理
def normalizeQMatrix(QMatrix):
    min_val = np.min(QMatrix)
    max_val = np.max(QMatrix)
    normalized_Q_matrix = (QMatrix - min_val) / (max_val - min_val)
    return normalized_Q_matrix

class Assignment:
    @classmethod
    def Spraypesticide(cls, Coe_matrix, La, L, num_drones, num_points, time_period, num_crops, Spraying_quantity, endurance_vector, mu, delta):

        # build a optimal problem
        pro = pl.LpProblem('MAX(connection and coverage )', pl.LpMaximize)
        solver = pl.getSolver('GUROBI_CMD', msg = True)
        # solver = pl.GUROBI_CMD(path="C:/gurobi1200/win64/gurobi_cl.exe", msg=True)

        # build variables for the optimal problem
        lpvars = [[[[pl.LpVariable("x" + str(i) + "y" + str(j) + "l" + str(t) + "z" + str(p), lowBound=0, upBound=1, cat='Integer') for p in range(num_crops)]
                    for t in range(time_period)] for j in range(num_points)] for i in range(num_drones)]

        # 计算g(t)生成自适应Q
        Q_values = np.zeros((num_drones, num_points, time_period, num_crops))
        for i in range(num_drones):
            for j in range(num_points):
                for t in range(time_period):
                    for p in range(num_crops):
                        g_value = g(t, mu, delta)
                        Q_values[i][j][t][p] = Coe_matrix[i][j][p] * g_value

        # 将 all_values 数组归一化
        normalized_Qvalues = normalizeQMatrix(Q_values)

        # 用归一化后的值构建目标函数
        all = pl.LpAffineExpression()
        for i in range(num_drones):
            for j in range(num_points):
                for t in range(time_period):
                    for p in range(num_crops):
                        all += normalized_Qvalues[i][j][t][p] * lpvars[i][j][t][p]
        pro += all

        # 满足网格点需求 L （尝试1）
        for j in range(num_points):
            for p in range(num_crops):
                tempsum_L = 0
                for i in range(num_drones):
                    for t in range(time_period):
                        tempsum_L += lpvars[i][j][t][p] * Spraying_quantity
                pro += tempsum_L >= L[j][p]

        # # 添加约束条件，确保每个网格点的农药需求得到满足（尝试2）
        # for j in range(num_points):
        #     for p in range(num_crops):
        #         tempsum_L = pl.lpSum(
        #             lpvars[i][j][t][p] * Spraying_quantity for i in range(num_drones) for t in range(time_period))
        #         pro += tempsum_L == L[j][p], f"Constraint_L_{j}_{p}"

        # # 满足网格点需求 L(把Spraying_quantity改成累加) （尝试3,行不通，tempsum_L未知不能作为上界）
        # for j in range(num_points):
        #     for p in range(num_crops):
        #         tempsum_L = 0
        #         Fin_L = 0
        #         for i in range(num_drones):
        #             for t in range(time_period):
        #                 tempsum_L += lpvars[i][j][t][p]
        #         for a in range(tempsum_L):  # 循环次数就是lpvars[i][j][t][p]等于1的总和tempsum_L
        #             Fin_L += Spraying_quantity
        #         pro += Fin_L == int(L[j][p])

        # 不能超过单个无人机可承载的农药量 La
        for i in range(num_drones):
            for p in range(num_crops):
                tempsum_La = 0
                for j in range(num_points):
                    for t in range(time_period):
                        tempsum_La += lpvars[i][j][t][p]
                pro += tempsum_La * Spraying_quantity <= La[i][p]

        # 不能超过无人机续航
        for i in range(num_drones):
            tempsum_endurance = 0
            for j in range(num_points):
                for t in range(time_period):
                    for p in range(num_crops):
                        tempsum_endurance += lpvars[i][j][t][p]
            pro += tempsum_endurance <= endurance_vector[i]

        # # 限制单个无人机只能携带1到2种的农药
        # for i in range(num_drones):
        #     for j in range(num_points):
        #         for t in range(time_period):
        #             pesticide_sum = 0
        #             for p in range(num_crops):
        #                 pesticide_sum += lpvars[i][j][t][p]
        #             pro += pesticide_sum <= 2
        #             pro += pesticide_sum >= 1

        # # 强化T边界约束
        # for i in range(num_drones):
        #     for j in range(num_points):
        #         for t in range(time_period):
        #             for p in range(num_crops):
        #                 pro += lpvars[i][j][t][p] >= 0
        #                 pro += lpvars[i][j][t][p] <= 1

        # solve optimal problem
        status = pro.solve(solver)
        # status = pro.solve()
        print("Assignment Status: ", pl.LpStatus[status])
        print("Final Assignment Result", pl.value(pro.objective))

        # get the result of T matrix
        T = [[[[lpvars[i][j][t][p].varValue for p in range(num_crops)] for t in range(time_period)] for j in range(num_points)]
             for i in range(num_drones)]
        return T

if __name__ == '__main__':

    # 生成作物分布图
    num_crops = 3 # 农药种类p的上界

    # 生成多个不重叠且不相交的四边形并进行网格化
    polygons, grid_points_list, grid_point_ids_list = generate_and_gridify_multiple_quadrilaterals()

    # 在多边形外部生成基站
    base_station = generate_base_station_outside(polygons)

    # 对网格点进行分配作物类型
    crop_assignments_list = []
    for grid_points in grid_points_list:
        crop_assignments = assign_crop_types(grid_points, num_crops)
        crop_assignments_list.append(crop_assignments)

    # 可视化多个四边形、网格点（带编号）以及基站
    plot_multiple_quadrilaterals_and_grid_with_numbers(polygons, grid_points_list, grid_point_ids_list, base_station,
                                                       crop_assignments_list)

    # 输出网格点的编号信息
    for grid_point_ids in grid_point_ids_list:
        for point, id in grid_point_ids.items():
            print(f"Point {point} has ID: {id}")

    # 指派基础模型

    # 模型参数设置
    num_drones = 3
    num_points = len(grid_points) # 统计编号的总数
    time_period = 100 # 时间t上界
    # Spraying_quantity = 5 # 无人机均匀喷洒量
    endurance_vector = generate_drone_endurance_vector(num_drones)

    # 正态分布参数
    mu = 5
    delta = 2

    # Q矩阵（放入指派模型中，这里只生成基础系数矩阵）
    Coe_matrix = genRanCoefficientMat(num_drones, num_points, num_crops)
    # QMatrix = genQMatrix(Coe_matrix)
    # normalized_Q_matrix = normalizeQMatrix(QMatrix)
    # Q = normalized_Q_matrix

    # La 和 L 矩阵
    La = np.random.randint(0, 100, (num_drones, num_crops))
    # L = np.random.randint(0, 100, (num_points, num_crops))
    L = np.random.randint(0, 200, (num_points, num_crops))

    Spraying_quantity = 50

    # 均匀喷洒
    # # 计算L[j][p]的总和
    # total_L = 0
    # for j in range(num_points):
    #     for p in range(num_crops):
    #         total_L += L[j][p]
    # # 计算均匀分配的喷洒量Spraying_quantity
    # Spraying_quantity = total_L / (num_drones * time_period)
    # Spraying_quantity = int(Spraying_quantity)

    # # 计算均匀分配的喷洒量 Spraying_quantity（通过累加计算）
    # Spraying_quantity = 0
    # for j in range(num_points):
    #     for p in range(num_crops):
    #         Spraying_quantity += L[j][p]
    # Spraying_quantity //= (num_drones * time_period)
    # Spraying_quantity = int(Spraying_quantity)

    # Spraying_quantities = [0] * num_drones
    # total_demand = 0

    # # 累加总需求
    # for j in range(num_points):
    #     for p in range(num_crops):
    #         total_demand += L[j][p]
    #
    # # 按每架无人机的负载能力分配喷洒量
    # for i in range(num_drones):
    #     Spraying_quantities[i] = min(La[i], total_demand // (num_drones * time_period))
    #
    # 模型运行
    start_1 = time.perf_counter()
    T1 = Assignment.Spraypesticide(Coe_matrix, La, L, num_drones, num_points, time_period, num_crops, Spraying_quantity, endurance_vector, mu, delta)
    total_1 = time.perf_counter() - start_1
    TMAT = np.array(T1)
    print(TMAT)

    # 模型结果验证
    # 验证L
    tempsumtest_L = np.zeros((num_points, num_crops))
    for j in range(num_points):
        for p in range(num_crops):
            for i in range(num_drones):
                for t in range(time_period):
                    tempsumtest_L[j][p] += TMAT[i][j][t][p]
            tempsumtest_L[j][p] = tempsumtest_L[j][p] * Spraying_quantity
    print(tempsumtest_L)

    # 验证La
    tempsumtest_La = np.zeros((num_drones, num_crops))
    for i in range(num_drones):
        for p in range(num_crops):
            for j in range(num_points):
                for t in range(time_period):
                    tempsumtest_La[i][p] += TMAT[i][j][t][p]
    print(tempsumtest_La)








