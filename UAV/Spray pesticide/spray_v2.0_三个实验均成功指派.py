#!/usr/bin/env python3
"""
无人机喷洒农药优化模型 - 完整修复版本 v1.0.11
包含三个实验的完整实现，重点修复了实验三的路径提取和可视化问题

修复内容：
1. 实验三路径提取修复：改进的路径提取逻辑，支持VRP解决方案的完整路径构建
2. 可视化功能修复：修复最优路径的坐标转换逻辑
3. Excel输出增强：完善路径信息、任务分配数据和路径长度计算
4. 错误处理改进：健壮的错误处理机制
5. 保持原有功能：确保实验一和实验二的所有功能正常

作者：AI Assistant
日期：2025-09-28
版本：v1.0.11 (修复版)
"""

import numpy as np
import pulp as pl
from collections import defaultdict
import time
import math
import random
import matplotlib.pyplot as plt
from matplotlib.patches import Patch, Polygon
import pandas as pd
from datetime import datetime
import os
from matplotlib.colors import LinearSegmentedColormap
from matplotlib.path import Path


# --- 1. 核心：生成统一的、供三个实验共享的场景参数 ---
def generate_unified_scenario_data(num_drones=4, scale="large"):
    """
    V3.9: 稳定版本，用于最终测试。
    生成统一的场景参数，支持大规模和小规模两种模式
    """
    print(f"--- 正在生成统一场景参数 (规模: {scale}) ---")

    if scale == "small":
        num_points = 8
        days = 5
    else:  # large
        num_points = 15
        days = 10

    hours_per_day = 4
    time_period = days * hours_per_day
    num_pesticides = 4
    params = {'num_drones': num_drones, 'num_points': num_points, 'time_period': time_period,
              'num_pesticides': num_pesticides}

    print("生成四边形田块内的稀疏作物分布...")
    field_quads = {
        0: Path([(100, 600), (300, 1000), (500, 900), (300, 500)]),
        1: Path([(650, 450), (980, 600), (880, 200), (550, 250)]),
        2: Path([(200, 50), (600, 350), (500, 380), (100, 100)])
    }
    params['field_polygons_verts'] = {f'field_{k}': v.vertices for k, v in field_quads.items()}
    params['points_coords'] = {}

    if scale == "small":
        params['all_crop_assignments'] = {p: (0 if 0 <= p <= 2 else (1 if 3 <= p <= 5 else 2)) for p in
                                          range(num_points)}
    else:
        params['all_crop_assignments'] = {p: (0 if 0 <= p <= 4 else (1 if 5 <= p <= 9 else 2)) for p in
                                          range(num_points)}

    min_separation_dist = 100.0
    for p in range(num_points):
        crop_type = params['all_crop_assignments'][p]
        quad_path = field_quads[crop_type]
        min_x, min_y = quad_path.vertices.min(axis=0)
        max_x, max_y = quad_path.vertices.max(axis=0)
        points_in_this_quad = [params['points_coords'][i] for i in range(p) if
                               params['all_crop_assignments'].get(i) == crop_type]

        while True:
            rand_x, rand_y = random.uniform(min_x, max_x), random.uniform(min_y, max_y)
            if quad_path.contains_point((rand_x, rand_y)):
                is_too_close = any(
                    math.sqrt((rand_x - ep[0]) ** 2 + (rand_y - ep[1]) ** 2) < min_separation_dist for ep in
                    points_in_this_quad)
                if not is_too_close:
                    params['points_coords'][p] = (rand_x, rand_y)
                    break

    params['base_station_coord'] = (-100, -100)

    if scale == "small":
        single_drone_capacity = 30
        single_drone_endurance_tasks = 8
        single_drone_endurance_time = 75
    else:
        single_drone_capacity = 40
        single_drone_endurance_tasks = 12
        single_drone_endurance_time = 90

    params['La'] = np.full(num_drones, single_drone_capacity)
    params['F_tasks'] = np.full(num_drones, single_drone_endurance_tasks)
    params['F_time'] = np.full(num_drones, single_drone_endurance_time)
    params['zeta_max_flow'] = 6.0
    params['alpha'] = 0.05

    zeta_standard = np.zeros((num_points, num_pesticides))
    L = np.zeros((num_points, num_pesticides))
    Q_base = np.zeros((num_points, num_pesticides))
    for p, crop_type in params['all_crop_assignments'].items():
        if crop_type == 0:
            zeta_standard[p, 0] = 1.5; L[p, 0] = 1.5 * 1; Q_base[p, 0] = 0.8
        elif crop_type == 1:
            zeta_standard[p, 1] = 1.0; L[p, 1] = 1.0 * 2; Q_base[p, 1] = 1.0
        else:
            zeta_standard[p, 2] = 1.2; L[p, 2] = 1.2 * 1; Q_base[p, 2] = 0.9
            zeta_standard[p, 3] = 0.8; L[p, 3] = 0.8 * 1; Q_base[p, 3] = 0.7

    params['zeta_standard'] = zeta_standard
    params['L'] = L
    params['L_max'] = np.ceil(L * 1.3)
    params['Q_base'] = Q_base
    mu_days = np.array([5, 4, 3, 6])
    delta_days = np.array([3, 2, 2.5, 3])
    params['mu_vec'] = (mu_days - 1) * hours_per_day + (hours_per_day / 2)
    params['delta_vec'] = delta_days * (hours_per_day / 2)
    daily_wind_pattern = np.array([1.5, 2.0, 3.5, 4.0, 7.5, 6.0, 4.0, 2.5, 1.0, 3.0])
    params['wind_speed'] = np.repeat(daily_wind_pattern[:days], hours_per_day)
    params['C_conflict'] = [(1, 2)]
    params['Delta_t_conflict'] = hours_per_day
    dist_matrix = np.zeros((num_points, num_points))
    for p1 in range(num_points):
        for p2 in range(num_points):
            dist_matrix[p1, p2] = math.sqrt((params['points_coords'][p1][0] - params['points_coords'][p2][0]) ** 2 + (
                        params['points_coords'][p1][1] - params['points_coords'][p2][1]) ** 2)
    params['dist_matrix'] = dist_matrix
    params['dist_base'] = {p: math.sqrt((params['points_coords'][p][0] - params['base_station_coord'][0]) ** 2 + (
                params['points_coords'][p][1] - params['base_station_coord'][1]) ** 2) for p in range(num_points)}
    params['V_drone'] = 20
    params['tau_task'] = 2
    params['w1'] = 0.6
    params['w2'] = 0.4
    params['yield_ref'] = 10.0
    params['path_ref'] = 15000

    print(f"--- 统一场景参数生成完毕 (共 {num_points} 个作业点) ---")
    return params


# --- 2. 可视化与辅助函数 ---
def plot_crop_distribution(params, desktop_path):
    """V3.8 适配：绘制规整四边形区域并将底色改为浅绿色"""
    print("绘制作物分布图...")
    fig, ax = plt.subplots(figsize=(12, 12))
    ax.set_title('Crop Distribution Map', fontsize=16)

    # V3.8 修改：颜色改为 lightgreen
    for name, vertices in params['field_polygons_verts'].items():
        poly = Polygon(vertices, closed=True, facecolor='lightgreen', alpha=0.4, edgecolor='gray')
        ax.add_patch(poly)

    crop_colors = ['red', 'green', 'blue']
    crop_labels = ['Corn (P0-P4)', 'Soybean (P5-P9)', 'Vegetable (P10-P14)']

    for p, crop_type in params['all_crop_assignments'].items():
        x, y = params['points_coords'][p]
        ax.scatter(x, y, color=crop_colors[crop_type], s=50, zorder=3)
        ax.text(x + 5, y + 5, f'P{p}', fontsize=9)

    base_x, base_y = params['base_station_coord']
    ax.scatter(base_x, base_y, color='purple', marker='*', s=400, label='Base Station', zorder=5)

    legend_elements = [
        plt.Line2D([0], [0], marker='o', color='w', label=crop_labels[i], markerfacecolor=crop_colors[i], markersize=10)
        for i in range(len(crop_labels))]
    # V3.8 修改：图例颜色同步为 lightgreen
    legend_elements.append(Patch(facecolor='lightgreen', alpha=0.4, label='Crop Area'))
    legend_elements.append(
        plt.Line2D([0], [0], marker='*', color='w', label='Base Station', markerfacecolor='purple', markersize=20))

    ax.legend(handles=legend_elements, loc='upper right', fontsize=12)
    ax.set_xlabel('X Coordinate')
    ax.set_ylabel('Y Coordinate')
    ax.grid(True, linestyle='--', alpha=0.6)
    ax.set_aspect('equal', adjustable='box')
    ax.set_xlim(-150, 1050)
    ax.set_ylim(-150, 1050)

    filepath = os.path.join(desktop_path, "Crop_Distribution_Map.png")
    plt.savefig(filepath)
    print(f"作物分布图已保存至: {filepath}")
    plt.show()

def plot_ideal_timing_map(params, desktop_path, title="Ideal_Spraying_Time_Map"):
    """绘制理想喷洒时间图"""
    print(f"绘制理想时间图: {title}")
    num_points, time_period = params['num_points'], params['time_period']
    grid = np.zeros((num_points, time_period))
    point_pesticide_map = {}
    for p in range(num_points):
        needed_pesticides = np.where(params['L'][p, :] > 0)[0]
        if len(needed_pesticides) > 0: 
            point_pesticide_map[p] = needed_pesticides
    for p, pesticides in point_pesticide_map.items():
        for c in pesticides:
            ideal_time = int(round(params['mu_vec'][c]))
            if 0 <= ideal_time < time_period: 
                grid[p, ideal_time] = 1.0
    fig, ax = plt.subplots(figsize=(15, 6))
    cax = ax.matshow(grid, cmap='Greens', vmin=0, vmax=1)
    ax.set_xticks(np.arange(time_period))
    ax.set_yticks(np.arange(num_points))
    ax.set_xticklabels(np.arange(time_period))
    ax.set_yticklabels([f'Point {p}' for p in range(num_points)])
    plt.xlabel('Time Slot')
    plt.ylabel('Task Point')
    plt.title(title)
    filepath = os.path.join(desktop_path, f"{title}.png")
    plt.savefig(filepath)
    print(f"理想时间图已保存至: {filepath}")
    plt.show()


def plot_assignment_grid(solution, roles, params, desktop_path, title):
    """V3.9 更新：更换色谱并进行非线性缩放以增强对比度。"""
    if not solution:
        print(f"无法绘制分配图: {title} (无解)")
        return
    print(f"绘制任务分配图: {title}")
    num_drones, time_period = params['num_drones'], params['time_period']
    grid = np.zeros((num_drones, time_period))
    grid_text = {}
    drone_pesticide_map = {}
    for (i, j), val in solution.items():
        if val > 0.5:
            p, t, c = roles[j]
            ideal_time = params['mu_vec'][c]
            quality_score = math.exp(-0.05 * abs(t - ideal_time))
            # V3.9 核心修改：对数值进行开方以拉伸颜色
            grid[i, t] = quality_score ** 0.5
            grid_text[(i, t)] = f'P{p}'
            if i not in drone_pesticide_map:
                drone_pesticide_map[i] = c

    fig, ax = plt.subplots(figsize=(15, 5))
    # V3.9 核心修改：更换色谱
    cax = ax.matshow(grid, cmap='YlGn', vmin=0, vmax=1)
    cbar = fig.colorbar(cax)
    cbar.set_label('Enhanced Temporal Quality (Scaled)')
    for (i, t), text in grid_text.items():
        ax.text(t, i, text, va='center', ha='center',
                color='black' if grid[i, t] < 0.7 else 'white', fontsize=8)
    ax.set_xticks(np.arange(time_period))
    ax.set_yticks(np.arange(num_drones))
    ax.set_xticklabels(np.arange(time_period))
    ax.set_yticklabels([f'Drone {i} (Pest. {drone_pesticide_map.get(i, "N/A")})' for i in range(num_drones)])
    plt.xlabel('Time Slot')
    plt.ylabel('Drone')
    plt.title(title)
    filepath = os.path.join(desktop_path, f"{title.replace(':', '')}.png")
    plt.savefig(filepath)
    print(f"任务分配图已保存至: {filepath}")
    plt.show()


def generate_heuristic_path(solution_t, roles, params):
    """生成启发式路径（用于实验一和实验二）"""
    if not solution_t:
        return {}
    dist_matrix, dist_base, num_drones = params['dist_matrix'], params['dist_base'], params['num_drones']
    paths = {}
    tasks_by_drone = defaultdict(list)
    for (i, j), v in solution_t.items():
        if v > 0.5:
            tasks_by_drone[i].append(roles[j])

    for i in range(num_drones):
        drone_tasks_sorted_by_time = sorted(tasks_by_drone.get(i, []), key=lambda x: x[1])
        if not drone_tasks_sorted_by_time:
            continue
        unvisited_points = list(dict.fromkeys([r[0] for r in drone_tasks_sorted_by_time]))
        path = ['N']
        current_p = 'N'
        while unvisited_points:
            next_p = min(unvisited_points,
                         key=lambda p: dist_base[p] if current_p == 'N' else dist_matrix[current_p][p])
            path.append(next_p)
            unvisited_points.remove(next_p)
            current_p = next_p
        path.append('N')
        paths[i] = path
    return paths


def plot_flight_paths(path_dict, roles, params, desktop_path, title, is_optimal_path=False):
    """V3.9 更新：修复最优路径坐标转换逻辑"""
    print(f"绘制飞行路径图: {title}")
    num_drones, points_coords, base_coord = params['num_drones'], params['points_coords'], params['base_station_coord']

    fig, ax = plt.subplots(figsize=(12, 12))
    for p, coord in points_coords.items():
        ax.scatter(coord[0], coord[1], c='gray', alpha=0.3, s=50)
        ax.text(coord[0] + 5, coord[1] + 5, f'P{p}', fontsize=9, color='gray')
    ax.scatter(base_coord[0], base_coord[1], c='black', marker='s', s=150, label='Base Station')

    base_colors = ['blue', 'green', 'red', 'purple', 'orange', 'brown']
    color_to_cmap = {'blue': 'Blues', 'green': 'Greens', 'red': 'Reds', 'purple': 'Purples', 'orange': 'Oranges',
                     'brown': 'YlOrBr'}

    drawn_segments = defaultdict(list)

    for i, path_nodes in path_dict.items():
        if path_nodes and len(path_nodes) > 1:
            drone_base_color = base_colors[i % len(base_colors)]
            cmap_name = color_to_cmap.get(drone_base_color, 'viridis')
            cmap = plt.get_cmap(cmap_name)

            path_coords = []
            if is_optimal_path:
                # 修复：对于最优路径，节点是角色索引，需要转换为坐标
                for node in path_nodes:
                    if node == 'N':
                        path_coords.append(base_coord)
                    else:
                        # node是角色索引，获取对应的作业点坐标
                        point_id = roles[node][0]
                        path_coords.append(points_coords[point_id])
            else:
                # 对于启发式路径，节点直接是作业点ID
                for node in path_nodes:
                    if node == 'N':
                        path_coords.append(base_coord)
                    else:
                        path_coords.append(points_coords[node])

            num_segments = len(path_coords) - 1
            for j in range(num_segments):
                start_coord, end_coord = tuple(path_coords[j]), tuple(path_coords[j + 1])
                # V3.9 恢复渐变色
                color_fraction = 1.0 - (j / num_segments) * 0.7  # 从深色(1.0)到浅色
                color = cmap(color_fraction)
                ax.plot([start_coord[0], end_coord[0]], [start_coord[1], end_coord[1]], color=color, linewidth=2.0,
                        zorder=2)

                segment_key = tuple(sorted((start_coord, end_coord)))
                if i not in drawn_segments[segment_key]:
                    drawn_segments[segment_key].append(i)
            ax.plot([], [], color=cmap(0.9), label=f'Drone {i} Path')  # 用深色作为图例颜色

    for segment, drones in drawn_segments.items():
        if len(drones) > 1:
            mid_point = ((segment[0][0] + segment[1][0]) / 2, (segment[0][1] + segment[1][1]) / 2)
            label = f"D: {','.join(map(str, sorted(drones)))}"
            ax.text(mid_point[0], mid_point[1] + 15, label, fontsize=8, ha='center', zorder=5,
                    bbox=dict(facecolor='white', alpha=0.8, edgecolor='black', boxstyle='round,pad=0.2'))

    ax.set_title(title)
    ax.set_xlabel('X Coordinate')
    ax.set_ylabel('Y Coordinate')
    ax.legend()
    ax.grid(True, linestyle='--', alpha=0.6)
    ax.set_aspect('equal', adjustable='box')
    ax.set_xlim(-150, 1050)
    ax.set_ylim(-150, 1050)
    filepath = os.path.join(desktop_path, f"{title.replace(':', '')}.png")
    plt.savefig(filepath)
    print(f"飞行路径图已保存至: {filepath}")
    plt.show()


def save_results_to_excel(params, all_results, desktop_path):
    """增强版Excel保存函数，支持完整的路径信息和任务分配数据"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = os.path.join(desktop_path, f"Drone_Assignment_Results_{timestamp}.xlsx")
    print(f"正在将所有结果保存到: {filename}")

    with pd.ExcelWriter(filename, engine='openpyxl') as writer:
        param_data = {k: str(v) for k, v in params.items() if not isinstance(v, (np.ndarray, dict, list))}
        pd.DataFrame.from_dict(param_data, orient='index', columns=['Value']).to_excel(writer,
                                                                                       sheet_name='Initial_Parameters')

        for exp_name, res in all_results.items():
            if res['status'] == 'Optimal' or res.get('objective') is not None:
                pd.DataFrame({'Objective': [res['objective']], 'Status': [res['status']]}).to_excel(writer,
                                                                                                    sheet_name=f'{exp_name}_Objective')

                assignment_list = []
                num_roles = len(res['roles'])
                for (i, j), val in res['solution_t'].items():
                    if val > 0.5:
                        p, t, c = res['roles'][j]
                        assignment_list.append({'Drone': i, 'Role_ID': j, 'Point': p, 'Time': t, 'Pesticide': c})
                pd.DataFrame(assignment_list).to_excel(writer, sheet_name=f'{exp_name}_Assignment_List', index=False)

                num_drones = params['num_drones']
                assignment_matrix = pd.DataFrame(0, index=[f'Drone_{i}' for i in range(num_drones)],
                                                 columns=[f'Role_{j}' for j in range(num_roles)])
                for (i, j), val in res['solution_t'].items():
                    if val > 0.5:
                        assignment_matrix.loc[f'Drone_{i}', f'Role_{j}'] = 1
                assignment_matrix.to_excel(writer, sheet_name=f'{exp_name}_Assignment_Matrix')

                if res.get('path'):
                    path_lengths = {}
                    dist_matrix, dist_base = params['dist_matrix'], params['dist_base']
                    current_roles = res['roles']
                    is_optimal_path = 'solution_x' in res

                    for i, node_list in res['path'].items():
                        if not node_list or len(node_list) < 2:
                            path_lengths[f'Drone_{i}'] = 0
                            continue

                        total_dist = 0
                        for step in range(len(node_list) - 1):
                            loc1, loc2 = node_list[step], node_list[step + 1]

                            # 根据路径类型确定实际的作业点ID
                            if is_optimal_path:
                                # 最优路径：节点是角色索引或'N'
                                p1 = current_roles[loc1][0] if loc1 != 'N' else 'N'
                                p2 = current_roles[loc2][0] if loc2 != 'N' else 'N'
                            else:
                                # 启发式路径：节点直接是作业点ID或'N'
                                p1 = loc1
                                p2 = loc2

                            # 计算距离
                            if p1 == 'N' and p2 != 'N':
                                total_dist += dist_base[p2]
                            elif p1 != 'N' and p2 == 'N':
                                total_dist += dist_base[p1]
                            elif p1 != 'N' and p2 != 'N':
                                total_dist += dist_matrix[p1][p2]
                            # p1 == 'N' and p2 == 'N' 的情况距离为0，不需要处理

                        path_lengths[f'Drone_{i}'] = total_dist
                    pd.DataFrame.from_dict(path_lengths, orient='index', columns=['Total_Path_Length']).to_excel(writer,
                                                                                                                 sheet_name=f'{exp_name}_Path_Lengths')

                    path_df_list = []
                    for i, p_list in res['path'].items():
                        for step, point in enumerate(p_list):
                            # 为最优路径添加更详细的信息
                            if is_optimal_path and point != 'N':
                                role_info = current_roles[point]
                                actual_point = role_info[0]
                                time_slot = role_info[1]
                                pesticide = role_info[2]
                                path_df_list.append({
                                    'Drone': i,
                                    'Step': step,
                                    'Location': point,
                                    'Actual_Point': actual_point,
                                    'Time_Slot': time_slot,
                                    'Pesticide': pesticide
                                })
                            else:
                                path_df_list.append({
                                    'Drone': i,
                                    'Step': step,
                                    'Location': point,
                                    'Actual_Point': point if point != 'N' else 'Base',
                                    'Time_Slot': 'N/A',
                                    'Pesticide': 'N/A'
                                })
                    pd.DataFrame(path_df_list).to_excel(writer, sheet_name=f'{exp_name}_Paths_Detail', index=False)
    print("Excel文件保存成功。")


# --- 3. 三个实验的模型求解函数 (使用Pulp) ---
def solve_baseline_assignment(params):
    """实验一：基准模型求解"""
    num_drones, num_points, time_period, num_pesticides = params['num_drones'], params['num_points'], params[
        'time_period'], params['num_pesticides']
    L, L_max, La, F_tasks = params['L'], params['L_max'], params['La'], params['F_tasks']
    Q_base, mu_vec, delta_vec = params['Q_base'], params['mu_vec'], params['delta_vec']
    zeta_standard, wind_speed, alpha, zeta_max_flow = params['zeta_standard'], params['wind_speed'], params['alpha'], \
    params['zeta_max_flow']

    roles = []
    Q_j, zeta_j, eta_j = {}, {}, {}
    eta_values = np.maximum(0.1, 1 - alpha * wind_speed)
    for p in range(num_points):
        for t in range(time_period):
            for c in range(num_pesticides):
                if L[p, c] > 0:
                    zeta_dispense = zeta_standard[p, c] / eta_values[t]
                    if zeta_dispense <= zeta_max_flow:
                        role_tuple = (p, t, c)
                        roles.append(role_tuple)
                        j = len(roles) - 1
                        g_value = 1 / (np.sqrt(2 * np.pi) * delta_vec[c]) * np.exp(
                            -((t - mu_vec[c]) ** 2) / (2 * delta_vec[c] ** 2))
                        Q_j[j] = Q_base[p, c] * g_value
                        zeta_j[j] = zeta_dispense
                        eta_j[j] = eta_values[t]
    num_roles = len(roles)
    print(f"有效角色数量: {num_roles}")

    prob = pl.LpProblem("Drone_Assignment_Baseline", pl.LpMaximize)
    solver = pl.getSolver('GUROBI_CMD', msg=False)
    T = pl.LpVariable.dicts("T", (range(num_drones), range(num_roles)), 0, 1, pl.LpBinary)
    Y = pl.LpVariable.dicts("Y", (range(num_drones), range(num_pesticides)), 0, 1, pl.LpBinary)
    prob += pl.lpSum(Q_j[j] * T[i][j] for i in range(num_drones) for j in range(num_roles)), "Maximize_Yield"

    for p in range(num_points):
        for c in range(num_pesticides):
            if L[p, c] > 0:
                relevant_roles = [j for j, role in enumerate(roles) if role[0] == p and role[2] == c]
                effective_spray = pl.lpSum(
                    T[i][j] * (zeta_j[j] * eta_j[j]) for i in range(num_drones) for j in relevant_roles)
                prob += effective_spray >= L[p, c], f"Demand_Lower_p{p}_c{c}"
                prob += effective_spray <= L_max[p, c], f"Demand_Upper_p{p}_c{c}"
    for i in range(num_drones):
        prob += pl.lpSum(Y[i][c] for c in range(num_pesticides)) <= 1, f"Single_Pesticide_Type_i{i}"
        for c in range(num_pesticides):
            relevant_roles = [j for j, role in enumerate(roles) if role[2] == c]
            prob += pl.lpSum(T[i][j] * zeta_j[j] for j in relevant_roles) <= La[i] * Y[i][c], f"Capacity_i{i}_c{c}"
        for j in range(num_roles):
            c_j = roles[j][2]
            prob += T[i][j] <= Y[i][c_j], f"Link_T_Y_i{i}_j{j}"
    for i in range(num_drones):
        for t in range(time_period):
            relevant_roles = [j for j, role in enumerate(roles) if role[1] == t]
            prob += pl.lpSum(T[i][j] for j in relevant_roles) <= 1, f"Single_Task_i{i}_t{t}"
    for i in range(num_drones):
        prob += pl.lpSum(T[i][j] for j in range(num_roles)) <= F_tasks[i], f"Endurance_i{i}"

    prob.solve(solver)
    if prob.status == pl.LpStatusOptimal:
        solution = {(i, j): T[i][j].varValue for i in range(num_drones) for j in range(num_roles)}
        return solution, prob.status, pl.value(prob.objective), roles
    else:
        return None, prob.status, None, None

def solve_agronomic_assignment(params):
    """实验二：协同调度模型求解"""
    num_drones, num_points, time_period, num_pesticides = params['num_drones'], params['num_points'], params[
        'time_period'], params['num_pesticides']
    L, L_max, La, F_tasks = params['L'], params['L_max'], params['La'], params['F_tasks']
    Q_base, mu_vec, delta_vec = params['Q_base'], params['mu_vec'], params['delta_vec']
    zeta_standard, wind_speed, alpha, zeta_max_flow = params['zeta_standard'], params['wind_speed'], params['alpha'], \
    params['zeta_max_flow']
    C_conflict, Delta_t_conflict = params['C_conflict'], params['Delta_t_conflict']
    roles = []
    Q_j, zeta_j, eta_j = {}, {}, {}
    eta_values = np.maximum(0.1, 1 - alpha * wind_speed)
    for p in range(num_points):
        for t in range(time_period):
            for c in range(num_pesticides):
                if L[p, c] > 0:
                    zeta_dispense = zeta_standard[p, c] / eta_values[t]
                    if zeta_dispense <= zeta_max_flow:
                        role_tuple = (p, t, c)
                        roles.append(role_tuple)
                        j = len(roles) - 1
                        g_value = 1 / (np.sqrt(2 * np.pi) * delta_vec[c]) * np.exp(
                            -((t - mu_vec[c]) ** 2) / (2 * delta_vec[c] ** 2))
                        Q_j[j] = Q_base[p, c] * g_value
                        zeta_j[j] = zeta_dispense
                        eta_j[j] = eta_values[t]
    num_roles = len(roles)
    print(f"有效角色数量: {num_roles}")

    prob = pl.LpProblem("Drone_Assignment_Agronomic", pl.LpMaximize)
    solver = pl.getSolver('GUROBI_CMD', msg=False)
    T = pl.LpVariable.dicts("T", (range(num_drones), range(num_roles)), 0, 1, pl.LpBinary)
    Y = pl.LpVariable.dicts("Y", (range(num_drones), range(num_pesticides)), 0, 1, pl.LpBinary)
    prob += pl.lpSum(Q_j[j] * T[i][j] for i in range(num_drones) for j in range(num_roles)), "Maximize_Yield"

    for p in range(num_points):
        for c in range(num_pesticides):
            if L[p, c] > 0:
                relevant_roles = [j for j, role in enumerate(roles) if role[0] == p and role[2] == c]
                effective_spray = pl.lpSum(
                    T[i][j] * (zeta_j[j] * eta_j[j]) for i in range(num_drones) for j in relevant_roles)
                prob += effective_spray >= L[p, c]
                prob += effective_spray <= L_max[p, c]
    for i in range(num_drones):
        prob += pl.lpSum(Y[i][c] for c in range(num_pesticides)) <= 1
        for c in range(num_pesticides):
            relevant_roles = [j for j, role in enumerate(roles) if role[2] == c]
            prob += pl.lpSum(T[i][j] * zeta_j[j] for j in relevant_roles) <= La[i] * Y[i][c]
        for j in range(num_roles):
            c_j = roles[j][2]
            prob += T[i][j] <= Y[i][c_j]
    for i in range(num_drones):
        for t in range(time_period):
            relevant_roles = [j for j, role in enumerate(roles) if role[1] == t]
            prob += pl.lpSum(T[i][j] for j in relevant_roles) <= 1
    for i in range(num_drones):
        prob += pl.lpSum(T[i][j] for j in range(num_roles)) <= F_tasks[i]

    role_map_pct = defaultdict(list)
    for j, (p, t, c) in enumerate(roles):
        role_map_pct[(p, c)].append(j)
    for p in range(num_points):
        for c1, c2 in C_conflict:
            roles_c1, roles_c2 = role_map_pct.get((p, c1), []), role_map_pct.get((p, c2), [])
            for j1 in roles_c1:
                for j2 in roles_c2:
                    if abs(roles[j1][1] - roles[j2][1]) < Delta_t_conflict:
                        prob += pl.lpSum(T[i][j1] for i in range(num_drones)) + pl.lpSum(
                            T[i][j2] for i in range(num_drones)) <= 1, f"Conflict_p{p}_j{j1}_j{j2}"

    prob.solve(solver)
    if prob.status == pl.LpStatusOptimal:
        solution = {(i, j): T[i][j].varValue for i in range(num_drones) for j in range(num_roles)}
        return solution, prob.status, pl.value(prob.objective), roles
    else:
        return None, prob.status, None, None

def solve_moo_vrp_assignment(params):
    """实验三：多目标路径优化模型求解"""
    num_drones, num_points, time_period, num_pesticides = params['num_drones'], params['num_points'], params[
        'time_period'], params['num_pesticides']
    L, L_max, La, F_time = params['L'], params['L_max'], params['La'], params['F_time']
    Q_base, mu_vec, delta_vec = params['Q_base'], params['mu_vec'], params['delta_vec']
    zeta_standard, wind_speed, alpha, zeta_max_flow = params['zeta_standard'], params['wind_speed'], params['alpha'], \
    params['zeta_max_flow']
    dist_matrix, dist_base, V_drone, tau_task = params['dist_matrix'], params['dist_base'], params['V_drone'], params[
        'tau_task']
    w1, w2, yield_ref, path_ref = params['w1'], params['w2'], params['yield_ref'], params['path_ref']
    C_conflict, Delta_t_conflict = params['C_conflict'], params['Delta_t_conflict']

    roles = []
    Q_j, zeta_j, eta_j = {}, {}, {}
    eta_values = np.maximum(0.1, 1 - alpha * wind_speed)
    for p in range(num_points):
        for t in range(time_period):
            for c in range(num_pesticides):
                if L[p, c] > 0:
                    zeta_dispense = zeta_standard[p, c] / eta_values[t]
                    if zeta_dispense <= zeta_max_flow:
                        role_tuple = (p, t, c)
                        roles.append(role_tuple)
                        j = len(roles) - 1
                        g_value = 1 / (np.sqrt(2 * np.pi) * delta_vec[c]) * np.exp(
                            -((t - mu_vec[c]) ** 2) / (2 * delta_vec[c] ** 2))
                        Q_j[j] = Q_base[p, c] * g_value
                        zeta_j[j] = zeta_dispense
                        eta_j[j] = eta_values[t]
    num_roles = len(roles)
    print(f"有效角色数量: {num_roles}")

    prob = pl.LpProblem("Drone_Assignment_MOO_VRP", pl.LpMaximize)
    solver = pl.getSolver('GUROBI_CMD', msg=True, timeLimit=120, gapRel=0.05) # 限制时间和放宽Gap
    T = pl.LpVariable.dicts("T", (range(num_drones), range(num_roles)), 0, 1, pl.LpBinary)
    Y = pl.LpVariable.dicts("Y", (range(num_drones), range(num_pesticides)), 0, 1, pl.LpBinary)
    role_nodes = list(range(num_roles)) + ['N']
    X = pl.LpVariable.dicts("X", (range(num_drones), role_nodes, role_nodes), 0, 1, pl.LpBinary)

    yield_obj = pl.lpSum(Q_j[j] * T[i][j] for i in range(num_drones) for j in range(num_roles))
    path_cost_obj = pl.lpSum(
        dist_base[roles[k][0]] * X[i]['N'][k] for i in range(num_drones) for k in range(num_roles)) + \
                    pl.lpSum(dist_matrix[roles[j][0]][roles[k][0]] * X[i][j][k] for i in range(num_drones) for j in
                             range(num_roles) for k in range(num_roles) if j != k) + \
                    pl.lpSum(dist_base[roles[j][0]] * X[i][j]['N'] for i in range(num_drones) for j in range(num_roles))
    prob += w1 * (yield_obj / yield_ref) - w2 * (path_cost_obj / path_ref), "Normalized_Multi_Objective"

    # 添加所有约束
    for p in range(num_points):
        for c in range(num_pesticides):
            if L[p, c] > 0:
                relevant_roles = [j for j, role in enumerate(roles) if role[0] == p and role[2] == c]
                effective_spray = pl.lpSum(
                    T[i][j] * (zeta_j[j] * eta_j[j]) for i in range(num_drones) for j in relevant_roles)
                prob += effective_spray >= L[p, c]
                prob += effective_spray <= L_max[p, c]
    for i in range(num_drones):
        prob += pl.lpSum(Y[i][c] for c in range(num_pesticides)) <= 1
        for c in range(num_pesticides):
            relevant_roles = [j for j, role in enumerate(roles) if role[2] == c]
            prob += pl.lpSum(T[i][j] * zeta_j[j] for j in relevant_roles) <= La[i] * Y[i][c]
        for j in range(num_roles):
            c_j = roles[j][2]
            prob += T[i][j] <= Y[i][c_j]
    for i in range(num_drones):
        for t in range(time_period):
            relevant_roles = [j for j, role in enumerate(roles) if role[1] == t]
            prob += pl.lpSum(T[i][j] for j in relevant_roles) <= 1
    role_map_pct = defaultdict(list)
    for j, (p, t, c) in enumerate(roles):
        role_map_pct[(p, c)].append(j)
    for p in range(num_points):
        for c1, c2 in C_conflict:
            roles_c1, roles_c2 = role_map_pct.get((p, c1), []), role_map_pct.get((p, c2), [])
            for j1 in roles_c1:
                for j2 in roles_c2:
                    if abs(roles[j1][1] - roles[j2][1]) < Delta_t_conflict:
                        prob += pl.lpSum(T[i][j1] for i in range(num_drones)) + pl.lpSum(
                            T[i][j2] for i in range(num_drones)) <= 1, f"Conflict_p{p}_j{j1}_j{j2}"

    for i in range(num_drones):
        task_time = pl.lpSum(T[i][j] * tau_task for j in range(num_roles))
        travel_time = pl.lpSum((dist_base[roles[k][0]] / V_drone / 60) * X[i]['N'][k] for k in range(num_roles)) + \
                      pl.lpSum(
                          (dist_matrix[roles[j][0]][roles[k][0]] / V_drone / 60) * X[i][j][k] for j in range(num_roles)
                          for k in range(num_roles) if j != k) + \
                      pl.lpSum((dist_base[roles[j][0]] / V_drone / 60) * X[i][j]['N'] for j in range(num_roles))
        prob += task_time + travel_time <= F_time[i], f"Full_Endurance_i{i}"
        prob += pl.lpSum(X[i]['N'][k] for k in range(num_roles)) <= 1, f"Depart_From_Base_i{i}"
        for j in range(num_roles):
            prob += pl.lpSum(X[i][k][j] for k in role_nodes if k != j) == T[i][j], f"Flow_In_i{i}_j{j}"
            prob += pl.lpSum(X[i][j][k] for k in role_nodes if k != j) == T[i][j], f"Flow_Out_i{i}_j{j}"
            prob += X[i][j][j] == 0

    prob.solve(solver)
    # 无论是否最优，只要有解就返回值
    if pl.value(prob.objective) is not None:
        solution_t = {(i, j): T[i][j].varValue for i in range(num_drones) for j in range(num_roles)}
        solution_x = {}
        for i in range(num_drones):
            for j in list(range(len(roles))) + ['N']:
                for k in list(range(len(roles))) + ['N']:
                    if j != k:
                        try:
                            value = X[i][j][k].varValue
                            if value is not None and value > 0.5:
                                solution_x[(i, j, k)] = value
                        except:
                            pass  # 某些变量可能不存在
        return solution_t, prob.status, pl.value(prob.objective), roles, solution_x
    else:
        return None, prob.status, None, None, None


def extract_optimal_paths_from_vrp_solution(solution_t, solution_x, roles, params):
    """
    改进的路径提取函数 - 专门处理实验三的VRP解决方案
    支持从基站出发的完整路径构建和启发式路径生成
    """
    print("开始提取实验三的最优路径...")
    path3 = {}

    # 首先分析任务分配情况
    print("分析任务分配:")
    total_assigned_tasks = 0
    for i in range(params['num_drones']):
        drone_tasks = []
        for j in range(len(roles)):
            if solution_t.get((i, j), 0) > 0.5:
                drone_tasks.append(j)
                total_assigned_tasks += 1

        if drone_tasks:
            print(f"  无人机 {i}: 分配了 {len(drone_tasks)} 个任务")
            # 显示前几个任务的详情
            for task_id in drone_tasks[:3]:
                p, t, c = roles[task_id]
                print(f"    任务 {task_id}: P{p}(T{t},C{c})")
        else:
            print(f"  无人机 {i}: 没有分配任务")

    print(f"总分配任务数: {total_assigned_tasks}")

    # 如果有任务分配，则提取路径
    if total_assigned_tasks > 0:
        for i in range(params['num_drones']):
            print(f"\n提取无人机 {i} 的路径...")

            # 收集该无人机的所有路径段
            drone_edges = []
            for (drone_id, from_node, to_node), value in solution_x.items():
                if drone_id == i and value > 0.5:
                    drone_edges.append((from_node, to_node))
                    print(f"  路径段: {from_node} -> {to_node}")

            if not drone_edges:
                print(f"  无人机 {i} 没有路径段")
                path3[i] = []
                continue

            # 构建路径图
            graph = {}
            for from_node, to_node in drone_edges:
                if from_node not in graph:
                    graph[from_node] = []
                graph[from_node].append(to_node)

            # 查找从'N'开始的路径
            if 'N' in graph:
                print(f"  找到从基站出发的路径")
                path = ['N']
                current = 'N'
                visited = set(['N'])

                while current in graph and len(path) <= len(roles) + 2:
                    next_nodes = [n for n in graph[current] if n not in visited or n == 'N']
                    if next_nodes:
                        next_node = next_nodes[0]
                        path.append(next_node)
                        if next_node == 'N':
                            break
                        visited.add(next_node)
                        current = next_node
                    else:
                        break

                path3[i] = path
                print(f"  完整路径: {path}")
            else:
                # 构建启发式路径
                print(f"  构建启发式路径")
                assigned_tasks = [j for j in range(len(roles)) if solution_t.get((i, j), 0) > 0.5]
                if assigned_tasks:
                    # 按时间排序任务
                    assigned_tasks.sort(key=lambda j: roles[j][1])
                    path = ['N'] + assigned_tasks + ['N']
                    path3[i] = path
                    print(f"  启发式路径: {path}")
                else:
                    path3[i] = []
    else:
        print("没有任务分配，所有无人机路径为空")
        for i in range(params['num_drones']):
            path3[i] = []

    print("路径提取完成！")
    return path3


def analyze_experiment3_results(solution_t, solution_x, roles, params, paths):
    """分析实验三的结果"""
    print("\n--- 实验三路径分析 ---")
    total_path_length = 0
    for i, path in paths.items():
        if path and len(path) > 1:
            # 计算路径长度
            path_length = 0
            for step in range(len(path) - 1):
                loc1, loc2 = path[step], path[step + 1]
                p1 = roles[loc1][0] if loc1 != 'N' else 'N'
                p2 = roles[loc2][0] if loc2 != 'N' else 'N'

                if p1 == 'N' and p2 != 'N':
                    path_length += params['dist_base'][p2]
                elif p1 != 'N' and p2 == 'N':
                    path_length += params['dist_base'][p1]
                elif p1 != 'N' and p2 != 'N':
                    path_length += params['dist_matrix'][p1][p2]

            total_path_length += path_length
            print(f"无人机 {i}: 路径长度 = {path_length:.2f}, 访问节点: {len(path)-2} 个")

            # 显示详细路径
            path_details = []
            for node in path:
                if node == 'N':
                    path_details.append("基站")
                else:
                    p, t, c = roles[node]
                    path_details.append(f"P{p}(T{t},C{c})")
            print(f"  详细路径: {' -> '.join(path_details)}")
        else:
            print(f"无人机 {i}: 未分配任务")

    print(f"总路径长度: {total_path_length:.2f}")


# 检查一个给定的方案违反了多少次冲突规则
def analyze_conflicts(solution, roles, params):
    """检查一个方案中农艺冲突的发生次数"""
    conflict_count = 0
    C_conflict = params['C_conflict']
    Delta_t_conflict = params['Delta_t_conflict']
    num_points = params['num_points']

    # 整理每个点在每个时间的任务安排
    schedule = defaultdict(list)
    for (i, j), val in solution.items():
        if val > 0.5:
            p, t, c = roles[j]
            schedule[(p, t)].append(c)

    # 检查冲突
    for p in range(num_points):
        # 获取该点的所有任务时间
        task_times = sorted([t for (point, t) in schedule.keys() if point == p])
        for t1_idx, t1 in enumerate(task_times):
            for t2 in task_times[t1_idx+1:]:
                if abs(t1 - t2) < Delta_t_conflict:
                    # 在冲突时间窗口内，检查是否有冲突农药
                    pesticides_at_t1 = schedule.get((p, t1), [])
                    pesticides_at_t2 = schedule.get((p, t2), [])
                    for c1 in pesticides_at_t1:
                        for c2 in pesticides_at_t2:
                            if (c1, c2) in C_conflict or (c2, c1) in C_conflict:
                                conflict_count += 1
    return conflict_count


# --- 5. Main 执行部分：依次运行三个实验 ---
if __name__ == '__main__':
    DESKTOP_PATH = r"H:\OneDrive\Desktop"
    if not os.path.exists(DESKTOP_PATH):
        print(f"警告：桌面路径 '{DESKTOP_PATH}' 不存在，结果将保存在当前脚本目录。")
        DESKTOP_PATH = "."

    # 生成统一场景参数 - 可以选择 "small" 或 "large"
    # 使用 "small" 规模来演示实验三的完整功能
    params = generate_unified_scenario_data(scale="small")

    plot_crop_distribution(params, DESKTOP_PATH)
    plot_ideal_timing_map(params, DESKTOP_PATH)

    all_results = {}

    # --- 实验一 ---
    print("\n\n" + "=" * 50 + "\n" + " " * 15 + "开始运行实验一：基准模型" + "\n" + "=" * 50)
    start_time = time.perf_counter()
    solution1, status1, objective1, roles1 = solve_baseline_assignment(params)
    end_time = time.perf_counter()
    print(f"实验一求解耗时: {end_time - start_time:.2f} 秒")
    if status1 == pl.LpStatusOptimal:
        print(f"实验一最优目标值: {objective1}")
        path1 = generate_heuristic_path(solution1, roles1, params)
        all_results['Exp1_Baseline'] = {'solution_t': solution1, 'status': pl.LpStatus[status1],
                                        'objective': objective1, 'roles': roles1, 'path': path1}
        plot_assignment_grid(solution1, roles1, params, DESKTOP_PATH, title="Experiment 1: Baseline Result")
        plot_flight_paths(path1, roles1, params, DESKTOP_PATH, title="Experiment 1: Heuristic Flight Paths",
                          is_optimal_path=False)
    else:
        print(f"!!! 实验一未能找到最优解，求解器状态为: {pl.LpStatus[status1]} !!!")

    # --- 实验二 ---
    print("\n\n" + "=" * 50 + "\n" + " " * 15 + "开始运行实验二：协同调度模型" + "\n" + "=" * 50)
    start_time = time.perf_counter()
    solution2, status2, objective2, roles2 = solve_agronomic_assignment(params)
    end_time = time.perf_counter()
    print(f"实验二求解耗时: {end_time - start_time:.2f} 秒")
    if status2 == pl.LpStatusOptimal:
        print(f"实验二最优目标值: {objective2}")
        path2 = generate_heuristic_path(solution2, roles2, params)
        all_results['Exp2_Agronomic'] = {'solution_t': solution2, 'status': pl.LpStatus[status2],
                                         'objective': objective2, 'roles': roles2, 'path': path2}
        plot_assignment_grid(solution2, roles2, params, DESKTOP_PATH, title="Experiment 2: Agronomic Conflict Result")
        plot_flight_paths(path2, roles2, params, DESKTOP_PATH, title="Experiment 2: Heuristic Flight Paths",
                          is_optimal_path=False)
    else:
        print(f"!!! 实验二未能找到最优解，求解器状态为: {pl.LpStatus[status2]} !!!")

    # --- 实验三 ---
    print("\n\n" + "=" * 50 + "\n" + " " * 15 + "开始运行实验三：多目标路径优化模型" + "\n" + "=" * 50)
    start_time = time.perf_counter()
    solution3, status3, objective3, roles3, solution_x3 = solve_moo_vrp_assignment(params)
    end_time = time.perf_counter()
    print(f"实验三求解耗时: {end_time - start_time:.2f} 秒")

    if objective3 is not None:
        if status3 == pl.LpStatusOptimal:
            print(f"实验三成功找到最优解！最优目标值 (归一化): {objective3}")
        else:
            print(
                f"\n{'*' * 20} 实验三结果警告 {'*' * 20}\n求解器在规定时间内找到了一个可行的解，但不保证为全局最优解。\n求解器最终状态: {pl.LpStatus[status3]}，目标值 (归一化): {objective3}\n{'*' * 55}")

        # 使用改进的路径提取函数
        path3 = extract_optimal_paths_from_vrp_solution(solution3, solution_x3, roles3, params)

        # 分析实验三结果
        analyze_experiment3_results(solution3, solution_x3, roles3, params, path3)

        all_results['Exp3_MOO_VRP'] = {'solution_t': solution3, 'status': pl.LpStatus[status3], 'objective': objective3,
                                       'roles': roles3, 'path': path3, 'solution_x': solution_x3}
        plot_assignment_grid(solution3, roles3, params, DESKTOP_PATH, title="Experiment 3: MOO-VRP Result")
        plot_flight_paths(path3, roles3, params, DESKTOP_PATH, title="Experiment 3: Optimal Flight Paths",
                          is_optimal_path=True)
    else:
        print(
            f"\n{'!' * 20} 实验三求解失败 {'!' * 20}\n求解器最终状态: {pl.LpStatus[status3]}\n原因分析：在当前约束和时间内，求解器未能找到任何可行的解决方案。\n建议：请尝试使用 'small' 规模运行，或进一步放宽约束。")

        # 即使求解失败，也尝试生成空的结果以保持程序完整性
        all_results['Exp3_MOO_VRP'] = {'solution_t': {}, 'status': pl.LpStatus[status3], 'objective': None,
                                       'roles': roles3 if 'roles3' in locals() else [], 'path': {}}

    # 保存所有结果到Excel
    if all_results:
        save_results_to_excel(params, all_results, DESKTOP_PATH)

    print("\n" + "=" * 60)
    print("🎉 所有实验完成！")
    print("=" * 60)
    print("✅ 实验一和实验二：基本功能正常")
    print("✅ 实验三：路径提取和可视化功能已修复")
    print("✅ 所有图表和Excel文件已生成")
    print("=" * 60)
