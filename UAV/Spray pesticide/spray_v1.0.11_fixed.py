#!/usr/bin/env python3
"""
修复版本：专门解决实验三的路径提取和可视化问题
基于spray_v1.0.11.py，重点修复实验三的路径相关功能
"""

# 导入原始文件的所有内容，然后重写关键函数
import sys
import os
sys.path.append(os.path.dirname(__file__))

# 从原始文件导入所有函数和类
from spray_v1_0_11 import *

def extract_paths_from_solution_x(solution_x, roles, num_drones):
    """
    改进的路径提取函数
    专门处理实验三的VRP解决方案
    """
    print("\n=== 改进的路径提取开始 ===")
    
    paths = {}
    
    for i in range(num_drones):
        print(f"\n处理无人机 {i}:")
        
        # 收集该无人机的所有路径段
        drone_edges = []
        for (drone_id, from_node, to_node), value in solution_x.items():
            if drone_id == i and value > 0.5:
                drone_edges.append((from_node, to_node))
                print(f"  路径段: {from_node} -> {to_node}")
        
        if not drone_edges:
            print(f"  无人机 {i} 没有路径段")
            paths[i] = []
            continue
        
        # 构建路径图
        graph = {}
        for from_node, to_node in drone_edges:
            if from_node not in graph:
                graph[from_node] = []
            graph[from_node].append(to_node)
        
        # 查找从'N'开始的路径
        if 'N' in graph:
            print(f"  找到从基站出发的路径")
            path = ['N']
            current = 'N'
            visited = set(['N'])
            
            while current in graph and len(path) <= len(roles) + 2:
                next_nodes = [n for n in graph[current] if n not in visited or n == 'N']
                if next_nodes:
                    next_node = next_nodes[0]  # 选择第一个可用节点
                    path.append(next_node)
                    if next_node == 'N':
                        break  # 回到基站，路径完成
                    visited.add(next_node)
                    current = next_node
                else:
                    break
            
            paths[i] = path
            print(f"  完整路径: {path}")
        else:
            # 如果没有从'N'开始的路径，尝试构建一个连通的路径
            print(f"  没有从基站出发的路径，尝试构建连通路径")
            
            # 找到所有节点
            all_nodes = set()
            for from_node, to_node in drone_edges:
                all_nodes.add(from_node)
                all_nodes.add(to_node)
            
            if all_nodes:
                # 选择一个起始节点（非'N'）
                start_node = next(iter(all_nodes - {'N'}), None)
                if start_node is not None:
                    path = [start_node]
                    current = start_node
                    visited = set([start_node])
                    
                    while current in graph and len(path) <= len(roles) + 2:
                        next_nodes = [n for n in graph[current] if n not in visited]
                        if next_nodes:
                            next_node = next_nodes[0]
                            path.append(next_node)
                            visited.add(next_node)
                            current = next_node
                        else:
                            break
                    
                    # 添加基站到路径的开始和结束
                    full_path = ['N'] + path + ['N']
                    paths[i] = full_path
                    print(f"  构建的路径: {full_path}")
                else:
                    paths[i] = []
            else:
                paths[i] = []
    
    print("=== 路径提取完成 ===\n")
    return paths

def analyze_experiment3_solution(solution_t, solution_x, roles, params):
    """
    分析实验三的解决方案
    """
    print("\n=== 实验三解决方案分析 ===")
    
    # 分析任务分配
    print("任务分配分析:")
    total_tasks = 0
    for i in range(params['num_drones']):
        drone_tasks = []
        for j in range(len(roles)):
            if solution_t.get((i, j), 0) > 0.5:
                drone_tasks.append(j)
                total_tasks += 1
        
        if drone_tasks:
            print(f"  无人机 {i}: 分配了 {len(drone_tasks)} 个任务")
            for task_id in drone_tasks[:3]:  # 只显示前3个任务
                p, t, c = roles[task_id]
                print(f"    任务 {task_id}: 作业点P{p}, 时间T{t}, 农药C{c}")
            if len(drone_tasks) > 3:
                print(f"    ... 还有 {len(drone_tasks)-3} 个任务")
        else:
            print(f"  无人机 {i}: 没有分配任务")
    
    print(f"总计分配任务数: {total_tasks}")
    
    # 分析路径变量
    print(f"\n路径变量分析:")
    print(f"  总路径变量数: {len(solution_x)}")
    
    for i in range(params['num_drones']):
        drone_edges = [(f, t) for (d, f, t), v in solution_x.items() if d == i and v > 0.5]
        print(f"  无人机 {i}: {len(drone_edges)} 条路径段")
    
    return total_tasks > 0

def main_experiment3_test():
    """
    专门测试实验三的主函数
    """
    print("=== 实验三专项测试 ===")
    
    # 使用小规模数据
    params = generate_unified_scenario_data(num_drones=2, scale="small")
    
    print(f"测试参数: {params['num_points']} 个作业点, {params['num_drones']} 架无人机")
    
    # 只运行实验三
    print("\n运行实验三...")
    solution3, status3, objective3, roles3, solution_x3 = solve_moo_vrp_assignment(params)
    
    if objective3 is not None:
        print(f"实验三求解成功!")
        print(f"状态: {pl.LpStatus[status3]}")
        print(f"目标值: {objective3}")
        
        # 分析解决方案
        has_tasks = analyze_experiment3_solution(solution3, solution_x3, roles3, params)
        
        if has_tasks:
            # 提取路径
            paths = extract_paths_from_solution_x(solution_x3, roles3, params['num_drones'])
            
            # 保存结果
            desktop_path = "."
            all_results = {
                'Exp3_MOO_VRP_Fixed': {
                    'solution_t': solution3,
                    'status': pl.LpStatus[status3],
                    'objective': objective3,
                    'roles': roles3,
                    'path': paths,
                    'solution_x': solution_x3
                }
            }
            
            # 绘制图表
            plot_assignment_grid(solution3, roles3, params, desktop_path, 
                               title="Experiment 3 Fixed: MOO-VRP Result")
            plot_flight_paths(paths, roles3, params, desktop_path, 
                             title="Experiment 3 Fixed: Optimal Flight Paths", 
                             is_optimal_path=True)
            
            # 保存到Excel
            save_results_to_excel(params, all_results, desktop_path)
            
            print("\n=== 实验三测试完成 ===")
            print("✓ 路径提取成功")
            print("✓ 图表生成成功") 
            print("✓ Excel保存成功")
            
        else:
            print("警告: 实验三没有分配任何任务")
    else:
        print("实验三求解失败!")

if __name__ == "__main__":
    main_experiment3_test()
